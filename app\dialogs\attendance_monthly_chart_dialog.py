"""
Monthly Comparison Chart Dialog for FootData application.

This module provides a dialog for visualizing monthly attendance comparisons.
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime
import matplotlib.pyplot as plt

from PySide6.QtWidgets import (
    QVBoxLayout, QHB<PERSON>Layout, QLabel, QComboBox, QCheckBox, QDateEdit
)
from PySide6.QtCore import Qt
from app.utils.locale_utils import apply_locale_to_date_edit
from PySide6.QtGui import QColor

from app.dialogs.attendance_chart_base_dialog import AttendanceChartBaseDialog
from app.utils.tooltip_helper import set_tooltip
from app.utils.chart_debug import debug_attendance_data

class AttendanceMonthlyChartDialog(AttendanceChartBaseDialog):
    """Dialog for visualizing monthly attendance comparisons."""

    def __init__(self, parent=None, attendance_manager=None, roster_manager=None,
                 season_start_date=None, season_end_date=None):
        """Initialize the dialog.

        Args:
            parent (QWidget, optional): Parent widget. Defaults to None.
            attendance_manager (AttendanceManager, optional): Attendance manager instance.
            roster_manager (RosterManager, optional): Roster manager instance.
            season_start_date (QDate, optional): Season start date.
            season_end_date (QDate, optional): Season end date.
        """
        # Initialize logger
        self.logger = logging.getLogger(__name__)

        # Initialize chart settings before calling parent constructor
        self.chart_type = "bar"  # bar, line, stacked
        self.show_percentage = False
        self.selected_months = []
        self.selected_status = []  # Will be initialized after parent constructor
        self.roster_manager = roster_manager  # Store for sample data generation

        super().__init__(
            parent=parent,
            attendance_manager=attendance_manager,
            roster_manager=roster_manager,
            season_start_date=season_start_date,
            season_end_date=season_end_date,
            title="Monthly Attendance Comparison"
        )

        # Initialize status list after parent constructor (to access status_text)
        self.selected_status = list(self.status_text.keys())

        # Debug attendance data
        self._debug_attendance_data()

    def _create_filter_controls(self, layout):
        """Create filter controls.

        Args:
            layout (QLayout): The layout to add controls to.
        """
        # Date range selection
        date_layout = QHBoxLayout()

        start_label = QLabel(self.tr("Start Date:"))
        self.start_date_edit = QDateEdit()
        apply_locale_to_date_edit(self.start_date_edit)
        self.start_date_edit.setDate(self.season_start_date)
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setMinimumDate(self.season_start_date)
        self.start_date_edit.setMaximumDate(self.season_end_date)

        end_label = QLabel(self.tr("End Date:"))
        self.end_date_edit = QDateEdit()
        apply_locale_to_date_edit(self.end_date_edit)
        self.end_date_edit.setDate(self.season_end_date)
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setMinimumDate(self.season_start_date)
        self.end_date_edit.setMaximumDate(self.season_end_date)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.start_date_edit)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.end_date_edit)

        layout.addLayout(date_layout)

        # Status selection
        status_layout = QVBoxLayout()
        status_label = QLabel(self.tr("Status Types:"))
        status_layout.addWidget(status_label)

        self.status_checkboxes = {}
        for status, text in self.status_text.items():
            checkbox = QCheckBox(text)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self._on_status_changed)
            self.status_checkboxes[status] = checkbox
            status_layout.addWidget(checkbox)

        layout.addLayout(status_layout)

    def _create_chart_options(self, layout):
        """Create chart options controls.

        Args:
            layout (QLayout): The layout to add controls to.
        """
        # Chart type selection
        type_layout = QHBoxLayout()
        type_label = QLabel(self.tr("Chart Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            self.tr("Bar Chart"),
            self.tr("Line Chart"),
            self.tr("Stacked Bar Chart")
        ])
        self.chart_type_combo.currentIndexChanged.connect(self._on_chart_type_changed)

        type_layout.addWidget(type_label)
        type_layout.addWidget(self.chart_type_combo)

        layout.addLayout(type_layout)

        # Show percentage option
        self.percentage_checkbox = QCheckBox(self.tr("Show as Percentage"))
        self.percentage_checkbox.setChecked(self.show_percentage)
        self.percentage_checkbox.stateChanged.connect(self._on_percentage_changed)
        set_tooltip(self.percentage_checkbox, self.tr("Display values as percentages instead of absolute counts"))

        layout.addWidget(self.percentage_checkbox)

    def _on_chart_type_changed(self, index):
        """Handle chart type change.

        Args:
            index (int): The selected index.
        """
        chart_types = ["bar", "line", "stacked"]
        self.chart_type = chart_types[index]
        # Update the chart immediately when the chart type changes
        self._update_chart()

    def _on_percentage_changed(self, state):
        """Handle percentage checkbox change.

        Args:
            state (int): The checkbox state.
        """
        self.show_percentage = (state == Qt.CheckState.Checked)
        # Update the chart immediately when the percentage option changes
        self._update_chart()

    def _on_status_changed(self, state):
        """Handle status checkbox change.

        Args:
            state (int): The checkbox state.
        """
        # Update selected status list
        self.selected_status = [
            status for status, checkbox in self.status_checkboxes.items()
            if checkbox.isChecked()
        ]
        # Update the chart immediately when the status selection changes
        self._update_chart()

    def _update_chart(self):
        """Update the chart with current data and settings."""
        # Clear the figure
        self.figure.clear()

        # Create a new subplot
        self.ax = self.figure.add_subplot(111)

        # Get date range
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # Log current settings for debugging
        self.logger.info(f"Chart update - show_percentage: {self.show_percentage}, chart_type: {self.chart_type}")
        self.logger.info(f"Percentage checkbox state: {self.percentage_checkbox.isChecked()}")

        # Ensure show_percentage matches checkbox state
        self.show_percentage = self.percentage_checkbox.isChecked()

        # Get attendance data
        self.logger.info("Getting monthly attendance data for chart")
        attendance_data = self._get_monthly_attendance_data(start_date, end_date)

        if attendance_data is None or attendance_data.empty:
            self.logger.warning("No data available for chart")
            self.ax.set_title(self.tr("No Data Available"))
            self.ax.text(0.5, 0.5, self.tr("No attendance data found for the selected period"),
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes)
            self.canvas.draw()
            return

        self.logger.info(f"Creating {self.chart_type} chart")

        # Create the chart based on type
        if self.chart_type == "bar":
            self._create_bar_chart(attendance_data)
        elif self.chart_type == "line":
            self._create_line_chart(attendance_data)
        elif self.chart_type == "stacked":
            self._create_stacked_chart(attendance_data)

        # Set title and labels
        self.ax.set_title(self.tr("Monthly Attendance Comparison"))
        self.ax.set_xlabel(self.tr("Month"))

        # Set y-axis label based on percentage setting
        if self.show_percentage:
            self.ax.set_ylabel(self.tr("Percentage (%)"))
            # Set y-axis to show percentages properly (0-100%)
            self.ax.set_ylim(0, 100)
        else:
            self.ax.set_ylabel(self.tr("Count"))

        # Add legend
        self.ax.legend()

        # Rotate x-axis labels for better readability
        plt.setp(self.ax.get_xticklabels(), rotation=45, ha="right")

        # Adjust layout
        self.figure.tight_layout()

        # Draw the canvas
        self.canvas.draw()

    def _get_monthly_attendance_data(self, start_date, end_date):
        """Get monthly attendance data.

        Args:
            start_date (str): Start date in YYYY-MM-DD format.
            end_date (str): End date in YYYY-MM-DD format.

        Returns:
            DataFrame: Monthly attendance data.
        """
        if not self.attendance_manager:
            self.logger.warning("No attendance manager available")
            return None

        try:
            # Get attendance records
            self.logger.info(f"Getting attendance records from {start_date} to {end_date}")
            records = self.attendance_manager.get_attendance(
                start_date=start_date,
                end_date=end_date
            )

            if not records:
                self.logger.warning("No attendance records found")
                return None

            self.logger.info(f"Found {len(records)} attendance records")

            # Convert to DataFrame
            df = pd.DataFrame(records)

            # Log the first few records for debugging
            if not df.empty:
                self.logger.info(f"First record: {df.iloc[0].to_dict()}")
                self.logger.info(f"Data columns: {df.columns.tolist()}")
                self.logger.info(f"Status values: {df['status'].unique().tolist()}")

            # Extract month and year from date
            df['date'] = pd.to_datetime(df['date'])
            df['month_year'] = df['date'].dt.strftime('%Y-%m')

            # Filter by selected status
            if self.selected_status:
                self.logger.info(f"Filtering by status: {self.selected_status}")
                df = df[df['status'].isin(self.selected_status)]
            else:
                self.logger.warning("No status types selected")
                return None

            if df.empty:
                self.logger.warning("No data after filtering by status")
                return None

            # Group by month and status
            self.logger.info("Grouping data by month and status")
            monthly_data = df.groupby(['month_year', 'status']).size().unstack(fill_value=0)

            if monthly_data.empty:
                self.logger.warning("No data after grouping")
                return None

            self.logger.info(f"Monthly data shape: {monthly_data.shape}")
            self.logger.info(f"Monthly data columns: {monthly_data.columns.tolist()}")
            self.logger.info(f"Monthly data index: {monthly_data.index.tolist()}")

            # Convert to percentage if needed
            if self.show_percentage:
                self.logger.info("Converting to percentage")
                # Make sure we have data before attempting division
                if not monthly_data.empty and monthly_data.sum(axis=1).min() > 0:
                    monthly_data = monthly_data.div(monthly_data.sum(axis=1), axis=0) * 100
                    self.logger.info("Data converted to percentage successfully")
                else:
                    self.logger.warning("Cannot convert to percentage: empty data or zero sum row")

            return monthly_data

        except Exception as e:
            self.logger.error(f"Error getting monthly attendance data: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None

    def _create_bar_chart(self, data):
        """Create a bar chart.

        Args:
            data (DataFrame): Monthly attendance data.
        """
        # Plot each status as a separate bar group
        bar_width = 0.15
        x = np.arange(len(data.index))

        for i, status in enumerate(data.columns):
            color = self._get_matplotlib_color(self.status_colors.get(status, QColor(200, 200, 200)))
            self.ax.bar(
                x + i * bar_width,
                data[status],
                width=bar_width,
                label=self.status_text.get(status, status),
                color=color
            )

        # Set x-axis labels
        self.ax.set_xticks(x + bar_width * (len(data.columns) - 1) / 2)
        self.ax.set_xticklabels(data.index)

    def _create_line_chart(self, data):
        """Create a line chart.

        Args:
            data (DataFrame): Monthly attendance data.
        """
        # Plot each status as a separate line
        for status in data.columns:
            color = self._get_matplotlib_color(self.status_colors.get(status, QColor(200, 200, 200)))
            self.ax.plot(
                data.index,
                data[status],
                marker='o',
                label=self.status_text.get(status, status),
                color=color
            )

    def _create_stacked_chart(self, data):
        """Create a stacked bar chart.

        Args:
            data (DataFrame): Monthly attendance data.
        """
        # Plot stacked bars
        bottom = np.zeros(len(data.index))

        for status in data.columns:
            color = self._get_matplotlib_color(self.status_colors.get(status, QColor(200, 200, 200)))
            self.ax.bar(
                data.index,
                data[status],
                bottom=bottom,
                label=self.status_text.get(status, status),
                color=color
            )
            bottom += data[status]

    def _debug_attendance_data(self):
        """Debug attendance data for charts."""
        if not self.attendance_manager:
            self.logger.warning("No attendance manager available for debugging")
            return

        # Get date range
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # Debug attendance data
        debug_info = debug_attendance_data(self.attendance_manager, start_date, end_date)

        # Log debug information
        self.logger.info(f"Attendance data debug info: {debug_info}")

        # Check if there's any data
        if not debug_info['has_data']:
            self.logger.warning("No attendance data available for the selected period.")


