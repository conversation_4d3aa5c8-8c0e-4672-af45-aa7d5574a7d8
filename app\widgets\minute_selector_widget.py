"""
Compact minute selector widget with grid layout.
"""

import logging
from typing import List, Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QGridLayout, QSizePolicy, QTableWidget,
    QTableWidgetItem, QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor


class MinuteSelectorWidget(QWidget):
    """Compact minute selector with 5-column grid layout like a calendar."""

    minute_selected = Signal(int)  # Selected minute
    _configured_row_height = 20  # Default row height, can be overridden by settings
    _configured_table_height = 220  # Default table height, can be overridden by settings

    def __init__(self, minutes: List[int], current_minute: int = 0, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.minute_selector")

        self.minutes = minutes
        self.current_minute = current_minute
        self.selected_minute = current_minute

        self._init_ui()

    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # Header
        header = self._create_header()
        layout.addWidget(header)

        # Create table for minute grid
        self.minute_table = QTableWidget()
        # Use configured table height or default
        table_height = getattr(MinuteSelectorWidget, '_configured_table_height', 220)
        self.minute_table.setMaximumHeight(table_height)

        # Calculate rows and columns
        columns = 5
        rows = (len(self.minutes) + columns - 1) // columns

        self.minute_table.setRowCount(rows)
        self.minute_table.setColumnCount(columns)

        # Hide headers
        self.minute_table.horizontalHeader().setVisible(False)
        self.minute_table.verticalHeader().setVisible(False)

        # Set selection mode
        self.minute_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Set uniform row and column sizes
        self.minute_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.minute_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Fixed)

        # Set comfortable row height for better visibility
        row_height = getattr(MinuteSelectorWidget, '_configured_row_height', 20)
        for row in range(rows):
            self.minute_table.setRowHeight(row, row_height)

        # Populate table with minutes
        self.minute_items = {}
        for i, minute in enumerate(self.minutes):
            row = i // columns
            col = i % columns

            item = QTableWidgetItem(f"{minute}'")
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            item.setData(Qt.ItemDataRole.UserRole, minute)

            # Style the item
            if minute == self.current_minute and self.current_minute > 0:
                item.setBackground(QColor("#4CAF50"))
                item.setForeground(QColor("white"))
            else:
                item.setBackground(QColor("#f0f0f0"))
                item.setForeground(QColor("#333"))

            # Set font - slightly larger for better visibility
            font = QFont()
            font.setPointSize(8)
            item.setFont(font)

            self.minute_table.setItem(row, col, item)
            self.minute_items[minute] = item

        # Connect selection signal
        self.minute_table.itemClicked.connect(self._on_table_item_clicked)

        layout.addWidget(self.minute_table)

        # Action buttons
        actions = self._create_actions()
        layout.addWidget(actions)

    def _create_header(self) -> QWidget:
        """Create header with info."""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # Info label
        info_label = QLabel(f"Select minute ({len(self.minutes)} available)")
        info_label.setStyleSheet("color: #666; font-size: 11px;")

        # Current selection
        self.selection_label = QLabel(f"Selected: {self.current_minute}'" if self.current_minute > 0 else "No selection")
        self.selection_label.setStyleSheet("color: #2196F3; font-weight: bold; font-size: 11px;")

        layout.addWidget(info_label)
        layout.addStretch()
        layout.addWidget(self.selection_label)

        return widget

    def _create_actions(self) -> QWidget:
        """Create action buttons."""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # Clear selection
        clear_btn = QPushButton("Clear")
        clear_btn.setFixedSize(60, 25)
        clear_btn.clicked.connect(self._clear_selection)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)

        # OK button
        ok_btn = QPushButton("OK")
        ok_btn.setFixedSize(60, 25)
        ok_btn.clicked.connect(self._confirm_selection)
        ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        layout.addWidget(clear_btn)
        layout.addStretch()
        layout.addWidget(ok_btn)

        return widget

    def _on_table_item_clicked(self, item):
        """Handle table item click."""
        if item:
            minute = item.data(Qt.ItemDataRole.UserRole)

            # Clear previous selection styling
            for m, itm in self.minute_items.items():
                if m == self.current_minute and self.current_minute > 0:
                    itm.setBackground(QColor("#4CAF50"))
                    itm.setForeground(QColor("white"))
                else:
                    itm.setBackground(QColor("#f0f0f0"))
                    itm.setForeground(QColor("#333"))

            # Highlight selected item
            item.setBackground(QColor("#2196F3"))
            item.setForeground(QColor("white"))

            # Update selection
            self.selected_minute = minute
            self.selection_label.setText(f"Selected: {minute}'")

            self.logger.info(f"Minute selected: {minute}")

            # Emit signal immediately for single-click selection
            self.minute_selected.emit(self.selected_minute)

    def _clear_selection(self):
        """Clear the current selection."""
        # Reset all items to default styling
        for m, item in self.minute_items.items():
            if m == self.current_minute and self.current_minute > 0:
                item.setBackground(QColor("#4CAF50"))
                item.setForeground(QColor("white"))
            else:
                item.setBackground(QColor("#f0f0f0"))
                item.setForeground(QColor("#333"))

        # Clear table selection
        self.minute_table.clearSelection()

        self.selected_minute = 0
        self.selection_label.setText("No selection")

    def _confirm_selection(self):
        """Confirm the selection and emit signal."""
        self.minute_selected.emit(self.selected_minute)

    def get_selected_minute(self) -> int:
        """Get the currently selected minute."""
        return self.selected_minute


class MinuteSelectorPopup(QWidget):
    """Popup widget containing the minute selector."""

    minute_selected = Signal(int)
    _configured_height = 300  # Default height, can be overridden by settings

    def __init__(self, minutes: List[int], current_minute: int = 0, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)

        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(1, 1, 1, 1)

        # Add frame for border
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box)
        frame.setStyleSheet("QFrame { border: 1px solid #ccc; background-color: white; }")
        frame_layout = QVBoxLayout(frame)

        # Create selector
        self.selector = MinuteSelectorWidget(minutes, current_minute)
        self.selector.minute_selected.connect(self._on_minute_selected)

        frame_layout.addWidget(self.selector)
        layout.addWidget(frame)

        # Set size using configured height or default
        popup_height = getattr(MinuteSelectorPopup, '_configured_height', 300)
        self.setFixedSize(260, popup_height)

    def _on_minute_selected(self, minute: int):
        """Handle minute selection."""
        self.minute_selected.emit(minute)
        self.close()

    def show_at_position(self, pos):
        """Show popup at specific position."""
        self.move(pos)
        self.show()
        self.raise_()
        self.activateWindow()
