"""
Football Rules Settings utility for managing game rules and data validation settings.
This module provides functions to save and load football rules settings.
"""

from PySide6.QtCore import QSettings


def save_football_rules_settings(players_per_side=11, max_subs=5, min_to_start=9, 
                                min_during_match=7, body_data_threshold=50):
    """
    Save football rules settings to QSettings.
    
    Args:
        players_per_side: Number of players per team on the field (default: 11)
        max_subs: Maximum number of substitutions allowed (default: 5)
        min_to_start: Minimum players required to start a match (default: 9)
        min_during_match: Minimum players required during a match (default: 7)
        body_data_threshold: Percentage threshold for outlier detection (default: 50)
    """
    settings = QSettings()
    settings.beginGroup("football_rules")
    
    settings.setValue("players_per_side", players_per_side)
    settings.setValue("max_substitutions", max_subs)
    settings.setValue("min_to_start", min_to_start)
    settings.setValue("min_during_match", min_during_match)
    settings.setValue("body_data_threshold", body_data_threshold)
    
    settings.endGroup()


def load_football_rules_settings():
    """
    Load football rules settings from QSettings.
    
    Returns:
        Dictionary with all football rules settings
    """
    settings = QSettings()
    settings.beginGroup("football_rules")
    
    rules = {
        'players_per_side': settings.value("players_per_side", 11, int),
        'max_substitutions': settings.value("max_substitutions", 5, int),
        'min_to_start': settings.value("min_to_start", 9, int),
        'min_during_match': settings.value("min_during_match", 7, int),
        'body_data_threshold': settings.value("body_data_threshold", 50, int)
    }
    
    settings.endGroup()
    return rules


def get_body_data_threshold():
    """
    Get the current body data outlier threshold percentage.
    
    Returns:
        Integer percentage threshold (default: 50)
    """
    settings = QSettings()
    settings.beginGroup("football_rules")
    threshold = settings.value("body_data_threshold", 50, int)
    settings.endGroup()
    return threshold


def set_body_data_threshold(threshold):
    """
    Set the body data outlier threshold percentage.
    
    Args:
        threshold: Integer percentage threshold (10-100)
    """
    # Validate threshold range
    threshold = max(10, min(100, int(threshold)))
    
    settings = QSettings()
    settings.beginGroup("football_rules")
    settings.setValue("body_data_threshold", threshold)
    settings.endGroup()


def get_game_rules():
    """
    Get the current game rules settings.
    
    Returns:
        Dictionary with game rules (players_per_side, max_substitutions, etc.)
    """
    settings = QSettings()
    settings.beginGroup("football_rules")
    
    rules = {
        'players_per_side': settings.value("players_per_side", 11, int),
        'max_substitutions': settings.value("max_substitutions", 5, int),
        'min_to_start': settings.value("min_to_start", 9, int),
        'min_during_match': settings.value("min_during_match", 7, int)
    }
    
    settings.endGroup()
    return rules


def validate_starting_lineup_size(lineup_size):
    """
    Check if the starting lineup size meets the minimum requirements.
    
    Args:
        lineup_size: Number of players in the starting lineup
        
    Returns:
        Tuple of (is_valid, message)
    """
    rules = get_game_rules()
    min_required = rules['min_to_start']
    
    if lineup_size < min_required:
        return False, f"Starting lineup requires at least {min_required} players. Current: {lineup_size}"
    
    return True, "Starting lineup size is valid"


def validate_match_continuation(current_players):
    """
    Check if the current number of players allows the match to continue.
    
    Args:
        current_players: Current number of players on the field
        
    Returns:
        Tuple of (can_continue, message)
    """
    rules = get_game_rules()
    min_required = rules['min_during_match']
    
    if current_players < min_required:
        return False, f"Match cannot continue with fewer than {min_required} players. Current: {current_players}"
    
    return True, "Sufficient players to continue match"


def validate_substitutions_count(subs_used):
    """
    Check if the number of substitutions used is within the allowed limit.
    
    Args:
        subs_used: Number of substitutions already used
        
    Returns:
        Tuple of (can_substitute, message)
    """
    rules = get_game_rules()
    max_subs = rules['max_substitutions']
    
    if subs_used >= max_subs:
        return False, f"Maximum substitutions ({max_subs}) already used"
    
    remaining = max_subs - subs_used
    return True, f"{remaining} substitution(s) remaining"


def get_default_settings():
    """
    Get the default football rules settings.
    
    Returns:
        Dictionary with default settings
    """
    return {
        'players_per_side': 11,
        'max_substitutions': 5,
        'min_to_start': 9,
        'min_during_match': 7,
        'body_data_threshold': 50
    }


def reset_to_defaults():
    """Reset all football rules settings to their default values."""
    defaults = get_default_settings()
    save_football_rules_settings(**defaults)


def export_settings():
    """
    Export current football rules settings.
    
    Returns:
        Dictionary with current settings
    """
    return load_football_rules_settings()


def import_settings(settings_dict):
    """
    Import football rules settings from a dictionary.
    
    Args:
        settings_dict: Dictionary with settings to import
    """
    defaults = get_default_settings()
    
    # Merge with defaults to ensure all keys are present
    merged_settings = {**defaults, **settings_dict}
    
    save_football_rules_settings(**merged_settings)
