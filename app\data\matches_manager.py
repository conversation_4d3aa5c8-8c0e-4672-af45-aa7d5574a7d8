import sqlite3
import logging
from pathlib import Path

class MatchesManager:
    """Manager for handling match data in the database."""

    def __init__(self, db_name="footdata.db"):
        """Initialize the matches manager with a database connection."""
        self.logger = logging.getLogger("app.matches_manager")

        # Use the same database path initialization as other managers
        script_dir = Path(__file__).parent
        self.db_path = script_dir / db_name
        self.logger.info(f"Database path: {self.db_path}")

        self.conn = None
        self.cursor = None

        # Connect to the database
        self._connect_db()

        # Create tables if they don't exist
        self._create_competitions_table()
        self._create_matches_tables()

    def _connect_db(self):
        """Connect to the SQLite database."""
        try:
            # Ensure the parent directory exists
            self.db_path.parent.mkdir(parents=True, exist_ok=True)

            # Check if the database file exists
            db_exists = self.db_path.exists()

            # Connect to the database
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # Return rows as dictionaries
            self.cursor = self.conn.cursor()

            # Enable foreign key support
            self.cursor.execute("PRAGMA foreign_keys = ON;")

            if not db_exists:
                self.logger.info(f"Created new database: {self.db_path}")
            else:
                self.logger.info(f"Connected to existing database: {self.db_path}")

        except sqlite3.Error as e:
            self.logger.error(f"Error connecting to database: {e}")

    def _create_competitions_table(self):
        """Create the competitions table if it doesn't exist."""
        if not self.conn:
            return

        try:
            # Create competitions table
            create_competitions_sql = """
            CREATE TABLE IF NOT EXISTS competitions (
                competition_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                organization TEXT,
                type TEXT,
                structure TEXT,
                notes TEXT,
                x_a_side INTEGER DEFAULT 11,
                match_duration INTEGER DEFAULT 90,
                subs_allowed INTEGER DEFAULT 5,
                minimum_players_allowed INTEGER DEFAULT 7,
                minimum_required_to_start INTEGER DEFAULT 9
            )
            """

            self.cursor.execute(create_competitions_sql)
            self.conn.commit()
            self.logger.info("'competitions' table created or already exists.")

            # Check if we need to populate the competitions table from settings
            self.cursor.execute("SELECT COUNT(*) FROM competitions")
            count = self.cursor.fetchone()[0]

            if count == 0:
                # Populate competitions from settings
                competitions = self.get_competitions()
                if competitions:
                    insert_sql = """
                    INSERT INTO competitions (
                        competition_id, name, organization, type, structure, notes,
                        x_a_side, match_duration, subs_allowed, minimum_players_allowed, minimum_required_to_start
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """

                    for comp in competitions:
                        self.cursor.execute(insert_sql, (
                            comp.get('id', ''),
                            comp.get('name', ''),
                            comp.get('organization', ''),
                            comp.get('type', ''),
                            comp.get('structure', ''),
                            comp.get('notes', ''),
                            comp.get('x_a_side', 11),
                            comp.get('match_duration', 90),
                            comp.get('subs_allowed', 5),
                            comp.get('minimum_players_allowed', 7),
                            comp.get('minimum_required_to_start', 9)
                        ))

                    self.conn.commit()
                    self.logger.info(f"Populated competitions table with {len(competitions)} competitions from settings.")

        except sqlite3.Error as e:
            self.logger.error(f"Error creating competitions table: {e}")
            import traceback
            self.logger.error(f"Detailed error: {traceback.format_exc()}")

    def _create_matches_tables(self):
        """Create the necessary tables for match management if they don't exist."""
        if not self.conn:
            return

        # Check if we need to recreate the matches table due to schema changes
        try:
            # Check if the competition_id column is TEXT type
            self.cursor.execute("PRAGMA table_info(matches)")
            columns = self.cursor.fetchall()

            needs_recreation = False
            for col in columns:
                if col[1] == 'competition_id' and col[2] != 'TEXT':
                    self.logger.warning("Matches table has incorrect competition_id type. Will attempt to recreate.")
                    needs_recreation = True
                    break

            if needs_recreation:
                # We're not actually restoring the data, just recreating the table
                # This is acceptable since this is a development version
                self.logger.info("Matches table will be recreated with the correct schema.")

                # Drop the table
                self.cursor.execute("DROP TABLE IF EXISTS matches")
                self.logger.info("Dropped matches table for recreation.")

                # The table will be recreated below
        except sqlite3.Error as e:
            self.logger.error(f"Error checking matches table schema: {e}")
            # Continue with table creation anyway

        # Create the main matches table
        create_matches_sql = """
        CREATE TABLE IF NOT EXISTS matches (
            match_id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            time TEXT,
            competition_id TEXT,
            season TEXT,
            matchday TEXT,
            is_home BOOLEAN NOT NULL,
            opponent TEXT NOT NULL,
            team_score INTEGER DEFAULT 0,
            opponent_score INTEGER DEFAULT 0,
            ht_team_score INTEGER DEFAULT 0,
            ht_opponent_score INTEGER DEFAULT 0,
            et_played BOOLEAN DEFAULT 0,
            et_team_score INTEGER DEFAULT 0,
            et_opponent_score INTEGER DEFAULT 0,
            et_duration INTEGER DEFAULT 30,
            penalties_played BOOLEAN DEFAULT 0,
            penalties_team_score INTEGER DEFAULT 0,
            penalties_opponent_score INTEGER DEFAULT 0,
            opponents_own_goals INTEGER DEFAULT 0,
            match_duration INTEGER DEFAULT 90,
            stoppage_time INTEGER DEFAULT 0,
            venue TEXT,
            city TEXT,
            country TEXT,
            weather TEXT,
            attendance INTEGER DEFAULT 0,
            broadcast TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (competition_id) REFERENCES competitions(competition_id) ON DELETE SET NULL
        );
        """

        # Create the match officials table
        create_officials_sql = """
        CREATE TABLE IF NOT EXISTS match_officials (
            official_id INTEGER PRIMARY KEY AUTOINCREMENT,
            match_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            role TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE
        );
        """

        # Create the match officials rating table
        create_officials_rating_sql = """
        CREATE TABLE IF NOT EXISTS match_officials_rating (
            rating_id INTEGER PRIMARY KEY AUTOINCREMENT,
            match_id INTEGER NOT NULL UNIQUE,
            rating INTEGER NOT NULL CHECK(rating >= 1 AND rating <= 10),
            comments TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE
        );
        """

        # Create the match team staff table
        create_team_staff_sql = """
        CREATE TABLE IF NOT EXISTS match_team_staff (
            staff_id INTEGER PRIMARY KEY AUTOINCREMENT,
            match_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            role TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE
        );
        """

        # Create indexes for better performance
        create_indexes_sql = [
            "CREATE INDEX IF NOT EXISTS idx_matches_date ON matches(date);",
            "CREATE INDEX IF NOT EXISTS idx_matches_competition ON matches(competition_id);",
            "CREATE INDEX IF NOT EXISTS idx_matches_season ON matches(season);",
            "CREATE INDEX IF NOT EXISTS idx_matches_opponent ON matches(opponent);"
        ]

        try:
            # Create tables
            self.cursor.execute(create_matches_sql)
            self.cursor.execute(create_officials_sql)
            self.cursor.execute(create_officials_rating_sql)
            self.cursor.execute(create_team_staff_sql)

            # Create indexes
            for index_sql in create_indexes_sql:
                self.cursor.execute(index_sql)

            self.conn.commit()
            self.logger.info("Match tables created or verified successfully")
        except sqlite3.Error as e:
            self.logger.error(f"Error creating match tables: {e}")

    def add_match(self, match_data):
        """Add a new match to the database.

        Args:
            match_data (dict): Dictionary containing match data

        Returns:
            int: The ID of the newly created match, or None if there was an error
        """
        if not self.conn:
            return None

        try:
            # Log the match data for debugging
            self.logger.info("Attempting to add match with data:")
            for key, value in match_data.items():
                if key != 'match_stats':  # Skip match_stats for brevity
                    self.logger.info(f"  {key}: {value}")
                else:
                    self.logger.info(f"  {key}: <match statistics data>")

            # Insert match data
            insert_sql = """
            INSERT INTO matches (
                date, time, competition_id, season, matchday,
                is_home, opponent, team_score, opponent_score,
                ht_team_score, ht_opponent_score, et_played,
                et_team_score, et_opponent_score, et_duration, penalties_played,
                penalties_team_score, penalties_opponent_score, opponents_own_goals,
                match_duration, stoppage_time,
                venue, city, country, weather, attendance, broadcast
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            # Get competition_id from match_data
            competition_id = match_data.get('competition_id')

            # Verify that the competition exists in the competitions table
            if competition_id:
                self.cursor.execute("SELECT competition_id FROM competitions WHERE competition_id = ?", (competition_id,))
                result = self.cursor.fetchone()
                if not result:
                    # Competition doesn't exist in the database, try to add it
                    self.logger.warning(f"Competition ID '{competition_id}' not found in database. Attempting to add it.")
                    competitions = self.get_competitions()
                    comp_found = False

                    for comp in competitions:
                        if comp.get('id') == competition_id:
                            comp_found = True
                            try:
                                insert_comp_sql = """
                                INSERT INTO competitions (
                                    competition_id, name, organization, type, structure, notes,
                                    x_a_side, match_duration, subs_allowed, minimum_players_allowed, minimum_required_to_start
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """

                                self.cursor.execute(insert_comp_sql, (
                                    comp.get('id', ''),
                                    comp.get('name', ''),
                                    comp.get('organization', ''),
                                    comp.get('type', ''),
                                    comp.get('structure', ''),
                                    comp.get('notes', ''),
                                    comp.get('x_a_side', 11),
                                    comp.get('match_duration', 90),
                                    comp.get('subs_allowed', 5),
                                    comp.get('minimum_players_allowed', 7),
                                    comp.get('minimum_required_to_start', 9)
                                ))

                                self.conn.commit()
                                self.logger.info(f"Added competition '{comp.get('name')}' to database.")
                                break
                            except sqlite3.Error as e:
                                self.logger.error(f"Error adding competition: {e}")
                                # Continue with the match insertion anyway

                    if not comp_found:
                        self.logger.warning(f"Competition ID '{competition_id}' not found in settings either. Match may fail to save.")

            # Extract values from match_data
            values = (
                match_data.get('date'),
                match_data.get('time'),
                competition_id,  # Use the validated competition_id
                match_data.get('season'),
                match_data.get('matchday'),
                match_data.get('is_home', False),
                match_data.get('opponent', ''),
                match_data.get('team_score', 0),
                match_data.get('opponent_score', 0),
                match_data.get('ht_team_score', 0),
                match_data.get('ht_opponent_score', 0),
                match_data.get('et_played', False),
                match_data.get('et_team_score', 0),
                match_data.get('et_opponent_score', 0),
                match_data.get('et_duration', 30),
                match_data.get('penalties_played', False),
                match_data.get('penalties_team_score', 0),
                match_data.get('penalties_opponent_score', 0),
                match_data.get('opponents_own_goals', 0),
                match_data.get('match_duration', 90),
                match_data.get('stoppage_time', 0),
                match_data.get('venue'),
                match_data.get('city'),
                match_data.get('country'),
                match_data.get('weather'),
                match_data.get('attendance', 0),
                match_data.get('broadcast')
            )

            # Log the SQL and values for debugging
            self.logger.info(f"SQL: {insert_sql}")
            self.logger.info(f"Values: {values}")

            self.cursor.execute(insert_sql, values)
            match_id = self.cursor.lastrowid

            # Add match statistics if provided
            match_stats = match_data.get('match_stats', {})
            if match_stats:
                try:
                    self._add_match_statistics(match_id, match_stats)
                    self.logger.info(f"Added match statistics for match ID: {match_id}")
                except Exception as stats_error:
                    self.logger.error(f"Error adding match statistics: {stats_error}")
                    # Continue with other parts even if statistics fail

            # Add officials if provided
            officials = match_data.get('officials', [])
            if officials:
                try:
                    self._add_match_officials(match_id, officials)
                    self.logger.info(f"Added match officials for match ID: {match_id}")
                except Exception as officials_error:
                    self.logger.error(f"Error adding match officials: {officials_error}")
                    # Continue with other parts even if officials fail

            # Add officials rating if provided
            rating = match_data.get('officials_rating')
            comments = match_data.get('officials_comments', '')
            if rating:
                try:
                    self._add_officials_rating(match_id, rating, comments)
                    self.logger.info(f"Added officials rating for match ID: {match_id}")
                except Exception as rating_error:
                    self.logger.error(f"Error adding officials rating: {rating_error}")
                    # Continue with other parts even if rating fails

            # Add team staff if provided
            team_staff = match_data.get('team_staff', [])
            if team_staff:
                try:
                    self._add_team_staff(match_id, team_staff)
                    self.logger.info(f"Added team staff for match ID: {match_id}")
                except Exception as staff_error:
                    self.logger.error(f"Error adding team staff: {staff_error}")
                    # Continue with other parts even if team staff fail

            self.conn.commit()
            self.logger.info(f"Successfully added match with ID: {match_id}")
            return match_id

        except sqlite3.Error as e:
            self.logger.error(f"Error adding match: {e}")
            # Try to get more detailed error information
            if "UNIQUE constraint failed" in str(e):
                self.logger.error("This appears to be a duplicate match entry")
            elif "NOT NULL constraint failed" in str(e):
                self.logger.error(f"A required field is missing: {str(e)}")
            elif "FOREIGN KEY constraint failed" in str(e):
                self.logger.error(f"Foreign key constraint failed: {str(e)}")

            # Print more detailed error information for debugging
            import traceback
            self.logger.error(f"Detailed error: {traceback.format_exc()}")

            # Rollback the transaction
            if self.conn:
                self.conn.rollback()

            return None
        except Exception as e:
            self.logger.error(f"Unexpected error adding match: {e}")
            import traceback
            self.logger.error(f"Detailed error: {traceback.format_exc()}")

            # Rollback the transaction
            if self.conn:
                self.conn.rollback()

            return None

    def _add_match_officials(self, match_id, officials):
        """Add officials for a match.

        Args:
            match_id (int): The match ID
            officials (list): List of dictionaries with official data
        """
        if not officials:
            return

        insert_sql = """
        INSERT INTO match_officials (match_id, name, role)
        VALUES (?, ?, ?)
        """

        try:
            for official in officials:
                if official.get('name'):
                    self.cursor.execute(insert_sql, (
                        match_id,
                        official.get('name'),
                        official.get('role', 'Referee')
                    ))
        except sqlite3.Error as e:
            self.logger.error(f"Error adding match officials: {e}")
            raise

    def _add_match_statistics(self, match_id, match_stats):
        """Add match statistics for a match.

        Args:
            match_id (int): The match ID
            match_stats (dict): Dictionary with team and opponent statistics
        """
        if not match_stats:
            return

        # Check if we have team and opponent stats
        team_stats = match_stats.get('team', {})
        opponent_stats = match_stats.get('opponent', {})

        if not team_stats and not opponent_stats:
            self.logger.warning(f"No statistics provided for match ID: {match_id}")
            return

        # Create the match_statistics table if it doesn't exist
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS match_statistics (
            stat_id INTEGER PRIMARY KEY AUTOINCREMENT,
            match_id INTEGER NOT NULL,
            team_type TEXT NOT NULL,  -- 'team' or 'opponent'
            stat_key TEXT NOT NULL,
            stat_value REAL NOT NULL,
            FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE,
            UNIQUE(match_id, team_type, stat_key)
        )
        """

        try:
            self.cursor.execute(create_table_sql)

            # Insert statistics for the team
            self._insert_team_statistics(match_id, 'team', team_stats)

            # Insert statistics for the opponent
            self._insert_team_statistics(match_id, 'opponent', opponent_stats)

        except sqlite3.Error as e:
            self.logger.error(f"Error creating match_statistics table: {e}")
            raise

    def _insert_team_statistics(self, match_id, team_type, stats):
        """Insert statistics for a team.

        Args:
            match_id (int): The match ID
            team_type (str): 'team' or 'opponent'
            stats (dict): Dictionary with statistics
        """
        if not stats:
            return

        insert_sql = """
        INSERT OR REPLACE INTO match_statistics (match_id, team_type, stat_key, stat_value)
        VALUES (?, ?, ?, ?)
        """

        try:
            for stat_key, stat_value in stats.items():
                # Skip empty or None values
                if stat_value is None:
                    continue

                # Convert to float if possible
                try:
                    if isinstance(stat_value, str) and stat_value.strip():
                        stat_value = float(stat_value)
                    elif not isinstance(stat_value, (int, float)):
                        stat_value = 0
                except ValueError:
                    # If conversion fails, use 0
                    stat_value = 0

                self.cursor.execute(insert_sql, (match_id, team_type, stat_key, stat_value))

        except sqlite3.Error as e:
            self.logger.error(f"Error inserting {team_type} statistics: {e}")
            raise

    def _add_officials_rating(self, match_id, rating, comments=''):
        """Add rating for match officials.

        Args:
            match_id (int): The match ID
            rating (int): Rating value (1-10)
            comments (str): Optional comments about the officiating
        """
        insert_sql = """
        INSERT INTO match_officials_rating (match_id, rating, comments)
        VALUES (?, ?, ?)
        """

        try:
            self.cursor.execute(insert_sql, (match_id, rating, comments))
        except sqlite3.Error as e:
            self.logger.error(f"Error adding officials rating: {e}")
            raise

    def _add_team_staff(self, match_id, team_staff):
        """Add team staff for a match.

        Args:
            match_id (int): The match ID
            team_staff (list): List of dictionaries with staff data
        """
        if not team_staff:
            return

        insert_sql = """
        INSERT INTO match_team_staff (match_id, name, role)
        VALUES (?, ?, ?)
        """

        try:
            for staff in team_staff:
                if staff.get('name'):
                    self.cursor.execute(insert_sql, (
                        match_id,
                        staff.get('name'),
                        staff.get('role', '')
                    ))
        except sqlite3.Error as e:
            self.logger.error(f"Error adding team staff: {e}")
            raise

    def update_match(self, match_data):
        """Update an existing match in the database.

        Args:
            match_data (dict): Dictionary containing updated match data with match_id

        Returns:
            bool: True if successful, False otherwise
        """
        match_id = match_data.get('match_id')
        if not self.conn or not match_id:
            return False

        try:
            # Update match data
            update_sql = """
            UPDATE matches SET
                date = ?,
                time = ?,
                competition_id = ?,
                season = ?,
                matchday = ?,
                is_home = ?,
                opponent = ?,
                team_score = ?,
                opponent_score = ?,
                ht_team_score = ?,
                ht_opponent_score = ?,
                et_played = ?,
                et_team_score = ?,
                et_opponent_score = ?,
                et_duration = ?,
                penalties_played = ?,
                penalties_team_score = ?,
                penalties_opponent_score = ?,
                opponents_own_goals = ?,
                match_duration = ?,
                stoppage_time = ?,
                venue = ?,
                city = ?,
                country = ?,
                weather = ?,
                attendance = ?,
                broadcast = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE match_id = ?
            """

            # Extract values from match_data
            values = (
                match_data.get('date'),
                match_data.get('time'),
                match_data.get('competition_id'),
                match_data.get('season'),
                match_data.get('matchday'),
                match_data.get('is_home', False),
                match_data.get('opponent', ''),
                match_data.get('team_score', 0),
                match_data.get('opponent_score', 0),
                match_data.get('ht_team_score', 0),
                match_data.get('ht_opponent_score', 0),
                match_data.get('et_played', False),
                match_data.get('et_team_score', 0),
                match_data.get('et_opponent_score', 0),
                match_data.get('et_duration', 30),
                match_data.get('penalties_played', False),
                match_data.get('penalties_team_score', 0),
                match_data.get('penalties_opponent_score', 0),
                match_data.get('opponents_own_goals', 0),
                match_data.get('match_duration', 90),
                match_data.get('stoppage_time', 0),
                match_data.get('venue'),
                match_data.get('city'),
                match_data.get('country'),
                match_data.get('weather'),
                match_data.get('attendance', 0),
                match_data.get('broadcast'),
                match_id
            )

            self.cursor.execute(update_sql, values)

            # Update officials if provided
            officials = match_data.get('officials')
            if officials is not None:
                # Delete existing officials
                self.cursor.execute("DELETE FROM match_officials WHERE match_id = ?", (match_id,))
                # Add new officials
                self._add_match_officials(match_id, officials)

            # Update officials rating if provided
            rating = match_data.get('officials_rating')
            if rating is not None:
                # Delete existing rating
                self.cursor.execute("DELETE FROM match_officials_rating WHERE match_id = ?", (match_id,))
                # Add new rating
                self._add_officials_rating(match_id, rating, match_data.get('officials_comments', ''))

            # Update team staff if provided
            team_staff = match_data.get('team_staff')
            if team_staff is not None:
                # Delete existing team staff
                self.cursor.execute("DELETE FROM match_team_staff WHERE match_id = ?", (match_id,))
                # Add new team staff
                self._add_team_staff(match_id, team_staff)

            self.conn.commit()
            self.logger.info(f"Updated match with ID: {match_id}")
            return True

        except sqlite3.Error as e:
            self.logger.error(f"Error updating match: {e}")
            # Print more detailed error information for debugging
            import traceback
            self.logger.error(f"Detailed error: {traceback.format_exc()}")
            self.conn.rollback()
            return False

    def delete_match(self, match_id):
        """Delete a match from the database.

        Args:
            match_id (int): The ID of the match to delete

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.conn or not match_id:
            return False

        try:
            # Delete match and related data (cascade will handle related tables)
            self.cursor.execute("DELETE FROM matches WHERE match_id = ?", (match_id,))
            self.conn.commit()
            self.logger.info(f"Deleted match with ID: {match_id}")
            return True

        except sqlite3.Error as e:
            self.logger.error(f"Error deleting match: {e}")
            self.conn.rollback()
            return False

    def get_match(self, match_id):
        """Get a match by ID.

        Args:
            match_id (int): The ID of the match to retrieve

        Returns:
            dict: Match data or None if not found
        """
        if not self.conn or not match_id:
            return None

        try:
            # Get match data
            self.cursor.execute("SELECT * FROM matches WHERE match_id = ?", (match_id,))
            match = dict(self.cursor.fetchone() or {})

            if not match:
                return None

            # Get officials
            self.cursor.execute("SELECT * FROM match_officials WHERE match_id = ?", (match_id,))
            match['officials'] = [dict(row) for row in self.cursor.fetchall()]

            # Get officials rating
            self.cursor.execute("SELECT * FROM match_officials_rating WHERE match_id = ?", (match_id,))
            rating_row = self.cursor.fetchone()
            if rating_row:
                match['officials_rating'] = dict(rating_row)

            # Get team staff
            self.cursor.execute("SELECT * FROM match_team_staff WHERE match_id = ?", (match_id,))
            match['team_staff'] = [dict(row) for row in self.cursor.fetchall()]

            # Get match statistics
            match_stats = {'team': {}, 'opponent': {}}

            # Query for match statistics
            self.cursor.execute("""
                SELECT team_type, stat_key, stat_value
                FROM match_statistics
                WHERE match_id = ?
            """, (match_id,))

            # Process statistics results
            for row in self.cursor.fetchall():
                team_type = row['team_type']
                stat_key = row['stat_key']
                stat_value = row['stat_value']

                # Add to the appropriate team stats dictionary
                if team_type in match_stats:
                    match_stats[team_type][stat_key] = stat_value

            # Add statistics to match data
            match['match_stats'] = match_stats

            self.logger.info(f"Loaded match statistics for match ID {match_id}: {match_stats}")

            return match

        except sqlite3.Error as e:
            self.logger.error(f"Error getting match: {e}")
            return None

    def get_matches(self, filters=None, sort_by='date', sort_order='DESC', limit=None):
        """Get matches with optional filtering and sorting.

        Args:
            filters (dict): Optional filters to apply (competition_id, season, date_from, date_to, etc.)
            sort_by (str): Field to sort by (default: 'date')
            sort_order (str): Sort order ('ASC' or 'DESC', default: 'DESC')
            limit (int): Maximum number of matches to return

        Returns:
            list: List of match dictionaries
        """
        if not self.conn:
            return []

        try:
            # Start building the query
            query = "SELECT * FROM matches"
            params = []

            # Apply filters if provided
            if filters:
                where_clauses = []

                if 'competition_id' in filters and filters['competition_id']:
                    where_clauses.append("competition_id = ?")
                    params.append(filters['competition_id'])

                if 'season' in filters and filters['season']:
                    where_clauses.append("season = ?")
                    params.append(filters['season'])

                if 'opponent' in filters and filters['opponent']:
                    where_clauses.append("opponent LIKE ?")
                    params.append(f"%{filters['opponent']}%")

                if 'date_from' in filters and filters['date_from']:
                    where_clauses.append("date >= ?")
                    params.append(filters['date_from'])

                if 'date_to' in filters and filters['date_to']:
                    where_clauses.append("date <= ?")
                    params.append(filters['date_to'])

                if 'result' in filters:
                    if filters['result'] == 'win':
                        where_clauses.append("((is_home = 1 AND team_score > opponent_score) OR (is_home = 0 AND team_score < opponent_score))")
                    elif filters['result'] == 'draw':
                        where_clauses.append("team_score = opponent_score")
                    elif filters['result'] == 'loss':
                        where_clauses.append("((is_home = 1 AND team_score < opponent_score) OR (is_home = 0 AND team_score > opponent_score))")

                # Add WHERE clause if any filters were applied
                if where_clauses:
                    query += " WHERE " + " AND ".join(where_clauses)

            # Add ORDER BY clause
            valid_sort_fields = ['date', 'competition_id', 'opponent', 'team_score', 'opponent_score']
            if sort_by not in valid_sort_fields:
                sort_by = 'date'

            valid_sort_orders = ['ASC', 'DESC']
            if sort_order.upper() not in valid_sort_orders:
                sort_order = 'DESC'

            query += f" ORDER BY {sort_by} {sort_order}"

            # Add LIMIT clause if provided
            if limit and isinstance(limit, int) and limit > 0:
                query += f" LIMIT {limit}"

            # Execute the query
            self.cursor.execute(query, params)
            matches = [dict(row) for row in self.cursor.fetchall()]

            # For each match, get the related data
            for match in matches:
                match_id = match['match_id']

                # Get officials
                self.cursor.execute("SELECT * FROM match_officials WHERE match_id = ?", (match_id,))
                match['officials'] = [dict(row) for row in self.cursor.fetchall()]

                # Get officials rating
                self.cursor.execute("SELECT * FROM match_officials_rating WHERE match_id = ?", (match_id,))
                rating_row = self.cursor.fetchone()
                if rating_row:
                    match['officials_rating'] = dict(rating_row)

                # Get team staff
                self.cursor.execute("SELECT * FROM match_team_staff WHERE match_id = ?", (match_id,))
                match['team_staff'] = [dict(row) for row in self.cursor.fetchall()]

                # Get match statistics
                match_stats = {'team': {}, 'opponent': {}}

                # Query for match statistics
                self.cursor.execute("""
                    SELECT team_type, stat_key, stat_value
                    FROM match_statistics
                    WHERE match_id = ?
                """, (match_id,))

                # Process statistics results
                for row in self.cursor.fetchall():
                    team_type = row['team_type']
                    stat_key = row['stat_key']
                    stat_value = row['stat_value']

                    # Add to the appropriate team stats dictionary
                    if team_type in match_stats:
                        match_stats[team_type][stat_key] = stat_value

                # Add statistics to match data
                match['match_stats'] = match_stats

            return matches

        except sqlite3.Error as e:
            self.logger.error(f"Error getting matches: {e}")
            return []

    def get_competitions(self):
        """Get all competitions from the application settings.

        Returns:
            list: List of competition dictionaries
        """
        try:
            from PySide6.QtCore import QSettings

            settings = QSettings()
            settings.beginGroup("football_competitions")

            # Get number of competitions
            count = settings.value("count", 0, int)
            competitions = []

            # Load each competition
            for i in range(count):
                comp_id = settings.value(f"{i}/id", "")
                name = settings.value(f"{i}/name", "")
                organization = settings.value(f"{i}/organization", "")
                comp_type = settings.value(f"{i}/type", "")
                structure = settings.value(f"{i}/structure", "")
                notes = settings.value(f"{i}/notes", "")

                # Get the new fields with default values
                x_a_side = settings.value(f"{i}/x_a_side", 11, int)
                match_duration = settings.value(f"{i}/match_duration", 90, int)
                subs_allowed = settings.value(f"{i}/subs_allowed", 5, int)
                minimum_players_allowed = settings.value(f"{i}/minimum_players_allowed", 7, int)
                minimum_required_to_start = settings.value(f"{i}/minimum_required_to_start", 9, int)

                competitions.append({
                    'id': comp_id,
                    'name': name,
                    'organization': organization,
                    'type': comp_type,
                    'structure': structure,
                    'notes': notes,
                    'x_a_side': x_a_side,
                    'match_duration': match_duration,
                    'subs_allowed': subs_allowed,
                    'minimum_players_allowed': minimum_players_allowed,
                    'minimum_required_to_start': minimum_required_to_start
                })

            settings.endGroup()
            return competitions

        except Exception as e:
            self.logger.error(f"Error getting competitions from settings: {e}")
            return []

    def get_seasons(self):
        """Get all unique seasons from matches.

        Returns:
            list: List of season strings
        """
        if not self.conn:
            return []

        try:
            # Get all unique seasons
            self.cursor.execute("SELECT DISTINCT season FROM matches ORDER BY season DESC")
            return [row[0] for row in self.cursor.fetchall() if row[0]]

        except sqlite3.Error as e:
            self.logger.error(f"Error getting seasons: {e}")
            return []

    def close_db(self):
        """Commits changes and closes the database connection."""
        if self.conn:
            try:
                self.conn.commit()
                self.conn.close()
                self.conn = None
                self.cursor = None
                self.logger.info(f"Database connection to '{self.db_path}' closed.")
            except sqlite3.Error as e:
                self.logger.error(f"Error closing database connection: {e}")

    def __del__(self):
        """Destructor to ensure database connection is closed."""
        self.close_db()