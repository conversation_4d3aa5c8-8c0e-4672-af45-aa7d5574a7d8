"""
Outlier detection utility for physical measurements in roster data.
Helps identify potential data entry errors by highlighting values that are
significantly different from the team average.
"""

from PySide6.QtCore import QSettings
import statistics
from typing import Dict, List, Optional, Tuple


class OutlierDetector:
    """Detects outliers in physical measurement data."""

    # Physical measurement fields that should be checked for outliers
    PHYSICAL_FIELDS = [
        'height',
        'weight',
        'waist',
        'hip',
        'neck',
        'wrist',
        'forearm',
        'thigh'
    ]

    def __init__(self):
        """Initialize the outlier detector."""
        self._threshold_percentage = None
        self._enabled = None
        self._settings_loaded = False

    def _ensure_settings_loaded(self):
        """Ensure settings are loaded from QSettings."""
        if not self._settings_loaded:
            print("DEBUG: Loading outlier detector settings for the first time")
            self._threshold_percentage = self._load_threshold()
            self._enabled = self._load_enabled()
            self._settings_loaded = True

    @property
    def threshold_percentage(self) -> int:
        """Get the outlier threshold percentage."""
        self._ensure_settings_loaded()
        return self._threshold_percentage

    @threshold_percentage.setter
    def threshold_percentage(self, value: int):
        """Set the outlier threshold percentage."""
        self._threshold_percentage = value

    @property
    def enabled(self) -> bool:
        """Get the outlier detection enabled state."""
        self._ensure_settings_loaded()
        return self._enabled

    @enabled.setter
    def enabled(self, value: bool):
        """Set the outlier detection enabled state."""
        self._enabled = value

    def _load_threshold(self) -> int:
        """Load the outlier threshold percentage from settings."""
        settings = QSettings()
        # Use the new settings location in pages section
        threshold = settings.value("pages/outlier_detection_threshold", 20, int)

        return threshold

    def _load_enabled(self) -> bool:
        """Load the outlier detection enabled state from settings."""
        settings = QSettings()
        # Use the new settings location in pages section
        enabled = settings.value("pages/outlier_detection_enabled", True, bool)
        return enabled

    def update_threshold(self, new_threshold: int):
        """Update the outlier threshold percentage."""

        self.threshold_percentage = new_threshold

        # Save to settings
        settings = QSettings()
        settings.setValue("pages/outlier_detection_threshold", new_threshold)
        settings.sync()


    def update_enabled(self, enabled: bool):
        """Update the outlier detection enabled state."""
        self.enabled = enabled

        # Save to settings
        settings = QSettings()
        settings.setValue("pages/outlier_detection_enabled", enabled)
        settings.sync()

    def calculate_team_averages(self, player_data: List[Dict]) -> Dict[str, float]:
        """
        Calculate average values for each physical measurement field.

        Args:
            player_data: List of player dictionaries with physical measurements

        Returns:
            Dictionary mapping field names to their average values
        """
        averages = {}

        for field in self.PHYSICAL_FIELDS:
            values = []

            for player in player_data:
                value = player.get(field)
                if value is not None and value > 0:  # Only include valid positive values
                    try:
                        values.append(float(value))
                    except (ValueError, TypeError):
                        continue

            if len(values) >= 2:  # Need at least 2 values to calculate meaningful average
                averages[field] = statistics.mean(values)

        return averages

    def is_outlier(self, value: float, average: float) -> bool:
        """
        Check if a value is an outlier based on the threshold percentage.

        Args:
            value: The value to check
            average: The team average for this field

        Returns:
            True if the value is an outlier, False otherwise
        """
        if average <= 0:
            return False

        # Calculate percentage difference from average
        percentage_diff = abs(value - average) / average * 100

        return percentage_diff > self.threshold_percentage

    def detect_outliers(self, player_data: List[Dict]) -> Dict[str, Dict[str, bool]]:
        """
        Detect outliers in all physical measurements for all players.

        Args:
            player_data: List of player dictionaries with physical measurements

        Returns:
            Dictionary mapping player_id -> field_name -> is_outlier (bool)
        """
        # If outlier detection is disabled, return empty results
        if not self.enabled:
            outliers = {}
            for player in player_data:
                player_id = player.get('player_id')
                if player_id:
                    outliers[player_id] = {field: False for field in self.PHYSICAL_FIELDS}
            return outliers

        averages = self.calculate_team_averages(player_data)
        outliers = {}

        for player in player_data:
            player_id = player.get('player_id')
            if not player_id:
                continue

            outliers[player_id] = {}

            for field in self.PHYSICAL_FIELDS:
                value = player.get(field)
                average = averages.get(field)

                is_outlier = False
                if value is not None and average is not None and value > 0:
                    try:
                        is_outlier = self.is_outlier(float(value), average)
                    except (ValueError, TypeError):
                        pass

                outliers[player_id][field] = is_outlier

        return outliers

    def get_outlier_info(self, value: float, average: float) -> Tuple[bool, str]:
        """
        Get outlier information including a descriptive message.

        Args:
            value: The value to check
            average: The team average for this field

        Returns:
            Tuple of (is_outlier, description_message)
        """
        if average <= 0:
            return False, ""

        percentage_diff = abs(value - average) / average * 100
        is_outlier = percentage_diff > self.threshold_percentage

        if is_outlier:
            direction = "above" if value > average else "below"
            message = f"Warning: {percentage_diff:.1f}% {direction} team average ({average:.1f})"
            return True, message

        return False, ""

    def get_field_statistics(self, player_data: List[Dict], field: str) -> Dict:
        """
        Get comprehensive statistics for a specific field.

        Args:
            player_data: List of player dictionaries
            field: The field name to analyze

        Returns:
            Dictionary with statistics (mean, median, min, max, std_dev, outlier_count)
        """
        values = []

        for player in player_data:
            value = player.get(field)
            if value is not None and value > 0:
                try:
                    values.append(float(value))
                except (ValueError, TypeError):
                    continue

        if not values:
            return {}

        stats = {
            'count': len(values),
            'mean': statistics.mean(values),
            'min': min(values),
            'max': max(values)
        }

        if len(values) >= 2:
            stats['median'] = statistics.median(values)

        if len(values) >= 3:
            try:
                stats['std_dev'] = statistics.stdev(values)
            except statistics.StatisticsError:
                stats['std_dev'] = 0

        # Count outliers
        outlier_count = 0
        for value in values:
            if self.is_outlier(value, stats['mean']):
                outlier_count += 1

        stats['outlier_count'] = outlier_count
        stats['outlier_percentage'] = (outlier_count / len(values)) * 100 if values else 0

        return stats


# Global instance for easy access
outlier_detector = OutlierDetector()


def get_outlier_style() -> str:
    """
    Get the CSS style for highlighting outlier values.

    Returns:
        CSS style string for outlier highlighting
    """
    return """
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    """


def should_highlight_value(player_data: List[Dict], player_id: str, field: str, value: float) -> bool:
    """
    Check if a specific value should be highlighted as an outlier.

    Args:
        player_data: List of all player data
        player_id: ID of the player
        field: Field name being checked
        value: Value to check

    Returns:
        True if the value should be highlighted
    """
    if field not in OutlierDetector.PHYSICAL_FIELDS:
        return False

    averages = outlier_detector.calculate_team_averages(player_data)
    average = averages.get(field)

    if average is None or value <= 0:
        return False

    return outlier_detector.is_outlier(value, average)
