=== Analysis of translations/footdata_el.ts ===
Total translations: 1206
Found in code: 77
Not found in code: 1129
Duplicates (same text in multiple contexts): 59

=== DUPLICATES ===

Text: "Other"
Found in contexts: OptionsPage, SponsorSectionWidget
NOT used in code in contexts: OptionsPage, SponsorSectionWidget

Text: "Type:"
Found in contexts: OptionsPage, SponsorSectionWidget
NOT used in code in contexts: OptionsPage, SponsorSectionWidget

Text: "Charity"
Found in contexts: SponsorsPanel, OptionsPage
NOT used in code in contexts: SponsorsPanel, OptionsPage

Text: "No Logo"
Found in contexts: SponsorSectionWidget, ClubWindow
NOT used in code in contexts: SponsorSectionWidget, ClubWindow

Text: "File Too Large"
Found in contexts: RosterPage, OptionsPage
NOT used in code in contexts: RosterPage, OptionsPage

Text: "Intensity"
Found in contexts: self.parent_widget, OptionsPage, PeriodizationChartDialog
NOT used in code in contexts: self.parent_widget, OptionsPage, PeriodizationChartDialog

Text: "Microcycles"
Found in contexts: PeriodizationChartDialog, TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, self.parent_widget, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, PeriodizationTimelineWidget, SeasonView, Timeline
NOT used in code in contexts: PeriodizationChartDialog, TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, self.parent_widget, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, PeriodizationTimelineWidget, SeasonView, Timeline

Text: "Phone:"
Found in contexts: RosterPage, SponsorSectionWidget, ClubWindow
NOT used in code in contexts: RosterPage, SponsorSectionWidget, ClubWindow

Text: "Email:"
Found in contexts: RosterPage, SponsorSectionWidget, ClubWindow
NOT used in code in contexts: RosterPage, SponsorSectionWidget, ClubWindow

Text: "Remove"
Found in contexts: OptionsPage, ClubWindow
NOT used in code in contexts: OptionsPage, ClubWindow

Text: "Settings"
Found in contexts: MainWindow, ClubWindow, SettingsWindow
NOT used in code in contexts: MainWindow, ClubWindow, SettingsWindow

Text: "Name"
Found in contexts: OptionsPage, RosterPage, ClubWindow
NOT used in code in contexts: OptionsPage, RosterPage, ClubWindow

Text: "PNG Images (*.png)"
Found in contexts: OptionsPage, RosterPage, ClubWindow, SponsorSectionWidget
NOT used in code in contexts: OptionsPage, RosterPage, ClubWindow, SponsorSectionWidget

Text: "Error copying logo!"
Found in contexts: SponsorSectionWidget, ClubWindow
NOT used in code in contexts: SponsorSectionWidget, ClubWindow

Text: "Remove Logo"
Found in contexts: SponsorSectionWidget, ClubWindow
NOT used in code in contexts: SponsorSectionWidget, ClubWindow

Text: "Add"
Found in contexts: OptionsPage, ClubWindow
NOT used in code in contexts: OptionsPage, ClubWindow

Text: "Search..."
Found in contexts: TeamGroupsWidget, ClubWindow
NOT used in code in contexts: TeamGroupsWidget, ClubWindow

Text: "Search:"
Found in contexts: TeamGroupsWidget, ClubWindow, SettingsWindow
NOT used in code in contexts: TeamGroupsWidget, ClubWindow, SettingsWindow

Text: "Filter"
Found in contexts: TeamGroupsWidget, StaffFilterDialog, ClubWindow
NOT used in code in contexts: TeamGroupsWidget, StaffFilterDialog, ClubWindow

Text: "Clear"
Found in contexts: TeamGroupsWidget, ClubWindow
NOT used in code in contexts: TeamGroupsWidget, ClubWindow

Text: "Clear Filter"
Found in contexts: TeamGroupsWidget, ClubWindow
NOT used in code in contexts: TeamGroupsWidget, ClubWindow

Text: "(Unassigned)"
Found in contexts: StaffAssignmentDialog, ClubWindow
NOT used in code in contexts: StaffAssignmentDialog, ClubWindow

Text: "No Image"
Found in contexts: RosterPage, ClubWindow
NOT used in code in contexts: RosterPage, ClubWindow

Text: "Error: Cannot read image file."
Found in contexts: SponsorSectionWidget, ClubWindow
NOT used in code in contexts: SponsorSectionWidget, ClubWindow

Text: "Error"
Found in contexts: RosterPage, CoachingStaffListDialog, RemoveFontDialog, TeamGroupsWidget, OptionsPage, SettingsWindow
NOT used in code in contexts: RosterPage, CoachingStaffListDialog, RemoveFontDialog, TeamGroupsWidget, OptionsPage, SettingsWindow

Text: "Options"
Found in contexts: OptionsPage, MainWindow, DateChangeConfirmationDialog
NOT used in code in contexts: OptionsPage, MainWindow, DateChangeConfirmationDialog

Text: "Show Months"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Show Weeks"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Show Intensity Chart"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Jan"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Feb"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Mar"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Apr"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "May"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Jun"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Jul"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Aug"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Sep"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Oct"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Nov"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Dec"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, QPlatformTheme, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Season"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, SponsorSectionWidget, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, self.parent_widget, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, OptionsPage, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, SponsorSectionWidget, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, self.parent_widget, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, OptionsPage, SeasonView, Timeline

Text: "Mesocycles"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, self.parent_widget, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, PeriodizationTimelineWidget, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, self.parent_widget, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, PeriodizationTimelineWidget, SeasonView, Timeline

Text: "Evaluations"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, self.parent_widget, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, self.parent_widget, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, SeasonView, Timeline

Text: "Competition"
Found in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, OptionsPage, SeasonView, Timeline
NOT used in code in contexts: TimelineControl, ΧρονοδιάγραμμαΣεζόν, Χρονοδιάγραμμα Σεζόν, TimelineForm, Χρονοδιάγραμμα, MainWindow, SeasonTimeline, Chronogram, SeasonChronogram, SeasonTimelineWidget, ChronogramWindow, χρονοδιάγραμμα, ChronogramForm, SeasonTimelineForm, ΧρονοδιάγραμμαΑγώνων, XronodiagraamaSezon, TimelineWindow, OptionsPage, SeasonView, Timeline

Text: "ID"
Found in contexts: OptionsPage, RosterPage
NOT used in code in contexts: OptionsPage, RosterPage

Text: "Notes"
Found in contexts: OptionsPage, SponsorSectionWidget
NOT used in code in contexts: OptionsPage, SponsorSectionWidget

Text: "Periodization Chart"
Found in contexts: OptionsPage, PeriodizationChartDialog
NOT used in code in contexts: OptionsPage, PeriodizationChartDialog

Text: "Duplicate Name"
Found in contexts: OptionsPage, TeamGroupsWidget
NOT used in code in contexts: OptionsPage, TeamGroupsWidget

Text: "Competitions"
Found in contexts: OptionsPage, self.parent_widget
NOT used in code in contexts: OptionsPage, self.parent_widget

Text: "Invalid File Type"
Found in contexts: OptionsPage, RosterPage
NOT used in code in contexts: OptionsPage, RosterPage

Text: "Upload Error"
Found in contexts: OptionsPage, SponsorSectionWidget
NOT used in code in contexts: OptionsPage, SponsorSectionWidget

Text: "Image Removed"
Found in contexts: OptionsPage, RosterPage
NOT used in code in contexts: OptionsPage, RosterPage

Text: "Remove Error"
Found in contexts: OptionsPage, SponsorSectionWidget
NOT used in code in contexts: OptionsPage, SponsorSectionWidget

Text: "Confirm Removal"
Found in contexts: OptionsPage, RosterPage, RemoveFontDialog, TeamGroupsWidget
NOT used in code in contexts: OptionsPage, RosterPage, RemoveFontDialog, TeamGroupsWidget

Text: "Remove Selected"
Found in contexts: RemoveFontDialog, TeamGroupsWidget
NOT used in code in contexts: RemoveFontDialog, TeamGroupsWidget

Text: "(None)"
Found in contexts: RosterPage, RosterDelegate
NOT used in code in contexts: RosterPage, RosterDelegate

Text: "Macrocycle"
Found in contexts: SeasonTimelineWidget, self.parent_widget
Used in code in contexts: SeasonTimelineWidget
NOT used in code in contexts: self.parent_widget

Text: "W"
Found in contexts: SeasonTimelineWidget, self.parent_widget
Used in code in contexts: SeasonTimelineWidget
NOT used in code in contexts: self.parent_widget


=== UNUSED TRANSLATIONS ===

Context: None
Text: "Are you sure you want to remove the competition '{competition_name}'?"
Locations: ../app/pages/options_page.py:4340
Is translated: True

Context: None
Text: "The competition '{}' is currently used in {} entries in:

Page: Options
Tab: Dates
Sub-tab: Season
Section: Competitions

Please remove these entries first before deleting the competition."
Locations: ../app/pages/options_page.py:4866
Is translated: True

Context: None
Text: "Are you sure you want to delete the competition '{}'?"
Locations: ../app/pages/options_page.py:4912
Is translated: True

Context: None
Text: "Error: Cannot save because there are overlapping microcycles in your schedule:

{}

Please fix the overlaps before saving."
Locations: ../app/pages/transition_mesocycle_methods3.py:20, ../app/pages/competition_mesocycle_methods3.py:20, ../app/pages/basic_mesocycle_methods3.py:20
Is translated: True

Context: ClubWindow
Text: "Club Name:"
Locations: ../app/windows/club_window.py:381, ../app/windows/club_window.py:2477
Is translated: True

Context: ClubWindow
Text: "Short Name:"
Locations: ../app/windows/club_window.py:382, ../app/windows/club_window.py:2478
Is translated: True

Context: ClubWindow
Text: "Nickname:"
Locations: ../app/windows/club_window.py:383, ../app/windows/club_window.py:2479
Is translated: True

Context: ClubWindow
Text: "Year Founded:"
Locations: ../app/windows/club_window.py:384, ../app/windows/club_window.py:2480
Is translated: True

Context: ClubWindow
Text: "City:"
Locations: ../app/windows/club_window.py:385, ../app/windows/club_window.py:2481
Is translated: True

Context: ClubWindow
Text: "Country:"
Locations: ../app/windows/club_window.py:386, ../app/windows/club_window.py:2482
Is translated: True

Context: ClubWindow
Text: "Region/Continent:"
Locations: ../app/windows/club_window.py:387, ../app/windows/club_window.py:2487
Is translated: True

Context: ClubWindow
Text: "Contact Information"
Locations: ../app/windows/club_window.py:531, ../app/windows/club_window.py:4286
Is translated: True

Context: ClubWindow
Text: "Address Line 1:"
Locations: ../app/windows/club_window.py:543, ../app/windows/club_window.py:4290
Is translated: True

Context: ClubWindow
Text: "Address Line 2:"
Locations: ../app/windows/club_window.py:544, ../app/windows/club_window.py:4291
Is translated: True

Context: ClubWindow
Text: "City/Town:"
Locations: ../app/windows/club_window.py:545, ../app/windows/club_window.py:4292
Is translated: True

Context: ClubWindow
Text: "State/Province:"
Locations: ../app/windows/club_window.py:546, ../app/windows/club_window.py:4293
Is translated: True

Context: ClubWindow
Text: "Postal Code:"
Locations: ../app/windows/club_window.py:547, ../app/windows/club_window.py:4294
Is translated: True

Context: ClubWindow
Text: "Phone:"
Locations: ../app/windows/club_window.py:548, ../app/windows/club_window.py:4295
Is translated: True

Context: ClubWindow
Text: "Email:"
Locations: ../app/windows/club_window.py:549, ../app/windows/club_window.py:4296
Is translated: True

Context: ClubWindow
Text: "Website:"
Locations: ../app/windows/club_window.py:550, ../app/windows/club_window.py:4299
Is translated: True

Context: ClubWindow
Text: "Images"
Locations: ../app/windows/club_window.py:899, ../app/windows/club_window.py:4590
Is translated: True

Context: ClubWindow
Text: "Front"
Locations: ../app/windows/club_window.py:909, ../app/windows/club_window.py:4608
Is translated: True

Context: ClubWindow
Text: "Remove"
Locations: ../app/windows/club_window.py:586, ../app/windows/club_window.py:658, ../app/windows/club_window.py:736, ../app/windows/club_window.py:966, ../app/windows/club_window.py:1102, ../app/windows/club_window.py:4312, ../app/windows/club_window.py:4403, ../app/windows/club_window.py:4485, ../app/windows/club_window.py:4622, ../app/windows/club_window.py:4630
Is translated: True

Context: ClubWindow
Text: "Back"
Locations: ../app/windows/club_window.py:1004, ../app/windows/club_window.py:4612
Is translated: True

Context: ClubWindow
Text: "Settings"
Locations: ../app/windows/club_window.py:1135, ../app/windows/club_window.py:4594
Is translated: True

Context: ClubWindow
Text: "Name"
Locations: ../app/windows/club_window.py:1152, ../app/windows/club_window.py:4598
Is translated: True

Context: ClubWindow
Text: "Font Color:"
Locations: ../app/windows/club_window.py:1183, ../app/windows/club_window.py:1238, ../app/windows/club_window.py:4647, ../app/windows/club_window.py:4666
Is translated: True

Context: ClubWindow
Text: "Font Family:"
Locations: ../app/windows/club_window.py:1184, ../app/windows/club_window.py:1239, ../app/windows/club_window.py:4648, ../app/windows/club_window.py:4667
Is translated: True

Context: ClubWindow
Text: "Font Size:"
Locations: ../app/windows/club_window.py:1185, ../app/windows/club_window.py:1240, ../app/windows/club_window.py:4649, ../app/windows/club_window.py:4668
Is translated: True

Context: ClubWindow
Text: "Vertical Offset:"
Locations: ../app/windows/club_window.py:1186, ../app/windows/club_window.py:1241, ../app/windows/club_window.py:4650, ../app/windows/club_window.py:4669
Is translated: True

Context: ClubWindow
Text: "Number"
Locations: ../app/windows/club_window.py:1207, ../app/windows/club_window.py:4602
Is translated: True

Context: ClubWindow
Text: "Error creating logo directory!"
Locations: ../app/windows/club_window.py:2090
Is translated: True

Context: ClubWindow
Text: "PNG Images (*.png)"
Locations: ../app/windows/club_window.py:2095, ../app/windows/club_window.py:2673
Is translated: True

Context: ClubWindow
Text: "Select Logo Image"
Locations: ../app/windows/club_window.py:2097
Is translated: True

Context: ClubWindow
Text: "Error copying logo!"
Locations: ../app/windows/club_window.py:2114
Is translated: True

Context: ClubWindow
Text: "Stadium Name:"
Locations: ../app/windows/club_window.py:465, ../app/windows/club_window.py:2498
Is translated: True

Context: ClubWindow
Text: "No Logo"
Locations: ../app/windows/club_window.py:390, ../app/windows/club_window.py:2015, ../app/windows/club_window.py:3428
Is translated: True

Context: ClubWindow
Text: "Remove Logo"
Locations: ../app/windows/club_window.py:402, ../app/windows/club_window.py:2017, ../app/windows/club_window.py:2548, ../app/windows/club_window.py:3430
Is translated: True

Context: ClubWindow
Text: "Capacity:"
Locations: ../app/windows/club_window.py:466, ../app/windows/club_window.py:2499
Is translated: True

Context: ClubWindow
Text: "Seating Capacity:"
Locations: ../app/windows/club_window.py:467, ../app/windows/club_window.py:2500
Is translated: True

Context: ClubWindow
Text: "Surface Type:"
Locations: ../app/windows/club_window.py:468, ../app/windows/club_window.py:2501
Is translated: True

Context: ClubWindow
Text: "Year Built:"
Locations: ../app/windows/club_window.py:469, ../app/windows/club_window.py:2502
Is translated: True

Context: ClubWindow
Text: "Stadium Owner:"
Locations: ../app/windows/club_window.py:470, ../app/windows/club_window.py:2506
Is translated: True

Context: ClubWindow
Text: "--- Select Surface ---"
Locations: ../app/windows/club_window.py:453, ../app/windows/club_window.py:2527
Is translated: True

Context: ClubWindow
Text: "--- Select Owner ---"
Locations: ../app/windows/club_window.py:460, ../app/windows/club_window.py:2537
Is translated: True

Context: ClubWindow
Text: "No Stadium Image"
Locations: ../app/windows/club_window.py:473, ../app/windows/club_window.py:2073, ../app/windows/club_window.py:3486
Is translated: True

Context: ClubWindow
Text: "Logo: {}"
Locations: ../app/windows/club_window.py:2006, ../app/windows/club_window.py:2111, ../app/windows/club_window.py:2558, ../app/windows/club_window.py:3419
Is translated: True

Context: ClubWindow
Text: "Error creating stadium directory!"
Locations: ../app/windows/club_window.py:2151
Is translated: True

Context: ClubWindow
Text: "Image Files (*.png *.jpg *.jpeg)"
Locations: ../app/windows/club_window.py:2155
Is translated: True

Context: ClubWindow
Text: "Select Stadium Image"
Locations: ../app/windows/club_window.py:2157
Is translated: True

Context: ClubWindow
Text: "Stadium Image: {}"
Locations: ../app/windows/club_window.py:2064, ../app/windows/club_window.py:2170, ../app/windows/club_window.py:2565, ../app/windows/club_window.py:3477
Is translated: True

Context: ClubWindow
Text: "Error copying stadium image!"
Locations: ../app/windows/club_window.py:2173
Is translated: True

Context: ClubWindow
Text: "1st Color:"
Locations: ../app/windows/club_window.py:845, ../app/windows/club_window.py:4573
Is translated: True

Context: ClubWindow
Text: "2nd Color:"
Locations: ../app/windows/club_window.py:846, ../app/windows/club_window.py:4576
Is translated: True

Context: ClubWindow
Text: "3rd Color:"
Locations: ../app/windows/club_window.py:847, ../app/windows/club_window.py:4579
Is translated: True

Context: ClubWindow
Text: "Colors"
Locations: ../app/windows/club_window.py:829
Is translated: True

Context: ClubWindow
Text: "1st Kit"
Locations: ../app/windows/club_window.py:830
Is translated: True

Context: ClubWindow
Text: "2nd Kit"
Locations: ../app/windows/club_window.py:831
Is translated: True

Context: ClubWindow
Text: "3rd Kit"
Locations: ../app/windows/club_window.py:832
Is translated: True

Context: ClubWindow
Text: "1st GK Kit"
Locations: ../app/windows/club_window.py:833
Is translated: True

Context: ClubWindow
Text: "2nd GK Kit"
Locations: ../app/windows/club_window.py:834
Is translated: True

Context: ClubWindow
Text: "Remove Stadium Image"
Locations: ../app/windows/club_window.py:485, ../app/windows/club_window.py:2075, ../app/windows/club_window.py:2552, ../app/windows/club_window.py:3488
Is translated: True

Context: ClubWindow
Text: "Upload"
Locations: ../app/windows/club_window.py:948, ../app/windows/club_window.py:1085, ../app/windows/club_window.py:2546, ../app/windows/club_window.py:2550, ../app/windows/club_window.py:4618, ../app/windows/club_window.py:4626
Is translated: True

Context: ClubWindow
Text: "Add"
Locations: ../app/windows/club_window.py:585, ../app/windows/club_window.py:657, ../app/windows/club_window.py:735, ../app/windows/club_window.py:4310, ../app/windows/club_window.py:4401, ../app/windows/club_window.py:4483
Is translated: True

Context: ClubWindow
Text: "Search..."
Locations: ../app/windows/club_window.py:588, ../app/windows/club_window.py:660, ../app/windows/club_window.py:738, ../app/windows/club_window.py:4218, ../app/windows/club_window.py:4240, ../app/windows/club_window.py:4275, ../app/windows/club_window.py:4316, ../app/windows/club_window.py:4407, ../app/windows/club_window.py:4489
Is translated: True

Context: ClubWindow
Text: "Search:"
Locations: ../app/windows/club_window.py:597, ../app/windows/club_window.py:676, ../app/windows/club_window.py:747, ../app/windows/club_window.py:4210, ../app/windows/club_window.py:4232, ../app/windows/club_window.py:4267, ../app/windows/club_window.py:4314, ../app/windows/club_window.py:4405, ../app/windows/club_window.py:4487
Is translated: True

Context: ClubWindow
Text: "Filter"
Locations: ../app/windows/club_window.py:591, ../app/windows/club_window.py:664, ../app/windows/club_window.py:741, ../app/windows/club_window.py:4212, ../app/windows/club_window.py:4234, ../app/windows/club_window.py:4269, ../app/windows/club_window.py:4320, ../app/windows/club_window.py:4411, ../app/windows/club_window.py:4493
Is translated: True

Context: ClubWindow
Text: "-- Select Type --"
Locations: ../app/windows/club_window.py:778, ../app/windows/club_window.py:4155, ../app/windows/club_window.py:4209, ../app/windows/club_window.py:4231, ../app/windows/club_window.py:4260, ../app/windows/club_window.py:4334, ../app/windows/club_window.py:4345, ../app/windows/club_window.py:4427, ../app/windows/club_window.py:4513
Is translated: True

Context: ClubWindow
Text: "Enable Outline:"
Locations: ../app/windows/club_window.py:1191, ../app/windows/club_window.py:1246, ../app/windows/club_window.py:4651, ../app/windows/club_window.py:4670
Is translated: True

Context: ClubWindow
Text: "Outline Color:"
Locations: ../app/windows/club_window.py:1197, ../app/windows/club_window.py:1252, ../app/windows/club_window.py:4652, ../app/windows/club_window.py:4671
Is translated: True

Context: ClubWindow
Text: "Outline Thickness:"
Locations: ../app/windows/club_window.py:1204, ../app/windows/club_window.py:1259, ../app/windows/club_window.py:4655, ../app/windows/club_window.py:4674
Is translated: True

Context: ClubWindow
Text: "Error: Dimensions exceed {dim}x{dim}px."
Locations: ../app/windows/club_window.py:2705
Is translated: True

Context: ClubWindow
Text: "Clear"
Locations: ../app/windows/club_window.py:589, ../app/windows/club_window.py:661, ../app/windows/club_window.py:739, ../app/windows/club_window.py:4216, ../app/windows/club_window.py:4238, ../app/windows/club_window.py:4273, ../app/windows/club_window.py:4318, ../app/windows/club_window.py:4409, ../app/windows/club_window.py:4491
Is translated: True

Context: ClubWindow
Text: "Clear Filter"
Locations: ../app/windows/club_window.py:592, ../app/windows/club_window.py:670, ../app/windows/club_window.py:742, ../app/windows/club_window.py:4214, ../app/windows/club_window.py:4236, ../app/windows/club_window.py:4271, ../app/windows/club_window.py:4322, ../app/windows/club_window.py:4413, ../app/windows/club_window.py:4495
Is translated: True

Context: ClubWindow
Text: "Select Kit Image ({side})"
Locations: ../app/windows/club_window.py:2675
Is translated: True

Context: ClubWindow
Text: "Error removing image!"
Locations: ../app/windows/club_window.py:2741
Is translated: True

Context: ClubWindow
Text: "(Unassigned)"
Locations: ../app/windows/club_window.py:4155
Is translated: True

Context: ClubWindow
Text: "Error: File size exceeds {max_kb}KB."
Locations: ../app/windows/club_window.py:2689
Is translated: True

Context: ClubWindow
Text: "No Image"
Locations: ../app/windows/club_window.py:2645
Is translated: True

Context: ClubWindow
Text: "Error: Cannot read image file."
Locations: ../app/windows/club_window.py:2699
Is translated: True

Context: ClubWindow
Text: "Error copying image!"
Locations: ../app/windows/club_window.py:2719
Is translated: True

Context: ClubWindow
Text: "Club Information"
Locations: ../app/windows/club_window.py:306
Is translated: True

Context: ClubWindow
Text: "Cannot Remove Staff Member"
Locations: ../app/windows/club_window.py:1877, ../app/windows/club_window.py:3586, ../app/windows/club_window.py:3898
Is translated: True

Context: ClubWindow
Text: "Cannot remove staff member '{name}' because they are assigned to the following team group(s): {groups}. Please remove the staff member from these groups first."
Locations: ../app/windows/club_window.py:1880, ../app/windows/club_window.py:3589, ../app/windows/club_window.py:3901
Is translated: True

Context: ClubWindow
Text: "(PNG, <500KB, <300x300px)"
Locations: ../app/windows/club_window.py:2010, ../app/windows/club_window.py:2014, ../app/windows/club_window.py:2068, ../app/windows/club_window.py:2072, ../app/windows/club_window.py:2560, ../app/windows/club_window.py:2567, ../app/windows/club_window.py:3423, ../app/windows/club_window.py:3427, ../app/windows/club_window.py:3481, ../app/windows/club_window.py:3485
Is translated: True

Context: ClubWindow
Text: "General Information"
Locations: ../app/windows/club_window.py:2458
Is translated: True

Context: ClubWindow
Text: "Stadium Information"
Locations: ../app/windows/club_window.py:2472
Is translated: True

Context: ClubWindow
Text: "--- Select Country ---"
Locations: ../app/windows/club_window.py:2518, ../app/windows/club_window.py:4156, ../app/windows/club_window.py:4337, ../app/windows/club_window.py:4349, ../app/windows/club_window.py:4431
Is translated: True

Context: ClubWindow
Text: "(PNG, <{max_kb}KB, <{dim}x{dim}px)"
Locations: ../app/windows/club_window.py:2639, ../app/windows/club_window.py:2667
Is translated: True

Context: ClubWindow
Text: "FOOT|DATA"
Locations: ../app/windows/club_window.py:4582
Is translated: True

Context: ClubWindow
Text: "(PNG, <300KB, <300x300px)"
Locations: ../app/windows/club_window.py:4636, ../app/windows/club_window.py:4640
Is translated: True

Context: CoachingStaffListDialog
Text: "Coaching Staff List"
Locations: ../app/dialogs/coaching_staff_list_dialog.py:28, ../app/dialogs/coaching_staff_list_dialog.py:36
Is translated: True

Context: CoachingStaffListDialog
Text: "Save List"
Locations: ../app/dialogs/coaching_staff_list_dialog.py:59
Is translated: True

Context: CoachingStaffListDialog
Text: "Save Coaching Staff List"
Locations: ../app/dialogs/coaching_staff_list_dialog.py:75
Is translated: True

Context: CoachingStaffListDialog
Text: "JSON Files (*.json);;All Files (*)"
Locations: ../app/dialogs/coaching_staff_list_dialog.py:78
Is translated: True

Context: CoachingStaffListDialog
Text: "Success"
Locations: ../app/dialogs/coaching_staff_list_dialog.py:89
Is translated: True

Context: CoachingStaffListDialog
Text: "Coaching staff list saved successfully to {path}"
Locations: ../app/dialogs/coaching_staff_list_dialog.py:90
Is translated: True

Context: CoachingStaffListDialog
Text: "Error"
Locations: ../app/dialogs/coaching_staff_list_dialog.py:95
Is translated: True

Context: CoachingStaffListDialog
Text: "Failed to save coaching staff list: {error}"
Locations: ../app/dialogs/coaching_staff_list_dialog.py:96
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Confirm Date Changes"
Locations: ../app/pages/options_page.py:52
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Changing the season dates will affect other date ranges in the application. Please review the changes below and select how you want to proceed."
Locations: ../app/pages/options_page.py:68
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Season Date Changes:"
Locations: ../app/pages/options_page.py:84
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Old range: {0} to {1}
New range: {2} to {3}"
Locations: ../app/pages/options_page.py:94, ../app/pages/options_page.py:120
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Macrocycle Date Changes:"
Locations: ../app/pages/options_page.py:110
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Microcycle Date Changes:"
Locations: ../app/pages/options_page.py:136
Is translated: True

Context: DateChangeConfirmationDialog
Text: "{0}: {1} to {2} → {3} to {4}"
Locations: ../app/pages/options_page.py:148
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Options"
Locations: ../app/pages/options_page.py:164
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Adjust all dates to maintain relationships"
Locations: ../app/pages/options_page.py:167
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Automatically adjust all dependent dates to maintain their relative positions"
Locations: ../app/pages/options_page.py:171
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Keep dates where possible"
Locations: ../app/pages/options_page.py:173
Is translated: True

Context: DateChangeConfirmationDialog
Text: "Only adjust dates that would be invalid with the new season dates"
Locations: ../app/pages/options_page.py:176
Is translated: True

Context: DeveloperSettingsWindow
Text: "Developer Settings"
Locations: ../app/windows/developer_settings_window.py:45, ../app/windows/developer_settings_window.py:135
Is translated: True

Context: DeveloperSettingsWindow
Text: "Component Log Levels"
Locations: ../app/windows/developer_settings_window.py:52, ../app/windows/developer_settings_window.py:138
Is translated: True

Context: DeveloperSettingsWindow
Text: "Close"
Locations: ../app/windows/developer_settings_window.py:98, ../app/windows/developer_settings_window.py:141
Is translated: True

Context: DeveloperSettingsWindow
Text: "Developer settings will appear here."
Locations: ../app/windows/developer_settings_window.py:136
Is translated: True

Context: MainWindow
Text: "FootData"
Locations: ../app/windows/main_window.py:403
Is translated: True

Context: MainWindow
Text: "FOOT|DATA - Header Placeholder"
Locations: ../app/windows/main_window.py:404
Is translated: True

Context: MainWindow
Text: "Roster"
Locations: ../app/windows/main_window.py:298
Is translated: True

Context: MainWindow
Text: "Main Content Area - Pages will load here."
Locations: ../app/windows/main_window.py:420
Is translated: True

Context: MainWindow
Text: "Footer Information Placeholder"
Locations: ../app/windows/main_window.py:405
Is translated: True

Context: MainWindow
Text: "Exit Application"
Locations: ../app/windows/main_window.py:514
Is translated: True

Context: MainWindow
Text: "Are you sure you want to exit FootData?"
Locations: ../app/windows/main_window.py:515
Is translated: True

Context: MainWindow
Text: "Yes"
Locations: ../app/windows/main_window.py:519
Is translated: True

Context: MainWindow
Text: "No"
Locations: ../app/windows/main_window.py:520
Is translated: True

Context: MainWindow
Text: "Configure application settings"
Locations: ../app/windows/main_window.py:566
Is translated: True

Context: MainWindow
Text: "View/Edit Club Information"
Locations: ../app/windows/main_window.py:570
Is translated: True

Context: MainWindow
Text: "Open the Player Roster"
Locations: ../app/windows/main_window.py:574
Is translated: True

Context: MainWindow
Text: "Configure football rules and nationality zones"
Locations: ../app/windows/main_window.py:578
Is translated: True

Context: MainWindow
Text: "Exit the application"
Locations: ../app/windows/main_window.py:584
Is translated: True

Context: MainWindow
Text: "&File"
Locations: ../app/windows/main_window.py:604, ../app/windows/main_window.py:640
Is translated: True

Context: MainWindow
Text: "&Help"
Locations: ../app/windows/main_window.py:614, ../app/windows/main_window.py:645
Is translated: True

Context: MainWindow
Text: "&Developer"
Locations: ../app/windows/main_window.py:619, ../app/windows/main_window.py:650
Is translated: True

Context: MainWindow
Text: "&Club Info..."
Locations: ../app/windows/main_window.py:569, ../app/windows/main_window.py:654
Is translated: True

Context: MainWindow
Text: "&Roster..."
Locations: ../app/windows/main_window.py:573, ../app/windows/main_window.py:657
Is translated: True

Context: MainWindow
Text: "&Settings..."
Locations: ../app/windows/main_window.py:565, ../app/windows/main_window.py:660
Is translated: True

Context: MainWindow
Text: "&Options..."
Locations: ../app/windows/main_window.py:577, ../app/windows/main_window.py:663
Is translated: True

Context: MainWindow
Text: "E&xit"
Locations: ../app/windows/main_window.py:582, ../app/windows/main_window.py:666
Is translated: True

Context: MainWindow
Text: "&About"
Locations: ../app/windows/main_window.py:587, ../app/windows/main_window.py:669
Is translated: True

Context: MainWindow
Text: "About &Qt"
Locations: ../app/windows/main_window.py:591, ../app/windows/main_window.py:672
Is translated: True

Context: MainWindow
Text: "Show the application's About box"
Locations: ../app/windows/main_window.py:588
Is translated: True

Context: MainWindow
Text: "Show the Qt library's About box"
Locations: ../app/windows/main_window.py:592
Is translated: True

Context: MainWindow
Text: "&Developer Settings"
Locations: ../app/windows/main_window.py:595, ../app/windows/main_window.py:675
Is translated: True

Context: MainWindow
Text: "Open Developer Settings"
Locations: ../app/windows/main_window.py:597
Is translated: True

Context: MainWindow
Text: "Settings"
Is translated: True

Context: MainWindow
Text: "Club"
Is translated: True

Context: MainWindow
Text: "Options"
Is translated: True

Context: MainWindow
Text: "Show Months"
Is translated: True

Context: MainWindow
Text: "Show Weeks"
Is translated: True

Context: MainWindow
Text: "Show Intensity Chart"
Is translated: True

Context: MainWindow
Text: "Jan"
Is translated: True

Context: MainWindow
Text: "Feb"
Is translated: True

Context: MainWindow
Text: "Mar"
Is translated: True

Context: MainWindow
Text: "Apr"
Is translated: True

Context: MainWindow
Text: "May"
Is translated: True

Context: MainWindow
Text: "Jun"
Is translated: True

Context: MainWindow
Text: "Jul"
Is translated: True

Context: MainWindow
Text: "Aug"
Is translated: True

Context: MainWindow
Text: "Sep"
Is translated: True

Context: MainWindow
Text: "Oct"
Is translated: True

Context: MainWindow
Text: "Nov"
Is translated: True

Context: MainWindow
Text: "Dec"
Is translated: True

Context: MainWindow
Text: "Season"
Is translated: True

Context: MainWindow
Text: "Mesocycles"
Is translated: True

Context: MainWindow
Text: "Microcycles"
Is translated: True

Context: MainWindow
Text: "Evaluations"
Is translated: True

Context: MainWindow
Text: "Competition"
Is translated: True

Context: OptionsPage
Text: "Dates"
Locations: ../app/pages/options_page.py:1040, ../app/pages/options_page.py:2260
Is translated: True

Context: OptionsPage
Text: "Football Rules"
Locations: ../app/pages/options_page.py:1038, ../app/pages/options_page.py:2256
Is translated: True

Context: OptionsPage
Text: "Nationality Zones"
Locations: ../app/pages/options_page.py:1039, ../app/pages/options_page.py:2258
Is translated: True

Context: OptionsPage
Text: "Season"
Locations: ../app/pages/options_page.py:841, ../app/pages/options_page.py:2268
Is translated: True

Context: OptionsPage
Text: "Periodization"
Locations: ../app/pages/options_page.py:405, ../app/pages/options_page.py:842, ../app/pages/options_page.py:2270, ../app/pages/options_page.py:2305
Is translated: True

Context: OptionsPage
Text: "Season Dates"
Locations: ../app/pages/options_page.py:299, ../app/pages/options_page.py:2276
Is translated: True

Context: OptionsPage
Text: "Start Date:"
Locations: ../app/pages/options_page.py:316, ../app/pages/options_page.py:2282, ../app/pages/options_page.py:3233, ../app/pages/options_page.py:3420
Is translated: True

Context: OptionsPage
Text: "End Date:"
Locations: ../app/pages/options_page.py:317, ../app/pages/options_page.py:2284, ../app/pages/options_page.py:3234, ../app/pages/options_page.py:3421
Is translated: True

Context: OptionsPage
Text: "Preparation Mesocycle"
Locations: ../app/pages/options_page.py:459, ../app/pages/options_page.py:2323
Is translated: True

Context: OptionsPage
Text: "Basic Mesocycle"
Locations: ../app/pages/options_page.py:532, ../app/pages/options_page.py:2338
Is translated: True

Context: OptionsPage
Text: "Competition Mesocycle"
Locations: ../app/pages/options_page.py:599, ../app/pages/options_page.py:2353
Is translated: True

Context: OptionsPage
Text: "Transition Mesocycle"
Locations: ../app/pages/options_page.py:666, ../app/pages/options_page.py:2368
Is translated: True

Context: OptionsPage
Text: "Add Microcycle"
Locations: ../app/pages/options_page.py:506, ../app/pages/options_page.py:583, ../app/pages/options_page.py:650, ../app/pages/options_page.py:717, ../app/pages/options_page.py:2331, ../app/pages/options_page.py:2346, ../app/pages/options_page.py:2361, ../app/pages/options_page.py:2376, ../app/pages/options_page.py:3176
Is translated: True

Context: OptionsPage
Text: "Edit Microcycle"
Locations: ../app/pages/options_page.py:507, ../app/pages/options_page.py:584, ../app/pages/options_page.py:651, ../app/pages/options_page.py:718, ../app/pages/options_page.py:2333, ../app/pages/options_page.py:2348, ../app/pages/options_page.py:2363, ../app/pages/options_page.py:2378, ../app/pages/options_page.py:3366
Is translated: True

Context: OptionsPage
Text: "Remove Microcycle"
Locations: ../app/pages/options_page.py:508, ../app/pages/options_page.py:585, ../app/pages/options_page.py:652, ../app/pages/options_page.py:719, ../app/pages/options_page.py:2335, ../app/pages/options_page.py:2350, ../app/pages/options_page.py:2365, ../app/pages/options_page.py:2380
Is translated: True

Context: OptionsPage
Text: "ID"
Locations: ../app/pages/options_page.py:340, ../app/pages/options_page.py:466, ../app/pages/options_page.py:539, ../app/pages/options_page.py:606, ../app/pages/options_page.py:673, ../app/pages/options_page.py:2290, ../app/pages/options_page.py:2326, ../app/pages/options_page.py:2341, ../app/pages/options_page.py:2356, ../app/pages/options_page.py:2371, ../app/pages/options_page.py:2466, ../app/pages/options_page.py:2585
Is translated: True

Context: OptionsPage
Text: "Start Microcycle"
Locations: ../app/pages/options_page.py:467, ../app/pages/options_page.py:540, ../app/pages/options_page.py:607, ../app/pages/options_page.py:674, ../app/pages/options_page.py:2326, ../app/pages/options_page.py:2341, ../app/pages/options_page.py:2356, ../app/pages/options_page.py:2371
Is translated: True

Context: OptionsPage
Text: "End Microcycle"
Locations: ../app/pages/options_page.py:468, ../app/pages/options_page.py:541, ../app/pages/options_page.py:608, ../app/pages/options_page.py:675, ../app/pages/options_page.py:2326, ../app/pages/options_page.py:2341, ../app/pages/options_page.py:2356, ../app/pages/options_page.py:2371
Is translated: True

Context: OptionsPage
Text: "Name"
Locations: ../app/pages/options_page.py:469, ../app/pages/options_page.py:542, ../app/pages/options_page.py:609, ../app/pages/options_page.py:676, ../app/pages/options_page.py:2326, ../app/pages/options_page.py:2341, ../app/pages/options_page.py:2356, ../app/pages/options_page.py:2371, ../app/pages/options_page.py:2466, ../app/pages/options_page.py:2587
Is translated: True

Context: OptionsPage
Text: "Target 1"
Locations: ../app/pages/options_page.py:470, ../app/pages/options_page.py:543, ../app/pages/options_page.py:610, ../app/pages/options_page.py:677, ../app/pages/options_page.py:2327, ../app/pages/options_page.py:2342, ../app/pages/options_page.py:2357, ../app/pages/options_page.py:2372
Is translated: True

Context: OptionsPage
Text: "Target 2"
Locations: ../app/pages/options_page.py:471, ../app/pages/options_page.py:544, ../app/pages/options_page.py:611, ../app/pages/options_page.py:678, ../app/pages/options_page.py:2327, ../app/pages/options_page.py:2342, ../app/pages/options_page.py:2357, ../app/pages/options_page.py:2372
Is translated: True

Context: OptionsPage
Text: "Target 3"
Locations: ../app/pages/options_page.py:472, ../app/pages/options_page.py:545, ../app/pages/options_page.py:612, ../app/pages/options_page.py:679, ../app/pages/options_page.py:2327, ../app/pages/options_page.py:2342, ../app/pages/options_page.py:2357, ../app/pages/options_page.py:2372
Is translated: True

Context: OptionsPage
Text: "Intensity"
Locations: ../app/pages/options_page.py:473, ../app/pages/options_page.py:546, ../app/pages/options_page.py:613, ../app/pages/options_page.py:680, ../app/pages/options_page.py:2328, ../app/pages/options_page.py:2343, ../app/pages/options_page.py:2358, ../app/pages/options_page.py:2373
Is translated: True

Context: OptionsPage
Text: "Notes"
Locations: ../app/pages/options_page.py:342, ../app/pages/options_page.py:475, ../app/pages/options_page.py:548, ../app/pages/options_page.py:615, ../app/pages/options_page.py:682, ../app/pages/options_page.py:2292, ../app/pages/options_page.py:2329, ../app/pages/options_page.py:2344, ../app/pages/options_page.py:2359, ../app/pages/options_page.py:2374, ../app/pages/options_page.py:2468, ../app/pages/options_page.py:2592
Is translated: True

Context: OptionsPage
Text: "Start Macrocycle:"
Locations: ../app/pages/options_page.py:425, ../app/pages/options_page.py:2311
Is translated: True

Context: OptionsPage
Text: "End Macrocycle:"
Locations: ../app/pages/options_page.py:426, ../app/pages/options_page.py:2313
Is translated: True

Context: OptionsPage
Text: "Validate && Save Dates"
Locations: ../app/pages/options_page.py:439, ../app/pages/options_page.py:2316
Is translated: True

Context: OptionsPage
Text: "Periodization Chart"
Locations: ../app/pages/options_page.py:444, ../app/pages/options_page.py:2319
Is translated: True

Context: OptionsPage
Text: "Options"
Locations: ../app/pages/options_page.py:241, ../app/pages/options_page.py:2252
Is translated: True

Context: OptionsPage
Text: "Invalid Date Range"
Locations: ../app/pages/options_page.py:1337, ../app/pages/options_page.py:1649
Is translated: True

Context: OptionsPage
Text: "The selected date range is invalid. Reverting to the last valid dates."
Locations: ../app/pages/options_page.py:1339
Is translated: True

Context: OptionsPage
Text: "End date must be after start date"
Locations: ../app/pages/options_page.py:1273, ../app/pages/options_page.py:1680, ../app/pages/options_page.py:1696, ../app/pages/options_page.py:1718, ../app/pages/options_page.py:3268, ../app/pages/options_page.py:3455
Is translated: True

Context: OptionsPage
Text: "Season cannot exceed {max_days} days"
Locations: ../app/pages/options_page.py:1287
Is translated: True

Context: OptionsPage
Text: "Name:"
Locations: ../app/pages/options_page.py:3232, ../app/pages/options_page.py:3419
Is translated: True

Context: OptionsPage
Text: "Target 1:"
Locations: ../app/pages/options_page.py:3235, ../app/pages/options_page.py:3422
Is translated: True

Context: OptionsPage
Text: "Target 2:"
Locations: ../app/pages/options_page.py:3236, ../app/pages/options_page.py:3423
Is translated: True

Context: OptionsPage
Text: "Target 3:"
Locations: ../app/pages/options_page.py:3237, ../app/pages/options_page.py:3424
Is translated: True

Context: OptionsPage
Text: "Intensity:"
Locations: ../app/pages/options_page.py:3238, ../app/pages/options_page.py:3425
Is translated: True

Context: OptionsPage
Text: "Notes:"
Locations: ../app/pages/options_page.py:3239, ../app/pages/options_page.py:3426
Is translated: True

Context: OptionsPage
Text: "No Data"
Locations: ../app/pages/options_page.py:3559
Is translated: True

Context: OptionsPage
Text: "No periodization data available. Please add microcycles to at least one mesocycle."
Locations: ../app/pages/options_page.py:3561
Is translated: True

Context: OptionsPage
Text: "No Selection"
Locations: ../app/pages/options_page.py:3357, ../app/pages/options_page.py:3573
Is translated: True

Context: OptionsPage
Text: "Please select a microcycle to edit."
Locations: ../app/pages/options_page.py:3358
Is translated: True

Context: OptionsPage
Text: "Are you sure you want to remove this microcycle?"
Locations: ../app/pages/options_page.py:3584
Is translated: True

Context: OptionsPage
Text: "Set the start date of the Macrocycle"
Locations: ../app/pages/options_page.py:415, ../app/pages/options_page.py:2307
Is translated: True

Context: OptionsPage
Text: "Set the end date of the Macrocycle"
Locations: ../app/pages/options_page.py:422, ../app/pages/options_page.py:2309
Is translated: True

Context: OptionsPage
Text: "Validate the macrocycle dates and save if valid"
Locations: ../app/pages/options_page.py:441, ../app/pages/options_page.py:2317
Is translated: True

Context: OptionsPage
Text: "Show a chart of intensity values across all periodization cycles"
Locations: ../app/pages/options_page.py:446, ../app/pages/options_page.py:2320
Is translated: True

Context: OptionsPage
Text: "Defined Zones:"
Locations: ../app/pages/options_page.py:887, ../app/pages/options_page.py:2421
Is translated: True

Context: OptionsPage
Text: "Available Nationalities:"
Locations: ../app/pages/options_page.py:908, ../app/pages/options_page.py:2423
Is translated: True

Context: OptionsPage
Text: "Assigned Nationalities:"
Locations: ../app/pages/options_page.py:909, ../app/pages/options_page.py:2425
Is translated: True

Context: OptionsPage
Text: "Add..."
Locations: ../app/pages/options_page.py:892, ../app/pages/options_page.py:2430
Is translated: True

Context: OptionsPage
Text: "Remove"
Locations: ../app/pages/options_page.py:893, ../app/pages/options_page.py:922, ../app/pages/options_page.py:2432, ../app/pages/options_page.py:2444
Is translated: True

Context: OptionsPage
Text: "Rename..."
Locations: ../app/pages/options_page.py:894, ../app/pages/options_page.py:2434
Is translated: True

Context: OptionsPage
Text: "Add"
Locations: ../app/pages/options_page.py:921, ../app/pages/options_page.py:2441
Is translated: True

Context: OptionsPage
Text: "Add Zone"
Locations: ../app/pages/options_page.py:3828
Is translated: True

Context: OptionsPage
Text: "Enter name for the new zone:"
Locations: ../app/pages/options_page.py:3828
Is translated: True

Context: OptionsPage
Text: "Invalid Name"
Locations: ../app/pages/options_page.py:3832, ../app/pages/options_page.py:3847, ../app/pages/options_page.py:3895, ../app/pages/options_page.py:3915
Is translated: True

Context: OptionsPage
Text: "Zone name cannot be empty."
Locations: ../app/pages/options_page.py:3832, ../app/pages/options_page.py:3847, ../app/pages/options_page.py:3895, ../app/pages/options_page.py:3915
Is translated: True

Context: OptionsPage
Text: "Duplicate Name"
Locations: ../app/pages/options_page.py:3835, ../app/pages/options_page.py:3900
Is translated: True

Context: OptionsPage
Text: "A zone with this name already exists."
Locations: ../app/pages/options_page.py:3835, ../app/pages/options_page.py:3900
Is translated: True

Context: OptionsPage
Text: "Select a zone to view/edit assigned nationalities."
Locations: ../app/pages/options_page.py:889, ../app/pages/options_page.py:2428
Is translated: True

Context: OptionsPage
Text: "Nationalities not assigned to the selected zone."
Locations: ../app/pages/options_page.py:913, ../app/pages/options_page.py:2437
Is translated: True

Context: OptionsPage
Text: "Nationalities currently assigned to the selected zone."
Locations: ../app/pages/options_page.py:917, ../app/pages/options_page.py:2439
Is translated: True

Context: OptionsPage
Text: "Assign selected available nationality to the current zone"
Locations: ../app/pages/options_page.py:923, ../app/pages/options_page.py:2442
Is translated: True

Context: OptionsPage
Text: "Remove selected assigned nationality from the current zone"
Locations: ../app/pages/options_page.py:924, ../app/pages/options_page.py:2445
Is translated: True

Context: OptionsPage
Text: "Set the start date of the season"
Locations: ../app/pages/options_page.py:306, ../app/pages/options_page.py:2278
Is translated: True

Context: OptionsPage
Text: "Set the end date of the season"
Locations: ../app/pages/options_page.py:313, ../app/pages/options_page.py:2280
Is translated: True

Context: OptionsPage
Text: "Competitions"
Locations: ../app/pages/options_page.py:333, ../app/pages/options_page.py:2287
Is translated: True

Context: OptionsPage
Text: "Competition"
Locations: ../app/pages/options_page.py:340, ../app/pages/options_page.py:1837, ../app/pages/options_page.py:2290, ../app/pages/options_page.py:3616, ../app/pages/options_page.py:3675
Is translated: True

Context: OptionsPage
Text: "Start Date"
Locations: ../app/pages/options_page.py:340, ../app/pages/options_page.py:2290
Is translated: True

Context: OptionsPage
Text: "End Date"
Locations: ../app/pages/options_page.py:340, ../app/pages/options_page.py:2290
Is translated: True

Context: OptionsPage
Text: "Type"
Locations: ../app/pages/options_page.py:341, ../app/pages/options_page.py:2291, ../app/pages/options_page.py:2467, ../app/pages/options_page.py:2589
Is translated: True

Context: OptionsPage
Text: "Structure"
Locations: ../app/pages/options_page.py:341, ../app/pages/options_page.py:2291, ../app/pages/options_page.py:2467, ../app/pages/options_page.py:2590
Is translated: True

Context: OptionsPage
Text: "Priority"
Locations: ../app/pages/options_page.py:341, ../app/pages/options_page.py:2291
Is translated: True

Context: OptionsPage
Text: "Add Competition"
Locations: ../app/pages/options_page.py:364, ../app/pages/options_page.py:2294, ../app/pages/options_page.py:2470, ../app/pages/options_page.py:2620
Is translated: True

Context: OptionsPage
Text: "Edit Competition"
Locations: ../app/pages/options_page.py:365, ../app/pages/options_page.py:2296, ../app/pages/options_page.py:2472, ../app/pages/options_page.py:2621
Is translated: True

Context: OptionsPage
Text: "Remove Competition"
Locations: ../app/pages/options_page.py:366, ../app/pages/options_page.py:2298, ../app/pages/options_page.py:2474, ../app/pages/options_page.py:2622
Is translated: True

Context: OptionsPage
Text: "Season Timeline"
Locations: ../app/pages/options_page.py:387, ../app/pages/options_page.py:2301
Is translated: True

Context: OptionsPage
Text: "Evaluation Dates"
Locations: ../app/pages/options_page.py:741, ../app/pages/options_page.py:2384
Is translated: True

Context: OptionsPage
Text: "1st Evaluation"
Locations: ../app/pages/options_page.py:745, ../app/pages/options_page.py:1796, ../app/pages/options_page.py:2386
Is translated: True

Context: OptionsPage
Text: "Set the start date of the 1st evaluation"
Locations: ../app/pages/options_page.py:752, ../app/pages/options_page.py:2388
Is translated: True

Context: OptionsPage
Text: "Set the end date of the 1st evaluation"
Locations: ../app/pages/options_page.py:757, ../app/pages/options_page.py:2390
Is translated: True

Context: OptionsPage
Text: "Start 1st Evaluation:"
Locations: ../app/pages/options_page.py:760, ../app/pages/options_page.py:2392
Is translated: True

Context: OptionsPage
Text: "End 1st Evaluation:"
Locations: ../app/pages/options_page.py:761, ../app/pages/options_page.py:2394
Is translated: True

Context: OptionsPage
Text: "2nd Evaluation"
Locations: ../app/pages/options_page.py:774, ../app/pages/options_page.py:1797, ../app/pages/options_page.py:2397
Is translated: True

Context: OptionsPage
Text: "Set the start date of the 2nd evaluation"
Locations: ../app/pages/options_page.py:781, ../app/pages/options_page.py:2399
Is translated: True

Context: OptionsPage
Text: "Set the end date of the 2nd evaluation"
Locations: ../app/pages/options_page.py:786, ../app/pages/options_page.py:2401
Is translated: True

Context: OptionsPage
Text: "Start 2nd Evaluation:"
Locations: ../app/pages/options_page.py:789, ../app/pages/options_page.py:2403
Is translated: True

Context: OptionsPage
Text: "End 2nd Evaluation:"
Locations: ../app/pages/options_page.py:790, ../app/pages/options_page.py:2405
Is translated: True

Context: OptionsPage
Text: "3rd Evaluation"
Locations: ../app/pages/options_page.py:803, ../app/pages/options_page.py:1798, ../app/pages/options_page.py:2408
Is translated: True

Context: OptionsPage
Text: "Set the start date of the 3rd evaluation"
Locations: ../app/pages/options_page.py:810, ../app/pages/options_page.py:2410
Is translated: True

Context: OptionsPage
Text: "Set the end date of the 3rd evaluation"
Locations: ../app/pages/options_page.py:815, ../app/pages/options_page.py:2412
Is translated: True

Context: OptionsPage
Text: "Start 3rd Evaluation:"
Locations: ../app/pages/options_page.py:818, ../app/pages/options_page.py:2414
Is translated: True

Context: OptionsPage
Text: "End 3rd Evaluation:"
Locations: ../app/pages/options_page.py:819, ../app/pages/options_page.py:2416
Is translated: True

Context: OptionsPage
Text: "Evaluation"
Locations: ../app/pages/options_page.py:843, ../app/pages/options_page.py:2272
Is translated: True

Context: OptionsPage
Text: "Football Pitch"
Locations: ../app/pages/options_page.py:953, ../app/pages/options_page.py:2449
Is translated: True

Context: OptionsPage
Text: "Positions Pitch"
Locations: ../app/pages/options_page.py:957, ../app/pages/options_page.py:2451
Is translated: True

Context: OptionsPage
Text: "Click to upload image"
Locations: ../app/pages/options_page.py:972, ../app/pages/options_page.py:982, ../app/pages/options_page.py:992, ../app/pages/options_page.py:2454, ../app/pages/options_page.py:2456, ../app/pages/options_page.py:2458, ../app/pages/options_page.py:2752, ../app/pages/options_page.py:2876, ../app/pages/options_page.py:3000
Is translated: True

Context: OptionsPage
Text: "Requirements: PNG format, < 200KB, max 400x600 pixels"
Locations: ../app/pages/options_page.py:1009, ../app/pages/options_page.py:2461
Is translated: True

Context: OptionsPage
Text: "App Media"
Locations: ../app/pages/options_page.py:1041, ../app/pages/options_page.py:2262
Is translated: True

Context: OptionsPage
Text: "Football Competitions"
Locations: ../app/pages/options_page.py:1042, ../app/pages/options_page.py:2264
Is translated: True

Context: OptionsPage
Text: "Macrocycle end date must be after start date"
Locations: ../app/pages/options_page.py:1621
Is translated: True

Context: OptionsPage
Text: "Macrocycle start date must be within season dates"
Locations: ../app/pages/options_page.py:1626
Is translated: True

Context: OptionsPage
Text: "Macrocycle end date must be within season dates"
Locations: ../app/pages/options_page.py:1630
Is translated: True

Context: OptionsPage
Text: "The selected macrocycle date range is invalid. Reverting to the last valid dates."
Locations: ../app/pages/options_page.py:1651
Is translated: True

Context: OptionsPage
Text: "Start date must be within season dates"
Locations: ../app/pages/options_page.py:1685, ../app/pages/options_page.py:1701, ../app/pages/options_page.py:1723
Is translated: True

Context: OptionsPage
Text: "End date must be within season dates"
Locations: ../app/pages/options_page.py:1690, ../app/pages/options_page.py:1706, ../app/pages/options_page.py:1728
Is translated: True

Context: OptionsPage
Text: "2nd evaluation must start after 1st evaluation ends"
Locations: ../app/pages/options_page.py:1712
Is translated: True

Context: OptionsPage
Text: "3rd evaluation must start after 2nd evaluation ends"
Locations: ../app/pages/options_page.py:1734
Is translated: True

Context: OptionsPage
Text: "Preparation"
Locations: ../app/pages/options_page.py:1829, ../app/pages/options_page.py:3614, ../app/pages/options_page.py:3673
Is translated: True

Context: OptionsPage
Text: "Basic"
Locations: ../app/pages/options_page.py:1833, ../app/pages/options_page.py:3615, ../app/pages/options_page.py:3674
Is translated: True

Context: OptionsPage
Text: "Transition"
Locations: ../app/pages/options_page.py:1841, ../app/pages/options_page.py:3617, ../app/pages/options_page.py:3676
Is translated: True

Context: OptionsPage
Text: "• Macrocycle end date must be after start date"
Locations: ../app/pages/options_page.py:2074
Is translated: True

Context: OptionsPage
Text: "• Macrocycle start date must be within season dates"
Locations: ../app/pages/options_page.py:2078
Is translated: True

Context: OptionsPage
Text: "• Macrocycle end date must be within season dates"
Locations: ../app/pages/options_page.py:2081
Is translated: True

Context: OptionsPage
Text: "• {}"
Locations: ../app/pages/options_page.py:2086
Is translated: True

Context: OptionsPage
Text: "The following issues were found with the macrocycle dates:"
Locations: ../app/pages/options_page.py:2102
Is translated: True

Context: OptionsPage
Text: "Date Range Issues:"
Locations: ../app/pages/options_page.py:2104
Is translated: True

Context: OptionsPage
Text: "Microcycles Outside Macrocycle Range:"
Locations: ../app/pages/options_page.py:2109
Is translated: True

Context: OptionsPage
Text: "Would you like to fix these issues automatically?"
Locations: ../app/pages/options_page.py:2115
Is translated: True

Context: OptionsPage
Text: "Validation Issues"
Locations: ../app/pages/options_page.py:2117
Is translated: True

Context: OptionsPage
Text: "Issues Fixed"
Locations: ../app/pages/options_page.py:2131
Is translated: True

Context: OptionsPage
Text: "The issues have been fixed and dates have been saved."
Locations: ../app/pages/options_page.py:2133
Is translated: True

Context: OptionsPage
Text: "Validation Successful"
Locations: ../app/pages/options_page.py:2157
Is translated: True

Context: OptionsPage
Text: "All dates are valid and have been saved successfully."
Locations: ../app/pages/options_page.py:2159
Is translated: True

Context: OptionsPage
Text: "Logo"
Locations: ../app/pages/options_page.py:2466, ../app/pages/options_page.py:2586
Is translated: True

Context: OptionsPage
Text: "Organization"
Locations: ../app/pages/options_page.py:2466, ../app/pages/options_page.py:2588
Is translated: True

Context: OptionsPage
Text: "Upload Image"
Locations: ../app/pages/options_page.py:2514, ../app/pages/options_page.py:2536, ../app/pages/options_page.py:2558
Is translated: True

Context: OptionsPage
Text: "Remove Image"
Locations: ../app/pages/options_page.py:2515, ../app/pages/options_page.py:2537, ../app/pages/options_page.py:2559, ../app/pages/options_page.py:2733, ../app/pages/options_page.py:2857, ../app/pages/options_page.py:2981
Is translated: True

Context: OptionsPage
Text: "Add to Position"
Locations: ../app/pages/options_page.py:2516, ../app/pages/options_page.py:2538, ../app/pages/options_page.py:2560
Is translated: True

Context: OptionsPage
Text: "Select Positions Pitch Image"
Locations: ../app/pages/options_page.py:2651, ../app/pages/options_page.py:2775, ../app/pages/options_page.py:2899
Is translated: True

Context: OptionsPage
Text: "PNG Images (*.png)"
Locations: ../app/pages/options_page.py:2654, ../app/pages/options_page.py:2778, ../app/pages/options_page.py:2902
Is translated: True

Context: OptionsPage
Text: "Invalid File Type"
Locations: ../app/pages/options_page.py:2663, ../app/pages/options_page.py:2787, ../app/pages/options_page.py:2911
Is translated: True

Context: OptionsPage
Text: "Only PNG files are allowed."
Locations: ../app/pages/options_page.py:2665, ../app/pages/options_page.py:2789, ../app/pages/options_page.py:2913
Is translated: True

Context: OptionsPage
Text: "File Too Large"
Locations: ../app/pages/options_page.py:2674, ../app/pages/options_page.py:2798, ../app/pages/options_page.py:2922
Is translated: True

Context: OptionsPage
Text: "The image file must be less than 200KB. Current size: {:.1f}KB"
Locations: ../app/pages/options_page.py:2675, ../app/pages/options_page.py:2799, ../app/pages/options_page.py:2923
Is translated: True

Context: OptionsPage
Text: "Image Too Large"
Locations: ../app/pages/options_page.py:2684, ../app/pages/options_page.py:2808, ../app/pages/options_page.py:2932
Is translated: True

Context: OptionsPage
Text: "The image dimensions must be at most 400x600 pixels. Current size: {}x{}"
Locations: ../app/pages/options_page.py:2685, ../app/pages/options_page.py:2809, ../app/pages/options_page.py:2933
Is translated: True

Context: OptionsPage
Text: "Image Uploaded"
Locations: ../app/pages/options_page.py:2714, ../app/pages/options_page.py:2838, ../app/pages/options_page.py:2962
Is translated: True

Context: OptionsPage
Text: "The positions pitch image has been uploaded successfully."
Locations: ../app/pages/options_page.py:2716, ../app/pages/options_page.py:2840, ../app/pages/options_page.py:2964
Is translated: True

Context: OptionsPage
Text: "Upload Error"
Locations: ../app/pages/options_page.py:2720, ../app/pages/options_page.py:2844, ../app/pages/options_page.py:2968
Is translated: True

Context: OptionsPage
Text: "An error occurred while uploading the image: {}"
Locations: ../app/pages/options_page.py:2721, ../app/pages/options_page.py:2845, ../app/pages/options_page.py:2969
Is translated: True

Context: OptionsPage
Text: "Are you sure you want to remove the positions pitch image?"
Locations: ../app/pages/options_page.py:2734, ../app/pages/options_page.py:2858, ../app/pages/options_page.py:2982
Is translated: True

Context: OptionsPage
Text: "Image Removed"
Locations: ../app/pages/options_page.py:2756, ../app/pages/options_page.py:2880, ../app/pages/options_page.py:3004
Is translated: True

Context: OptionsPage
Text: "The positions pitch image has been removed successfully."
Locations: ../app/pages/options_page.py:2758, ../app/pages/options_page.py:2882, ../app/pages/options_page.py:3006
Is translated: True

Context: OptionsPage
Text: "Remove Error"
Locations: ../app/pages/options_page.py:2762, ../app/pages/options_page.py:2886, ../app/pages/options_page.py:3010
Is translated: True

Context: OptionsPage
Text: "An error occurred while removing the image: {}"
Locations: ../app/pages/options_page.py:2763, ../app/pages/options_page.py:2887, ../app/pages/options_page.py:3011
Is translated: True

Context: OptionsPage
Text: "Image Not Found"
Locations: ../app/pages/options_page.py:3102
Is translated: True

Context: OptionsPage
Text: "The selected image could not be found."
Locations: ../app/pages/options_page.py:3104
Is translated: True

Context: OptionsPage
Text: "Image Added"
Locations: ../app/pages/options_page.py:3119
Is translated: True

Context: OptionsPage
Text: "The image has been added to the Position tab in the Roster page."
Locations: ../app/pages/options_page.py:3121
Is translated: True

Context: OptionsPage
Text: "Error"
Locations: ../app/pages/options_page.py:3131
Is translated: True

Context: OptionsPage
Text: "An error occurred while adding the image to the Position tab: {}"
Locations: ../app/pages/options_page.py:3132
Is translated: True

Context: OptionsPage
Text: "Microcycle Overlap Warning"
Locations: ../app/pages/options_page.py:3148, ../app/pages/options_page.py:3977
Is translated: True

Context: OptionsPage
Text: "There are overlapping microcycles in your schedule:

{}

Do you want to close without saving the microcycle data?"
Locations: ../app/pages/options_page.py:3149, ../app/pages/options_page.py:3978
Is translated: True

Context: OptionsPage
Text: "Start date must be within macrocycle dates"
Locations: ../app/pages/options_page.py:3274, ../app/pages/options_page.py:3461
Is translated: True

Context: OptionsPage
Text: "End date must be within macrocycle dates"
Locations: ../app/pages/options_page.py:3279, ../app/pages/options_page.py:3466
Is translated: True

Context: OptionsPage
Text: "Microcycle dates cannot overlap with existing microcycles in this mesocycle"
Locations: ../app/pages/options_page.py:3292, ../app/pages/options_page.py:3480
Is translated: True

Context: OptionsPage
Text: "Microcycle dates cannot overlap with existing microcycles: {}"
Locations: ../app/pages/options_page.py:3299, ../app/pages/options_page.py:3487
Is translated: True

Context: OptionsPage
Text: "Please select a microcycle to remove."
Locations: ../app/pages/options_page.py:3574
Is translated: True

Context: OptionsPage
Text: "Confirm Removal"
Locations: ../app/pages/options_page.py:3583, ../app/pages/options_page.py:3858
Is translated: True

Context: OptionsPage
Text: "Overlap detected between {} Mesocycle microcycle '{}' ({} to {}) and {} Mesocycle microcycle '{}' ({} to {})"
Locations: ../app/pages/options_page.py:3643
Is translated: True

Context: OptionsPage
Text: "Dates overlap with {} Mesocycle microcycle '{}' ({} to {})"
Locations: ../app/pages/options_page.py:3696
Is translated: True

Context: OptionsPage
Text: "Microcycle Overlap Error"
Locations: ../app/pages/options_page.py:3715
Is translated: True

Context: OptionsPage
Text: "Error: Cannot save because there are overlapping microcycles in your schedule:

{}

Please fix the overlaps before saving."
Locations: ../app/pages/options_page.py:3716
Is translated: True

Context: OptionsPage
Text: "Are you sure you want to remove the zone '{}'?"
Locations: ../app/pages/options_page.py:3859
Is translated: True

Context: OptionsPage
Text: "Rename Zone"
Locations: ../app/pages/options_page.py:3887
Is translated: True

Context: OptionsPage
Text: "Enter new name for zone '{}':"
Locations: ../app/pages/options_page.py:3888
Is translated: True

Context: OptionsPage
Text: "Organization:"
Is translated: True

Context: OptionsPage
Text: "Type:"
Is translated: True

Context: OptionsPage
Text: "Structure:"
Is translated: True

Context: OptionsPage
Text: "National"
Is translated: True

Context: OptionsPage
Text: "International"
Is translated: True

Context: OptionsPage
Text: "Local"
Is translated: True

Context: OptionsPage
Text: "Other"
Is translated: True

Context: OptionsPage
Text: "League"
Is translated: True

Context: OptionsPage
Text: "League+playoffs/playouts"
Is translated: True

Context: OptionsPage
Text: "Group+Knockouts"
Is translated: True

Context: OptionsPage
Text: "Knockouts"
Is translated: True

Context: OptionsPage
Text: "Tournament"
Is translated: True

Context: OptionsPage
Text: "Preparation matches"
Is translated: True

Context: OptionsPage
Text: "Charity"
Is translated: True

Context: PeriodizationChartDialog
Text: "Periodization Chart"
Locations: ../app/dialogs/periodization_chart_dialog.py:25, ../app/dialogs/periodization_chart_dialog_fixed.py:20, ../app/dialogs/periodization_chart_dialog_fixed.py:308, ../app/dialogs/periodization_chart_dialog_complete.py:25, ../app/dialogs/periodization_chart_dialog_updated.py:25
Is translated: True

Context: PeriodizationChartDialog
Text: "Periodization Intensity Chart"
Locations: ../app/dialogs/periodization_chart_dialog.py:74, ../app/dialogs/periodization_chart_dialog_complete.py:66, ../app/dialogs/periodization_chart_dialog_updated.py:66
Is translated: True

Context: PeriodizationChartDialog
Text: "Chart Type"
Locations: ../app/dialogs/periodization_chart_dialog.py:91, ../app/dialogs/periodization_chart_dialog_fixed.py:68, ../app/dialogs/periodization_chart_dialog_complete.py:83, ../app/dialogs/periodization_chart_dialog_updated.py:83
Is translated: True

Context: PeriodizationChartDialog
Text: "Line Chart"
Locations: ../app/dialogs/periodization_chart_dialog.py:94, ../app/dialogs/periodization_chart_dialog_complete.py:86, ../app/dialogs/periodization_chart_dialog_updated.py:86
Is translated: True

Context: PeriodizationChartDialog
Text: "Area Chart"
Locations: ../app/dialogs/periodization_chart_dialog.py:95, ../app/dialogs/periodization_chart_dialog_complete.py:87, ../app/dialogs/periodization_chart_dialog_updated.py:87
Is translated: True

Context: PeriodizationChartDialog
Text: "Step Chart"
Locations: ../app/dialogs/periodization_chart_dialog.py:96, ../app/dialogs/periodization_chart_dialog_complete.py:88, ../app/dialogs/periodization_chart_dialog_updated.py:88
Is translated: True

Context: PeriodizationChartDialog
Text: "Bar Chart"
Locations: ../app/dialogs/periodization_chart_dialog.py:97, ../app/dialogs/periodization_chart_dialog_complete.py:89, ../app/dialogs/periodization_chart_dialog_updated.py:89
Is translated: True

Context: PeriodizationChartDialog
Text: "Color Scheme"
Locations: ../app/dialogs/periodization_chart_dialog.py:115, ../app/dialogs/periodization_chart_dialog_fixed.py:82, ../app/dialogs/periodization_chart_dialog_complete.py:107, ../app/dialogs/periodization_chart_dialog_updated.py:107
Is translated: True

Context: PeriodizationChartDialog
Text: "Color by Mesocycle Type"
Locations: ../app/dialogs/periodization_chart_dialog.py:118, ../app/dialogs/periodization_chart_dialog_complete.py:110, ../app/dialogs/periodization_chart_dialog_updated.py:110
Is translated: True

Context: PeriodizationChartDialog
Text: "Single Color Gradient"
Locations: ../app/dialogs/periodization_chart_dialog.py:119, ../app/dialogs/periodization_chart_dialog_complete.py:111, ../app/dialogs/periodization_chart_dialog_updated.py:111
Is translated: True

Context: PeriodizationChartDialog
Text: "Custom Colors"
Locations: ../app/dialogs/periodization_chart_dialog.py:120, ../app/dialogs/periodization_chart_dialog.py:136, ../app/dialogs/periodization_chart_dialog_fixed.py:96, ../app/dialogs/periodization_chart_dialog_complete.py:112, ../app/dialogs/periodization_chart_dialog_updated.py:112
Is translated: True

Context: PeriodizationChartDialog
Text: "Additional Display Options"
Locations: ../app/dialogs/periodization_chart_dialog.py:171, ../app/dialogs/periodization_chart_dialog_complete.py:145, ../app/dialogs/periodization_chart_dialog_updated.py:145
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Mesocycle Boundaries"
Locations: ../app/dialogs/periodization_chart_dialog.py:174, ../app/dialogs/periodization_chart_dialog_fixed.py:163, ../app/dialogs/periodization_chart_dialog_complete.py:148, ../app/dialogs/periodization_chart_dialog_updated.py:148
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Microcycle Names"
Locations: ../app/dialogs/periodization_chart_dialog.py:175, ../app/dialogs/periodization_chart_dialog_complete.py:149, ../app/dialogs/periodization_chart_dialog_updated.py:149
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Date Ranges"
Locations: ../app/dialogs/periodization_chart_dialog.py:176, ../app/dialogs/periodization_chart_dialog_complete.py:150, ../app/dialogs/periodization_chart_dialog_updated.py:150
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Grid Lines"
Locations: ../app/dialogs/periodization_chart_dialog.py:177, ../app/dialogs/periodization_chart_dialog_complete.py:151, ../app/dialogs/periodization_chart_dialog_updated.py:151
Is translated: True

Context: PeriodizationChartDialog
Text: "Line Width"
Locations: ../app/dialogs/periodization_chart_dialog.py:191, ../app/dialogs/periodization_chart_dialog_fixed.py:126, ../app/dialogs/periodization_chart_dialog_complete.py:165, ../app/dialogs/periodization_chart_dialog_updated.py:165
Is translated: True

Context: PeriodizationChartDialog
Text: "Updates"
Locations: ../app/dialogs/periodization_chart_dialog.py:220, ../app/dialogs/periodization_chart_dialog_fixed.py:189, ../app/dialogs/periodization_chart_dialog_complete.py:194, ../app/dialogs/periodization_chart_dialog_updated.py:194
Is translated: True

Context: PeriodizationChartDialog
Text: "Enable Real-time Updates"
Locations: ../app/dialogs/periodization_chart_dialog.py:223, ../app/dialogs/periodization_chart_dialog_complete.py:197, ../app/dialogs/periodization_chart_dialog_updated.py:197
Is translated: True

Context: PeriodizationChartDialog
Text: "Update Chart"
Locations: ../app/dialogs/periodization_chart_dialog.py:230, ../app/dialogs/periodization_chart_dialog_complete.py:204, ../app/dialogs/periodization_chart_dialog_updated.py:204
Is translated: True

Context: PeriodizationChartDialog
Text: "Date"
Locations: ../app/dialogs/periodization_chart_dialog.py:375, ../app/dialogs/periodization_chart_dialog_fixed.py:313, ../app/dialogs/periodization_chart_dialog_complete.py:335
Is translated: True

Context: PeriodizationChartDialog
Text: "Intensity (%)"
Locations: ../app/dialogs/periodization_chart_dialog.py:380, ../app/dialogs/periodization_chart_dialog_fixed.py:318, ../app/dialogs/periodization_chart_dialog_complete.py:340
Is translated: True

Context: PeriodizationChartDialog
Text: "Intensity"
Locations: ../app/dialogs/periodization_chart_dialog.py:467, ../app/dialogs/periodization_chart_dialog.py:578, ../app/dialogs/periodization_chart_dialog.py:647, ../app/dialogs/periodization_chart_dialog.py:897, ../app/dialogs/periodization_chart_dialog_fixed.py:382, ../app/dialogs/periodization_chart_dialog_fixed.py:474, ../app/dialogs/periodization_chart_dialog_complete.py:412, ../app/dialogs/periodization_chart_dialog_complete.py:520, ../app/dialogs/periodization_chart_dialog_complete.py:586
Is translated: True

Context: PeriodizationChartDialog
Text: "Microcycles"
Locations: ../app/dialogs/periodization_chart_dialog.py:923
Is translated: True

Context: PeriodizationChartDialog
Text: "Select Color for {mesocycle_type}"
Locations: ../app/dialogs/periodization_chart_dialog.py:314
Is translated: True

Context: PeriodizationChartDialog
Text: "Data Display Options"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:143, ../app/dialogs/periodization_chart_dialog_complete.py:128, ../app/dialogs/periodization_chart_dialog_updated.py:128
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Intensity Values"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:146, ../app/dialogs/periodization_chart_dialog_complete.py:131, ../app/dialogs/periodization_chart_dialog_updated.py:131
Is translated: True

Context: PeriodizationChartDialog
Text: "Show intensity percentage values on chart points"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:149
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Target 1"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:151, ../app/dialogs/periodization_chart_dialog_complete.py:132, ../app/dialogs/periodization_chart_dialog_updated.py:132
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Target 2"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:155, ../app/dialogs/periodization_chart_dialog_complete.py:133, ../app/dialogs/periodization_chart_dialog_updated.py:133
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Target 3"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:159, ../app/dialogs/periodization_chart_dialog_complete.py:134, ../app/dialogs/periodization_chart_dialog_updated.py:134
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Mesocycle Names"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:167
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Dates"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:171
Is translated: True

Context: PeriodizationChartDialog
Text: "Show Grid"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:175
Is translated: True

Context: PeriodizationChartDialog
Text: "Real-time Updates"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:192
Is translated: True

Context: PeriodizationChartDialog
Text: "Update chart automatically when settings change"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:194
Is translated: True

Context: PeriodizationChartDialog
Text: "Refresh Chart"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:198
Is translated: None

Context: PeriodizationChartDialog
Text: "Click to update the chart with current settings"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:199
Is translated: True

Context: PeriodizationChartDialog
Text: "Select Color"
Locations: ../app/dialogs/periodization_chart_dialog_fixed.py:259
Is translated: True

Context: PeriodizationTimelineWidget
Text: "Macrocycles"
Locations: ../app/widgets/periodization_timeline_widget.py:44
Is translated: True

Context: PeriodizationTimelineWidget
Text: "Mesocycles"
Locations: ../app/widgets/periodization_timeline_widget.py:48
Is translated: True

Context: PeriodizationTimelineWidget
Text: "Microcycles"
Locations: ../app/widgets/periodization_timeline_widget.py:52
Is translated: True

Context: RemoveFontDialog
Text: "Remove Custom Fonts"
Locations: ../app/windows/settings_window.py:42
Is translated: True

Context: RemoveFontDialog
Text: "Select custom font family to remove:"
Locations: ../app/windows/settings_window.py:51
Is translated: True

Context: RemoveFontDialog
Text: "Remove Selected"
Locations: ../app/windows/settings_window.py:55
Is translated: True

Context: RemoveFontDialog
Text: "Error"
Locations: ../app/windows/settings_window.py:125, ../app/windows/settings_window.py:130
Is translated: True

Context: RemoveFontDialog
Text: "Font information not found for {}."
Locations: ../app/windows/settings_window.py:125
Is translated: True

Context: RemoveFontDialog
Text: "No font files associated with {}."
Locations: ../app/windows/settings_window.py:130
Is translated: True

Context: RemoveFontDialog
Text: "Confirm Removal"
Locations: ../app/windows/settings_window.py:140
Is translated: True

Context: RemoveFontDialog
Text: "Are you sure you want to remove the font family '{}'?
This will delete the following file(s):
 - {}"
Locations: ../app/windows/settings_window.py:141
Is translated: True

Context: RemoveFontDialog
Text: "Could not delete file {}: {}"
Locations: ../app/windows/settings_window.py:186
Is translated: True

Context: RemoveFontDialog
Text: "Unexpected error removing {}: {}"
Locations: ../app/windows/settings_window.py:188
Is translated: True

Context: RemoveFontDialog
Text: "Removal Partially Failed"
Locations: ../app/windows/settings_window.py:207
Is translated: True

Context: RemoveFontDialog
Text: "Removed family '{}'.
Deleted {} file(s).
Encountered errors:
 - {}"
Locations: ../app/windows/settings_window.py:208
Is translated: True

Context: RemoveFontDialog
Text: "Removal Successful"
Locations: ../app/windows/settings_window.py:210
Is translated: True

Context: RemoveFontDialog
Text: "Successfully removed font family '{}' and deleted {} associated file(s)."
Locations: ../app/windows/settings_window.py:211
Is translated: True

Context: RemoveFontDialog
Text: "Internal error finding font ID for {})."
Locations: ../app/windows/settings_window.py:184
Is translated: True

Context: RosterDelegate
Text: "(None)"
Locations: ../app/pages/roster_page.py:218, ../app/pages/roster_page.py:303
Is translated: True

Context: RosterPage
Text: "Show Body Data Columns"
Locations: ../app/pages/roster_page.py:3957
Is translated: True

Context: RosterPage
Text: "ID:"
Locations: ../app/pages/roster_page.py:663, ../app/pages/roster_page.py:3448
Is translated: True

Context: RosterPage
Text: "Last Name:"
Locations: ../app/pages/roster_page.py:664, ../app/pages/roster_page.py:3449
Is translated: True

Context: RosterPage
Text: "First Name:"
Locations: ../app/pages/roster_page.py:665, ../app/pages/roster_page.py:3450
Is translated: True

Context: RosterPage
Text: "Shirt Number:"
Locations: ../app/pages/roster_page.py:666, ../app/pages/roster_page.py:3451
Is translated: True

Context: RosterPage
Text: "Position:"
Locations: ../app/pages/roster_page.py:667, ../app/pages/roster_page.py:3452, ../app/pages/roster_page.py:3499
Is translated: True

Context: RosterPage
Text: "Detailed Position:"
Locations: ../app/pages/roster_page.py:668, ../app/pages/roster_page.py:3453, ../app/pages/roster_page.py:3500
Is translated: True

Context: RosterPage
Text: "Date of Birth:"
Locations: ../app/pages/roster_page.py:669, ../app/pages/roster_page.py:3454
Is translated: True

Context: RosterPage
Text: "Age:"
Locations: ../app/pages/roster_page.py:670, ../app/pages/roster_page.py:3455
Is translated: True

Context: RosterPage
Text: "Personal"
Locations: ../app/pages/roster_page.py:711, ../app/pages/roster_page.py:3407, ../app/pages/roster_page.py:3424
Is translated: True

Context: RosterPage
Text: "Height:"
Locations: ../app/pages/roster_page.py:859, ../app/pages/roster_page.py:3523
Is translated: True

Context: RosterPage
Text: "Weight:"
Locations: ../app/pages/roster_page.py:860, ../app/pages/roster_page.py:3524
Is translated: True

Context: RosterPage
Text: "Physical"
Locations: ../app/pages/roster_page.py:906, ../app/pages/roster_page.py:3409, ../app/pages/roster_page.py:3426
Is translated: True

Context: RosterPage
Text: "ID"
Locations: ../app/pages/roster_page.py:1376, ../app/pages/roster_page.py:3811
Is translated: True

Context: RosterPage
Text: "Last Name"
Locations: ../app/pages/roster_page.py:1377, ../app/pages/roster_page.py:3812
Is translated: True

Context: RosterPage
Text: "Name"
Locations: ../app/pages/roster_page.py:1378, ../app/pages/roster_page.py:3813
Is translated: True

Context: RosterPage
Text: "No."
Locations: ../app/pages/roster_page.py:1379, ../app/pages/roster_page.py:3814
Is translated: True

Context: RosterPage
Text: "Pos."
Locations: ../app/pages/roster_page.py:1380, ../app/pages/roster_page.py:3815
Is translated: True

Context: RosterPage
Text: "Detailed Pos."
Locations: ../app/pages/roster_page.py:1381, ../app/pages/roster_page.py:3816
Is translated: True

Context: RosterPage
Text: "Date of Birth"
Locations: ../app/pages/roster_page.py:1382, ../app/pages/roster_page.py:3817
Is translated: True

Context: RosterPage
Text: "Age"
Locations: ../app/pages/roster_page.py:1383, ../app/pages/roster_page.py:3818
Is translated: True

Context: RosterPage
Text: "Height"
Locations: ../app/pages/roster_page.py:1384, ../app/pages/roster_page.py:3819
Is translated: True

Context: RosterPage
Text: "Weight"
Locations: ../app/pages/roster_page.py:1385, ../app/pages/roster_page.py:3820
Is translated: True

Context: RosterPage
Text: "Add Player"
Locations: ../app/pages/roster_page.py:1314, ../app/pages/roster_page.py:3844
Is translated: True

Context: RosterPage
Text: "Remove Player"
Locations: ../app/pages/roster_page.py:1315, ../app/pages/roster_page.py:3846
Is translated: True

Context: RosterPage
Text: "New"
Locations: ../app/pages/roster_page.py:2681
Is translated: True

Context: RosterPage
Text: "Player"
Locations: ../app/pages/roster_page.py:2682
Is translated: True

Context: RosterPage
Text: "Error"
Locations: ../app/pages/roster_page.py:1560, ../app/pages/roster_page.py:2707, ../app/pages/roster_page.py:2746, ../app/pages/roster_page.py:4448
Is translated: True

Context: RosterPage
Text: "Could not add new player to the database."
Locations: ../app/pages/roster_page.py:2707
Is translated: True

Context: RosterPage
Text: "Confirm Removal"
Locations: ../app/pages/roster_page.py:2720, ../app/pages/roster_page.py:4779
Is translated: True

Context: RosterPage
Text: "Are you sure you want to remove player: {}?"
Locations: ../app/pages/roster_page.py:2721
Is translated: True

Context: RosterPage
Text: "Could not remove the selected player from the database."
Locations: ../app/pages/roster_page.py:2746
Is translated: True

Context: RosterPage
Text: "(None)"
Locations: ../app/pages/roster_page.py:1796, ../app/pages/roster_page.py:2461, ../app/pages/roster_page.py:2623, ../app/pages/roster_page.py:4104, ../app/pages/roster_page.py:4531, ../app/pages/roster_page.py:4551, ../app/pages/roster_page.py:5218
Is translated: True

Context: RosterPage
Text: "Status"
Locations: ../app/pages/roster_page.py:1022, ../app/pages/roster_page.py:3410, ../app/pages/roster_page.py:3428
Is translated: True

Context: RosterPage
Text: "Profile"
Locations: ../app/pages/roster_page.py:1216, ../app/pages/roster_page.py:3415, ../app/pages/roster_page.py:3430, ../app/pages/roster_page.py:3577
Is translated: True

Context: RosterPage
Text: "Nationality:"
Locations: ../app/pages/roster_page.py:676, ../app/pages/roster_page.py:3479
Is translated: True

Context: RosterPage
Text: "Sex:"
Locations: ../app/pages/roster_page.py:683, ../app/pages/roster_page.py:3457
Is translated: True

Context: RosterPage
Text: "Preferred Foot:"
Locations: ../app/pages/roster_page.py:684, ../app/pages/roster_page.py:3458
Is translated: True

Context: RosterPage
Text: "Strong Eye:"
Locations: ../app/pages/roster_page.py:685, ../app/pages/roster_page.py:3459
Is translated: True

Context: RosterPage
Text: "Zone:"
Locations: ../app/pages/roster_page.py:679, ../app/pages/roster_page.py:3456
Is translated: True

Context: RosterPage
Text: "Male"
Locations: ../app/pages/roster_page.py:650, ../app/pages/roster_page.py:3864
Is translated: True

Context: RosterPage
Text: "Female"
Locations: ../app/pages/roster_page.py:651, ../app/pages/roster_page.py:3865
Is translated: True

Context: RosterPage
Text: "Left"
Locations: ../app/pages/roster_page.py:3877, ../app/pages/roster_page.py:3880, ../app/pages/roster_page.py:3893, ../app/pages/roster_page.py:3896
Is translated: True

Context: RosterPage
Text: "Right"
Locations: ../app/pages/roster_page.py:3877, ../app/pages/roster_page.py:3882, ../app/pages/roster_page.py:3893, ../app/pages/roster_page.py:3898
Is translated: True

Context: RosterPage
Text: "Both"
Locations: ../app/pages/roster_page.py:3877, ../app/pages/roster_page.py:3884
Is translated: True

Context: RosterPage
Text: "Not available"
Locations: ../app/pages/roster_page.py:3893, ../app/pages/roster_page.py:3900
Is translated: True

Context: RosterPage
Text: "Waist:"
Locations: ../app/pages/roster_page.py:861, ../app/pages/roster_page.py:3525
Is translated: True

Context: RosterPage
Text: "Hip:"
Locations: ../app/pages/roster_page.py:862, ../app/pages/roster_page.py:3526
Is translated: True

Context: RosterPage
Text: "Neck:"
Locations: ../app/pages/roster_page.py:863, ../app/pages/roster_page.py:3527
Is translated: True

Context: RosterPage
Text: "Wrist:"
Locations: ../app/pages/roster_page.py:864, ../app/pages/roster_page.py:3528
Is translated: True

Context: RosterPage
Text: "Forearm:"
Locations: ../app/pages/roster_page.py:865, ../app/pages/roster_page.py:3529
Is translated: True

Context: RosterPage
Text: "Thigh:"
Locations: ../app/pages/roster_page.py:866, ../app/pages/roster_page.py:3530
Is translated: True

Context: RosterPage
Text: "BMI:"
Locations: ../app/pages/roster_page.py:891, ../app/pages/roster_page.py:3533
Is translated: True

Context: RosterPage
Text: "Status Tags"
Locations: ../app/pages/roster_page.py:1401, ../app/pages/roster_page.py:3803, ../app/pages/roster_page.py:3836
Is translated: True

Context: RosterPage
Text: "Fitness:"
Locations: ../app/pages/roster_page.py:867, ../app/pages/roster_page.py:3531
Is translated: True

Context: RosterPage
Text: "Transfer Status:"
Locations: ../app/pages/roster_page.py:950, ../app/pages/roster_page.py:3760
Is translated: True

Context: RosterPage
Text: "Contract End Date:"
Locations: ../app/pages/roster_page.py:971, ../app/pages/roster_page.py:3779
Is translated: True

Context: RosterPage
Text: "Loan Club:"
Locations: ../app/pages/roster_page.py:981, ../app/pages/roster_page.py:3781
Is translated: True

Context: RosterPage
Text: "Loan Start Date:"
Locations: ../app/pages/roster_page.py:985, ../app/pages/roster_page.py:3782
Is translated: True

Context: RosterPage
Text: "Loan End Date:"
Locations: ../app/pages/roster_page.py:990, ../app/pages/roster_page.py:3783
Is translated: True

Context: RosterPage
Text: "Nationality"
Locations: ../app/pages/roster_page.py:1392, ../app/pages/roster_page.py:3827
Is translated: True

Context: RosterPage
Text: "Sex"
Locations: ../app/pages/roster_page.py:1394, ../app/pages/roster_page.py:3829
Is translated: True

Context: RosterPage
Text: "Zone"
Locations: ../app/pages/roster_page.py:1397, ../app/pages/roster_page.py:3832
Is translated: True

Context: RosterPage
Text: "Fitness"
Locations: ../app/pages/roster_page.py:1398, ../app/pages/roster_page.py:3833
Is translated: True

Context: RosterPage
Text: "Transfer Status"
Locations: ../app/pages/roster_page.py:1402, ../app/pages/roster_page.py:3804, ../app/pages/roster_page.py:3837
Is translated: True

Context: RosterPage
Text: "Basic Columns"
Locations: ../app/pages/roster_page.py:3848
Is translated: True

Context: RosterPage
Text: "Extended Columns"
Locations: ../app/pages/roster_page.py:3850
Is translated: True

Context: RosterPage
Text: "All Columns"
Locations: ../app/pages/roster_page.py:3852
Is translated: True

Context: RosterPage
Text: "Primary Group"
Locations: ../app/pages/roster_page.py:1399, ../app/pages/roster_page.py:3834
Is translated: True

Context: RosterPage
Text: "Secondary Group"
Locations: ../app/pages/roster_page.py:1400, ../app/pages/roster_page.py:3835
Is translated: True

Context: RosterPage
Text: "Primary Group:"
Locations: ../app/pages/roster_page.py:687, ../app/pages/roster_page.py:3460
Is translated: True

Context: RosterPage
Text: "Secondary Group:"
Locations: ../app/pages/roster_page.py:688, ../app/pages/roster_page.py:3465
Is translated: True

Context: RosterPage
Text: "Date Joined Club:"
Locations: ../app/pages/roster_page.py:1150, ../app/pages/roster_page.py:3584
Is translated: True

Context: RosterPage
Text: "Personality Type:"
Locations: ../app/pages/roster_page.py:1151, ../app/pages/roster_page.py:3585
Is translated: True

Context: RosterPage
Text: "Mentality:"
Locations: ../app/pages/roster_page.py:1153, ../app/pages/roster_page.py:3586
Is translated: True

Context: RosterPage
Text: "Work Ethic:"
Locations: ../app/pages/roster_page.py:1154, ../app/pages/roster_page.py:3587
Is translated: True

Context: RosterPage
Text: "Professionalism:"
Locations: ../app/pages/roster_page.py:1155, ../app/pages/roster_page.py:3588
Is translated: True

Context: RosterPage
Text: "Determination:"
Locations: ../app/pages/roster_page.py:1156, ../app/pages/roster_page.py:3589
Is translated: True

Context: RosterPage
Text: "Team Spirit:"
Locations: ../app/pages/roster_page.py:1157, ../app/pages/roster_page.py:3590
Is translated: True

Context: RosterPage
Text: "Adaptability:"
Locations: ../app/pages/roster_page.py:1158, ../app/pages/roster_page.py:3591
Is translated: True

Context: RosterPage
Text: "Temperament:"
Locations: ../app/pages/roster_page.py:1159, ../app/pages/roster_page.py:3592
Is translated: True

Context: RosterPage
Text: "Ambition:"
Locations: ../app/pages/roster_page.py:1160, ../app/pages/roster_page.py:3593
Is translated: True

Context: RosterPage
Text: "Leadership:"
Locations: ../app/pages/roster_page.py:1161, ../app/pages/roster_page.py:3594
Is translated: True

Context: RosterPage
Text: "Charisma:"
Locations: ../app/pages/roster_page.py:1162, ../app/pages/roster_page.py:3595
Is translated: True

Context: RosterPage
Text: "Emotional Intelligence Notes:"
Locations: ../app/pages/roster_page.py:1164, ../app/pages/roster_page.py:3625
Is translated: True

Context: RosterPage
Text: "Life Motto / Quote:"
Locations: ../app/pages/roster_page.py:1165, ../app/pages/roster_page.py:3626
Is translated: True

Context: RosterPage
Text: "Personal Goals (Outside Football):"
Locations: ../app/pages/roster_page.py:1166, ../app/pages/roster_page.py:3627
Is translated: True

Context: RosterPage
Text: "Hometown:"
Locations: ../app/pages/roster_page.py:1168, ../app/pages/roster_page.py:3596
Is translated: True

Context: RosterPage
Text: "Current Residence:"
Locations: ../app/pages/roster_page.py:1169, ../app/pages/roster_page.py:3597
Is translated: True

Context: RosterPage
Text: "Marital Status:"
Locations: ../app/pages/roster_page.py:1170, ../app/pages/roster_page.py:3598
Is translated: True

Context: RosterPage
Text: "Partner's Name:"
Locations: ../app/pages/roster_page.py:1171, ../app/pages/roster_page.py:3599
Is translated: True

Context: RosterPage
Text: "Father's Name:"
Locations: ../app/pages/roster_page.py:1172, ../app/pages/roster_page.py:3600
Is translated: True

Context: RosterPage
Text: "Mother's Name:"
Locations: ../app/pages/roster_page.py:1173, ../app/pages/roster_page.py:3601
Is translated: True

Context: RosterPage
Text: "Parents' Occupations:"
Locations: ../app/pages/roster_page.py:1174, ../app/pages/roster_page.py:3628
Is translated: True

Context: RosterPage
Text: "Siblings Summary:"
Locations: ../app/pages/roster_page.py:1175, ../app/pages/roster_page.py:3629
Is translated: True

Context: RosterPage
Text: "Family Background Notes:"
Locations: ../app/pages/roster_page.py:1176, ../app/pages/roster_page.py:3630
Is translated: True

Context: RosterPage
Text: "Number of Children:"
Locations: ../app/pages/roster_page.py:1177, ../app/pages/roster_page.py:3602
Is translated: True

Context: RosterPage
Text: "Pets Description:"
Locations: ../app/pages/roster_page.py:3631
Is translated: True

Context: RosterPage
Text: "Superstitions / Rituals:"
Locations: ../app/pages/roster_page.py:3632
Is translated: True

Context: RosterPage
Text: "Religion / Faith:"
Locations: ../app/pages/roster_page.py:1179, ../app/pages/roster_page.py:3603
Is translated: True

Context: RosterPage
Text: "Religious Practices Notes:"
Locations: ../app/pages/roster_page.py:3633
Is translated: True

Context: RosterPage
Text: "Political Views Summary:"
Locations: ../app/pages/roster_page.py:3634
Is translated: True

Context: RosterPage
Text: "Education Level:"
Locations: ../app/pages/roster_page.py:1180, ../app/pages/roster_page.py:3604
Is translated: True

Context: RosterPage
Text: "Field of Study:"
Locations: ../app/pages/roster_page.py:1181, ../app/pages/roster_page.py:3605
Is translated: True

Context: RosterPage
Text: "University/School:"
Locations: ../app/pages/roster_page.py:3606
Is translated: True

Context: RosterPage
Text: "Other Job:"
Locations: ../app/pages/roster_page.py:3607
Is translated: True

Context: RosterPage
Text: "Coaching Interest:"
Locations: ../app/pages/roster_page.py:3608
Is translated: True

Context: RosterPage
Text: "Media Friendliness:"
Locations: ../app/pages/roster_page.py:1185, ../app/pages/roster_page.py:3609
Is translated: True

Context: RosterPage
Text: "Website/Blog:"
Locations: ../app/pages/roster_page.py:3610
Is translated: True

Context: RosterPage
Text: "Email:"
Locations: ../app/pages/roster_page.py:3611
Is translated: True

Context: RosterPage
Text: "Phone:"
Locations: ../app/pages/roster_page.py:3612
Is translated: True

Context: RosterPage
Text: "Agent Name:"
Locations: ../app/pages/roster_page.py:1189, ../app/pages/roster_page.py:3613
Is translated: True

Context: RosterPage
Text: "Personal Assistant:"
Locations: ../app/pages/roster_page.py:3618
Is translated: True

Context: RosterPage
Text: "Charity/Community:"
Locations: ../app/pages/roster_page.py:3635
Is translated: True

Context: RosterPage
Text: "Volunteer Work:"
Locations: ../app/pages/roster_page.py:3636
Is translated: True

Context: RosterPage
Text: "Business Ventures:"
Locations: ../app/pages/roster_page.py:3637
Is translated: True

Context: RosterPage
Text: "Public Image:"
Locations: ../app/pages/roster_page.py:3638
Is translated: True

Context: RosterPage
Text: "Media Notes:"
Locations: ../app/pages/roster_page.py:3639
Is translated: True

Context: RosterPage
Text: "Home Address:"
Locations: ../app/pages/roster_page.py:1178, ../app/pages/roster_page.py:3640
Is translated: True

Context: RosterPage
Text: "Retirement Plans:"
Locations: ../app/pages/roster_page.py:3641
Is translated: True

Context: RosterPage
Text: "Search all columns..."
Locations: ../app/pages/roster_page.py:1334, ../app/pages/roster_page.py:3856
Is translated: True

Context: RosterPage
Text: "Player Status Tags:"
Locations: ../app/pages/roster_page.py:928, ../app/pages/roster_page.py:3727
Is translated: True

Context: RosterPage
Text: "Select a player from the list"
Locations: ../app/pages/roster_page.py:529, ../app/pages/roster_page.py:721, ../app/pages/roster_page.py:799, ../app/pages/roster_page.py:915, ../app/pages/roster_page.py:1046, ../app/pages/roster_page.py:2252, ../app/pages/roster_page.py:3550, ../app/pages/roster_page.py:3552, ../app/pages/roster_page.py:3554, ../app/pages/roster_page.py:3556, ../app/pages/roster_page.py:3703
Is translated: True

Context: RosterPage
Text: "Waist"
Locations: ../app/pages/roster_page.py:1386, ../app/pages/roster_page.py:3821
Is translated: True

Context: RosterPage
Text: "Hip"
Locations: ../app/pages/roster_page.py:1387, ../app/pages/roster_page.py:3822
Is translated: True

Context: RosterPage
Text: "Neck"
Locations: ../app/pages/roster_page.py:1388, ../app/pages/roster_page.py:3823
Is translated: True

Context: RosterPage
Text: "Wrist"
Locations: ../app/pages/roster_page.py:1389, ../app/pages/roster_page.py:3824
Is translated: True

Context: RosterPage
Text: "Forearm"
Locations: ../app/pages/roster_page.py:1390, ../app/pages/roster_page.py:3825
Is translated: True

Context: RosterPage
Text: "Thigh"
Locations: ../app/pages/roster_page.py:1391, ../app/pages/roster_page.py:3826
Is translated: True

Context: RosterPage
Text: "Flag"
Locations: ../app/pages/roster_page.py:1393, ../app/pages/roster_page.py:3828
Is translated: True

Context: RosterPage
Text: "Pref. Foot"
Locations: ../app/pages/roster_page.py:1395, ../app/pages/roster_page.py:3830
Is translated: True

Context: RosterPage
Text: "Strong Eye"
Locations: ../app/pages/roster_page.py:1396, ../app/pages/roster_page.py:3831
Is translated: True

Context: RosterPage
Text: "Body Fat % (Est. Navy):"
Locations: ../app/pages/roster_page.py:892, ../app/pages/roster_page.py:3534
Is translated: True

Context: RosterPage
Text: "Lean Body Mass (Est.):"
Locations: ../app/pages/roster_page.py:893, ../app/pages/roster_page.py:3535
Is translated: True

Context: RosterPage
Text: "Waist-to-Hip Ratio:"
Locations: ../app/pages/roster_page.py:894, ../app/pages/roster_page.py:3536
Is translated: True

Context: RosterPage
Text: "Waist-to-Height Ratio:"
Locations: ../app/pages/roster_page.py:895, ../app/pages/roster_page.py:3537
Is translated: True

Context: RosterPage
Text: "View Physical Profile Chart"
Locations: ../app/pages/roster_page.py:899, ../app/pages/roster_page.py:3546
Is translated: True

Context: RosterPage
Text: "Click to upload player image (PNG, 200x300, <20KB)"
Locations: ../app/pages/roster_page.py:545
Is translated: True

Context: RosterPage
Text: "Clear All Ratings"
Locations: ../app/pages/roster_page.py:779, ../app/pages/roster_page.py:1540
Is translated: True

Context: RosterPage
Text: "Remove all position ratings for the selected player"
Locations: ../app/pages/roster_page.py:780
Is translated: True

Context: RosterPage
Text: "Position"
Locations: ../app/pages/roster_page.py:789, ../app/pages/roster_page.py:3408
Is translated: True

Context: RosterPage
Text: "Date Added to List:"
Locations: ../app/pages/roster_page.py:976, ../app/pages/roster_page.py:3780
Is translated: True

Context: RosterPage
Text: "Club Sold To:"
Locations: ../app/pages/roster_page.py:995, ../app/pages/roster_page.py:3784
Is translated: True

Context: RosterPage
Text: "Date Joining New Club:"
Locations: ../app/pages/roster_page.py:999, ../app/pages/roster_page.py:3785
Is translated: True

Context: RosterPage
Text: "Date Released:"
Locations: ../app/pages/roster_page.py:1004, ../app/pages/roster_page.py:3786
Is translated: True

Context: RosterPage
Text: "Enter player biography notes here..."
Locations: ../app/pages/roster_page.py:1145
Is translated: True

Context: RosterPage
Text: "University / School:"
Locations: ../app/pages/roster_page.py:1182
Is translated: True

Context: RosterPage
Text: "Dual Career / Other Job:"
Locations: ../app/pages/roster_page.py:1183
Is translated: True

Context: RosterPage
Text: "Coach / Mentor Willingness:"
Locations: ../app/pages/roster_page.py:1184
Is translated: True

Context: RosterPage
Text: "Website / Blog:"
Locations: ../app/pages/roster_page.py:1186
Is translated: True

Context: RosterPage
Text: "Personal Email:"
Locations: ../app/pages/roster_page.py:1187
Is translated: True

Context: RosterPage
Text: "Personal Phone:"
Locations: ../app/pages/roster_page.py:1188
Is translated: True

Context: RosterPage
Text: "Personal Assistant / Manager:"
Locations: ../app/pages/roster_page.py:1190
Is translated: True

Context: RosterPage
Text: "Select Basic"
Locations: ../app/pages/roster_page.py:1317
Is translated: True

Context: RosterPage
Text: "Select Extended"
Locations: ../app/pages/roster_page.py:1318
Is translated: None

Context: RosterPage
Text: "Select All"
Locations: ../app/pages/roster_page.py:1319
Is translated: True

Context: RosterPage
Text: "Are you sure you want to clear all position ratings for this player?"
Locations: ../app/pages/roster_page.py:1541
Is translated: True

Context: RosterPage
Text: "Failed to clear position ratings. Please try again."
Locations: ../app/pages/roster_page.py:1562
Is translated: True

Context: RosterPage
Text: "Summary of selected status tags."
Locations: ../app/pages/roster_page.py:1769
Is translated: True

Context: RosterPage
Text: "Determined by nationality based on configured zones."
Locations: ../app/pages/roster_page.py:1788
Is translated: True

Context: RosterPage
Text: "Error loading profile data."
Locations: ../app/pages/roster_page.py:2139
Is translated: True

Context: RosterPage
Text: "Update Failed"
Locations: ../app/pages/roster_page.py:2350, ../app/pages/roster_page.py:2463, ../app/pages/roster_page.py:2551, ../app/pages/roster_page.py:5220, ../app/pages/roster_page.py:5308
Is translated: True

Context: RosterPage
Text: "Could not save changes to the database."
Locations: ../app/pages/roster_page.py:2350
Is translated: True

Context: RosterPage
Text: "Could not save group change to the database."
Locations: ../app/pages/roster_page.py:2463, ../app/pages/roster_page.py:5220
Is translated: True

Context: RosterPage
Text: "Invalid Input"
Locations: ../app/pages/roster_page.py:2484, ../app/pages/roster_page.py:5241
Is translated: True

Context: RosterPage
Text: "Shirt number {} is already assigned."
Locations: ../app/pages/roster_page.py:2484, ../app/pages/roster_page.py:5241
Is translated: True

Context: RosterPage
Text: "Could not save change for {} to the database."
Locations: ../app/pages/roster_page.py:2551, ../app/pages/roster_page.py:5308
Is translated: True

Context: RosterPage
Text: "Select Player"
Locations: ../app/pages/roster_page.py:2789
Is translated: True

Context: RosterPage
Text: "Please select a player before uploading an image."
Locations: ../app/pages/roster_page.py:2790
Is translated: True

Context: RosterPage
Text: "Select Player Image"
Locations: ../app/pages/roster_page.py:2795
Is translated: True

Context: RosterPage
Text: "PNG Images (*.png)"
Locations: ../app/pages/roster_page.py:2796
Is translated: True

Context: RosterPage
Text: "Invalid File Type"
Locations: ../app/pages/roster_page.py:2812
Is translated: True

Context: RosterPage
Text: "Please select a PNG image file."
Locations: ../app/pages/roster_page.py:2813
Is translated: True

Context: RosterPage
Text: "File Too Large"
Locations: ../app/pages/roster_page.py:2822
Is translated: True

Context: RosterPage
Text: "Image file size must be less than {}KB. Selected size: {:.1f}KB"
Locations: ../app/pages/roster_page.py:2824
Is translated: True

Context: RosterPage
Text: "Error Reading File"
Locations: ../app/pages/roster_page.py:2828
Is translated: True

Context: RosterPage
Text: "Could not read file properties: {}"
Locations: ../app/pages/roster_page.py:2829
Is translated: True

Context: RosterPage
Text: "Cannot Read Image"
Locations: ../app/pages/roster_page.py:2835
Is translated: True

Context: RosterPage
Text: "Could not read image data. The file might be corrupted."
Locations: ../app/pages/roster_page.py:2836
Is translated: True

Context: RosterPage
Text: "Incorrect Dimensions"
Locations: ../app/pages/roster_page.py:2844
Is translated: True

Context: RosterPage
Text: "Image dimensions must be no larger than {}x{} pixels. Selected dimensions: {}x{} pixels."
Locations: ../app/pages/roster_page.py:2846
Is translated: True

Context: RosterPage
Text: "Upload Successful"
Locations: ../app/pages/roster_page.py:2861
Is translated: True

Context: RosterPage
Text: "Player image updated successfully."
Locations: ../app/pages/roster_page.py:2862
Is translated: True

Context: RosterPage
Text: "Upload Failed"
Locations: ../app/pages/roster_page.py:2865
Is translated: True

Context: RosterPage
Text: "Could not save the image: {}"
Locations: ../app/pages/roster_page.py:2866
Is translated: True

Context: RosterPage
Text: "Primary Position:"
Locations: ../app/pages/roster_page.py:3501
Is translated: True

Context: RosterPage
Text: "Secondary Position:"
Locations: ../app/pages/roster_page.py:3502
Is translated: True

Context: RosterPage
Text: "Position Notes:"
Locations: ../app/pages/roster_page.py:3503
Is translated: True

Context: RosterPage
Text: "This column cannot be hidden."
Locations: ../app/pages/roster_page.py:3942
Is translated: True

Context: RosterPage
Text: "Group ID columns cannot be toggled here."
Locations: ../app/pages/roster_page.py:3946
Is translated: True

Context: RosterPage
Text: "No Player Selected"
Locations: ../app/pages/roster_page.py:4422
Is translated: True

Context: RosterPage
Text: "Please select a player from the roster first."
Locations: ../app/pages/roster_page.py:4423
Is translated: True

Context: RosterPage
Text: "Insufficient Data"
Locations: ../app/pages/roster_page.py:4442
Is translated: True

Context: RosterPage
Text: "Not enough physical data available for the selected player to generate a chart."
Locations: ../app/pages/roster_page.py:4443
Is translated: True

Context: RosterPage
Text: "Failed to retrieve data for the chart."
Locations: ../app/pages/roster_page.py:4448
Is translated: True

Context: RosterPage
Text: "Upload Photo"
Locations: ../app/pages/roster_page.py:4741
Is translated: True

Context: RosterPage
Text: "Remove Photo"
Locations: ../app/pages/roster_page.py:4749
Is translated: True

Context: RosterPage
Text: "No specific image to remove for this player."
Locations: ../app/pages/roster_page.py:4754
Is translated: True

Context: RosterPage
Text: "No Image"
Locations: ../app/pages/roster_page.py:4773
Is translated: True

Context: RosterPage
Text: "There is no specific image file to remove for this player."
Locations: ../app/pages/roster_page.py:4774
Is translated: True

Context: RosterPage
Text: "Are you sure you want to remove the image for player ID {}?"
Locations: ../app/pages/roster_page.py:4780
Is translated: True

Context: RosterPage
Text: "Image Removed"
Locations: ../app/pages/roster_page.py:4789
Is translated: True

Context: RosterPage
Text: "Player image removed successfully."
Locations: ../app/pages/roster_page.py:4790
Is translated: True

Context: RosterPage
Text: "Removal Failed"
Locations: ../app/pages/roster_page.py:4795
Is translated: True

Context: RosterPage
Text: "Could not remove the image file: {}"
Locations: ../app/pages/roster_page.py:4796
Is translated: True

Context: SeasonTimelineWidget
Text: "Refresh Timeline"
Locations: ../app/widgets/season_timeline_widget_new.py:86, ../app/widgets/season_timeline_widget_new.py:139, ../app/widgets/season_timeline_widget.py:87
Is translated: True

Context: SeasonTimelineWidget
Text: "Refresh the timeline visualization"
Locations: ../app/widgets/season_timeline_widget_new.py:88, ../app/widgets/season_timeline_widget_new.py:140, ../app/widgets/season_timeline_widget.py:89
Is translated: True

Context: SeasonTimelineWidget
Text: "Show Months"
Locations: ../app/widgets/season_timeline_widget_new.py:92, ../app/widgets/season_timeline_widget_new.py:143
Is translated: True

Context: SeasonTimelineWidget
Text: "Show Weeks"
Locations: ../app/widgets/season_timeline_widget_new.py:96, ../app/widgets/season_timeline_widget_new.py:144
Is translated: True

Context: SeasonTimelineWidget
Text: "Show Intensity Chart"
Locations: ../app/widgets/season_timeline_widget_new.py:100, ../app/widgets/season_timeline_widget_new.py:145
Is translated: True

Context: SeasonTimelineWidget
Text: "Jan"
Is translated: True

Context: SeasonTimelineWidget
Text: "Feb"
Is translated: True

Context: SeasonTimelineWidget
Text: "Mar"
Is translated: True

Context: SeasonTimelineWidget
Text: "Apr"
Is translated: True

Context: SeasonTimelineWidget
Text: "May"
Is translated: True

Context: SeasonTimelineWidget
Text: "Jun"
Is translated: True

Context: SeasonTimelineWidget
Text: "Jul"
Is translated: True

Context: SeasonTimelineWidget
Text: "Aug"
Is translated: True

Context: SeasonTimelineWidget
Text: "Sep"
Is translated: True

Context: SeasonTimelineWidget
Text: "Oct"
Is translated: True

Context: SeasonTimelineWidget
Text: "Nov"
Is translated: True

Context: SeasonTimelineWidget
Text: "Dec"
Is translated: True

Context: SeasonTimelineWidget
Text: "Season"
Is translated: True

Context: SeasonTimelineWidget
Text: "Mesocycles"
Is translated: True

Context: SeasonTimelineWidget
Text: "Microcycles"
Is translated: True

Context: SeasonTimelineWidget
Text: "Evaluations"
Is translated: True

Context: SeasonTimelineWidget
Text: "Competition"
Is translated: True

Context: SettingsWindow
Text: "Settings"
Locations: ../app/windows/settings_window.py:323, ../app/windows/settings_window.py:943
Is translated: True

Context: SettingsWindow
Text: "Error"
Locations: ../app/windows/settings_window.py:694, ../app/windows/settings_window.py:697, ../app/windows/settings_window.py:763
Is translated: True

Context: SettingsWindow
Text: "Could not apply language change immediately."
Locations: ../app/windows/settings_window.py:694
Is translated: True

Context: SettingsWindow
Text: "Could not create custom fonts directory: {} {}"
Locations: ../app/windows/settings_window.py:763
Is translated: True

Context: SettingsWindow
Text: "Font Files (*.ttf *.otf)"
Locations: ../app/windows/settings_window.py:768
Is translated: True

Context: SettingsWindow
Text: "Select Custom Font Files"
Locations: ../app/windows/settings_window.py:769
Is translated: True

Context: SettingsWindow
Text: "Add Custom Fonts Result"
Locations: ../app/windows/settings_window.py:835
Is translated: True

Context: SettingsWindow
Text: "Restart Required"
Locations: ../app/windows/settings_window.py:894
Is translated: True

Context: SettingsWindow
Text: "The application must be restarted for the zoom level change to take effect."
Locations: ../app/windows/settings_window.py:895
Is translated: True

Context: SettingsWindow
Text: "Language:"
Locations: ../app/windows/settings_window.py:946
Is translated: True

Context: SettingsWindow
Text: "General"
Locations: ../app/windows/settings_window.py:949
Is translated: True

Context: SettingsWindow
Text: "Appearance"
Locations: ../app/windows/settings_window.py:950
Is translated: True

Context: SettingsWindow
Text: "Theme"
Locations: ../app/windows/settings_window.py:1004
Is translated: True

Context: SettingsWindow
Text: "Light"
Locations: ../app/windows/settings_window.py:404, ../app/windows/settings_window.py:1005
Is translated: True

Context: SettingsWindow
Text: "Dark"
Locations: ../app/windows/settings_window.py:405, ../app/windows/settings_window.py:1006
Is translated: True

Context: SettingsWindow
Text: "Accent Color"
Locations: ../app/windows/settings_window.py:1009
Is translated: True

Context: SettingsWindow
Text: "Button Shape"
Locations: ../app/windows/settings_window.py:438, ../app/windows/settings_window.py:1014
Is translated: True

Context: SettingsWindow
Text: "Layout Density"
Locations: ../app/windows/settings_window.py:1019
Is translated: True

Context: SettingsWindow
Text: "Zoom Level"
Locations: ../app/windows/settings_window.py:1024
Is translated: True

Context: SettingsWindow
Text: "Font"
Locations: ../app/windows/settings_window.py:1027
Is translated: True

Context: SettingsWindow
Text: "Family:"
Locations: ../app/windows/settings_window.py:1028
Is translated: True

Context: SettingsWindow
Text: "Size:"
Locations: ../app/windows/settings_window.py:1029
Is translated: True

Context: SettingsWindow
Text: "Add Font..."
Locations: ../app/windows/settings_window.py:1030
Is translated: True

Context: SettingsWindow
Text: "Remove Font..."
Locations: ../app/windows/settings_window.py:1031
Is translated: True

Context: SettingsWindow
Text: "System"
Locations: ../app/windows/settings_window.py:406, ../app/windows/settings_window.py:1007
Is translated: True

Context: SettingsWindow
Text: "Select the application language"
Locations: ../app/windows/settings_window.py:341
Is translated: True

Context: SettingsWindow
Text: "Choose the language for the application interface"
Locations: ../app/windows/settings_window.py:343
Is translated: True

Context: SettingsWindow
Text: "Enable or disable tooltips throughout the application"
Locations: ../app/windows/settings_window.py:359
Is translated: True

Context: SettingsWindow
Text: "Set how long tooltips remain visible"
Locations: ../app/windows/settings_window.py:365
Is translated: True

Context: SettingsWindow
Text: "Choose the duration tooltips remain visible"
Locations: ../app/windows/settings_window.py:367
Is translated: True

Context: SettingsWindow
Text: "An unexpected error occurred while changing language: {})."
Locations: ../app/windows/settings_window.py:697
Is translated: True

Context: SettingsWindow
Text: "Search:"
Locations: ../app/windows/settings_window.py:1034
Is translated: True

Context: SettingsWindow
Text: "Blue"
Is translated: True

Context: SettingsWindow
Text: "Green"
Is translated: True

Context: SettingsWindow
Text: "Red"
Is translated: True

Context: SettingsWindow
Text: "Orange"
Is translated: True

Context: SettingsWindow
Text: "Yellow"
Is translated: True

Context: SettingsWindow
Text: "Light Grey"
Is translated: True

Context: SettingsWindow
Text: "Grey"
Is translated: True

Context: SettingsWindow
Text: "Square"
Is translated: True

Context: SettingsWindow
Text: "Rounded"
Is translated: True

Context: SettingsWindow
Text: "Compact"
Is translated: True

Context: SettingsWindow
Text: "Default"
Is translated: True

Context: SettingsWindow
Text: "Comfortable"
Is translated: True

Context: SponsorSectionWidget
Text: "Sponsor Name:"
Locations: ../app/widgets/sponsors_panel.py:120, ../app/widgets/sponsors_panel.py:630
Is translated: True

Context: SponsorSectionWidget
Text: "Category:"
Locations: ../app/widgets/sponsors_panel.py:121, ../app/widgets/sponsors_panel.py:631
Is translated: True

Context: SponsorSectionWidget
Text: "Type:"
Locations: ../app/widgets/sponsors_panel.py:122, ../app/widgets/sponsors_panel.py:632
Is translated: True

Context: SponsorSectionWidget
Text: "Deal Start:"
Locations: ../app/widgets/sponsors_panel.py:123, ../app/widgets/sponsors_panel.py:633
Is translated: True

Context: SponsorSectionWidget
Text: "Deal End:"
Locations: ../app/widgets/sponsors_panel.py:124, ../app/widgets/sponsors_panel.py:634
Is translated: True

Context: SponsorSectionWidget
Text: "Deal Term:"
Locations: ../app/widgets/sponsors_panel.py:125, ../app/widgets/sponsors_panel.py:635
Is translated: True

Context: SponsorSectionWidget
Text: "Fee/Value:"
Locations: ../app/widgets/sponsors_panel.py:126, ../app/widgets/sponsors_panel.py:636
Is translated: True

Context: SponsorSectionWidget
Text: "Notes"
Locations: ../app/widgets/sponsors_panel.py:131, ../app/widgets/sponsors_panel.py:688
Is translated: True

Context: SponsorSectionWidget
Text: "Enter any relevant notes here..."
Locations: ../app/widgets/sponsors_panel.py:136, ../app/widgets/sponsors_panel.py:666
Is translated: True

Context: SponsorSectionWidget
Text: "No Logo"
Locations: ../app/widgets/sponsors_panel.py:161, ../app/widgets/sponsors_panel.py:486, ../app/widgets/sponsors_panel.py:693
Is translated: True

Context: SponsorSectionWidget
Text: "Upload Logo"
Locations: ../app/widgets/sponsors_panel.py:172, ../app/widgets/sponsors_panel.py:694
Is translated: True

Context: SponsorSectionWidget
Text: "Remove Logo"
Locations: ../app/widgets/sponsors_panel.py:173, ../app/widgets/sponsors_panel.py:695
Is translated: True

Context: SponsorSectionWidget
Text: "Contact Info"
Locations: ../app/widgets/sponsors_panel.py:195, ../app/widgets/sponsors_panel.py:690
Is translated: True

Context: SponsorSectionWidget
Text: "Contact Person:"
Locations: ../app/widgets/sponsors_panel.py:207, ../app/widgets/sponsors_panel.py:707
Is translated: True

Context: SponsorSectionWidget
Text: "Email:"
Locations: ../app/widgets/sponsors_panel.py:208, ../app/widgets/sponsors_panel.py:708
Is translated: True

Context: SponsorSectionWidget
Text: "Phone:"
Locations: ../app/widgets/sponsors_panel.py:209, ../app/widgets/sponsors_panel.py:709
Is translated: True

Context: SponsorSectionWidget
Text: "Select deal"
Locations: ../app/widgets/sponsors_panel.py:106, ../app/widgets/sponsors_panel.py:674
Is translated: True

Context: SponsorSectionWidget
Text: "Daily"
Locations: ../app/widgets/sponsors_panel.py:107, ../app/widgets/sponsors_panel.py:675
Is translated: True

Context: SponsorSectionWidget
Text: "Monthly"
Locations: ../app/widgets/sponsors_panel.py:108, ../app/widgets/sponsors_panel.py:676
Is translated: True

Context: SponsorSectionWidget
Text: "Season"
Locations: ../app/widgets/sponsors_panel.py:109, ../app/widgets/sponsors_panel.py:677
Is translated: True

Context: SponsorSectionWidget
Text: "Yearly"
Locations: ../app/widgets/sponsors_panel.py:110, ../app/widgets/sponsors_panel.py:678
Is translated: True

Context: SponsorSectionWidget
Text: "Other"
Locations: ../app/widgets/sponsors_panel.py:112, ../app/widgets/sponsors_panel.py:680
Is translated: True

Context: SponsorSectionWidget
Text: "Sponsor Name..."
Locations: ../app/widgets/sponsors_panel.py:92, ../app/widgets/sponsors_panel.py:663
Is translated: True

Context: SponsorSectionWidget
Text: "Type..."
Locations: ../app/widgets/sponsors_panel.py:97, ../app/widgets/sponsors_panel.py:664
Is translated: True

Context: SponsorSectionWidget
Text: "Contact Person..."
Locations: ../app/widgets/sponsors_panel.py:201, ../app/widgets/sponsors_panel.py:665
Is translated: True

Context: SponsorSectionWidget
Text: "Email..."
Locations: ../app/widgets/sponsors_panel.py:203, ../app/widgets/sponsors_panel.py:667
Is translated: True

Context: SponsorSectionWidget
Text: "Phone..."
Locations: ../app/widgets/sponsors_panel.py:205, ../app/widgets/sponsors_panel.py:668
Is translated: True

Context: SponsorSectionWidget
Text: "(PNG, <{kb}KB, <{w}x{h}px)"
Locations: ../app/widgets/sponsors_panel.py:182, ../app/widgets/sponsors_panel.py:491, ../app/widgets/sponsors_panel.py:697
Is translated: True

Context: SponsorSectionWidget
Text: "Sponsor name cannot be empty."
Locations: ../app/widgets/sponsors_panel.py:258
Is translated: True

Context: SponsorSectionWidget
Text: "Please enter a valid email address."
Locations: ../app/widgets/sponsors_panel.py:263
Is translated: True

Context: SponsorSectionWidget
Text: "Please enter a valid numeric fee/value."
Locations: ../app/widgets/sponsors_panel.py:268
Is translated: True

Context: SponsorSectionWidget
Text: "End date cannot be before start date."
Locations: ../app/widgets/sponsors_panel.py:271
Is translated: True

Context: SponsorSectionWidget
Text: "Type cannot be empty."
Locations: ../app/widgets/sponsors_panel.py:275
Is translated: True

Context: SponsorSectionWidget
Text: "Upload Error"
Locations: ../app/widgets/sponsors_panel.py:303
Is translated: True

Context: SponsorSectionWidget
Text: "Could not create directory for sponsor logos."
Locations: ../app/widgets/sponsors_panel.py:303
Is translated: True

Context: SponsorSectionWidget
Text: "PNG Images (*.png)"
Locations: ../app/widgets/sponsors_panel.py:308
Is translated: True

Context: SponsorSectionWidget
Text: "Select Sponsor Logo"
Locations: ../app/widgets/sponsors_panel.py:310
Is translated: True

Context: SponsorSectionWidget
Text: "Error: File size exceeds {kb}KB."
Locations: ../app/widgets/sponsors_panel.py:324
Is translated: True

Context: SponsorSectionWidget
Text: "Error: Cannot read image file."
Locations: ../app/widgets/sponsors_panel.py:333
Is translated: True

Context: SponsorSectionWidget
Text: "Error: Dimensions exceed {w}x{h}px."
Locations: ../app/widgets/sponsors_panel.py:340
Is translated: True

Context: SponsorSectionWidget
Text: "Error copying logo!"
Locations: ../app/widgets/sponsors_panel.py:376
Is translated: True

Context: SponsorSectionWidget
Text: "Remove Error"
Locations: ../app/widgets/sponsors_panel.py:401
Is translated: True

Context: SponsorSectionWidget
Text: "Could not remove the logo file. Check permissions."
Locations: ../app/widgets/sponsors_panel.py:401
Is translated: True

Context: SponsorsPanel
Text: "Main"
Locations: ../app/widgets/sponsors_panel.py:975, ../app/widgets/sponsors_panel.py:1178
Is translated: True

Context: SponsorsPanel
Text: "Supporters"
Locations: ../app/widgets/sponsors_panel.py:976, ../app/widgets/sponsors_panel.py:1179
Is translated: True

Context: SponsorsPanel
Text: "Charity"
Locations: ../app/widgets/sponsors_panel.py:977, ../app/widgets/sponsors_panel.py:1180
Is translated: True

Context: SponsorsPanel
Text: "Sports Brand"
Locations: ../app/widgets/sponsors_panel.py:982, ../app/widgets/sponsors_panel.py:1185
Is translated: True

Context: StaffAssignmentDialog
Text: "Assign Staff to {team_name}"
Locations: ../app/widgets/staff_assignment_dialog.py:30, ../app/dialogs/staff_assignment_dialog.py:30
Is translated: True

Context: StaffAssignmentDialog
Text: "Assign staff members to {team_name}"
Locations: ../app/widgets/staff_assignment_dialog.py:38, ../app/dialogs/staff_assignment_dialog.py:38
Is translated: True

Context: StaffAssignmentDialog
Text: "Technical Director"
Locations: ../app/widgets/staff_assignment_dialog.py:49, ../app/dialogs/staff_assignment_dialog.py:49
Is translated: True

Context: StaffAssignmentDialog
Text: "Head Coach"
Locations: ../app/widgets/staff_assignment_dialog.py:50, ../app/dialogs/staff_assignment_dialog.py:50
Is translated: True

Context: StaffAssignmentDialog
Text: "Coach"
Locations: ../app/widgets/staff_assignment_dialog.py:51, ../app/dialogs/staff_assignment_dialog.py:51
Is translated: True

Context: StaffAssignmentDialog
Text: "Assistant Coach"
Locations: ../app/widgets/staff_assignment_dialog.py:52, ../app/dialogs/staff_assignment_dialog.py:52
Is translated: True

Context: StaffAssignmentDialog
Text: "Staff 1"
Locations: ../app/widgets/staff_assignment_dialog.py:53, ../app/dialogs/staff_assignment_dialog.py:53
Is translated: True

Context: StaffAssignmentDialog
Text: "Staff 2"
Locations: ../app/widgets/staff_assignment_dialog.py:54, ../app/dialogs/staff_assignment_dialog.py:54
Is translated: True

Context: StaffAssignmentDialog
Text: "(Unassigned)"
Locations: ../app/widgets/staff_assignment_dialog.py:67, ../app/dialogs/staff_assignment_dialog.py:67
Is translated: True

Context: StaffFilterDialog
Text: "-- Any --"
Locations: ../app/dialogs/staff_filter_dialog.py:59, ../app/dialogs/staff_filter_dialog.py:248, ../app/dialogs/staff_filter_dialog.py:254
Is translated: True

Context: StaffFilterDialog
Text: "Filter"
Locations: ../app/dialogs/staff_filter_dialog.py:207
Is translated: True

Context: StaffFilterDialog
Text: "OK"
Locations: ../app/dialogs/staff_filter_dialog.py:215
Is translated: True

Context: StaffFilterDialog
Text: "Cancel"
Locations: ../app/dialogs/staff_filter_dialog.py:217
Is translated: True

Context: StaffFilterDialog
Text: "Clear Filters"
Locations: ../app/dialogs/staff_filter_dialog.py:219
Is translated: True

Context: TeamGroupsWidget
Text: "Group Id"
Locations: ../app/widgets/team_groups_widget.py:1030
Is translated: True

Context: TeamGroupsWidget
Text: "Team Name"
Locations: ../app/widgets/team_groups_widget.py:1032
Is translated: True

Context: TeamGroupsWidget
Text: "Group Category"
Locations: ../app/widgets/team_groups_widget.py:1034
Is translated: True

Context: TeamGroupsWidget
Text: "Add Group"
Locations: ../app/widgets/team_groups_widget.py:486, ../app/widgets/team_groups_widget.py:977
Is translated: True

Context: TeamGroupsWidget
Text: "Remove Selected"
Locations: ../app/widgets/team_groups_widget.py:487, ../app/widgets/team_groups_widget.py:978
Is translated: True

Context: TeamGroupsWidget
Text: "Search:"
Locations: ../app/widgets/team_groups_widget.py:503, ../app/widgets/team_groups_widget.py:991, ../app/widgets/team_groups_widget.py:1000
Is translated: True

Context: TeamGroupsWidget
Text: "Search..."
Locations: ../app/widgets/team_groups_widget.py:491, ../app/widgets/team_groups_widget.py:1006
Is translated: True

Context: TeamGroupsWidget
Text: "Clear"
Locations: ../app/widgets/team_groups_widget.py:492, ../app/widgets/team_groups_widget.py:1008
Is translated: True

Context: TeamGroupsWidget
Text: "Filter"
Locations: ../app/widgets/team_groups_widget.py:1011
Is translated: True

Context: TeamGroupsWidget
Text: "Clear Filter"
Locations: ../app/widgets/team_groups_widget.py:1014
Is translated: True

Context: TeamGroupsWidget
Text: "Add New Team Group"
Locations: ../app/widgets/team_groups_widget.py:657
Is translated: True

Context: TeamGroupsWidget
Text: "Enter the name for the new group:"
Locations: ../app/widgets/team_groups_widget.py:658
Is translated: True

Context: TeamGroupsWidget
Text: "Input Error"
Locations: ../app/widgets/team_groups_widget.py:663, ../app/widgets/team_groups_widget.py:700
Is translated: True

Context: TeamGroupsWidget
Text: "Group name cannot be empty."
Locations: ../app/widgets/team_groups_widget.py:663, ../app/widgets/team_groups_widget.py:700, ../app/widgets/team_groups_widget.py:787
Is translated: True

Context: TeamGroupsWidget
Text: "Duplicate Name"
Locations: ../app/widgets/team_groups_widget.py:669
Is translated: True

Context: TeamGroupsWidget
Text: "A group with the name '{name}' already exists."
Locations: ../app/widgets/team_groups_widget.py:670, ../app/widgets/team_groups_widget.py:791
Is translated: True

Context: TeamGroupsWidget
Text: "Database Error"
Locations: ../app/widgets/team_groups_widget.py:697, ../app/widgets/team_groups_widget.py:750, ../app/widgets/team_groups_widget.py:820
Is translated: True

Context: TeamGroupsWidget
Text: "Failed to add the new group to the database."
Locations: ../app/widgets/team_groups_widget.py:698
Is translated: True

Context: TeamGroupsWidget
Text: "Error"
Locations: ../app/widgets/team_groups_widget.py:717, ../app/widgets/team_groups_widget.py:724
Is translated: True

Context: TeamGroupsWidget
Text: "Could not retrieve group information from the selected row."
Locations: ../app/widgets/team_groups_widget.py:717
Is translated: True

Context: TeamGroupsWidget
Text: "Could not retrieve group ID."
Locations: ../app/widgets/team_groups_widget.py:724
Is translated: True

Context: TeamGroupsWidget
Text: "Cannot Remove Group"
Locations: ../app/widgets/team_groups_widget.py:730
Is translated: True

Context: TeamGroupsWidget
Text: "Cannot remove group '{name}' because {count} player(s) are still assigned to it. Please reassign players first."
Locations: ../app/widgets/team_groups_widget.py:732
Is translated: True

Context: TeamGroupsWidget
Text: "Confirm Removal"
Locations: ../app/widgets/team_groups_widget.py:737
Is translated: True

Context: TeamGroupsWidget
Text: "Are you sure you want to remove the group: '{name}'?"
Locations: ../app/widgets/team_groups_widget.py:738
Is translated: True

Context: TeamGroupsWidget
Text: "Failed to remove the group from the database."
Locations: ../app/widgets/team_groups_widget.py:751
Is translated: True

Context: TeamGroupsWidget
Text: "Validation Error"
Locations: ../app/widgets/team_groups_widget.py:798
Is translated: True

Context: TeamGroupsWidget
Text: "Failed to update group in the database."
Locations: ../app/widgets/team_groups_widget.py:820
Is translated: True

Context: TeamGroupsWidget
Text: "M1"
Locations: ../app/widgets/team_groups_widget.py:1036
Is translated: True

Context: TeamGroupsWidget
Text: "C (1)"
Locations: ../app/widgets/team_groups_widget.py:1040
Is translated: True

Context: self.parent_widget
Text: "Season"
Locations: ../app/widgets/season_timeline_widget_new.py:274
Is translated: True

Context: self.parent_widget
Text: "Macrocycle"
Locations: ../app/widgets/season_timeline_widget_new.py:275, ../app/widgets/season_timeline_widget_new.py:462
Is translated: True

Context: self.parent_widget
Text: "Mesocycles"
Locations: ../app/widgets/season_timeline_widget_new.py:276
Is translated: True

Context: self.parent_widget
Text: "Microcycles"
Locations: ../app/widgets/season_timeline_widget_new.py:277
Is translated: True

Context: self.parent_widget
Text: "Evaluations"
Locations: ../app/widgets/season_timeline_widget_new.py:278
Is translated: True

Context: self.parent_widget
Text: "Competitions"
Locations: ../app/widgets/season_timeline_widget_new.py:282
Is translated: True

Context: self.parent_widget
Text: "W"
Locations: ../app/widgets/season_timeline_widget_new.py:385
Is translated: True

Context: self.parent_widget
Text: "Intensity"
Locations: ../app/widgets/season_timeline_widget_new.py:636
Is translated: True

Context: TimelineWindow
Text: "Show Months"
Is translated: True

Context: TimelineWindow
Text: "Show Weeks"
Is translated: True

Context: TimelineWindow
Text: "Show Intensity Chart"
Is translated: True

Context: TimelineWindow
Text: "Jan"
Is translated: True

Context: TimelineWindow
Text: "Feb"
Is translated: True

Context: TimelineWindow
Text: "Mar"
Is translated: True

Context: TimelineWindow
Text: "Apr"
Is translated: True

Context: TimelineWindow
Text: "May"
Is translated: True

Context: TimelineWindow
Text: "Jun"
Is translated: True

Context: TimelineWindow
Text: "Jul"
Is translated: True

Context: TimelineWindow
Text: "Aug"
Is translated: True

Context: TimelineWindow
Text: "Sep"
Is translated: True

Context: TimelineWindow
Text: "Oct"
Is translated: True

Context: TimelineWindow
Text: "Nov"
Is translated: True

Context: TimelineWindow
Text: "Dec"
Is translated: True

Context: TimelineWindow
Text: "Season"
Is translated: True

Context: TimelineWindow
Text: "Mesocycles"
Is translated: True

Context: TimelineWindow
Text: "Microcycles"
Is translated: True

Context: TimelineWindow
Text: "Evaluations"
Is translated: True

Context: TimelineWindow
Text: "Competition"
Is translated: True

Context: Timeline
Text: "Show Months"
Is translated: True

Context: Timeline
Text: "Show Weeks"
Is translated: True

Context: Timeline
Text: "Show Intensity Chart"
Is translated: True

Context: Timeline
Text: "Jan"
Is translated: True

Context: Timeline
Text: "Feb"
Is translated: True

Context: Timeline
Text: "Mar"
Is translated: True

Context: Timeline
Text: "Apr"
Is translated: True

Context: Timeline
Text: "May"
Is translated: True

Context: Timeline
Text: "Jun"
Is translated: True

Context: Timeline
Text: "Jul"
Is translated: True

Context: Timeline
Text: "Aug"
Is translated: True

Context: Timeline
Text: "Sep"
Is translated: True

Context: Timeline
Text: "Oct"
Is translated: True

Context: Timeline
Text: "Nov"
Is translated: True

Context: Timeline
Text: "Dec"
Is translated: True

Context: Timeline
Text: "Season"
Is translated: True

Context: Timeline
Text: "Mesocycles"
Is translated: True

Context: Timeline
Text: "Microcycles"
Is translated: True

Context: Timeline
Text: "Evaluations"
Is translated: True

Context: Timeline
Text: "Competition"
Is translated: True

Context: SeasonTimeline
Text: "Show Months"
Is translated: True

Context: SeasonTimeline
Text: "Show Weeks"
Is translated: True

Context: SeasonTimeline
Text: "Show Intensity Chart"
Is translated: True

Context: SeasonTimeline
Text: "Jan"
Is translated: True

Context: SeasonTimeline
Text: "Feb"
Is translated: True

Context: SeasonTimeline
Text: "Mar"
Is translated: True

Context: SeasonTimeline
Text: "Apr"
Is translated: True

Context: SeasonTimeline
Text: "May"
Is translated: True

Context: SeasonTimeline
Text: "Jun"
Is translated: True

Context: SeasonTimeline
Text: "Jul"
Is translated: True

Context: SeasonTimeline
Text: "Aug"
Is translated: True

Context: SeasonTimeline
Text: "Sep"
Is translated: True

Context: SeasonTimeline
Text: "Oct"
Is translated: True

Context: SeasonTimeline
Text: "Nov"
Is translated: True

Context: SeasonTimeline
Text: "Dec"
Is translated: True

Context: SeasonTimeline
Text: "Season"
Is translated: True

Context: SeasonTimeline
Text: "Mesocycles"
Is translated: True

Context: SeasonTimeline
Text: "Microcycles"
Is translated: True

Context: SeasonTimeline
Text: "Evaluations"
Is translated: True

Context: SeasonTimeline
Text: "Competition"
Is translated: True

Context: ChronogramWindow
Text: "Show Months"
Is translated: True

Context: ChronogramWindow
Text: "Show Weeks"
Is translated: True

Context: ChronogramWindow
Text: "Show Intensity Chart"
Is translated: True

Context: ChronogramWindow
Text: "Jan"
Is translated: True

Context: ChronogramWindow
Text: "Feb"
Is translated: True

Context: ChronogramWindow
Text: "Mar"
Is translated: True

Context: ChronogramWindow
Text: "Apr"
Is translated: True

Context: ChronogramWindow
Text: "May"
Is translated: True

Context: ChronogramWindow
Text: "Jun"
Is translated: True

Context: ChronogramWindow
Text: "Jul"
Is translated: True

Context: ChronogramWindow
Text: "Aug"
Is translated: True

Context: ChronogramWindow
Text: "Sep"
Is translated: True

Context: ChronogramWindow
Text: "Oct"
Is translated: True

Context: ChronogramWindow
Text: "Nov"
Is translated: True

Context: ChronogramWindow
Text: "Dec"
Is translated: True

Context: ChronogramWindow
Text: "Season"
Is translated: True

Context: ChronogramWindow
Text: "Mesocycles"
Is translated: True

Context: ChronogramWindow
Text: "Microcycles"
Is translated: True

Context: ChronogramWindow
Text: "Evaluations"
Is translated: True

Context: ChronogramWindow
Text: "Competition"
Is translated: True

Context: Chronogram
Text: "Show Months"
Is translated: True

Context: Chronogram
Text: "Show Weeks"
Is translated: True

Context: Chronogram
Text: "Show Intensity Chart"
Is translated: True

Context: Chronogram
Text: "Jan"
Is translated: True

Context: Chronogram
Text: "Feb"
Is translated: True

Context: Chronogram
Text: "Mar"
Is translated: True

Context: Chronogram
Text: "Apr"
Is translated: True

Context: Chronogram
Text: "May"
Is translated: True

Context: Chronogram
Text: "Jun"
Is translated: True

Context: Chronogram
Text: "Jul"
Is translated: True

Context: Chronogram
Text: "Aug"
Is translated: True

Context: Chronogram
Text: "Sep"
Is translated: True

Context: Chronogram
Text: "Oct"
Is translated: True

Context: Chronogram
Text: "Nov"
Is translated: True

Context: Chronogram
Text: "Dec"
Is translated: True

Context: Chronogram
Text: "Season"
Is translated: True

Context: Chronogram
Text: "Mesocycles"
Is translated: True

Context: Chronogram
Text: "Microcycles"
Is translated: True

Context: Chronogram
Text: "Evaluations"
Is translated: True

Context: Chronogram
Text: "Competition"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Show Months"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Show Weeks"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Show Intensity Chart"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Jan"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Feb"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Mar"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Apr"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "May"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Jun"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Jul"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Aug"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Sep"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Oct"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Nov"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Dec"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Season"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Mesocycles"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Microcycles"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Evaluations"
Is translated: True

Context: Χρονοδιάγραμμα
Text: "Competition"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Show Months"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Show Weeks"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Show Intensity Chart"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Jan"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Feb"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Mar"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Apr"
Is translated: True

Context: χρονοδιάγραμμα
Text: "May"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Jun"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Jul"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Aug"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Sep"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Oct"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Nov"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Dec"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Season"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Mesocycles"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Microcycles"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Evaluations"
Is translated: True

Context: χρονοδιάγραμμα
Text: "Competition"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Show Months"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Show Weeks"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Show Intensity Chart"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Jan"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Feb"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Mar"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Apr"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "May"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Jun"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Jul"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Aug"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Sep"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Oct"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Nov"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Dec"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Season"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Mesocycles"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Microcycles"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Evaluations"
Is translated: True

Context: Χρονοδιάγραμμα Σεζόν
Text: "Competition"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Show Months"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Show Weeks"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Show Intensity Chart"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Jan"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Feb"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Mar"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Apr"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "May"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Jun"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Jul"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Aug"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Sep"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Oct"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Nov"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Dec"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Season"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Mesocycles"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Microcycles"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Evaluations"
Is translated: True

Context: ΧρονοδιάγραμμαΣεζόν
Text: "Competition"
Is translated: True

Context: SeasonChronogram
Text: "Show Months"
Is translated: True

Context: SeasonChronogram
Text: "Show Weeks"
Is translated: True

Context: SeasonChronogram
Text: "Show Intensity Chart"
Is translated: True

Context: SeasonChronogram
Text: "Jan"
Is translated: True

Context: SeasonChronogram
Text: "Feb"
Is translated: True

Context: SeasonChronogram
Text: "Mar"
Is translated: True

Context: SeasonChronogram
Text: "Apr"
Is translated: True

Context: SeasonChronogram
Text: "May"
Is translated: True

Context: SeasonChronogram
Text: "Jun"
Is translated: True

Context: SeasonChronogram
Text: "Jul"
Is translated: True

Context: SeasonChronogram
Text: "Aug"
Is translated: True

Context: SeasonChronogram
Text: "Sep"
Is translated: True

Context: SeasonChronogram
Text: "Oct"
Is translated: True

Context: SeasonChronogram
Text: "Nov"
Is translated: True

Context: SeasonChronogram
Text: "Dec"
Is translated: True

Context: SeasonChronogram
Text: "Season"
Is translated: True

Context: SeasonChronogram
Text: "Mesocycles"
Is translated: True

Context: SeasonChronogram
Text: "Microcycles"
Is translated: True

Context: SeasonChronogram
Text: "Evaluations"
Is translated: True

Context: SeasonChronogram
Text: "Competition"
Is translated: True

Context: XronodiagraamaSezon
Text: "Show Months"
Is translated: True

Context: XronodiagraamaSezon
Text: "Show Weeks"
Is translated: True

Context: XronodiagraamaSezon
Text: "Show Intensity Chart"
Is translated: True

Context: XronodiagraamaSezon
Text: "Jan"
Is translated: True

Context: XronodiagraamaSezon
Text: "Feb"
Is translated: True

Context: XronodiagraamaSezon
Text: "Mar"
Is translated: True

Context: XronodiagraamaSezon
Text: "Apr"
Is translated: True

Context: XronodiagraamaSezon
Text: "May"
Is translated: True

Context: XronodiagraamaSezon
Text: "Jun"
Is translated: True

Context: XronodiagraamaSezon
Text: "Jul"
Is translated: True

Context: XronodiagraamaSezon
Text: "Aug"
Is translated: True

Context: XronodiagraamaSezon
Text: "Sep"
Is translated: True

Context: XronodiagraamaSezon
Text: "Oct"
Is translated: True

Context: XronodiagraamaSezon
Text: "Nov"
Is translated: True

Context: XronodiagraamaSezon
Text: "Dec"
Is translated: True

Context: XronodiagraamaSezon
Text: "Season"
Is translated: True

Context: XronodiagraamaSezon
Text: "Mesocycles"
Is translated: True

Context: XronodiagraamaSezon
Text: "Microcycles"
Is translated: True

Context: XronodiagraamaSezon
Text: "Evaluations"
Is translated: True

Context: XronodiagraamaSezon
Text: "Competition"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Show Months"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Show Weeks"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Show Intensity Chart"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Jan"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Feb"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Mar"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Apr"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "May"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Jun"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Jul"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Aug"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Sep"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Oct"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Nov"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Dec"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Season"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Mesocycles"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Microcycles"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Evaluations"
Is translated: True

Context: ΧρονοδιάγραμμαΑγώνων
Text: "Competition"
Is translated: True

Context: TimelineForm
Text: "Show Months"
Is translated: True

Context: TimelineForm
Text: "Show Weeks"
Is translated: True

Context: TimelineForm
Text: "Show Intensity Chart"
Is translated: True

Context: TimelineForm
Text: "Jan"
Is translated: True

Context: TimelineForm
Text: "Feb"
Is translated: True

Context: TimelineForm
Text: "Mar"
Is translated: True

Context: TimelineForm
Text: "Apr"
Is translated: True

Context: TimelineForm
Text: "May"
Is translated: True

Context: TimelineForm
Text: "Jun"
Is translated: True

Context: TimelineForm
Text: "Jul"
Is translated: True

Context: TimelineForm
Text: "Aug"
Is translated: True

Context: TimelineForm
Text: "Sep"
Is translated: True

Context: TimelineForm
Text: "Oct"
Is translated: True

Context: TimelineForm
Text: "Nov"
Is translated: True

Context: TimelineForm
Text: "Dec"
Is translated: True

Context: TimelineForm
Text: "Season"
Is translated: True

Context: TimelineForm
Text: "Mesocycles"
Is translated: True

Context: TimelineForm
Text: "Microcycles"
Is translated: True

Context: TimelineForm
Text: "Evaluations"
Is translated: True

Context: TimelineForm
Text: "Competition"
Is translated: True

Context: TimelineControl
Text: "Show Months"
Is translated: True

Context: TimelineControl
Text: "Show Weeks"
Is translated: True

Context: TimelineControl
Text: "Show Intensity Chart"
Is translated: True

Context: TimelineControl
Text: "Jan"
Is translated: True

Context: TimelineControl
Text: "Feb"
Is translated: True

Context: TimelineControl
Text: "Mar"
Is translated: True

Context: TimelineControl
Text: "Apr"
Is translated: True

Context: TimelineControl
Text: "May"
Is translated: True

Context: TimelineControl
Text: "Jun"
Is translated: True

Context: TimelineControl
Text: "Jul"
Is translated: True

Context: TimelineControl
Text: "Aug"
Is translated: True

Context: TimelineControl
Text: "Sep"
Is translated: True

Context: TimelineControl
Text: "Oct"
Is translated: True

Context: TimelineControl
Text: "Nov"
Is translated: True

Context: TimelineControl
Text: "Dec"
Is translated: True

Context: TimelineControl
Text: "Season"
Is translated: True

Context: TimelineControl
Text: "Mesocycles"
Is translated: True

Context: TimelineControl
Text: "Microcycles"
Is translated: True

Context: TimelineControl
Text: "Evaluations"
Is translated: True

Context: TimelineControl
Text: "Competition"
Is translated: True

Context: SeasonTimelineForm
Text: "Show Months"
Is translated: True

Context: SeasonTimelineForm
Text: "Show Weeks"
Is translated: True

Context: SeasonTimelineForm
Text: "Show Intensity Chart"
Is translated: True

Context: SeasonTimelineForm
Text: "Jan"
Is translated: True

Context: SeasonTimelineForm
Text: "Feb"
Is translated: True

Context: SeasonTimelineForm
Text: "Mar"
Is translated: True

Context: SeasonTimelineForm
Text: "Apr"
Is translated: True

Context: SeasonTimelineForm
Text: "May"
Is translated: True

Context: SeasonTimelineForm
Text: "Jun"
Is translated: True

Context: SeasonTimelineForm
Text: "Jul"
Is translated: True

Context: SeasonTimelineForm
Text: "Aug"
Is translated: True

Context: SeasonTimelineForm
Text: "Sep"
Is translated: True

Context: SeasonTimelineForm
Text: "Oct"
Is translated: True

Context: SeasonTimelineForm
Text: "Nov"
Is translated: True

Context: SeasonTimelineForm
Text: "Dec"
Is translated: True

Context: SeasonTimelineForm
Text: "Season"
Is translated: True

Context: SeasonTimelineForm
Text: "Mesocycles"
Is translated: True

Context: SeasonTimelineForm
Text: "Microcycles"
Is translated: True

Context: SeasonTimelineForm
Text: "Evaluations"
Is translated: True

Context: SeasonTimelineForm
Text: "Competition"
Is translated: True

Context: SeasonView
Text: "Show Months"
Is translated: True

Context: SeasonView
Text: "Show Weeks"
Is translated: True

Context: SeasonView
Text: "Show Intensity Chart"
Is translated: True

Context: SeasonView
Text: "Jan"
Is translated: True

Context: SeasonView
Text: "Feb"
Is translated: True

Context: SeasonView
Text: "Mar"
Is translated: True

Context: SeasonView
Text: "Apr"
Is translated: True

Context: SeasonView
Text: "May"
Is translated: True

Context: SeasonView
Text: "Jun"
Is translated: True

Context: SeasonView
Text: "Jul"
Is translated: True

Context: SeasonView
Text: "Aug"
Is translated: True

Context: SeasonView
Text: "Sep"
Is translated: True

Context: SeasonView
Text: "Oct"
Is translated: True

Context: SeasonView
Text: "Nov"
Is translated: True

Context: SeasonView
Text: "Dec"
Is translated: True

Context: SeasonView
Text: "Season"
Is translated: True

Context: SeasonView
Text: "Mesocycles"
Is translated: True

Context: SeasonView
Text: "Microcycles"
Is translated: True

Context: SeasonView
Text: "Evaluations"
Is translated: True

Context: SeasonView
Text: "Competition"
Is translated: True

Context: ChronogramForm
Text: "Show Months"
Is translated: True

Context: ChronogramForm
Text: "Show Weeks"
Is translated: True

Context: ChronogramForm
Text: "Show Intensity Chart"
Is translated: True

Context: ChronogramForm
Text: "Jan"
Is translated: True

Context: ChronogramForm
Text: "Feb"
Is translated: True

Context: ChronogramForm
Text: "Mar"
Is translated: True

Context: ChronogramForm
Text: "Apr"
Is translated: True

Context: ChronogramForm
Text: "May"
Is translated: True

Context: ChronogramForm
Text: "Jun"
Is translated: True

Context: ChronogramForm
Text: "Jul"
Is translated: True

Context: ChronogramForm
Text: "Aug"
Is translated: True

Context: ChronogramForm
Text: "Sep"
Is translated: True

Context: ChronogramForm
Text: "Oct"
Is translated: True

Context: ChronogramForm
Text: "Nov"
Is translated: True

Context: ChronogramForm
Text: "Dec"
Is translated: True

Context: ChronogramForm
Text: "Season"
Is translated: True

Context: ChronogramForm
Text: "Mesocycles"
Is translated: True

Context: ChronogramForm
Text: "Microcycles"
Is translated: True

Context: ChronogramForm
Text: "Evaluations"
Is translated: True

Context: ChronogramForm
Text: "Competition"
Is translated: True

Context: QPlatformTheme
Text: "Jan"
Is translated: True

Context: QPlatformTheme
Text: "Feb"
Is translated: True

Context: QPlatformTheme
Text: "Mar"
Is translated: True

Context: QPlatformTheme
Text: "Apr"
Is translated: True

Context: QPlatformTheme
Text: "May"
Is translated: True

Context: QPlatformTheme
Text: "Jun"
Is translated: True

Context: QPlatformTheme
Text: "Jul"
Is translated: True

Context: QPlatformTheme
Text: "Aug"
Is translated: True

Context: QPlatformTheme
Text: "Sep"
Is translated: True

Context: QPlatformTheme
Text: "Oct"
Is translated: True

Context: QPlatformTheme
Text: "Nov"
Is translated: True

Context: QPlatformTheme
Text: "Dec"
Is translated: True
