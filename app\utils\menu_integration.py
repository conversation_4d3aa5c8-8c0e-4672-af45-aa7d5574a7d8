"""
Menu integration utility for adding Football Rules settings to the main application menu.
"""

from PySide6.QtWidgets import QAction
from PySide6.QtCore import QCoreApplication

# Removed football_rules_dialog import - now integrated into settings


def open_settings_on_pages_tab(main_window):
    """
    Open the Settings window and navigate to the Pages tab.

    Args:
        main_window: The main window instance
    """
    try:
        from app.windows.settings_window import SettingsWindow

        # Create and show settings window
        settings_window = SettingsWindow(main_window)

        # Navigate to Pages tab (index 1, assuming General is 0)
        if hasattr(settings_window, 'tab_widget'):
            settings_window.tab_widget.setCurrentIndex(1)  # Pages tab

        settings_window.show()
        settings_window.raise_()
        settings_window.activateWindow()

    except ImportError:
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(
            main_window,
            QCoreApplication.translate("MainWindow", "Settings Unavailable"),
            QCoreApplication.translate("MainWindow", "Could not open settings window.")
        )
    except Exception as e:
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(
            main_window,
            QCoreApplication.translate("MainWindow", "Error"),
            QCoreApplication.translate("MainWindow", f"Error opening settings: {e}")
        )


def add_football_rules_menu_item(main_window, menu_bar):
    """
    Add Football Rules menu item to the main application menu.

    Args:
        main_window: The main window instance
        menu_bar: The menu bar to add the item to
    """
    # Find or create Settings menu
    settings_menu = None
    for action in menu_bar.actions():
        if action.menu() and action.text() == QCoreApplication.translate("MainWindow", "&Settings"):
            settings_menu = action.menu()
            break

    if not settings_menu:
        # Create Settings menu if it doesn't exist
        settings_menu = menu_bar.addMenu(QCoreApplication.translate("MainWindow", "&Settings"))

    # Add Football Rules action that opens Settings window on Pages tab
    football_rules_action = QAction(QCoreApplication.translate("MainWindow", "&Roster Settings..."), main_window)
    football_rules_action.setStatusTip(QCoreApplication.translate("MainWindow", "Configure roster settings including outlier detection"))
    football_rules_action.triggered.connect(lambda: open_settings_on_pages_tab(main_window))

    # Add to menu
    settings_menu.addAction(football_rules_action)

    return football_rules_action


def add_outlier_detection_menu_item(main_window, menu_bar):
    """
    Add Outlier Detection menu item to the main application menu.

    Args:
        main_window: The main window instance
        menu_bar: The menu bar to add the item to
    """
    # Find or create Tools menu
    tools_menu = None
    for action in menu_bar.actions():
        if action.menu() and action.text() == QCoreApplication.translate("MainWindow", "&Tools"):
            tools_menu = action.menu()
            break

    if not tools_menu:
        # Create Tools menu if it doesn't exist
        tools_menu = menu_bar.addMenu(QCoreApplication.translate("MainWindow", "&Tools"))

    # Add Outlier Detection action
    outlier_action = QAction(QCoreApplication.translate("MainWindow", "&Outlier Detection..."), main_window)
    outlier_action.setStatusTip(QCoreApplication.translate("MainWindow", "Configure outlier detection for physical measurements"))
    outlier_action.triggered.connect(lambda: open_settings_on_pages_tab(main_window))

    # Add to menu
    tools_menu.addAction(outlier_action)

    return outlier_action


def create_roster_settings_toolbar_action(main_window):
    """
    Create a toolbar action for Roster settings.

    Args:
        main_window: The main window instance

    Returns:
        QAction for the toolbar
    """
    action = QAction(QCoreApplication.translate("MainWindow", "Roster Settings"), main_window)
    action.setStatusTip(QCoreApplication.translate("MainWindow", "Configure roster settings and outlier detection"))
    action.setToolTip(QCoreApplication.translate("MainWindow", "Roster Settings"))
    action.triggered.connect(lambda: open_settings_on_pages_tab(main_window))

    return action


def integrate_roster_settings(main_window):
    """
    Integrate Roster settings into the main application.

    This function adds menu items and toolbar actions for accessing
    the Roster settings in the Settings window.

    Args:
        main_window: The main window instance
    """
    if not hasattr(main_window, 'menuBar') or not main_window.menuBar():
        return

    menu_bar = main_window.menuBar()

    # Add menu items
    add_football_rules_menu_item(main_window, menu_bar)

    # Optionally add toolbar action if toolbar exists
    if hasattr(main_window, 'addToolBar'):
        toolbar_action = create_roster_settings_toolbar_action(main_window)
        # You can add this to a specific toolbar if needed
        # main_window.some_toolbar.addAction(toolbar_action)


def show_outlier_statistics(main_window):
    """
    Show outlier statistics for the current roster.

    Args:
        main_window: The main window instance
    """
    from PySide6.QtWidgets import QMessageBox
    from app.utils.roster_outlier_integration import get_outlier_statistics_text

    # Get roster manager from main window
    roster_manager = None
    if hasattr(main_window, 'roster_manager'):
        roster_manager = main_window.roster_manager
    elif hasattr(main_window, 'roster_page') and hasattr(main_window.roster_page, 'roster_manager'):
        roster_manager = main_window.roster_page.roster_manager

    if not roster_manager:
        QMessageBox.warning(
            main_window,
            QCoreApplication.translate("MainWindow", "No Roster Data"),
            QCoreApplication.translate("MainWindow", "No roster data available for outlier analysis.")
        )
        return

    # Get statistics text
    stats_text = get_outlier_statistics_text(roster_manager)

    # Show in message box
    msg_box = QMessageBox(main_window)
    msg_box.setWindowTitle(QCoreApplication.translate("MainWindow", "Outlier Detection Statistics"))
    msg_box.setText(stats_text)
    msg_box.setIcon(QMessageBox.Icon.Information)
    msg_box.exec()


def refresh_outlier_detection(main_window):
    """
    Refresh outlier detection in the roster page.

    Args:
        main_window: The main window instance
    """
    # Find roster page and refresh outlier detection
    if hasattr(main_window, 'roster_page') and hasattr(main_window.roster_page, '_apply_outlier_detection'):
        main_window.roster_page._apply_outlier_detection()
    elif hasattr(main_window, 'pages') and 'roster' in main_window.pages:
        roster_page = main_window.pages['roster']
        if hasattr(roster_page, '_apply_outlier_detection'):
            roster_page._apply_outlier_detection()


def add_outlier_tools_menu(main_window, menu_bar):
    """
    Add outlier detection tools to the Tools menu.

    Args:
        main_window: The main window instance
        menu_bar: The menu bar to add items to
    """
    # Find or create Tools menu
    tools_menu = None
    for action in menu_bar.actions():
        if action.menu() and action.text() == QCoreApplication.translate("MainWindow", "&Tools"):
            tools_menu = action.menu()
            break

    if not tools_menu:
        tools_menu = menu_bar.addMenu(QCoreApplication.translate("MainWindow", "&Tools"))

    # Add separator if menu has items
    if tools_menu.actions():
        tools_menu.addSeparator()

    # Add outlier statistics action
    stats_action = QAction(QCoreApplication.translate("MainWindow", "Show &Outlier Statistics"), main_window)
    stats_action.setStatusTip(QCoreApplication.translate("MainWindow", "Show statistics about outlier detection"))
    stats_action.triggered.connect(lambda: show_outlier_statistics(main_window))
    tools_menu.addAction(stats_action)

    # Add refresh outlier detection action
    refresh_action = QAction(QCoreApplication.translate("MainWindow", "&Refresh Outlier Detection"), main_window)
    refresh_action.setStatusTip(QCoreApplication.translate("MainWindow", "Refresh outlier detection highlighting"))
    refresh_action.triggered.connect(lambda: refresh_outlier_detection(main_window))
    tools_menu.addAction(refresh_action)

    return stats_action, refresh_action
