"""
Timeline Completion Widget for completing missing event information.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QLineEdit, QCheckBox, QGroupBox,
    QScrollArea, QFrame, QSizePolicy, QMessageBox, QFormLayout,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt, Signal, QSettings
from PySide6.QtGui import QFont, QPainter, QPen, QBrush, QColor, QFontMetrics

from app.widgets.timeline_completion_dialogs import (
    GoalCompletionDialog, AssistCompletionDialog, CardCompletionDialog
)


@dataclass
class PlayerEventInfo:
    """Information about a player's events that need completion."""
    player_name: str
    event_type: str  # "goal", "assist", "yellow", "red"
    count: int
    completed_minutes: List[int]  # Minutes already set
    missing_count: int  # How many still need minutes
    playing_time: Tuple[int, int]  # (start_minute, end_minute) when player was on pitch

    @property
    def is_complete(self) -> bool:
        return self.missing_count == 0

    @property
    def status_text(self) -> str:
        if self.is_complete:
            return "✓ Complete"
        return f"{self.missing_count} minute(s) needed"


class TimelineCompletionWidget(QWidget):
    """Widget for completing missing timeline event information."""

    # Signals
    timeline_completed = Signal()  # All events have timing information
    event_completed = Signal(str, str, int, int)  # player_name, event_type, count, minute

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.timeline_completion")

        # Data
        self.player_events: List[PlayerEventInfo] = []
        self.completed_events: Dict[str, List[int]] = {}  # player_event_key -> [minutes]
        self.goal_minutes: List[int] = []  # Available goal minutes for assists

        self._init_ui()

    def _init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # Title and status
        title_layout = self._create_title_section()
        main_layout.addWidget(title_layout)

        # Completion table
        completion_table = self._create_completion_table()
        main_layout.addWidget(completion_table)

        # Action buttons
        actions_layout = self._create_actions_section()
        main_layout.addWidget(actions_layout)

        # Visual timeline preview (placeholder for now)
        timeline_preview = self._create_timeline_preview()
        main_layout.addWidget(timeline_preview)

    def _create_title_section(self) -> QWidget:
        """Create the title and status section."""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # Title
        title_label = QLabel("Complete Your Timeline")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)

        # Status
        self.status_label = QLabel("Loading events...")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")

        layout.addWidget(title_label)
        layout.addStretch()
        layout.addWidget(self.status_label)

        return widget

    def _create_completion_table(self) -> QWidget:
        """Create the completion table showing missing information."""
        group = QGroupBox("Missing Information")
        layout = QVBoxLayout(group)

        # Table
        self.completion_table = QTableWidget()
        self.completion_table.setColumnCount(5)
        self.completion_table.setHorizontalHeaderLabels([
            "Player", "Event Type", "Count", "Status", "Action"
        ])

        # Table settings
        self.completion_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.completion_table.setAlternatingRowColors(True)
        self.completion_table.verticalHeader().setVisible(False)

        # Column widths
        header = self.completion_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Player
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Event Type
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Count
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # Status
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Action

        layout.addWidget(self.completion_table)
        return group

    def _create_actions_section(self) -> QWidget:
        """Create the action buttons section."""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # Quick complete all button
        self.quick_complete_btn = QPushButton("Quick Complete All")
        self.quick_complete_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.quick_complete_btn.clicked.connect(self._quick_complete_all)

        # Refresh from stats button
        self.refresh_btn = QPushButton("Refresh from Player Stats")
        self.refresh_btn.clicked.connect(self._refresh_from_stats)

        # Clear all button
        self.clear_btn = QPushButton("Clear All Timings")
        self.clear_btn.setStyleSheet("color: #F44336;")
        self.clear_btn.clicked.connect(self._clear_all_timings)

        layout.addWidget(self.quick_complete_btn)
        layout.addWidget(self.refresh_btn)
        layout.addStretch()
        layout.addWidget(self.clear_btn)

        return widget

    def _create_timeline_preview(self) -> QWidget:
        """Create the timeline preview section."""
        group = QGroupBox("Timeline Preview")
        layout = QVBoxLayout(group)

        # Preview label (will be replaced with actual timeline later)
        self.timeline_preview_label = QLabel("Timeline preview will appear here")
        self.timeline_preview_label.setMinimumHeight(80)
        self.timeline_preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ccc;
                background-color: #f9f9f9;
                text-align: center;
                color: #666;
                font-style: italic;
                border-radius: 4px;
            }
        """)
        self.timeline_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(self.timeline_preview_label)
        return group

    def set_player_events(self, events: List[PlayerEventInfo]):
        """Set the player events that need completion."""
        self.player_events = events
        self._refresh_completion_table()
        self._update_status()

    def _refresh_completion_table(self):
        """Refresh the completion table with current event data."""
        # Clear existing rows
        self.completion_table.setRowCount(0)

        # Add rows for incomplete events
        incomplete_events = [event for event in self.player_events if not event.is_complete]

        self.completion_table.setRowCount(len(incomplete_events))

        for row, event in enumerate(incomplete_events):
            # Player name
            player_item = QTableWidgetItem(event.player_name)
            player_item.setFlags(player_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.completion_table.setItem(row, 0, player_item)

            # Event type
            event_type_text = event.event_type.replace('_', ' ').title()
            if event.count > 1:
                event_type_text += f" ({event.count})"
            event_type_item = QTableWidgetItem(event_type_text)
            event_type_item.setFlags(event_type_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.completion_table.setItem(row, 1, event_type_item)

            # Count
            count_item = QTableWidgetItem(str(event.count))
            count_item.setFlags(count_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.completion_table.setItem(row, 2, count_item)

            # Status
            status_item = QTableWidgetItem(event.status_text)
            status_item.setFlags(status_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            if event.is_complete:
                status_item.setForeground(QColor("#4CAF50"))
            else:
                status_item.setForeground(QColor("#F44336"))
            self.completion_table.setItem(row, 3, status_item)

            # Action button
            action_btn = QPushButton("Set Times" if event.missing_count > 1 else "Set Time")
            action_btn.clicked.connect(lambda checked, e=event: self._complete_event(e))
            self.completion_table.setCellWidget(row, 4, action_btn)

    def _update_status(self):
        """Update the status label."""
        total_events = len(self.player_events)
        incomplete_events = len([e for e in self.player_events if not e.is_complete])

        if incomplete_events == 0:
            self.status_label.setText("✓ All events completed!")
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            self.quick_complete_btn.setEnabled(False)
        else:
            self.status_label.setText(f"{incomplete_events} of {total_events} events need timing")
            self.status_label.setStyleSheet("color: #F44336; font-style: italic;")
            self.quick_complete_btn.setEnabled(True)

    def _complete_event(self, event: PlayerEventInfo):
        """Complete timing for a specific event."""
        self.logger.info(f"Completing event: {event.player_name} - {event.event_type}")

        if event.event_type == "goal":
            self._complete_goals(event)
        elif event.event_type == "assist":
            self._complete_assists(event)
        elif event.event_type in ["yellow", "red"]:
            self._complete_cards(event)

    def _complete_goals(self, event: PlayerEventInfo):
        """Complete goal timing for a player."""
        dialog = GoalCompletionDialog(
            event.player_name,
            event.missing_count,
            event.playing_time,
            self
        )

        dialog.goals_completed.connect(lambda minutes: self._on_goals_completed(event, minutes))
        dialog.exec()

    def _complete_assists(self, event: PlayerEventInfo):
        """Complete assist timing for a player."""
        dialog = AssistCompletionDialog(
            event.player_name,
            event.missing_count,
            self.goal_minutes,
            self
        )

        dialog.assists_completed.connect(lambda minutes: self._on_assists_completed(event, minutes))
        dialog.exec()

    def _complete_cards(self, event: PlayerEventInfo):
        """Complete card timing for a player."""
        dialog = CardCompletionDialog(
            event.player_name,
            event.event_type,
            event.missing_count,
            self
        )

        dialog.cards_completed.connect(lambda minutes: self._on_cards_completed(event, minutes))
        dialog.exec()

    def _on_goals_completed(self, event: PlayerEventInfo, minutes: List[int]):
        """Handle completion of goal timing."""
        self.logger.info(f"Goals completed for {event.player_name}: {minutes}")

        # Update event data
        event.completed_minutes.extend(minutes)
        event.missing_count = max(0, event.count - len(event.completed_minutes))

        # Add to goal minutes for assists
        self.goal_minutes.extend(minutes)
        self.goal_minutes = sorted(list(set(self.goal_minutes)))

        # Emit completion signals
        for minute in minutes:
            self.event_completed.emit(event.player_name, event.event_type, 1, minute)

        # Refresh display
        self._refresh_completion_table()
        self._update_status()

    def _on_assists_completed(self, event: PlayerEventInfo, minutes: List[int]):
        """Handle completion of assist timing."""
        self.logger.info(f"Assists completed for {event.player_name}: {minutes}")

        # Update event data
        event.completed_minutes.extend(minutes)
        event.missing_count = max(0, event.count - len(event.completed_minutes))

        # Emit completion signals
        for minute in minutes:
            self.event_completed.emit(event.player_name, "assist", 1, minute)

        # Refresh display
        self._refresh_completion_table()
        self._update_status()

    def _on_cards_completed(self, event: PlayerEventInfo, minutes: List[int]):
        """Handle completion of card timing."""
        self.logger.info(f"{event.event_type.title()} cards completed for {event.player_name}: {minutes}")

        # Update event data
        event.completed_minutes.extend(minutes)
        event.missing_count = max(0, event.count - len(event.completed_minutes))

        # Emit completion signals
        for minute in minutes:
            self.event_completed.emit(event.player_name, event.event_type, 1, minute)

            # If this is a second yellow card, also create a red card
            if event.event_type == "yellow" and len(event.completed_minutes) == 2:
                self.event_completed.emit(event.player_name, "red", 1, minute)

        # Refresh display
        self._refresh_completion_table()
        self._update_status()

    def _quick_complete_all(self):
        """Quick complete all missing events using wizard."""
        incomplete_events = [e for e in self.player_events if not e.is_complete]

        if not incomplete_events:
            QMessageBox.information(self, "Complete", "All events are already completed!")
            return

        # For now, show summary - will implement wizard later
        event_summary = "\n".join([
            f"• {e.player_name}: {e.missing_count} {e.event_type}(s)"
            for e in incomplete_events
        ])

        QMessageBox.information(
            self,
            "Quick Complete All",
            f"Will complete these events:\n\n{event_summary}\n\n"
            f"This will be a step-by-step wizard in the next step."
        )

    def _refresh_from_stats(self):
        """Refresh events from player stats."""
        # This will be connected to the parent to reload data
        self.logger.info("Refreshing from player stats")

    def _clear_all_timings(self):
        """Clear all completed timings."""
        reply = QMessageBox.question(
            self,
            "Clear All Timings",
            "Are you sure you want to clear all completed event timings?\n"
            "This will reset all events to incomplete status.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.completed_events.clear()
            # Reset all events to incomplete
            for event in self.player_events:
                event.completed_minutes.clear()
                event.missing_count = event.count
            self._refresh_completion_table()
            self._update_status()

    def get_completion_status(self) -> Dict[str, Any]:
        """Get the current completion status."""
        total_events = len(self.player_events)
        completed_events = len([e for e in self.player_events if e.is_complete])

        return {
            "total_events": total_events,
            "completed_events": completed_events,
            "completion_percentage": (completed_events / total_events * 100) if total_events > 0 else 100,
            "is_complete": completed_events == total_events
        }

    def set_goal_minutes(self, minutes: List[int]):
        """Set available goal minutes for assist completion."""
        self.goal_minutes = sorted(minutes)

    def add_test_data(self):
        """Add test data for development."""
        test_events = [
            PlayerEventInfo("Smith", "goal", 2, [], 2, (0, 90)),
            PlayerEventInfo("Jones", "assist", 1, [], 1, (0, 67)),
            PlayerEventInfo("Brown", "yellow", 1, [], 1, (0, 90)),
            PlayerEventInfo("Wilson", "sub_out", 1, [67], 0, (0, 67)),  # Complete
            PlayerEventInfo("Davis", "sub_in", 1, [67], 0, (67, 90)),  # Complete
        ]
        self.set_player_events(test_events)
        self.set_goal_minutes([23, 78])  # Test goal minutes for assists
