"""
Attendance Trends Chart Dialog for FootData application.

This module provides a dialog for visualizing attendance trends over time.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

from PySide6.QtWidgets import (
    Q<PERSON>oxLayout, Q<PERSON><PERSON><PERSON>Layout, QLabel, QComboBox, QCheckBox, QDateEdit, QSpinBox
)
from PySide6.QtCore import Qt, QDate
from app.utils.locale_utils import apply_locale_to_date_edit
from PySide6.QtGui import QColor

from app.dialogs.attendance_chart_base_dialog import AttendanceChartBaseDialog
from app.utils.tooltip_helper import set_tooltip

class AttendanceTrendsChartDialog(AttendanceChartBaseDialog):
    """Dialog for visualizing attendance trends over time."""

    def __init__(self, parent=None, attendance_manager=None, roster_manager=None,
                 season_start_date=None, season_end_date=None):
        """Initialize the dialog.

        Args:
            parent (QWidget, optional): Parent widget. Defaults to None.
            attendance_manager (AttendanceManager, optional): Attendance manager instance.
            roster_manager (RosterManager, optional): Roster manager instance.
            season_start_date (QDate, optional): Season start date.
            season_end_date (QDate, optional): Season end date.
        """
        # Initialize chart settings before calling parent constructor
        self.chart_type = "line"  # line, area, bar
        self.show_percentage = True
        self.selected_status = []  # Will be initialized after parent constructor
        self.time_interval = "week"  # day, week, month
        self.moving_average = 0  # 0 means no moving average

        super().__init__(
            parent=parent,
            attendance_manager=attendance_manager,
            roster_manager=roster_manager,
            season_start_date=season_start_date,
            season_end_date=season_end_date,
            title="Attendance Trends"
        )

        # Initialize status list after parent constructor (to access status_text)
        self.selected_status = list(self.status_text.keys())

    def _create_filter_controls(self, layout):
        """Create filter controls.

        Args:
            layout (QLayout): The layout to add controls to.
        """
        # Date range selection
        date_layout = QHBoxLayout()

        start_label = QLabel(self.tr("Start Date:"))
        self.start_date_edit = QDateEdit()
        apply_locale_to_date_edit(self.start_date_edit)
        self.start_date_edit.setDate(self.season_start_date)
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setMinimumDate(self.season_start_date)
        self.start_date_edit.setMaximumDate(self.season_end_date)

        end_label = QLabel(self.tr("End Date:"))
        self.end_date_edit = QDateEdit()
        apply_locale_to_date_edit(self.end_date_edit)
        self.end_date_edit.setDate(self.season_end_date)
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setMinimumDate(self.season_start_date)
        self.end_date_edit.setMaximumDate(self.season_end_date)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.start_date_edit)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.end_date_edit)

        layout.addLayout(date_layout)

        # Status selection
        status_layout = QVBoxLayout()
        status_label = QLabel(self.tr("Status Types:"))
        status_layout.addWidget(status_label)

        self.status_checkboxes = {}
        for status, text in self.status_text.items():
            checkbox = QCheckBox(text)
            checkbox.setChecked(True)
            checkbox.stateChanged.connect(self._on_status_changed)
            self.status_checkboxes[status] = checkbox
            status_layout.addWidget(checkbox)

        layout.addLayout(status_layout)

        # Time interval selection
        interval_layout = QHBoxLayout()
        interval_label = QLabel(self.tr("Time Interval:"))
        self.interval_combo = QComboBox()
        self.interval_combo.addItems([
            self.tr("Daily"),
            self.tr("Weekly"),
            self.tr("Monthly")
        ])
        self.interval_combo.setCurrentIndex(1)  # Default to Weekly
        self.interval_combo.currentIndexChanged.connect(self._on_interval_changed)

        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(self.interval_combo)

        layout.addLayout(interval_layout)

    def _create_chart_options(self, layout):
        """Create chart options controls.

        Args:
            layout (QLayout): The layout to add controls to.
        """
        # Chart type selection
        type_layout = QHBoxLayout()
        type_label = QLabel(self.tr("Chart Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            self.tr("Line Chart"),
            self.tr("Area Chart"),
            self.tr("Bar Chart")
        ])
        self.chart_type_combo.currentIndexChanged.connect(self._on_chart_type_changed)

        type_layout.addWidget(type_label)
        type_layout.addWidget(self.chart_type_combo)

        layout.addLayout(type_layout)

        # Show percentage option
        self.percentage_checkbox = QCheckBox(self.tr("Show as Percentage"))
        self.percentage_checkbox.setChecked(self.show_percentage)
        self.percentage_checkbox.stateChanged.connect(self._on_percentage_changed)
        set_tooltip(self.percentage_checkbox, self.tr("Display values as percentages instead of absolute counts"))

        layout.addWidget(self.percentage_checkbox)

        # Moving average option
        ma_layout = QHBoxLayout()
        ma_label = QLabel(self.tr("Moving Average:"))
        self.ma_spinbox = QSpinBox()
        self.ma_spinbox.setRange(0, 30)
        self.ma_spinbox.setValue(self.moving_average)
        self.ma_spinbox.setSuffix(self.tr(" periods"))
        self.ma_spinbox.valueChanged.connect(self._on_ma_changed)
        set_tooltip(self.ma_spinbox, self.tr("Apply moving average smoothing (0 = disabled)"))

        ma_layout.addWidget(ma_label)
        ma_layout.addWidget(self.ma_spinbox)

        layout.addLayout(ma_layout)

    def _on_chart_type_changed(self, index):
        """Handle chart type change.

        Args:
            index (int): The selected index.
        """
        chart_types = ["line", "area", "bar"]
        self.chart_type = chart_types[index]
        # Update the chart immediately when the chart type changes
        self._update_chart()

    def _on_percentage_changed(self, state):
        """Handle percentage checkbox change.

        Args:
            state (int): The checkbox state.
        """
        self.show_percentage = (state == Qt.CheckState.Checked)
        # Update the chart immediately when the percentage option changes
        self._update_chart()

    def _on_status_changed(self, state):
        """Handle status checkbox change.

        Args:
            state (int): The checkbox state.
        """
        # Update selected status list
        self.selected_status = [
            status for status, checkbox in self.status_checkboxes.items()
            if checkbox.isChecked()
        ]
        # Update the chart immediately when the status selection changes
        self._update_chart()

    def _on_interval_changed(self, index):
        """Handle interval combo change.

        Args:
            index (int): The selected index.
        """
        intervals = ["day", "week", "month"]
        self.time_interval = intervals[index]
        # Update the chart immediately when the interval changes
        self._update_chart()

    def _on_ma_changed(self, value):
        """Handle moving average spinbox change.

        Args:
            value (int): The spinbox value.
        """
        self.moving_average = value
        # Update the chart immediately when the moving average value changes
        self._update_chart()

    def _update_chart(self):
        """Update the chart with current data and settings."""
        # Clear the figure
        self.figure.clear()

        # Create a new subplot
        self.ax = self.figure.add_subplot(111)

        # Get date range
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # Ensure settings match UI controls
        self.show_percentage = self.percentage_checkbox.isChecked()
        self.selected_status = [
            status for status, checkbox in self.status_checkboxes.items()
            if checkbox.isChecked()
        ]
        intervals = ["day", "week", "month"]
        self.time_interval = intervals[self.interval_combo.currentIndex()]
        self.moving_average = self.ma_spinbox.value()

        print(f"Trends Chart update - show_percentage: {self.show_percentage}")
        print(f"Selected status: {self.selected_status}, interval: {self.time_interval}")
        print(f"Moving average: {self.moving_average}")

        # Get attendance data
        trends_data = self._get_attendance_trends_data(start_date, end_date)

        if trends_data is None or trends_data.empty:
            self.ax.set_title(self.tr("No Data Available"))
            self.canvas.draw()
            return

        # Create the chart based on type
        if self.chart_type == "line":
            self._create_line_chart(trends_data)
        elif self.chart_type == "area":
            self._create_area_chart(trends_data)
        elif self.chart_type == "bar":
            self._create_bar_chart(trends_data)

        # Set title and labels
        interval_text = {
            "day": self.tr("Daily"),
            "week": self.tr("Weekly"),
            "month": self.tr("Monthly")
        }[self.time_interval]

        self.ax.set_title(self.tr("{} Attendance Trends").format(interval_text))
        self.ax.set_xlabel(self.tr("Date"))

        if self.show_percentage:
            self.ax.set_ylabel(self.tr("Percentage (%)"))
        else:
            self.ax.set_ylabel(self.tr("Count"))

        # Add legend
        self.ax.legend()

        # Rotate x-axis labels for better readability
        plt.setp(self.ax.get_xticklabels(), rotation=45, ha="right")

        # Adjust layout
        self.figure.tight_layout()

        # Draw the canvas
        self.canvas.draw()

    def _get_attendance_trends_data(self, start_date, end_date):
        """Get attendance trends data.

        Args:
            start_date (str): Start date in YYYY-MM-DD format.
            end_date (str): End date in YYYY-MM-DD format.

        Returns:
            DataFrame: Attendance trends data.
        """
        if not self.attendance_manager:
            return None

        # Get attendance records
        records = self.attendance_manager.get_attendance(
            start_date=start_date,
            end_date=end_date
        )

        if not records:
            return None

        # Convert to DataFrame
        df = pd.DataFrame(records)

        # Filter by selected status
        df = df[df['status'].isin(self.selected_status)]

        if df.empty:
            return None

        # Convert date to datetime
        df['date'] = pd.to_datetime(df['date'])

        # Group by time interval and status
        if self.time_interval == "day":
            # Daily grouping
            df['period'] = df['date'].dt.date
        elif self.time_interval == "week":
            # Weekly grouping
            df['period'] = df['date'] - pd.to_timedelta(df['date'].dt.dayofweek, unit='d')
            df['period'] = df['period'].dt.date
        else:  # month
            # Monthly grouping
            df['period'] = df['date'].dt.strftime('%Y-%m')

        # Group by period and status
        trends_data = df.groupby(['period', 'status']).size().unstack(fill_value=0)

        # Apply moving average if needed
        if self.moving_average > 0:
            trends_data = trends_data.rolling(window=self.moving_average, min_periods=1).mean()

        # Convert to percentage if needed
        if self.show_percentage:
            print(f"Converting trends data to percentage. Current data shape: {trends_data.shape}")
            row_sums = trends_data.sum(axis=1)
            # Make sure we have data before attempting division
            if not trends_data.empty and (row_sums > 0).all():
                trends_data = trends_data.div(row_sums, axis=0) * 100
                print("Data converted to percentage successfully")
            else:
                print("Warning: Cannot convert to percentage - empty data or zero sum row")

        return trends_data

    def _create_line_chart(self, data):
        """Create a line chart.

        Args:
            data (DataFrame): Attendance trends data.
        """
        # Plot each status as a separate line
        for status in data.columns:
            if status not in self.status_colors:
                continue

            color = self._get_matplotlib_color(self.status_colors[status])
            self.ax.plot(
                data.index,
                data[status],
                marker='o',
                label=self.status_text.get(status, status),
                color=color
            )

    def _create_area_chart(self, data):
        """Create an area chart.

        Args:
            data (DataFrame): Attendance trends data.
        """
        # Plot each status as a stacked area
        for status in data.columns:
            if status not in self.status_colors:
                continue

            color = self._get_matplotlib_color(self.status_colors[status])
            self.ax.fill_between(
                data.index,
                data[status],
                label=self.status_text.get(status, status),
                color=color,
                alpha=0.7
            )

    def _create_bar_chart(self, data):
        """Create a bar chart.

        Args:
            data (DataFrame): Attendance trends data.
        """
        # Plot each status as a separate bar
        bar_width = 0.8 / len(data.columns)
        x = np.arange(len(data.index))

        for i, status in enumerate(data.columns):
            if status not in self.status_colors:
                continue

            color = self._get_matplotlib_color(self.status_colors[status])
            self.ax.bar(
                x + i * bar_width - 0.4 + bar_width/2,
                data[status],
                width=bar_width,
                label=self.status_text.get(status, status),
                color=color
            )

        # Set x-axis labels
        self.ax.set_xticks(x)
        self.ax.set_xticklabels([str(idx) for idx in data.index])
