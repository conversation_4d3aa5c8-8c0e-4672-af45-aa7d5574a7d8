"""
Tooltip Manager for FootData application.

This module provides a centralized way to manage tooltips throughout the application,
including support for translations, customization of appearance, and enabling/disabling.
"""

import logging
from PySide6.QtCore import QObject, QSettings, QCoreApplication, QEvent, QTimer, QPoint, Qt, QTranslator
from PySide6.QtWidgets import QApplication, QWidget, QToolTip, QLabel
from PySide6.QtGui import QCursor

class CustomTooltip(QLabel):
    """Custom tooltip widget with controlled duration."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.ToolTip)
        self.setStyleSheet("""
            background-color: #FFFFDC;
            color: black;
            border: 1px solid #999999;
            border-radius: 3px;
            padding: 3px;
        """)
        self.timer = QTimer(self)
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self.hide)

    def showText(self, pos, text, duration):
        """Show tooltip at position with specified duration."""
        self.setText(text)
        self.adjustSize()
        self.move(pos)
        self.show()
        self.timer.start(duration)


class TooltipManager(QObject):
    """
    Manages tooltips throughout the application.

    Features:
    - Enable/disable tooltips globally
    - Set tooltip duration
    - Support for translations
    - Custom tooltip implementation for reliable duration control
    """

    # Default values
    DEFAULT_ENABLED = True
    DEFAULT_DURATION = 4000  # 4 seconds

    def __init__(self, parent=None):
        """Initialize the tooltip manager."""
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.settings = QSettings()

        # Load settings
        self.enabled = self.settings.value("general/tooltip_enabled", self.DEFAULT_ENABLED, type=bool)
        self.duration = self.settings.value("general/tooltip_duration", self.DEFAULT_DURATION, type=int)

        # Create custom tooltip widget
        self.custom_tooltip = CustomTooltip()
        self.active_widget = None
        self.hover_timer = QTimer(self)
        self.hover_timer.setSingleShot(True)
        self.hover_timer.timeout.connect(self._show_tooltip)

        # Keep track of widgets with tooltips for refreshing
        self.tooltip_widgets = {}  # {widget: original_text}

        # Apply settings
        self.apply_settings()

        # Install event filter on the application to intercept tooltip events
        app = QApplication.instance()
        if app:
            app.installEventFilter(self)
            self.logger.info("Installed tooltip event filter")

        self.logger.info("TooltipManager initialized")

    def eventFilter(self, obj, event):
        """Filter events to handle tooltips with custom duration."""
        if event.type() == QEvent.Type.ToolTip:
            self.logger.debug(f"ToolTip event detected for object: {obj}")

            # Check if tooltips are enabled both in our manager and in settings
            settings = QSettings()
            settings_enabled = settings.value("general/tooltip_enabled", True, type=bool)

            if not self.enabled or not settings_enabled:
                # Block all tooltip events when tooltips are disabled
                self.logger.debug(f"Tooltips are disabled (manager={self.enabled}, settings={settings_enabled}), blocking tooltip event")
                return True

            # Get the widget that triggered the tooltip
            widget = QApplication.instance().widgetAt(QCursor.pos())
            self.logger.debug(f"Widget at cursor position: {widget}")

            if widget and widget.toolTip():
                # Store the widget for later use
                self.active_widget = widget
                self.logger.debug(f"Widget has tooltip: '{widget.toolTip()}'")
                # Start a short timer to show the tooltip (prevents flickering)
                self.hover_timer.start(200)
                # Block the default tooltip
                return True
            else:
                if widget:
                    self.logger.debug(f"Widget has no tooltip text")
                else:
                    self.logger.debug(f"No widget found at cursor position")

        elif event.type() == QEvent.Type.Leave:
            # Hide tooltip when mouse leaves the widget
            if self.custom_tooltip.isVisible():
                self.logger.debug(f"Hiding tooltip due to Leave event for: {obj}")
                self.custom_tooltip.hide()

        # Also hide tooltip on mouse press
        elif event.type() == QEvent.Type.MouseButtonPress:
            if self.custom_tooltip.isVisible():
                self.logger.debug("Hiding tooltip due to MouseButtonPress event")
                self.custom_tooltip.hide()

        # Let other events pass through
        return super().eventFilter(obj, event)

    def _show_tooltip(self):
        """Show the custom tooltip for the active widget."""
        self.logger.debug("_show_tooltip called")

        # Check if tooltips are enabled both in our manager and in settings
        settings = QSettings()
        settings_enabled = settings.value("general/tooltip_enabled", True, type=bool)

        if not self.enabled or not settings_enabled:
            self.logger.debug(f"Tooltips are disabled (manager={self.enabled}, settings={settings_enabled}), not showing tooltip")
            return

        if not self.active_widget:
            self.logger.debug("No active widget, cannot show tooltip")
            return

        # Get the tooltip text
        try:
            text = self.active_widget.toolTip()
            if not text:
                self.logger.debug("Active widget has no tooltip text")
                return
        except RuntimeError:
            self.logger.debug("Active widget was destroyed, cannot show tooltip")
            self.active_widget = None
            return

        self.logger.debug(f"Preparing to show tooltip for widget: {self.active_widget}")

        # Calculate position (below the cursor)
        pos = QCursor.pos()
        pos.setY(pos.y() + 20)
        self.logger.debug(f"Tooltip position: {pos.x()}, {pos.y()}")

        # Show the tooltip with the configured duration
        try:
            self.custom_tooltip.showText(pos, text, self.duration)
            self.logger.debug(f"Showing tooltip with duration {self.duration}ms: '{text}'")
        except Exception as e:
            self.logger.error(f"Error showing tooltip: {e}")
            # Try to recover by hiding the tooltip
            try:
                self.custom_tooltip.hide()
            except:
                pass

    def apply_settings(self):
        """Apply tooltip settings to the application."""
        app = QApplication.instance()
        if not app:
            return

        # Update custom tooltip style based on settings
        if self.enabled:
            # Set a nice style for the custom tooltip
            self.custom_tooltip.setStyleSheet("""
                background-color: #FFFFDC;
                color: black;
                border: 1px solid #999999;
                border-radius: 3px;
                padding: 3px;
            """)
            self.logger.info(f"Applied tooltip settings: enabled={self.enabled}, duration={self.duration}ms")
        else:
            # Hide the custom tooltip if it's visible
            if self.custom_tooltip.isVisible():
                self.custom_tooltip.hide()
            self.logger.info("Tooltips disabled")

    def set_enabled(self, enabled):
        """Enable or disable tooltips globally."""
        self.enabled = enabled
        self.settings.setValue("general/tooltip_enabled", enabled)
        self.logger.info(f"Tooltips {'enabled' if enabled else 'disabled'}")

        # Hide the tooltip if it's currently visible and we're disabling
        if not enabled and self.custom_tooltip.isVisible():
            self.custom_tooltip.hide()

        # Apply settings to update the UI
        self.apply_settings()

    def set_duration(self, duration_ms):
        """Set tooltip duration in milliseconds."""
        self.duration = duration_ms
        self.settings.setValue("general/tooltip_duration", duration_ms)
        self.logger.info(f"Tooltip duration set to {duration_ms}ms")

        # If a tooltip is currently visible, update its duration
        if self.custom_tooltip.isVisible():
            # Hide and re-show with new duration
            text = self.custom_tooltip.text()
            pos = self.custom_tooltip.pos()
            self.custom_tooltip.hide()
            self.custom_tooltip.showText(pos, text, duration_ms)

        self.apply_settings()

    def set_tooltip(self, widget, text):
        """
        Set tooltip for a widget with translation support.

        Args:
            widget: The widget to set the tooltip for
            text: The tooltip text (will be translated)
        """
        # Check if tooltips are enabled
        settings = QSettings()
        tooltip_enabled = settings.value("general/tooltip_enabled", True, type=bool)

        if not tooltip_enabled:
            self.logger.debug(f"Tooltips are disabled, not setting tooltip for: '{text}'")
            try:
                widget.setToolTip("")  # Clear any existing tooltip
            except RuntimeError:
                pass  # Widget might be destroyed
            return

        # Check if widget is valid
        if not widget:
            self.logger.warning(f"Cannot set tooltip for None widget: '{text}'")
            return

        try:
            # Store the original text for refreshing later
            self.tooltip_widgets[widget] = text

            # Get current language for debugging
            current_language = settings.value("general/language", "en")

            # For English, just use the original text
            if current_language == "en":
                translated_text = text
                self.logger.debug(f"Using original English text: '{translated_text}'")
            else:
                # For other languages, try direct translation dictionaries first
                from app.utils.tooltip_helper import get_direct_translation
                direct_dict_translation = get_direct_translation(text, current_language)

                if direct_dict_translation != text:
                    # Direct dictionary translation worked
                    translated_text = direct_dict_translation
                    self.logger.debug(f"Using direct dictionary translation: '{translated_text}'")
                else:
                    # Check if we have a direct translation stored in the widget's properties
                    direct_translation = widget.property("direct_translation")
                    if direct_translation:
                        translated_text = direct_translation
                        self.logger.debug(f"Using direct translation property: '{translated_text}'")
                    else:
                        # Try QCoreApplication.translate
                        translated_text = QCoreApplication.translate("Tooltips", text)
                        self.logger.debug(f"Qt translation for '{text}': '{translated_text}'")

                        # If translation failed, try loading translation files manually
                        if translated_text == text:
                            self.logger.debug(f"Translation may have failed! Text is the same in language '{current_language}'")

                            # Try to manually load and test the translation
                            test_translator = QTranslator()
                            import os

                            # Try different tooltip translation files
                            tooltip_files_to_try = [
                                f"tooltips_{current_language}.qm",
                                f"tooltips_{current_language}_correct.qm",
                                f"roster_tooltips_{current_language}.qm"
                            ]

                            translations_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                                          "translations")

                            for tooltip_file in tooltip_files_to_try:
                                tooltip_qm_file_path = os.path.join(translations_dir, tooltip_file)
                                self.logger.debug(f"Testing manual translation load from: {tooltip_qm_file_path}")

                                if os.path.exists(tooltip_qm_file_path):
                                    if test_translator.load(tooltip_qm_file_path):
                                        self.logger.debug(f"Successfully loaded test translator from {tooltip_qm_file_path}")
                                        test_translation = test_translator.translate("Tooltips", text)
                                        self.logger.debug(f"Test translation: '{text}' -> '{test_translation}'")
                                        if test_translation != text:
                                            translated_text = test_translation
                                            self.logger.debug(f"Using test translation: '{translated_text}'")
                                            break  # Found a working translation, stop trying
                                    else:
                                        self.logger.debug(f"Failed to load test translator from {tooltip_qm_file_path}")
                                else:
                                    self.logger.debug(f"Test translator file not found: {tooltip_qm_file_path}")

            # Set the tooltip on the widget (our event filter will handle showing it)
            widget.setToolTip(translated_text)

            # Store the translated text as a property for future reference
            widget.setProperty("direct_translation", translated_text)

        except RuntimeError as e:
            self.logger.error(f"Error setting tooltip: {e}")
            # Widget might have been destroyed during the operation
        except Exception as e:
            self.logger.error(f"Unexpected error setting tooltip: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def refresh_tooltips(self):
        """Refresh all tooltips with the current language."""
        self.logger.info(f"Refreshing tooltips for {len(self.tooltip_widgets)} widgets")

        # Check if tooltips are enabled
        settings = QSettings()
        tooltip_enabled = settings.value("general/tooltip_enabled", True, type=bool)

        if not tooltip_enabled:
            self.logger.info("Tooltips are disabled, clearing all tooltips")
            # Clear all tooltips
            widgets_to_clear = list(self.tooltip_widgets.keys())
            for widget in widgets_to_clear:
                try:
                    if widget:
                        widget.setToolTip("")
                except RuntimeError:
                    pass  # Widget might be destroyed
            return

        # Get current language
        current_language = settings.value("general/language", "en")
        self.logger.info(f"Refreshing tooltips for language: {current_language}")

        # Make a copy of the dictionary items to avoid modification during iteration
        widgets_to_refresh = list(self.tooltip_widgets.items())
        widgets_to_remove = []

        # Count successful refreshes for logging
        success_count = 0
        translation_count = 0

        for widget, original_text in widgets_to_refresh:
            try:
                if widget:
                    self.logger.debug(f"Refreshing tooltip for widget: '{original_text}'")

                    # Get the current tooltip text before refresh
                    current_tooltip = widget.toolTip()

                    # Re-apply the tooltip with the current language
                    self.set_tooltip(widget, original_text)

                    # Check if translation changed
                    new_tooltip = widget.toolTip()
                    if new_tooltip != current_tooltip and new_tooltip != original_text:
                        translation_count += 1
                        self.logger.debug(f"Translation changed: '{current_tooltip}' -> '{new_tooltip}'")

                    success_count += 1
                else:
                    # Widget is None, mark for removal
                    widgets_to_remove.append(widget)
            except RuntimeError:
                # Widget was likely destroyed, mark for removal
                self.logger.warning(f"Widget for tooltip '{original_text}' appears to be destroyed")
                widgets_to_remove.append(widget)
            except Exception as e:
                self.logger.error(f"Error refreshing tooltip '{original_text}': {e}")
                # Don't remove the widget, we might be able to refresh it later

        # Remove destroyed widgets from our dictionary
        for widget in widgets_to_remove:
            if widget in self.tooltip_widgets:
                del self.tooltip_widgets[widget]
                self.logger.debug("Removed destroyed widget from tooltip tracking")

        self.logger.info(f"Tooltip refresh complete. Successfully refreshed {success_count} tooltips, translated {translation_count} tooltips. Removed {len(widgets_to_remove)} destroyed widgets.")

    def clear_tooltip(self, widget):
        """Clear tooltip from a widget."""
        if not widget:
            self.logger.warning("Cannot clear tooltip for None widget")
            return

        try:
            # Remove from tracked widgets
            if widget in self.tooltip_widgets:
                del self.tooltip_widgets[widget]
                self.logger.debug(f"Removed widget from tooltip tracking")

            # Clear the tooltip
            widget.setToolTip("")

            # Hide the custom tooltip if it's for this widget
            if self.active_widget == widget and self.custom_tooltip.isVisible():
                self.custom_tooltip.hide()

        except RuntimeError:
            # Widget was likely destroyed
            self.logger.debug("Widget was destroyed, cannot clear tooltip")
            if self.active_widget == widget:
                self.active_widget = None
                if self.custom_tooltip.isVisible():
                    self.custom_tooltip.hide()
        except Exception as e:
            self.logger.error(f"Error clearing tooltip: {e}")
