import sys
import os
import shutil
import logging # Add logging import
from PySide6.QtWidgets import (
    QA<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QLabel, QComboBox, QMessageBox,
    QGroupBox, QRadioButton, QButtonGroup, QCheckBox,
    QSpinBox, QGridLayout, QFormLayout,
    QPushButton, QFileDialog, QDialog, QListWidget, QDialogButtonBox, QSlider, QScrollArea
)
# Restore QCoreApplication for tr(), keep Signal, remove custom imports
from PySide6.QtCore import QSettings, QCoreApplication, Qt, QTranslator, Signal
from PySide6.QtGui import QFontDatabase # Added QFontDatabase

# Import the App Colors tab initialization function
from app.pages.app_colors_tab import init_app_colors_tab

# Import screen calculator for dynamic dashboard options
from app.utils.screen_calculator import get_screen_calculator

# Remove custom translation imports
# from i18n.translations import get_translation, set_current_language

# Restore dependency on main module (needs refactoring later)
import __main__ as main_module

# We will need QColor and potentially QPixmap/QIcon if adding color previews
# from PySide6.QtGui import QColor, QPixmap, QIcon

# Assuming we kept this structure from the dictionary approach reversion
# If we reverted fully to QTranslator, this needs adjustment
# Let's assume we need a way to get translatable text
# For now, mock it if reverted, or use the actual function if kept.
# TODO: Confirm final translation approach and adjust import/call if needed
# from i18n.translations import get_translation, set_current_language
def get_translation(text): # Mock implementation if reverted
    # In a real scenario with QTranslator, use QCoreApplication.translate() or self.tr()
    return text

# --- New Remove Font Dialog ---
class RemoveFontDialog(QDialog):
    font_removed = Signal(str) # Signal to emit when a font is removed

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.settings.remove_font_dialog") # Initialize its own logger
        self.setWindowTitle(self.tr("Remove Custom Fonts"))
        self.setMinimumWidth(400)
        self.font_files_map = {} # {family_name: [file_path1, ...]}
        self.file_path_to_id_map = {} # New map: {file_path: font_id}

        layout = QVBoxLayout(self)

        self.list_widget = QListWidget()
        self.list_widget.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        layout.addWidget(QLabel(self.tr("Select custom font family to remove:")))
        layout.addWidget(self.list_widget)

        self.button_box = QDialogButtonBox()
        self.remove_button = self.button_box.addButton(self.tr("Remove Selected"), QDialogButtonBox.ButtonRole.ActionRole)
        self.cancel_button = self.button_box.addButton(QDialogButtonBox.StandardButton.Cancel)
        layout.addWidget(self.button_box)

        self.remove_button.setEnabled(False) # Initially disabled

        self.list_widget.itemSelectionChanged.connect(self._update_button_state)
        self.remove_button.clicked.connect(self._remove_selected_font)
        self.cancel_button.clicked.connect(self.reject)

        self._populate_font_list()

    def tr(self, text): # Simple tr for dialog
        return QApplication.translate("RemoveFontDialog", text)

    def _populate_font_list(self):
        """Scans the custom fonts directory and populates the list and maps."""
        self.list_widget.clear()
        self.font_files_map.clear()
        self.file_path_to_id_map.clear() # Clear ID map too
        font_db = QFontDatabase()

        custom_fonts_dir = os.path.join(os.path.dirname(os.path.abspath(sys.argv[0])), 'custom_fonts')

        if not os.path.isdir(custom_fonts_dir):
            return

        found_families = set()
        temp_map = {}
        temp_id_map = {} # Temporary map for IDs

        for filename in os.listdir(custom_fonts_dir):
            if filename.lower().endswith(('.ttf', '.otf')):
                file_path = os.path.join(custom_fonts_dir, filename)
                # Attempt to load/add the font to get its ID
                font_id = font_db.addApplicationFont(file_path)
                if font_id != -1:
                    # Store the ID mapping
                    temp_id_map[file_path] = font_id

                    families = font_db.applicationFontFamilies(font_id)
                    if families:
                        for family in families:
                            if family not in temp_map:
                                temp_map[family] = []
                            if file_path not in temp_map[family]:
                                temp_map[family].append(file_path)
                            found_families.add(family)
                    # else: # Don't unload here, keep it loaded
                    #    QFontDatabase.removeApplicationFont(font_id)
                else:
                    # Use logger for warnings
                    self.logger.warning(f"Could not load {filename} to get family name/ID.")

        self.font_files_map = temp_map
        self.file_path_to_id_map = temp_id_map # Store the collected IDs
        sorted_families = sorted(list(found_families))
        self.list_widget.addItems(sorted_families)
        self._update_button_state()

    def _update_button_state(self):
        """Enables/disables the remove button based on selection."""
        self.remove_button.setEnabled(len(self.list_widget.selectedItems()) > 0)

    def _remove_selected_font(self):
        """Removes the selected font file and updates state using font ID."""
        selected_items = self.list_widget.selectedItems()
        if not selected_items: return
        family_name = selected_items[0].text()
        if family_name not in self.font_files_map:
            QMessageBox.warning(self, self.tr("Error"), self.tr("Font information not found for {}.").format(family_name))
            self._populate_font_list() # Refresh list
            return
        file_paths = self.font_files_map.get(family_name, [])
        if not file_paths:
             QMessageBox.warning(self, self.tr("Error"), self.tr("No font files associated with {}.").format(family_name))
             self._populate_font_list() # Refresh list
             return

        errors = []
        files_deleted = 0
        # No need for QFontDatabase() instance here, methods are static

        # Confirmation dialog
        reply = QMessageBox.question(self,
            self.tr("Confirm Removal"),
            self.tr("Are you sure you want to remove the font family '{}'?\nThis will delete the following file(s):\n - {}").format(family_name, "\n - ".join(os.path.basename(p) for p in file_paths)),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No # Default button
        )

        if reply == QMessageBox.StandardButton.No:
            return

        # --- Proceed with removal ---
        for file_path in file_paths:
            font_unloaded = False
            try:
                # 1. Unload font using its ID
                font_id = self.file_path_to_id_map.get(file_path, -1)
                if font_id != -1:
                    if QFontDatabase.removeApplicationFont(font_id):
                        # Use logger for info/debug
                        self.logger.info(f"Unloaded font ID {font_id} ({os.path.basename(file_path)}).")
                        font_unloaded = True
                        # Remove from map once successfully unloaded
                        del self.file_path_to_id_map[file_path]
                    else:
                        # This might happen if it was somehow already unloaded
                        # Use logger for warnings
                        self.logger.warning(f"Failed to unload font ID {font_id} ({os.path.basename(file_path)}), but proceeding with file deletion.")
                else:
                    # Use logger for warnings
                    self.logger.warning(f"Font ID not found for {file_path}. Cannot unload from database.")

                # 2. Delete file
                if os.path.exists(file_path):
                    os.remove(file_path)
                    # Use logger for info/debug
                    self.logger.info(f"Deleted font file: {file_path}")
                    files_deleted += 1
                else:
                    # Use logger for info/debug
                    self.logger.info(f"File not found for deletion (already deleted?): {file_path}")

            except KeyError:
                 # Should not happen if get() was used correctly, but belt-and-suspenders
                # Use logger for errors
                self.logger.error(f"Could not find ID for {file_path} in map during removal.")
                errors.append(self.tr("Internal error finding font ID for {}).").format(os.path.basename(file_path)))
            except OSError as e:
                errors.append(self.tr("Could not delete file {}: {}").format(os.path.basename(file_path), e))
            except Exception as e:
                errors.append(self.tr("Unexpected error removing {}: {}").format(os.path.basename(file_path), e))

        # 3. Update global list in main module
        if family_name in main_module.loaded_custom_font_families:
            try:
                main_module.loaded_custom_font_families.remove(family_name)
                # Use logger for info/debug
                self.logger.info(f"Removed '{family_name}' from main_module list.")
            except ValueError:
                pass # Should not happen if check passed, but be safe

        # 4. Emit signal for SettingsWindow
        self.font_removed.emit(family_name)

        # 5. Refresh list in this dialog
        self._populate_font_list()

        # 6. Show result message
        if errors:
            QMessageBox.warning(self, self.tr("Removal Partially Failed"),
                                self.tr("Removed family '{}'.\nDeleted {} file(s).\nEncountered errors:\n - {}").format(family_name, files_deleted, "\n - ".join(errors)))
        else:
            QMessageBox.information(self, self.tr("Removal Successful"),
                                    self.tr("Successfully removed font family '{}' and deleted {} associated file(s).").format(family_name, files_deleted))

class SettingsWindow(QWidget):
    language_changed = Signal()
    theme_changed = Signal(str)
    accent_color_changed = Signal(str) # Signal for accent color
    button_shape_changed = Signal(str) # Signal for button shape
    font_family_changed = Signal(str) # New signal
    font_size_changed = Signal(int)   # New signal
    density_changed = Signal(str) # New signal for density
    zoom_changed = Signal(int) # New signal for zoom - Use int
    # Tooltip signals
    tooltip_enabled_changed = Signal(bool)
    tooltip_duration_changed = Signal(int)
    # Dashboard signals
    grid_columns_changed = Signal(int)  # Signal for grid columns (e.g., 6)
    card_size_changed = Signal(str)  # Signal for card size (e.g., "medium")
    LANGUAGES = {"English": "en", "Greek": "el", "Chinese": "zh", "Albanian": "sq"}
    LANGUAGE_CODES = {v: k for k, v in LANGUAGES.items()}
    # Map theme display names to internal identifiers
    THEMES = {
        "Light": "light",
        "Dark": "dark",
        "System": "system"
    }
    THEME_IDS = {v: k for k, v in THEMES.items()} # Reverse mapping
    # Define Accent Colors (name -> identifier, could be hex codes)
    ACCENT_COLORS = {
        "Blue": "blue", # Default
        "Green": "green",
        "Red": "red",
        "Orange": "orange",
        "Yellow": "yellow",     # Added
        "Light Grey": "lightgrey", # Added
        "Grey": "grey"          # Added
    }
    ACCENT_COLOR_IDS = {v: k for k, v in ACCENT_COLORS.items()} # Reverse
    # Define Button Shapes
    BUTTON_SHAPES = {
        "Square": "square", # Default (or slight radius from theme)
        "Rounded": "rounded",
        # "Oval": "oval" # Removed
    }
    BUTTON_SHAPE_IDS = {v: k for k, v in BUTTON_SHAPES.items()}
    # Define Font Options
    CUSTOM_FONTS_DIR = os.path.join(os.path.dirname(os.path.abspath(sys.argv[0])), 'custom_fonts')
    # Add Density levels from main module (or redefine if preferred)
    # DENSITY_LEVELS = main_module.DENSITY_LEVELS
    # DENSITY_IDS = main_module.DENSITY_IDS
    # DEFAULT_DENSITY = main_module.DEFAULT_DENSITY

    # Tooltip settings
    TOOLTIP_DURATIONS = {
        "Short (2s)": 2000,
        "Medium (4s)": 4000,
        "Long (8s)": 8000
    }

    # Translated tooltip duration names
    def get_translated_durations(self):
        """Get tooltip durations with translated names."""
        # Get current language
        settings = QSettings()
        current_language = settings.value("general/language", "en")

        # Import the direct translation dictionaries if needed
        from app.utils.tooltip_helper import GREEK_TRANSLATIONS, CHINESE_TRANSLATIONS, ALBANIAN_TRANSLATIONS

        # Function to get the best translation
        def get_best_translation(text):
            # Try Qt translation first
            qt_translation = QCoreApplication.translate("Tooltips", text)
            if qt_translation != text:
                return qt_translation

            # Fall back to direct translation if needed
            if current_language == "el":
                return GREEK_TRANSLATIONS.get(text, text)
            elif current_language == "zh":
                return CHINESE_TRANSLATIONS.get(text, text)
            elif current_language == "sq":
                return ALBANIAN_TRANSLATIONS.get(text, text)
            return text

        # Return the dictionary with the best translations
        return {
            get_best_translation("Short (2s)"): 2000,
            get_best_translation("Medium (4s)"): 4000,
            get_best_translation("Long (8s)"): 8000
        }

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("SettingsWindow")
        self.logger = logging.getLogger("app.settings") # Initialize logger for the instance
        self.settings = QSettings()
        # Access constants from main module after import
        self.MIN_FONT_SIZE = main_module.MIN_FONT_SIZE
        self.MAX_FONT_SIZE = main_module.MAX_FONT_SIZE
        self.DEFAULT_FONT_SIZE = main_module.DEFAULT_FONT_SIZE
        # Load density constants here
        self.DENSITY_LEVELS = main_module.DENSITY_LEVELS
        self.DENSITY_IDS = main_module.DENSITY_IDS
        self.DEFAULT_DENSITY = main_module.DEFAULT_DENSITY
        # Load zoom constants
        self.MIN_ZOOM_PERCENT = main_module.MIN_ZOOM_PERCENT
        self.MAX_ZOOM_PERCENT = main_module.MAX_ZOOM_PERCENT
        self.DEFAULT_ZOOM_PERCENT = main_module.DEFAULT_ZOOM_PERCENT
        # Flag to prevent saving during initialization
        self._settings_loading = True

        self.init_ui()
        self.load_settings()

        # Now allow saving
        self._settings_loading = False

        self.language_changed.connect(self.retranslateUi)
        # We don't need to connect theme_changed here, MainWindow will connect

        # Initialize screen calculator and update dashboard options
        self.screen_calculator = get_screen_calculator()
        self._update_dashboard_options()

    def init_ui(self):
        """Sets up the user interface elements."""
        self.setWindowTitle(self.tr("Settings")) # Use tr()
        # Decide on initial size, or let it size automatically
        self.resize(500, 700)

        # Main layout for the settings window
        self.main_layout = QVBoxLayout(self)

        # --- Tab Widget ---
        self.tab_widget = QTabWidget()
        self.main_layout.addWidget(self.tab_widget)

        # --- General Tab ---
        general_tab = QWidget()
        self.general_layout = QVBoxLayout(general_tab)

        # --- Language Setting ---
        lang_layout = QHBoxLayout() # Horizontal layout for label + combo box
        self.lang_label = QLabel() # Store reference for retranslate
        self.lang_label.setToolTip(self.tr("Select the application language"))
        self.lang_combo = QComboBox()
        self.lang_combo.setToolTip(self.tr("Choose the language for the application interface"))
        self.lang_combo.addItems(list(self.LANGUAGES.keys())) # Populate from dict keys
        # TODO: Load saved language setting and set current index

        lang_layout.addWidget(self.lang_label)
        lang_layout.addWidget(self.lang_combo)
        lang_layout.addStretch() # Push label/combo to the left

        self.general_layout.addLayout(lang_layout) # Add the horizontal layout to the tab layout

        # --- Language Explanation Text ---
        self.language_explanation_label = QLabel()
        self.language_explanation_label.setWordWrap(True)
        self.language_explanation_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 11px;
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                margin: 5px 0px;
            }
        """)
        self.general_layout.addWidget(self.language_explanation_label)

        # --- Tooltip Settings ---
        self.tooltip_group_box = QGroupBox()
        tooltip_layout = QVBoxLayout(self.tooltip_group_box)

        # Enable/Disable Checkbox
        self.tooltip_enabled_checkbox = QCheckBox()
        self.tooltip_enabled_checkbox.setToolTip(self.tr("Enable or disable tooltips throughout the application"))
        tooltip_layout.addWidget(self.tooltip_enabled_checkbox)

        # Duration
        duration_layout = QHBoxLayout()
        self.tooltip_duration_label = QLabel()
        self.tooltip_duration_label.setToolTip(self.tr("Set how long tooltips remain visible"))
        self.tooltip_duration_combo = QComboBox()
        self.tooltip_duration_combo.setToolTip(self.tr("Choose the duration tooltips remain visible"))
        # We'll populate the combo box in retranslateUi to ensure translations are used
        duration_layout.addWidget(self.tooltip_duration_label)
        duration_layout.addWidget(self.tooltip_duration_combo)
        duration_layout.addStretch()
        tooltip_layout.addLayout(duration_layout)

        self.general_layout.addWidget(self.tooltip_group_box)

        # Add other general settings widgets here later...
        self.general_layout.addStretch() # Push content to the top
        self.tab_widget.addTab(general_tab, "") # Text set in retranslate

        # --- Appearance Tab (Modified with ScrollArea) ---
        appearance_tab_widget = QWidget() # Main container for the tab
        appearance_tab_layout = QVBoxLayout(appearance_tab_widget) # Layout for the tab itself
        appearance_tab_layout.setContentsMargins(0,0,0,0) # No margin for the scroll area

        # Create Scroll Area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True) # Crucial for layout to work
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff) # No horizontal scroll needed
        appearance_tab_layout.addWidget(scroll_area)

        # Create container widget for scroll area content
        appearance_content_widget = QWidget()
        scroll_area.setWidget(appearance_content_widget) # Set this widget inside scroll area

        # Create layout for the content *inside* the scroll area
        # This is the layout we add the group boxes to
        self.appearance_layout = QVBoxLayout(appearance_content_widget)

        # --- Create and Add GroupBoxes to self.appearance_layout ---
        # Theme GroupBox
        self.theme_group_box = QGroupBox()
        theme_layout = QVBoxLayout(self.theme_group_box)
        self.theme_button_group = QButtonGroup(self)
        self.radio_light = QRadioButton(self.tr("Light"))
        self.radio_dark = QRadioButton(self.tr("Dark"))
        self.radio_system = QRadioButton(self.tr("System"))

        # Add buttons to layout and button group
        theme_layout.addWidget(self.radio_light)
        theme_layout.addWidget(self.radio_dark)
        theme_layout.addWidget(self.radio_system)
        self.theme_button_group.addButton(self.radio_light, 0) # Assign IDs if needed
        self.theme_button_group.addButton(self.radio_dark, 1)
        self.theme_button_group.addButton(self.radio_system, 2)

        self.appearance_layout.addWidget(self.theme_group_box)
        self.appearance_layout.addStretch()
        self.tab_widget.addTab(appearance_tab_widget, "") # Text set in retranslate

        # --- Accent Color GroupBox ---
        self.accent_group_box = QGroupBox()
        accent_layout = QVBoxLayout(self.accent_group_box)
        self.accent_button_group = QButtonGroup(self)

        # Create radio buttons dynamically from ACCENT_COLORS
        self.accent_radio_buttons = {}
        for name, color_id in self.ACCENT_COLORS.items():
            # Wrap the name in self.tr() to enable translation
            radio = QRadioButton(self.tr(name))
            self.accent_radio_buttons[color_id] = radio # Store by id
            accent_layout.addWidget(radio)
            self.accent_button_group.addButton(radio)

        self.appearance_layout.addWidget(self.accent_group_box)
        # No stretch here, add it after all group boxes

        # --- Button Shape GroupBox (Restored Loop) ---
        self.shape_group_box = QGroupBox(self.tr("Button Shape")) # Restore GroupBox
        shape_layout = QVBoxLayout(self.shape_group_box) # Restore QVBoxLayout
        self.shape_button_group = QButtonGroup(self) # Restore ButtonGroup instance variable
        self.shape_radio_buttons = {} # Restore dictionary to store buttons
        for name, shape_id in self.BUTTON_SHAPES.items(): # Loop over corrected BUTTON_SHAPES
             # Use self.tr() for the display name
             radio = QRadioButton(self.tr(name))
             self.shape_radio_buttons[shape_id] = radio # Store by id
             shape_layout.addWidget(radio)
             self.shape_button_group.addButton(radio)
        self.appearance_layout.addWidget(self.shape_group_box) # Add the correct groupbox

        # --- Layout Density GroupBox ---
        self.density_group_box = QGroupBox()
        density_layout = QVBoxLayout(self.density_group_box)
        self.density_button_group = QButtonGroup(self)
        self.density_radio_buttons = {}
        for name, density_id in self.DENSITY_LEVELS.items():
             # Use self.tr() for the display name
             radio = QRadioButton(self.tr(name))
             self.density_radio_buttons[density_id] = radio
             density_layout.addWidget(radio)
             self.density_button_group.addButton(radio)
        self.appearance_layout.addWidget(self.density_group_box)

        # --- Zoom GroupBox (New) ---
        self.zoom_group_box = QGroupBox()
        zoom_layout = QVBoxLayout(self.zoom_group_box)
        zoom_slider_layout = QHBoxLayout() # Layout for slider + label

        self.zoom_slider = QSlider(Qt.Orientation.Horizontal)
        self.zoom_slider.setRange(self.MIN_ZOOM_PERCENT, self.MAX_ZOOM_PERCENT)
        self.zoom_slider.setValue(self.DEFAULT_ZOOM_PERCENT)
        self.zoom_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.zoom_slider.setTickInterval(5) # Add ticks every 5%
        self.zoom_slider.setSingleStep(1)

        self.zoom_label = QLabel(f"{self.DEFAULT_ZOOM_PERCENT}%") # Initial label text
        self.zoom_label.setMinimumWidth(40) # Ensure space for label

        zoom_slider_layout.addWidget(self.zoom_slider)
        zoom_slider_layout.addWidget(self.zoom_label)
        zoom_layout.addLayout(zoom_slider_layout)
        self.appearance_layout.addWidget(self.zoom_group_box)
        # --- End Zoom GroupBox ---

        # --- Font GroupBox (Modified) ---
        self.font_group_box = QGroupBox()
        font_layout = QGridLayout(self.font_group_box)
        self.font_family_label = QLabel()
        self.font_family_combo = QComboBox()

        # --- Populate with basic + loaded custom fonts ---
        # Access lists from main module
        basic_fonts = main_module.BASIC_FONT_FAMILIES
        custom_fonts = main_module.loaded_custom_font_families
        combined_fonts = sorted(list(set(basic_fonts + custom_fonts))) # Combine, remove duplicates, sort
        self.font_family_combo.addItems(combined_fonts)
        # ---

        self.font_size_label = QLabel()
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setRange(self.MIN_FONT_SIZE, self.MAX_FONT_SIZE)
        self.font_size_spinbox.setSuffix(" pt")

        font_layout.addWidget(self.font_family_label, 0, 0)
        font_layout.addWidget(self.font_family_combo, 0, 1)
        font_layout.addWidget(self.font_size_label, 1, 0)
        font_layout.addWidget(self.font_size_spinbox, 1, 1)
        font_layout.setColumnStretch(1, 1)
        self.appearance_layout.addWidget(self.font_group_box)

        # --- Font Action Buttons Layout ---
        font_actions_layout = QHBoxLayout()
        self.add_font_button = QPushButton() # Text set in retranslateUi
        self.remove_font_button = QPushButton() # New button, text set in retranslateUi
        font_actions_layout.addWidget(self.add_font_button)
        font_actions_layout.addWidget(self.remove_font_button)
        font_actions_layout.addStretch()
        # Add this layout instead of individual buttons directly
        self.appearance_layout.addLayout(font_actions_layout)

        # --- Dashboard GroupBox ---
        self.dashboard_group_box = QGroupBox()
        dashboard_layout = QFormLayout(self.dashboard_group_box)

        # Grid columns settings (will be populated dynamically)
        self.grid_columns_label = QLabel()
        self.grid_columns_combo = QComboBox()
        # Don't add items here - will be populated by _update_dashboard_options()
        dashboard_layout.addRow(self.grid_columns_label, self.grid_columns_combo)

        # Card size settings
        self.card_size_label = QLabel()
        self.card_size_combo = QComboBox()
        self.card_size_combo.addItems([
            self.tr("Small"),     # 180×110 (was "Medium")
            self.tr("Medium"),    # 220×130 (was "Large")
            self.tr("Large")      # 260×150 (was "Extra Large")
        ])
        dashboard_layout.addRow(self.card_size_label, self.card_size_combo)

        self.appearance_layout.addWidget(self.dashboard_group_box)

        self.appearance_layout.addStretch()
        self.tab_widget.addTab(appearance_tab_widget, "") # Text set in retranslate

        # --- App Colors Tab ---
        self.app_colors_tab = QWidget()
        init_app_colors_tab(self.app_colors_tab)
        self.app_colors_tab_index = self.tab_widget.addTab(self.app_colors_tab, self.tr("App Colors"))

        # --- Pages Tab ---
        self.pages_tab = QWidget()
        self._init_pages_tab()
        self.pages_tab_index = self.tab_widget.addTab(self.pages_tab, self.tr("Pages"))

        # Get permission manager and connect to permission changes
        from app.utils.permission_manager import get_permission_manager
        self.permission_manager = get_permission_manager()
        self.permission_manager.permission_changed.connect(self.update_permissions)

        # Update permissions to show/hide tabs based on developer mode
        self.update_permissions()

        # --- Connections ---
        self.lang_combo.currentTextChanged.connect(self._on_language_changed)
        self.theme_button_group.buttonClicked.connect(self._on_theme_button_clicked)
        self.accent_button_group.buttonClicked.connect(self._on_accent_color_button_clicked)
        self.shape_button_group.buttonClicked.connect(self._on_button_shape_clicked)
        self.font_family_combo.currentTextChanged.connect(self._on_font_family_changed)
        self.font_size_spinbox.valueChanged.connect(self._on_font_size_changed)
        self.add_font_button.clicked.connect(self._add_custom_fonts)
        self.remove_font_button.clicked.connect(self._open_remove_font_dialog) # Connect new button
        self.density_button_group.buttonClicked.connect(self._on_density_button_clicked) # Connect new group
        # Connect zoom slider signals
        self.zoom_slider.valueChanged.connect(self._on_zoom_slider_changed)
        self.zoom_slider.sliderReleased.connect(self._on_zoom_slider_released)

        # Connect tooltip signals
        self.tooltip_enabled_checkbox.toggled.connect(self._on_tooltip_enabled_changed)
        self.tooltip_duration_combo.currentIndexChanged.connect(self._on_tooltip_duration_changed)

        # Connect dashboard signals
        self.grid_columns_combo.currentIndexChanged.connect(self._on_grid_columns_changed)
        self.card_size_combo.currentIndexChanged.connect(self._on_card_size_changed)

        # Ensure the window has maximize/restore/close buttons
        # This is usually default for QWidget depending on OS and flags,
        # but can be explicitly set if needed.
        # self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # Initial UI text setup
        self.retranslateUi()

    def load_settings(self):
        """Loads settings and updates UI elements."""
        self.logger.info("load_settings() called - starting to load all settings")
        saved_lang_code = self.settings.value("general/language", "en")
        if saved_lang_code in self.LANGUAGE_CODES:
            display_name = self.LANGUAGE_CODES[saved_lang_code]
            # Block signals temporarily to prevent _on_language_changed from firing during setup
            self.lang_combo.blockSignals(True)
            self.lang_combo.setCurrentText(display_name)
            self.lang_combo.blockSignals(False)
        else:
            self.lang_combo.blockSignals(True)
            self.lang_combo.setCurrentIndex(0) # Default to first item (English)
            self.lang_combo.blockSignals(False)

        # Theme settings
        saved_theme = self.settings.value("appearance/theme", "system") # Default to system
        if saved_theme == "light":
            self.radio_light.setChecked(True)
        elif saved_theme == "dark":
            self.radio_dark.setChecked(True)
        else: # Default to system
            self.radio_system.setChecked(True)

        # --- Load Accent Color ---
        saved_accent = self.settings.value("appearance/accent_color", "blue") # Default blue
        if saved_accent in self.accent_radio_buttons:
            self.accent_radio_buttons[saved_accent].setChecked(True)
        else: # If saved value is invalid, default to blue
             if "blue" in self.accent_radio_buttons:
                 self.accent_radio_buttons["blue"].setChecked(True)

        # --- Load Button Shape (Simplified) ---
        current_shape_id = self.settings.value("appearance/button_shape", "rounded") # Use "rounded" directly

        # Validate the loaded shape ID against the current available shapes
        if current_shape_id not in self.BUTTON_SHAPES.values(): # Check against the values ('square', 'rounded')
            self.logger.warning(f"Saved button shape ID '{current_shape_id}' is no longer valid. Falling back to default 'rounded'.")
            current_shape_id = "rounded" # Use "rounded" directly

        # Find the radio button corresponding to the valid shape ID
        button_to_check = self.shape_radio_buttons.get(current_shape_id)

        if button_to_check:
            button_to_check.setChecked(True)
        else:
            # This case should ideally not happen if the default is always valid
            # and present in shape_radio_buttons, but as a fallback:
            self.logger.error(f"Could not find radio button for shape ID '{current_shape_id}'. Checking first available button.")
            if self.shape_button_group.buttons():
                self.shape_button_group.buttons()[0].setChecked(True)

        # --- Load Font Family ---
        saved_family = self.settings.value("appearance/font_family", "System Default")
        # Check if the saved font is actually in the combo box list
        found_index = self.font_family_combo.findText(saved_family)
        if found_index != -1:
             self.font_family_combo.blockSignals(True)
             self.font_family_combo.setCurrentIndex(found_index)
             self.font_family_combo.blockSignals(False)
        else: # Font not found in curated list, default to System Default
             default_index = self.font_family_combo.findText("System Default")
             self.font_family_combo.blockSignals(True)
             self.font_family_combo.setCurrentIndex(default_index if default_index != -1 else 0) # Fallback to index 0 if even default isn't found
             self.font_family_combo.blockSignals(False)
             # Optionally save the fallback immediately? Or wait for user change.
             # self.settings.setValue("appearance/font_family", "System Default")

        saved_size = self.settings.value("appearance/font_size", self.DEFAULT_FONT_SIZE)
        try:
            size_int = int(saved_size)
            if self.MIN_FONT_SIZE <= size_int <= self.MAX_FONT_SIZE:
                self.font_size_spinbox.blockSignals(True)
                self.font_size_spinbox.setValue(size_int)
                self.font_size_spinbox.blockSignals(False)
            else:
                self.font_size_spinbox.setValue(self.DEFAULT_FONT_SIZE)
        except (ValueError, TypeError):
             self.font_size_spinbox.setValue(self.DEFAULT_FONT_SIZE)

        # Load Density Setting
        saved_density = self.settings.value("appearance/density", self.DEFAULT_DENSITY)
        if saved_density in self.density_radio_buttons:
            self.density_radio_buttons[saved_density].setChecked(True)
        else:
            if self.DEFAULT_DENSITY in self.density_radio_buttons:
                 self.density_radio_buttons[self.DEFAULT_DENSITY].setChecked(True)

        # Load Zoom Setting (New)
        saved_zoom = self.settings.value("appearance/zoom_percent", self.DEFAULT_ZOOM_PERCENT)
        try:
            zoom_int = int(saved_zoom)
            if not (self.MIN_ZOOM_PERCENT <= zoom_int <= self.MAX_ZOOM_PERCENT):
                zoom_int = self.DEFAULT_ZOOM_PERCENT
        except (ValueError, TypeError):
            zoom_int = self.DEFAULT_ZOOM_PERCENT
        # Block signals while setting initial value
        self.zoom_slider.blockSignals(True)
        self.zoom_slider.setValue(zoom_int)
        self.zoom_slider.blockSignals(False)
        self.zoom_label.setText(f"{zoom_int}%") # Update label too

        # Load dashboard settings
        saved_grid_columns = self.settings.value("dashboard/grid_columns", 6, type=int)
        columns_map = {5: 0, 6: 1, 7: 2, 8: 3, 9: 4, 10: 5}
        columns_index = columns_map.get(saved_grid_columns, 1)  # Default to "6 columns"
        self.grid_columns_combo.blockSignals(True)
        self.grid_columns_combo.setCurrentIndex(columns_index)
        self.grid_columns_combo.blockSignals(False)

        saved_card_size = self.settings.value("dashboard/card_size", "small")  # Default to new "Small"
        # Updated mapping: Small=180×110, Medium=220×130, Large=260×150
        card_size_map = {
            "small": 0,      # New Small (was Medium)
            "medium": 0,     # Old Medium -> New Small
            "large": 1,      # Old Large -> New Medium
            "extra large": 2 # Old Extra Large -> New Large
        }
        card_index = card_size_map.get(saved_card_size.lower(), 0)  # Default to "Small"
        self.card_size_combo.blockSignals(True)
        self.card_size_combo.setCurrentIndex(card_index)
        self.card_size_combo.blockSignals(False)

        # Load Tooltip Settings
        # Enable/Disable
        tooltip_enabled = self.settings.value("general/tooltip_enabled", True, type=bool)
        self.tooltip_enabled_checkbox.blockSignals(True)
        self.tooltip_enabled_checkbox.setChecked(tooltip_enabled)
        self.tooltip_enabled_checkbox.blockSignals(False)

        # Duration - we'll populate the combo box in retranslateUi
        # Just store the duration value for now
        self.saved_tooltip_duration = self.settings.value("general/tooltip_duration", 4000, type=int)

        # Load Pages settings
        self._load_pages_settings()

    def _on_language_changed(self, language_display_name):
        language_code = self.LANGUAGES.get(language_display_name)
        if language_code:
            self.logger.info(f"Language selected: {language_display_name} (Code: {language_code})")
            # 1. Save the preference
            self.settings.setValue("general/language", language_code)
            self.logger.info("Saved language preference.")
            # 2. Call the global load_translator from main
            try:
                main_module.load_translator(language_code)
                self.logger.info("Translator reloaded.")

                # Update the UI with the new translations
                self.retranslateUi()
                self.logger.info("UI retranslated.")

                # Emit language_changed signal
                self.language_changed.emit()
                self.logger.info("language_changed signal emitted.")

                # Find the main window and emit its language_changed signal
                from PySide6.QtWidgets import QApplication
                main_window = None
                for widget in QApplication.topLevelWidgets():
                    if hasattr(widget, 'language_changed'):
                        main_window = widget
                        break

                if main_window and hasattr(main_window, 'language_changed'):
                    main_window.language_changed.emit()
                    self.logger.info("Main window language_changed signal emitted.")
            except AttributeError:
                 self.logger.error("Could not call load_translator. Running standalone?")
                 QMessageBox.warning(self, self.tr("Error"), self.tr("Could not apply language change immediately."))
            except Exception as e:
                 self.logger.critical(f"Error reloading translator: {e}")
                 QMessageBox.critical(self, self.tr("Error"), self.tr("An unexpected error occurred while changing language: {}).").format(e))

    def _on_theme_button_clicked(self, button):
        """Handles theme radio button clicks."""
        selected_theme = "system" # Default
        if button == self.radio_light:
            selected_theme = "light"
        elif button == self.radio_dark:
            selected_theme = "dark"

        self.logger.info(f"Theme selected: {selected_theme}")
        # Save the preference
        self.settings.setValue("appearance/theme", selected_theme)
        self.logger.info("Saved theme preference.")
        # Emit the signal with the theme identifier
        self.theme_changed.emit(selected_theme)
        self.logger.info("theme_changed signal emitted.")

    def _on_accent_color_button_clicked(self, button):
        """Handles accent color radio button clicks."""
        selected_accent_id = "blue" # Default
        for color_id, radio in self.accent_radio_buttons.items():
            if button == radio:
                selected_accent_id = color_id
                break

        self.logger.info(f"Accent color selected: {selected_accent_id}")
        self.settings.setValue("appearance/accent_color", selected_accent_id)
        self.logger.info("Saved accent color preference.")
        self.accent_color_changed.emit(selected_accent_id)
        self.logger.info("accent_color_changed signal emitted.")

    def _on_button_shape_clicked(self, button):
        """Handles button shape radio button clicks."""
        selected_shape_id = "rounded" # Default
        for shape_id, radio in self.shape_radio_buttons.items():
            if button == radio:
                selected_shape_id = shape_id
                break
        self.logger.info(f"Button shape selected: {selected_shape_id}")
        self.settings.setValue("appearance/button_shape", selected_shape_id)
        self.logger.info("Saved button shape preference.")
        self.button_shape_changed.emit(selected_shape_id)
        self.logger.info("button_shape_changed signal emitted.")

    # --- New Slots for Font Changes ---
    def _on_font_family_changed(self, family):
        """Handles font family selection change."""
        self.logger.info(f"Font family selected: {family}")
        self.settings.setValue("appearance/font_family", family)
        self.font_family_changed.emit(family)
        self.logger.info("font_family_changed signal emitted.")

    def _on_font_size_changed(self, size):
        """Handles font size spinbox change."""
        self.logger.info(f"Font size selected: {size}")
        self.settings.setValue("appearance/font_size", size)
        self.font_size_changed.emit(size)
        self.logger.info("font_size_changed signal emitted.")

    def _add_custom_fonts(self):
        """Opens file dialog, copies fonts, loads them, and updates the combo box."""
        # Ensure custom fonts directory exists
        try:
            os.makedirs(self.CUSTOM_FONTS_DIR, exist_ok=True)
        except OSError as e:
            QMessageBox.critical(self, self.tr("Error"), self.tr("Could not create custom fonts directory: {} {}").format(self.CUSTOM_FONTS_DIR, e))
            return

        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        file_dialog.setNameFilter(self.tr("Font Files (*.ttf *.otf)"))
        file_dialog.setWindowTitle(self.tr("Select Custom Font Files"))

        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            copied_count = 0
            loaded_count = 0
            newly_added_families = [] # Track families added in this action
            errors = []
            font_db = QFontDatabase()

            for source_path in selected_files:
                filename = os.path.basename(source_path)
                dest_path = os.path.join(self.CUSTOM_FONTS_DIR, filename)
                try:
                    font_already_exists_in_folder = os.path.exists(dest_path)
                    if not font_already_exists_in_folder:
                        shutil.copy2(source_path, dest_path)
                        self.logger.info(f"Copied font: {filename}")
                        copied_count += 1
                    else:
                         self.logger.info(f"Font already exists, skipping copy: {filename}")

                    # Attempt to load/reload the font into the application database
                    font_id = font_db.addApplicationFont(dest_path)
                    if font_id != -1:
                         current_families = font_db.applicationFontFamilies(font_id)
                         if current_families:
                            self.logger.info(f"Loaded/Reloaded font family: {current_families[0]}")
                            loaded_count +=1
                            # Add to combo box if not already present
                            for family in current_families:
                                if self.font_family_combo.findText(family) == -1:
                                     newly_added_families.append(family)
                         else:
                             errors.append(f"{filename}: Could not get family name after loading.")
                    else:
                         # If it existed, maybe it was already loaded? Or just invalid.
                         if not font_already_exists_in_folder:
                             errors.append(f"{filename}: Failed to load into application (invalid format?).")
                         else:
                             self.logger.info(f"Font {filename} existed but failed to reload (already loaded or invalid?).")

                except shutil.Error as e:
                    errors.append(f"{filename}: Copy error - {e}")
                except Exception as e:
                    errors.append(f"{filename}: Unexpected error - {e}")

            # Add newly added families to the combo box
            if newly_added_families:
                # Remove potential duplicates before adding
                unique_new_families = sorted(list(set(newly_added_families)))
                self.font_family_combo.blockSignals(True)
                self.font_family_combo.addItems(unique_new_families)
                # Optionally re-sort the whole list? Might be confusing if selection changes.
                # items = [self.font_family_combo.itemText(i) for i in range(self.font_family_combo.count())]
                # self.font_family_combo.clear()
                # self.font_family_combo.addItems(sorted(items))
                self.font_family_combo.blockSignals(False)
                self.logger.info(f"Added to dropdown: {unique_new_families}")

            # Show summary message
            summary_message = f"Processed {len(selected_files)} file(s).\nCopied {copied_count} new font file(s).\nLoaded/Reloaded {loaded_count} font(s) into the application this session."
            if newly_added_families:
                summary_message += f"\nAdded {len(unique_new_families)} new families to the dropdown."
            if errors:
                summary_message += "\n\nErrors encountered:\n" + "\n".join(errors)
            QMessageBox.information(self, self.tr("Add Custom Fonts Result"), summary_message)

    def _open_remove_font_dialog(self):
        dialog = RemoveFontDialog(self)
        dialog.font_removed.connect(self._handle_font_removed)
        dialog.exec()

    def _handle_font_removed(self, family_name):
        """Slot to remove font from the combo box."""
        # Check if the font to be removed is currently selected
        was_selected = (self.font_family_combo.currentText() == family_name)

        index = self.font_family_combo.findText(family_name)
        if index != -1:
            self.font_family_combo.blockSignals(True) # Prevent signals during modification
            self.font_family_combo.removeItem(index)

            # If the removed font was selected, reset to default
            if was_selected:
                default_index = self.font_family_combo.findText("System Default")
                self.font_family_combo.setCurrentIndex(default_index if default_index != -1 else 0)
                # Manually trigger the change signal/logic if needed,
                # as blockSignals was True when we set the index.
                # We might need to emit font_family_changed or call _on_font_family_changed directly
                # Let's call the slot to ensure settings are saved and style applied:
                self._on_font_family_changed(self.font_family_combo.currentText())

            self.font_family_combo.blockSignals(False)
            self.logger.info(f"[SettingsWindow] Removed '{family_name}' from dropdown.")
        else:
            self.logger.warning(f"[SettingsWindow] '{family_name}' not found in dropdown after removal signal.")

    def _on_density_button_clicked(self, button):
        """Handles density radio button clicks."""
        selected_density_id = self.DEFAULT_DENSITY # Default
        for density_id, radio in self.density_radio_buttons.items():
            if button == radio:
                selected_density_id = density_id
                break
        self.logger.info(f"Density selected: {selected_density_id}")
        self.settings.setValue("appearance/density", selected_density_id)
        self.logger.info("Saved density preference.")
        self.density_changed.emit(selected_density_id)
        self.logger.info("density_changed signal emitted.")

    # --- New Zoom Slots ---
    def _on_zoom_slider_changed(self, value):
        """Updates the zoom percentage label as the slider moves."""
        self.zoom_label.setText(f"{value}%")

    def _on_zoom_slider_released(self):
        """Saves the zoom setting and informs user about restart."""
        current_value = self.zoom_slider.value()
        self.logger.info(f"Zoom level chosen: {current_value}%")
        # Save the integer percentage
        self.settings.setValue("appearance/zoom_percent", current_value)
        self.logger.info("Saved zoom preference.")
        # Inform user
        QMessageBox.information(self,
                                self.tr("Restart Required"),
                                self.tr("The application must be restarted for the zoom level change to take effect."))

    # --- Tooltip Methods ---
    def _on_tooltip_enabled_changed(self, enabled):
        """Handles tooltip enable/disable checkbox change."""
        self.logger.info(f"Tooltip enabled: {enabled}")
        self.settings.setValue("general/tooltip_enabled", enabled)
        self.tooltip_enabled_changed.emit(enabled)
        self.logger.info("tooltip_enabled_changed signal emitted.")

    def _on_tooltip_duration_changed(self, index):
        """Handles tooltip duration combo box change."""
        duration_name = self.tooltip_duration_combo.currentText()
        # Get duration from translated durations dictionary
        translated_durations = self.get_translated_durations()
        duration_ms = translated_durations.get(duration_name, 4000)  # Default to 4s
        self.logger.info(f"Tooltip duration selected: {duration_name} ({duration_ms}ms)")
        self.settings.setValue("general/tooltip_duration", duration_ms)
        self.tooltip_duration_changed.emit(duration_ms)
        self.logger.info("tooltip_duration_changed signal emitted.")

    # --- Dashboard Settings Methods ---
    def _on_grid_columns_changed(self, index):
        """Handles grid columns combo box change."""
        columns_text = self.grid_columns_combo.currentText()
        # Extract number from text (e.g., "6 columns" -> 6)
        columns = int(columns_text.split()[0])

        # Save to settings
        self.settings.setValue("dashboard/grid_columns", columns)

        # Emit signal
        self.grid_columns_changed.emit(columns)
        self.logger.info(f"grid_columns_changed signal emitted with value: {columns}")

        # Update available options based on new column count
        self._update_dashboard_options()

    def _on_card_size_changed(self, index):
        """Handles card size combo box change."""
        card_size = self.card_size_combo.currentText().lower()

        # Save to settings
        self.settings.setValue("dashboard/card_size", card_size)

        # Emit signal
        self.card_size_changed.emit(card_size)
        self.logger.info(f"card_size_changed signal emitted with value: {card_size}")

        # Update available options based on new card size
        self._update_dashboard_options()

    def _update_dashboard_options(self):
        """Update dashboard options based on current screen size and settings."""
        try:
            # Get current screen info (try to get parent window for screen detection)
            parent_window = self.parent()
            if not parent_window:
                # If no parent, try to find main window
                for widget in QApplication.topLevelWidgets():
                    if widget.__class__.__name__ == "MainWindow":
                        parent_window = widget
                        break

            # Get current settings
            current_columns = self.settings.value("dashboard/grid_columns", 6, type=int)
            current_card_size = self.settings.value("dashboard/card_size", "small")

            self.logger.debug(f"🔄 Updating dashboard options for screen...")
            self.logger.debug(f"   Current: {current_columns} columns, {current_card_size} cards")

            # Get valid options from screen calculator
            valid_columns = self.screen_calculator.get_valid_columns_for_card_size(current_card_size, parent_window)
            valid_card_sizes = self.screen_calculator.get_valid_card_sizes_for_columns(current_columns, parent_window)

            # Update grid columns combo box
            self.grid_columns_combo.blockSignals(True)
            current_columns_text = self.grid_columns_combo.currentText()
            self.grid_columns_combo.clear()

            columns_items = []
            for cols in valid_columns:
                item_text = f"{cols} columns"
                columns_items.append(item_text)
                self.grid_columns_combo.addItem(item_text)

            # Restore selection or set to current value
            if current_columns_text in columns_items:
                index = columns_items.index(current_columns_text)
                self.grid_columns_combo.setCurrentIndex(index)
            else:
                # Find closest valid option
                target_text = f"{current_columns} columns"
                if target_text in columns_items:
                    index = columns_items.index(target_text)
                    self.grid_columns_combo.setCurrentIndex(index)
                elif columns_items:
                    # Default to first valid option
                    self.grid_columns_combo.setCurrentIndex(0)

            self.grid_columns_combo.blockSignals(False)

            # Update card size combo box
            self.card_size_combo.blockSignals(True)
            current_card_size_index = self.card_size_combo.currentIndex()

            # Enable/disable card size options based on valid sizes
            card_size_options = ['small', 'medium', 'large']
            for i, size in enumerate(card_size_options):
                item = self.card_size_combo.model().item(i)
                if item:
                    if size in valid_card_sizes:
                        item.setEnabled(True)
                        self.logger.debug(f"   ✅ {size.capitalize()} cards: enabled")
                    else:
                        item.setEnabled(False)
                        self.logger.debug(f"   ❌ {size.capitalize()} cards: disabled")

            # If current selection is invalid, switch to first valid option
            if current_card_size not in valid_card_sizes and valid_card_sizes:
                new_card_size = valid_card_sizes[0]
                new_index = card_size_options.index(new_card_size)
                self.card_size_combo.setCurrentIndex(new_index)
                self.settings.setValue("dashboard/card_size", new_card_size)
                self.logger.debug(f"   🔄 Switched to {new_card_size} cards (was invalid)")

            self.card_size_combo.blockSignals(False)

            self.logger.debug(f"✅ Dashboard options updated")
            self.logger.debug(f"   Valid columns: {valid_columns}")
            self.logger.debug(f"   Valid card sizes: {valid_card_sizes}")

        except Exception as e:
            self.logger.error(f"Error updating dashboard options: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def showEvent(self, event):
        """Handle show event to update dashboard options when window is shown."""
        super().showEvent(event)
        # Update dashboard options when window is shown (might be on different screen)
        self._update_dashboard_options()

    def moveEvent(self, event):
        """Handle move event to update dashboard options when window is moved to different screen."""
        super().moveEvent(event)
        # Update dashboard options when window is moved (might be to different screen)
        self._update_dashboard_options()

    def retranslateUi(self):
        """Updates all translatable text in the UI using self.tr()."""
        from app.utils.tooltip_helper import set_tooltip, apply_tab_tooltips, apply_window_tooltip
        import logging

        logger = logging.getLogger(__name__)
        logger.info("Retranslating SettingsWindow UI")

        # Apply tooltip to the window itself
        apply_window_tooltip(self)

        # Apply tooltips to all tabs
        if hasattr(self, 'tab_widget'):
            apply_tab_tooltips(self.tab_widget)

        # Update permissions to show/hide tabs based on developer mode
        self.update_permissions()

        # --- Debugging ---
        test_source_theme = "Theme"
        translated_theme = QCoreApplication.translate("SettingsWindow", test_source_theme)
        self.logger.debug(f"  Context='SettingsWindow', Source='{test_source_theme}', Translated='{translated_theme}'")
        self.logger.debug(f"  self.tr('{test_source_theme}') = '{self.tr(test_source_theme)}'")

        test_source_appearance = "Appearance"
        translated_appearance = QCoreApplication.translate("SettingsWindow", test_source_appearance)
        self.logger.debug(f"  Context='SettingsWindow', Source='{test_source_appearance}', Translated='{translated_appearance}'")
        self.logger.debug(f"  self.tr('{test_source_appearance}') = '{self.tr(test_source_appearance)}'")
        # --- End Debugging ---

        self.setWindowTitle(self.tr("Settings"))

        # General Tab
        self.lang_label.setText(self.tr("Language:"))
        general_tab_index = 0
        appearance_tab_index = 1
        self.tab_widget.setTabText(general_tab_index, self.tr("General"))
        self.tab_widget.setTabText(appearance_tab_index, self.tr("Appearance"))

        # App Colors Tab
        if hasattr(self, 'app_colors_tab_index'):
            self.tab_widget.setTabText(self.app_colors_tab_index, self.tr("App Colors"))

        # Pages Tab
        if hasattr(self, 'pages_tab_index'):
            self.tab_widget.setTabText(self.pages_tab_index, self.tr("Pages"))

        # Apply tooltips to language settings
        set_tooltip(self.lang_label, "Select the application language")
        set_tooltip(self.lang_combo, "Choose the language for the application interface")

        # Set language explanation text
        self.language_explanation_label.setText(self.tr(
            "Note: Calendar widgets (date pickers) use your Windows operating system language settings, "
            "not the application language setting above. This ensures calendar tools display in the "
            "language you're familiar with from your OS."
        ))

        # Tooltip section - use QCoreApplication.translate with "Tooltips" context
        # Get current language for direct translations
        from PySide6.QtCore import QSettings
        settings_obj = QSettings()
        current_language = settings_obj.value("general/language", "en")

        # Import the direct translation dictionaries
        from app.utils.tooltip_helper import GREEK_TRANSLATIONS, CHINESE_TRANSLATIONS, ALBANIAN_TRANSLATIONS

        # Function to get the best translation
        def get_best_translation(text):
            # Try Qt translation first
            qt_translation = QCoreApplication.translate("Tooltips", text)
            if qt_translation != text:
                return qt_translation

            # Fall back to direct translation if needed
            if current_language == "el":
                return GREEK_TRANSLATIONS.get(text, text)
            elif current_language == "zh":
                return CHINESE_TRANSLATIONS.get(text, text)
            elif current_language == "sq":
                return ALBANIAN_TRANSLATIONS.get(text, text)
            return text

        # Set the tooltip section texts with the best translations
        self.tooltip_group_box.setTitle(get_best_translation("Tooltips"))
        self.tooltip_enabled_checkbox.setText(get_best_translation("Enable Tooltips"))
        self.tooltip_duration_label.setText(get_best_translation("Duration:"))

        # Update tooltip duration combo box with translated options
        # Clear and repopulate with translated options
        self.tooltip_duration_combo.blockSignals(True)
        self.tooltip_duration_combo.clear()
        translated_durations = self.get_translated_durations()
        self.tooltip_duration_combo.addItems(list(translated_durations.keys()))

        # Restore previous selection based on saved duration value
        new_index = 1  # Default to Medium (4s)
        if hasattr(self, 'saved_tooltip_duration'):
            for i, (_, ms) in enumerate(translated_durations.items()):
                if ms == self.saved_tooltip_duration:
                    new_index = i
                    break
        self.tooltip_duration_combo.setCurrentIndex(new_index)
        self.tooltip_duration_combo.blockSignals(False)

        # --- Appearance Tab Elements ---
        self.theme_group_box.setTitle(self.tr("Theme"))
        self.radio_light.setText(self.tr("Light"))
        self.radio_dark.setText(self.tr("Dark"))
        self.radio_system.setText(self.tr("System"))

        self.accent_group_box.setTitle(self.tr("Accent Color"))
        for color_id, radio in self.accent_radio_buttons.items():
            display_name = self.ACCENT_COLOR_IDS.get(color_id, color_id)
            radio.setText(self.tr(display_name))

        self.shape_group_box.setTitle(self.tr("Button Shape"))
        for shape_id, radio in self.shape_radio_buttons.items():
            display_name = self.BUTTON_SHAPE_IDS.get(shape_id, shape_id)
            radio.setText(self.tr(display_name))

        self.density_group_box.setTitle(self.tr("Layout Density"))
        for density_id, radio in self.density_radio_buttons.items():
            display_name = self.DENSITY_IDS.get(density_id, density_id)
            radio.setText(self.tr(display_name))

        self.zoom_group_box.setTitle(self.tr("Zoom Level"))
        # Label next to slider doesn't need translation, it's numeric

        self.font_group_box.setTitle(self.tr("Font"))
        self.font_family_label.setText(self.tr("Family:"))
        self.font_size_label.setText(self.tr("Size:"))
        self.add_font_button.setText(self.tr("Add Font..."))
        self.remove_font_button.setText(self.tr("Remove Font..."))

        # Dashboard settings
        if hasattr(self, 'dashboard_group_box'):
            self.dashboard_group_box.setTitle(self.tr("Dashboard"))
            self.grid_columns_label.setText(self.tr("Grid Columns:"))
            self.card_size_label.setText(self.tr("Card Size:"))

        if hasattr(self, 'medical_search_label'):
            self.medical_search_label.setText(self.tr("Search:"))

    def update_permissions(self):
        """Update UI elements based on current permissions."""
        # Check if developer mode is enabled
        is_developer = self.permission_manager.developer_mode

        # Show/hide the App Colors tab based on developer mode
        if hasattr(self, 'app_colors_tab_index'):
            self.tab_widget.setTabVisible(self.app_colors_tab_index, is_developer)

            # If App Colors tab is currently selected and developer mode is disabled,
            # switch to the first tab (General)
            if not is_developer and self.tab_widget.currentIndex() == self.app_colors_tab_index:
                self.tab_widget.setCurrentIndex(0)  # Switch to the first tab

    def _init_pages_tab(self):
        """Initialize the Pages tab with UI settings."""
        from PySide6.QtWidgets import QVBoxLayout, QGroupBox, QFormLayout, QLabel, QComboBox, QSpinBox, QCheckBox

        pages_layout = QVBoxLayout(self.pages_tab)

        # Create Matches section
        matches_group = QGroupBox(self.tr("Matches"))
        matches_layout = QFormLayout(matches_group)

        # Minutes table height setting
        self.minutes_table_height_label = QLabel(self.tr("Minutes table height:"))
        self.minutes_table_height_combo = QComboBox()
        self.minutes_table_height_combo.addItems([
            self.tr("Small"),
            self.tr("Medium"),
            self.tr("Large"),
            self.tr("Extra Large"),
            self.tr("Maximum")
        ])
        self.minutes_table_height_combo.setCurrentText(self.tr("Medium"))  # Default
        self.minutes_table_height_combo.setToolTip(self.tr("Select the height for minute selection popup windows"))

        # Connect signal to save setting when changed
        self.minutes_table_height_combo.currentTextChanged.connect(self._on_minutes_table_height_changed)

        matches_layout.addRow(self.minutes_table_height_label, self.minutes_table_height_combo)

        # Add matches group to main layout
        pages_layout.addWidget(matches_group)

        # Create Roster section
        roster_group = QGroupBox(self.tr("Roster"))
        roster_layout = QFormLayout(roster_group)

        # Outlier Detection settings
        self.outlier_detection_enabled_label = QLabel(self.tr("Enable outlier detection:"))
        self.outlier_detection_enabled_checkbox = QCheckBox()
        # Block signals during creation to prevent unwanted saves
        self.outlier_detection_enabled_checkbox.blockSignals(True)
        # Don't set default values here - they will be loaded from settings in _load_pages_settings()
        self.outlier_detection_enabled_checkbox.setToolTip(self.tr("Enable automatic detection of outlier values in physical measurements"))

        self.outlier_threshold_label = QLabel(self.tr("Outlier detection threshold (%):"))
        self.outlier_threshold_spinbox = QSpinBox()
        # Block signals during creation to prevent unwanted saves
        self.outlier_threshold_spinbox.blockSignals(True)
        self.outlier_threshold_spinbox.setRange(1, 50)
        # Don't set default value here - it will be loaded from settings in _load_pages_settings()
        self.outlier_threshold_spinbox.setSuffix("%")
        self.outlier_threshold_spinbox.setToolTip(self.tr("Percentage threshold for outlier detection. Lower values are more sensitive."))

        # Don't connect signals here - they will be connected after settings are loaded in _load_pages_settings()

        roster_layout.addRow(self.outlier_detection_enabled_label, self.outlier_detection_enabled_checkbox)
        roster_layout.addRow(self.outlier_threshold_label, self.outlier_threshold_spinbox)

        # Add roster group to main layout
        pages_layout.addWidget(roster_group)
        pages_layout.addStretch()

    def _on_minutes_table_height_changed(self, text):
        """Handle minutes table height setting change."""
        self._save_pages_settings()
        # Apply the height change immediately
        self._apply_minutes_table_height()

    def _on_outlier_detection_settings_changed(self):
        """Handle outlier detection settings change."""
        self._save_pages_settings()
        # Apply the outlier detection settings immediately
        self._apply_outlier_detection_settings()

    def _load_pages_settings(self):
        """Load pages settings from QSettings."""
        self.logger.info("_load_pages_settings() called - starting to load pages settings")
        # Load minutes table height setting
        height_setting = self.settings.value("pages/minutes_table_height", "Medium")

        # Set the combo box to the loaded value with signals blocked
        index = self.minutes_table_height_combo.findText(height_setting)
        if index >= 0:
            self.minutes_table_height_combo.blockSignals(True)
            self.minutes_table_height_combo.setCurrentIndex(index)
            self.minutes_table_height_combo.blockSignals(False)

        # Apply the loaded setting
        self._apply_minutes_table_height()

        # Load outlier detection settings
        outlier_enabled = self.settings.value("pages/outlier_detection_enabled", True, type=bool)
        outlier_threshold = self.settings.value("pages/outlier_detection_threshold", 20, type=int)

        print(f"OUTLIER DEBUG: Loading outlier settings: enabled={outlier_enabled}, threshold={outlier_threshold}%")

        # Debug: Check what's actually in QSettings
        print("OUTLIER DEBUG: All QSettings keys:")
        for key in self.settings.allKeys():
            if "outlier" in key.lower():
                value = self.settings.value(key)
                print(f"  {key} = {value}")

        # Debug: Check specific keys with different methods
        print("OUTLIER DEBUG: Direct key checks:")
        print(f"  pages/outlier_detection_enabled = {self.settings.value('pages/outlier_detection_enabled')}")
        print(f"  pages/outlier_detection_threshold = {self.settings.value('pages/outlier_detection_threshold')}")

        # Set the controls to the loaded values with signals blocked to prevent triggering saves
        self.outlier_detection_enabled_checkbox.blockSignals(True)
        self.outlier_threshold_spinbox.blockSignals(True)

        self.outlier_detection_enabled_checkbox.setChecked(outlier_enabled)
        self.outlier_threshold_spinbox.setValue(outlier_threshold)

        self.logger.info(f"Set UI controls: checkbox={self.outlier_detection_enabled_checkbox.isChecked()}, spinbox={self.outlier_threshold_spinbox.value()}%")

        self.outlier_detection_enabled_checkbox.blockSignals(False)
        self.outlier_threshold_spinbox.blockSignals(False)

        # Apply the loaded settings
        self._apply_outlier_detection_settings()

        # Connect signals AFTER settings are loaded to prevent overwriting saved values
        print("OUTLIER DEBUG: Connecting signals after settings loaded")
        self.outlier_detection_enabled_checkbox.toggled.connect(self._on_outlier_detection_settings_changed)
        self.outlier_threshold_spinbox.valueChanged.connect(self._on_outlier_detection_settings_changed)

    def _save_pages_settings(self):
        """Save pages settings to QSettings."""
        # Don't save during initialization
        if getattr(self, '_settings_loading', False):
            print("OUTLIER DEBUG: Skipping save during initialization")
            return

        # Save minutes table height setting
        self.settings.setValue("pages/minutes_table_height", self.minutes_table_height_combo.currentText())

        # Save outlier detection settings
        enabled = self.outlier_detection_enabled_checkbox.isChecked()
        threshold = self.outlier_threshold_spinbox.value()

        print(f"OUTLIER DEBUG: Saving outlier settings: enabled={enabled}, threshold={threshold}%")

        self.settings.setValue("pages/outlier_detection_enabled", enabled)
        self.settings.setValue("pages/outlier_detection_threshold", threshold)

        self.settings.sync()
        print("OUTLIER DEBUG: Settings saved and synced")

    def _apply_minutes_table_height(self):
        """Apply the minutes table height setting to the minute selector widget."""
        height_setting = self.minutes_table_height_combo.currentText()

        # Map setting to actual heights (popup, table, row)
        height_configs = {
            self.tr("Small"): {"popup": 250, "table": 180, "row": 18},
            self.tr("Medium"): {"popup": 300, "table": 220, "row": 20},
            self.tr("Large"): {"popup": 350, "table": 260, "row": 22},
            self.tr("Extra Large"): {"popup": 400, "table": 310, "row": 25},
            self.tr("Maximum"): {"popup": 450, "table": 360, "row": 28}
        }

        config = height_configs.get(height_setting, height_configs[self.tr("Medium")])  # Default to medium

        # Update the MinuteSelectorPopup and MinuteSelectorWidget heights
        try:
            from app.widgets.minute_selector_widget import MinuteSelectorPopup, MinuteSelectorWidget

            # Store the height settings as class attributes for future instances
            MinuteSelectorPopup._configured_height = config["popup"]
            MinuteSelectorWidget._configured_table_height = config["table"]
            MinuteSelectorWidget._configured_row_height = config["row"]

            self.logger.info(f"Applied minutes table height: {height_setting} (popup: {config['popup']}px, table: {config['table']}px, row: {config['row']}px)")
        except ImportError:
            self.logger.warning("Could not import minute selector widgets to apply height setting")

    def _apply_outlier_detection_settings(self):
        """Apply the outlier detection settings to the outlier detector."""
        try:
            from app.utils.outlier_detection import outlier_detector

            # Get current settings
            enabled = self.outlier_detection_enabled_checkbox.isChecked()
            threshold = self.outlier_threshold_spinbox.value()

            # Apply settings to the outlier detector (without saving to QSettings again)
            outlier_detector.enabled = enabled
            outlier_detector.threshold_percentage = threshold

            print(f"OUTLIER DEBUG: Applied settings to outlier_detector: enabled={enabled}, threshold={threshold}%")

            self.logger.info(f"Applied outlier detection settings: enabled={enabled}, threshold={threshold}%")

            # Refresh outlier detection in roster page if it exists
            self._refresh_roster_outlier_detection()

        except ImportError:
            self.logger.warning("Could not import outlier detection module to apply settings")
        except Exception as e:
            self.logger.error(f"Error applying outlier detection settings: {e}")

    def _refresh_roster_outlier_detection(self):
        """Refresh outlier detection in the roster page."""
        try:
            # Try to find the main window and roster page
            main_window = self.parent()
            if main_window and hasattr(main_window, 'roster_page'):
                roster_page = main_window.roster_page
                if hasattr(roster_page, '_apply_outlier_detection'):
                    roster_page._apply_outlier_detection()
                    self.logger.debug("Refreshed outlier detection in roster page")
            elif main_window and hasattr(main_window, 'pages') and 'roster' in main_window.pages:
                roster_page = main_window.pages['roster']
                if hasattr(roster_page, '_apply_outlier_detection'):
                    roster_page._apply_outlier_detection()
                    self.logger.debug("Refreshed outlier detection in roster page")
        except Exception as e:
            self.logger.debug(f"Could not refresh roster outlier detection: {e}")

# Example run block needs modification for standalone test
if __name__ == '__main__':
    QCoreApplication.setOrganizationName("MyCompanyOrName")
    QCoreApplication.setOrganizationDomain("mycompany.com")
    QCoreApplication.setApplicationName("FootDataSettingsTest")
    app = QApplication(sys.argv)
    # Need a dummy QTranslator for tr() to work standalone
    dummy_translator = QTranslator()
    QCoreApplication.installTranslator(dummy_translator)
    settings_win = SettingsWindow()
    settings_win.show()
    sys.exit(app.exec())