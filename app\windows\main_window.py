import sys
from PySide6.QtWidgets import (
    QA<PERSON>lication, Q<PERSON>ainWindow, QWidget,
    QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton,
    QLabel, QFrame, QSizePolicy, QMessageBox,
)
# Restore QCoreApplication and QTranslator for tr() and standalone test
from PySide6.QtCore import Qt, QSize, QCoreApplication, QTranslator, Signal, QSettings, QMimeData, QPoint # Keep Signal if used elsewhere, maybe not needed now
from PySide6.QtGui import QIcon, QAction, QKeySequence, QPixmap, QDrag, QPainter
import logging
import sys # Required for StreamHandler to stdout
import os

# Import permission manager
from app.utils.permission_manager import get_permission_manager


class DraggableDashboardCard(QPushButton):
    """A draggable dashboard card widget."""

    # Signal emitted when card is dropped on another position
    card_dropped = Signal(str, int, int)  # card_name, source_index, target_index

    def __init__(self, card_info, card_index, parent=None):
        super().__init__(parent)
        self.card_info = card_info
        self.card_index = card_index
        self.card_name = card_info['name']
        self.drag_start_position = QPoint()
        self.main_window = parent  # Store reference to main window

        # Set up the card with default medium size (will be updated by parent)
        self.setFixedSize(180, 110)  # Default medium size
        self.clicked.connect(card_info['action'])

        # Enable drag and drop
        self.setAcceptDrops(True)

        # Set up hover effects
        self._setup_hover_effects()

        # Create card content
        self._setup_card_content()

    def _setup_card_content(self):
        """Set up the visual content of the card."""
        # Use flexible layout that adapts to any card size
        # Create card layout with flexible spacing
        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(10, 10, 10, 10)  # Fixed moderate margins
        card_layout.setSpacing(5)  # Fixed moderate spacing

        # Icon label - special handling for Club card
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        if self.card_info['name'] == 'Club':
            # Try to load club logo, fallback to emoji
            club_logo_icon = None
            if self.main_window and hasattr(self.main_window, '_load_club_logo_icon'):
                club_logo_icon = self.main_window._load_club_logo_icon(32)  # Fixed size

            if club_logo_icon:
                icon_label.setPixmap(club_logo_icon.pixmap(32, 32))
                icon_label.setStyleSheet("margin-top: 5px; margin-bottom: 3px;")
            else:
                # Fallback to emoji if logo not available
                icon_label.setText(self.card_info['icon'])
                icon_label.setStyleSheet("font-size: 32px; margin-top: 5px; margin-bottom: 3px;")
        else:
            # Use emoji for other cards
            icon_label.setText(self.card_info['icon'])
            icon_label.setStyleSheet("font-size: 32px; margin-top: 5px; margin-bottom: 3px;")

        # Title label
        title_label = QLabel(self.card_info['name'])
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")

        # Hint label
        hint_label = QLabel(self.card_info['hint'])
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        hint_label.setStyleSheet("font-size: 11px; color: #666666;")
        hint_label.setWordWrap(True)

        # Add labels to layout
        card_layout.addWidget(icon_label)
        card_layout.addWidget(title_label)
        card_layout.addWidget(hint_label)

        # Set the layout
        self.setLayout(card_layout)

    def _setup_hover_effects(self):
        """Set up hover effects for the card."""
        # Store original style properties
        self.original_size = self.size()
        self.is_hovered = False

        # Enable mouse tracking for hover effects
        self.setMouseTracking(True)

    def enterEvent(self, event):
        """Handle mouse enter event for hover effects."""
        super().enterEvent(event)
        if not self.is_hovered:
            self.is_hovered = True
            self._apply_hover_effect()

    def leaveEvent(self, event):
        """Handle mouse leave event for hover effects."""
        super().leaveEvent(event)
        if self.is_hovered:
            self.is_hovered = False
            self._remove_hover_effect()

    def _apply_hover_effect(self):
        """Apply visual hover effect while preserving card size."""
        # CRITICAL: Store current size before applying hover effect
        current_size = self.size()

        # Get current theme and accent color for appropriate hover colors
        from PySide6.QtCore import QSettings
        settings = QSettings()
        current_theme = settings.value("appearance/theme", "light")
        current_accent_color_id = settings.value("appearance/accent_color", "blue")

        # Get accent color hex value
        from main import ACCENT_COLOR_MAP
        accent_color_hex = ACCENT_COLOR_MAP.get(current_accent_color_id, ACCENT_COLOR_MAP["blue"])

        if current_theme == 'dark':
            hover_style = f"""
                QPushButton {{
                    background-color: #4e4e4e;
                    border: 2px solid {accent_color_hex};
                    border-radius: 10px;
                    text-align: center;
                    padding: 0px;
                    color: #ffffff;
                }}
                QLabel {{
                    background-color: transparent;
                    color: #ffffff;
                }}
            """
        else:
            hover_style = f"""
                QPushButton {{
                    background-color: #f0f8ff;
                    border: 2px solid {accent_color_hex};
                    border-radius: 10px;
                    text-align: center;
                    padding: 0px;
                    color: #000000;
                }}
                QLabel {{
                    background-color: transparent;
                    color: #000000;
                }}
            """

        self.setStyleSheet(hover_style)

        # CRITICAL: Restore the exact size after applying hover styling
        self.setFixedSize(current_size)
        self.setMinimumSize(current_size)
        self.setMaximumSize(current_size)
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)

        # Add a subtle "lift" effect by adjusting the widget's position
        self.raise_()  # Bring to front

    def _remove_hover_effect(self):
        """Remove visual hover effect while preserving card size."""
        # CRITICAL: Store current size before restoring styling
        current_size = self.size()

        # Restore original styling by reapplying card styling
        if self.main_window and hasattr(self.main_window, '_apply_card_styling'):
            self.main_window._apply_card_styling(self)

        # CRITICAL: Restore the exact size after applying original styling
        self.setFixedSize(current_size)
        self.setMinimumSize(current_size)
        self.setMaximumSize(current_size)
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)

    def refresh_content(self):
        """Refresh card content after size change."""
        try:
            # Store current card info for recreation
            card_info = self.card_info

            # Clear existing layout more safely
            if self.layout():
                # Remove all widgets from layout
                while self.layout().count():
                    child = self.layout().takeAt(0)
                    if child.widget():
                        child.widget().setParent(None)  # Immediate removal

                # Delete the layout itself
                old_layout = self.layout()
                old_layout.setParent(None)

            # Recreate content with new size, ensuring card_info is available
            self.card_info = card_info  # Make sure card_info is still available
            self._setup_card_content()

        except Exception as e:
            # If refresh fails, log error but don't crash
            if self.main_window and hasattr(self.main_window, 'logger'):
                self.main_window.logger.error(f"Error refreshing card content for {self.card_name}: {e}")
            # Fallback: try to recreate with basic content
            try:
                self._setup_card_content()
            except:
                pass  # If even fallback fails, just continue

    def mousePressEvent(self, event):
        """Handle mouse press for drag initiation."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.position().toPoint()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move for drag detection."""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return

        if ((event.position().toPoint() - self.drag_start_position).manhattanLength() <
            QApplication.startDragDistance()):
            return

        # Start drag operation
        self._start_drag()

    def _start_drag(self):
        """Start the drag operation."""
        drag = QDrag(self)
        mime_data = QMimeData()

        # Store card information in mime data
        mime_data.setText(f"dashboard_card:{self.card_name}:{self.card_index}")
        drag.setMimeData(mime_data)

        # Create drag pixmap (visual representation during drag)
        pixmap = self.grab()
        drag.setPixmap(pixmap)
        drag.setHotSpot(self.drag_start_position)

        # Execute drag
        drop_action = drag.exec(Qt.DropAction.MoveAction)

    def dragEnterEvent(self, event):
        """Handle drag enter event."""
        if event.mimeData().hasText() and event.mimeData().text().startswith("dashboard_card:"):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):
        """Handle drop event."""
        if event.mimeData().hasText() and event.mimeData().text().startswith("dashboard_card:"):
            # Parse dropped card info
            data = event.mimeData().text().split(":")
            if len(data) >= 3:
                dropped_card_name = data[1]
                source_index = int(data[2])

                # Emit signal for parent to handle the drop
                self.card_dropped.emit(dropped_card_name, source_index, self.card_index)
                event.acceptProposedAction()
        else:
            event.ignore()

# Windows/Pages
from app.windows.settings_window import SettingsWindow
from app.windows.club_window import ClubWindow
from app.pages.roster_page import RosterPage # Keep import
from app.pages.options_page import OptionsPage # IMPORT NEW OptionsPage
from app.pages.attendance_page import AttendancePage # IMPORT NEW AttendancePage
from app.pages.evaluations_page import EvaluationsPage # IMPORT NEW EvaluationsPage
from app.pages.matches_page import MatchesPage # IMPORT NEW MatchesPage
from app.windows.developer_settings_window import DeveloperSettingsWindow # IMPORT DeveloperSettingsWindow

# Data Managers
# Remove RosterManager import from here
# from app.data.roster_manager import RosterManager
# --- ADD: Import ClubDataManager and AttendanceManager ---
from app.data.club_data_manager import ClubDataManager
from app.data.attendance_manager import AttendanceManager
from app.data.evaluation_manager import EvaluationManager
# ---------------------------------

# Import the theme application function
import __main__ as main_module

# --- Initialize Logging System ---
# This should be one of the first things your application does.
logging.basicConfig(
    level=logging.DEBUG,  # Set the root logger to DEBUG.
                          # This allows all messages to pass through initially.
                          # Specific loggers can then be controlled by the Dev Window.
                          # If set to INFO, DEBUG messages won't show even if a specific
                          # logger is set to DEBUG.
    format='%(asctime)s - %(name)-25s - %(levelname)-8s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)  # Log messages to the console.
    ]
)
# Optional: Get a specific logger and test it.
# startup_logger = logging.getLogger("app.startup")
# startup_logger.info("Application starting. Logging configured.")
# --- End Logging System Initialization ---

class MainWindow(QMainWindow):
    # Remove request_page signal
    # request_page = Signal(str)

    # Add language_changed signal
    language_changed = Signal()

    def __init__(self):
        super().__init__()
        self.setWindowTitle("FootData - Manager")
        self.setObjectName("MainWindow")
        self.opened_windows = {} # Restore tracking for separate windows
        self.logger = logging.getLogger("app.main_window") # ADDED logger initialization

        # Debug: Log current geometry on startup
        self.logger.debug(f"🔍 MainWindow initial geometry: {self.geometry()}")
        self.logger.debug(f"🔍 MainWindow initial position: {self.pos()}")
        self.logger.debug(f"🔍 MainWindow initial size: {self.size()}")

        # --- ADD: Instantiate Shared Data Managers --- #
        self.club_data_manager = ClubDataManager()
        self.attendance_manager = AttendanceManager()
        self.evaluation_manager = EvaluationManager()
        # -------------------------------------------- #

        # Initialize tooltip manager
        from app.utils.tooltip_manager import TooltipManager
        from app.utils.tooltip_helper import set_tooltip_manager
        self.tooltip_manager = TooltipManager(self)
        set_tooltip_manager(self.tooltip_manager)
        self.logger.info("Tooltip manager initialized and set in helper")

        # Initialize permission manager
        self.permission_manager = get_permission_manager()
        # Connect to permission changes to update UI
        self.permission_manager.permission_changed.connect(self._update_version_indicator)
        self.logger.info("Permission manager initialized")

        # Remove Manager instantiation from MainWindow
        # self.roster_manager = RosterManager()

        self.init_ui()
        self._create_actions()
        self._create_menus()
        self._setup_connections()

        # Apply initial appearance from main module (assuming main loads settings)
        self._apply_global_appearance()

        # Update version indicator
        self._update_version_indicator()

        self.developer_settings_window_instance = None # To keep track of the window instance

    def _create_dashboard_cards(self):
        """Creates the dashboard cards with icons and content hints."""
        cards_data = [
            {
                'name': 'Settings',
                'icon': '⚙️',
                'hint': 'Configure app settings',
                'action': self.open_settings
            },
            {
                'name': 'Club',
                'icon': '🏢',
                'hint': 'Manage club information',
                'action': self.open_club
            },
            {
                'name': 'Roster',
                'icon': '👥',
                'hint': 'Manage team roster',
                'action': self.open_roster
            },
            {
                'name': 'Attendance',
                'icon': '✅',
                'hint': 'Track player attendance',
                'action': self.open_attendance
            },
            {
                'name': 'Evaluations',
                'icon': '⭐',
                'hint': 'Player evaluations',
                'action': self.open_evaluations
            },
            {
                'name': 'Matches',
                'icon': '⚽',
                'hint': 'Manage matches',
                'action': self.open_matches
            },
            {
                'name': 'Options',
                'icon': '🎛️',
                'hint': 'Football rules & zones',
                'action': self.open_options
            }
        ]

        dashboard_cards = []
        self.nav_buttons = {}  # Initialize nav_buttons dictionary

        for i, card_info in enumerate(cards_data):
            # Create draggable card widget
            card_widget = DraggableDashboardCard(card_info, i, self)

            # Connect drag and drop signal
            card_widget.card_dropped.connect(self._handle_card_drop)

            # Apply card styling
            self._apply_card_styling(card_widget)

            dashboard_cards.append({
                'name': card_info['name'],
                'widget': card_widget,
                'action': card_info['action'],
                'index': i
            })
            # Store button reference for retranslation
            self.nav_buttons[card_info['name']] = card_widget

        return dashboard_cards

    def _handle_card_drop(self, dropped_card_name, source_index, target_index):
        """Handle when a card is dropped on another card position."""
        if source_index == target_index:
            return  # No change needed

        self.logger.info(f"Card drop: {dropped_card_name} from index {source_index} to {target_index}")

        # Swap the cards in the dashboard_cards list
        if 0 <= source_index < len(self.dashboard_cards) and 0 <= target_index < len(self.dashboard_cards):
            # Swap the cards
            self.dashboard_cards[source_index], self.dashboard_cards[target_index] = \
                self.dashboard_cards[target_index], self.dashboard_cards[source_index]

            # Update the card indices
            self.dashboard_cards[source_index]['index'] = source_index
            self.dashboard_cards[target_index]['index'] = target_index
            self.dashboard_cards[source_index]['widget'].card_index = source_index
            self.dashboard_cards[target_index]['widget'].card_index = target_index

            # Rebuild the grid layout with new positions
            self._rebuild_dashboard_layout()

            # Save the new layout to settings
            self._save_dashboard_layout()

            self.logger.info(f"Cards swapped successfully: {source_index} ↔ {target_index}")

    def _rebuild_dashboard_layout(self):
        """Rebuild the dashboard grid layout with current card positions."""
        if not hasattr(self, 'dashboard_cards'):
            return

        # Find the dashboard container and layout
        dashboard_container = None
        for card_data in self.dashboard_cards:
            widget = card_data['widget']
            if widget.parent():
                dashboard_container = widget.parent()
                break

        if dashboard_container and hasattr(dashboard_container, 'layout'):
            layout = dashboard_container.layout()
            if isinstance(layout, QGridLayout):
                # Get current column count
                from PySide6.QtCore import QSettings
                settings = QSettings()
                columns = settings.value("dashboard/grid_columns", 6, type=int)

                # Clear existing layout
                for i in reversed(range(layout.count())):
                    layout.itemAt(i).widget().setParent(None)

                # Re-add cards in their new positions
                for i, card_data in enumerate(self.dashboard_cards):
                    row = i // columns
                    col = i % columns
                    layout.addWidget(card_data['widget'], row, col)

                # Preserve current card sizes
                self._preserve_current_card_sizes()

                self.logger.debug("Dashboard layout rebuilt successfully")

    def _save_dashboard_layout(self):
        """Save the current dashboard layout to settings."""
        if not hasattr(self, 'dashboard_cards'):
            return

        # Create layout data - list of card names in their current order
        layout_data = [card_data['name'] for card_data in self.dashboard_cards]

        # Save to QSettings
        from PySide6.QtCore import QSettings
        settings = QSettings()
        settings.setValue("dashboard/card_layout", layout_data)

        self.logger.debug(f"Dashboard layout saved: {layout_data}")

    def _load_dashboard_layout(self):
        """Load and apply saved dashboard layout."""
        if not hasattr(self, 'dashboard_cards'):
            return

        from PySide6.QtCore import QSettings
        settings = QSettings()
        saved_layout = settings.value("dashboard/card_layout", [])

        if not saved_layout:
            self.logger.debug("No saved dashboard layout found, using default order")
            return

        # Create a mapping of card names to card data
        card_map = {card_data['name']: card_data for card_data in self.dashboard_cards}

        # Reorder cards according to saved layout
        reordered_cards = []
        used_names = set()

        # Add cards in saved order
        for card_name in saved_layout:
            if card_name in card_map and card_name not in used_names:
                reordered_cards.append(card_map[card_name])
                used_names.add(card_name)

        # Add any missing cards (new cards not in saved layout)
        for card_data in self.dashboard_cards:
            if card_data['name'] not in used_names:
                reordered_cards.append(card_data)

        # Update the dashboard_cards list and indices
        self.dashboard_cards = reordered_cards
        for i, card_data in enumerate(self.dashboard_cards):
            card_data['index'] = i
            card_data['widget'].card_index = i

        self.logger.debug(f"Dashboard layout loaded: {[card['name'] for card in self.dashboard_cards]}")

    def _apply_card_styling(self, card):
        """Applies theme-aware styling to a dashboard card with accent color support."""
        # Get current theme and accent color from settings (more reliable than main module)
        from PySide6.QtCore import QSettings
        settings = QSettings()

        # Get theme from settings
        current_theme = settings.value("appearance/theme", "light")
        current_accent_color_id = settings.value("appearance/accent_color", "blue")

        # Get accent color hex value
        from main import ACCENT_COLOR_MAP
        accent_color_hex = ACCENT_COLOR_MAP.get(current_accent_color_id, ACCENT_COLOR_MAP["blue"])

        self.logger.debug(f"🎨 _apply_card_styling: theme={current_theme}, accent={current_accent_color_id}, hex={accent_color_hex} (hover border handled by global stylesheet)")

        if current_theme == 'dark':
            card.setStyleSheet(f"""
                QPushButton {{
                    background-color: #3e3e3e;
                    border: 1px solid #555555;
                    border-radius: 8px;
                    text-align: center;
                    padding: 0px;
                    color: #ffffff;
                }}
                QPushButton:pressed {{
                    background-color: #2e2e2e;
                    border-color: #444444;
                }}
                QLabel {{
                    background-color: transparent;
                    color: #ffffff;
                }}
            """)
        else:  # light or default theme
            card.setStyleSheet(f"""
                QPushButton {{
                    background-color: #ffffff;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    text-align: center;
                    padding: 0px;
                    color: #000000;
                }}
                QPushButton:pressed {{
                    background-color: #e8e8e8;
                    border-color: #c0c0c0;
                }}
                QLabel {{
                    background-color: transparent;
                    color: #000000;
                }}
            """)

    def _update_dashboard_card_translations(self):
        """Updates dashboard card text with current translations."""
        if not hasattr(self, 'dashboard_cards'):
            return

        card_translations = {
            'Settings': self.tr('Settings'),
            'Club': self.tr('Club'),
            'Roster': self.tr('Roster'),
            'Attendance': self.tr('Attendance'),
            'Evaluations': self.tr('Evaluations'),
            'Matches': self.tr('Matches'),
            'Options': self.tr('Options')
        }

        hint_translations = {
            'Settings': self.tr('Configure app settings'),
            'Club': self.tr('Manage club information'),
            'Roster': self.tr('Manage team roster'),
            'Attendance': self.tr('Track player attendance'),
            'Evaluations': self.tr('Player evaluations'),
            'Matches': self.tr('Manage matches'),
            'Options': self.tr('Football rules & zones')
        }

        for card_data in self.dashboard_cards:
            card_name = card_data['name']
            card_widget = card_data['widget']

            # Update title and hint labels within the card
            layout = card_widget.layout()
            if layout and layout.count() >= 3:
                title_label = layout.itemAt(1).widget()  # Title is second item
                hint_label = layout.itemAt(2).widget()   # Hint is third item

                if title_label and isinstance(title_label, QLabel):
                    title_label.setText(card_translations.get(card_name, card_name))
                if hint_label and isinstance(hint_label, QLabel):
                    hint_label.setText(hint_translations.get(card_name, ''))

    def _update_dashboard_tooltips(self):
        """Updates dashboard card tooltips."""
        from app.utils.tooltip_helper import set_tooltip

        if not hasattr(self, 'dashboard_cards'):
            return

        tooltip_texts = {
            'Settings': "Open application settings",
            'Club': "View and edit club information",
            'Roster': "Manage player roster",
            'Attendance': "Track player attendance",
            'Evaluations': "Manage player evaluations",
            'Matches': "Manage matches and competitions",
            'Options': "Configure football rules and nationality zones"
        }

        for card_data in self.dashboard_cards:
            card_name = card_data['name']
            card_widget = card_data['widget']
            tooltip_text = tooltip_texts.get(card_name, '')
            if tooltip_text:
                set_tooltip(card_widget, tooltip_text)

    def _refresh_dashboard_styling(self):
        """Refreshes dashboard card styling when theme changes."""
        self.logger.debug("🎨 _refresh_dashboard_styling() called")

        if not hasattr(self, 'dashboard_cards'):
            self.logger.debug("❌ No dashboard cards found, skipping styling refresh")
            return

        # Get current accent color for logging
        from PySide6.QtCore import QSettings
        settings = QSettings()
        current_accent = settings.value("appearance/accent_color", "blue")
        self.logger.debug(f"🎨 Refreshing dashboard styling with accent color: {current_accent}")

        # Apply styling to all cards
        for i, card_data in enumerate(self.dashboard_cards):
            card_widget = card_data['widget']
            card_name = card_data.get('name', f'Card{i}')
            self.logger.debug(f"🎨 Applying styling to card: {card_name}")
            self._apply_card_styling(card_widget)

        # CRITICAL: Preserve card sizes after styling changes
        # Styling changes can reset widget dimensions, so we need to reapply saved sizes
        self._preserve_current_card_sizes()

        self.logger.debug("✅ Dashboard styling refreshed and card sizes preserved")

    def init_ui(self):
        """Sets up the main window UI."""
        self.resize(800, 600)
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # --- Header Section --- (Keep as is)
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        self.header_label = QLabel()
        self.header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.header_label.setStyleSheet("background-color: #e0e0e0; padding: 10px; font-weight: bold;")
        header_layout.addWidget(self.header_label)
        header_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        header_widget.setFixedHeight(50)
        main_layout.addWidget(header_widget)

        # --- Dashboard Menu Section ---
        dashboard_widget = QWidget()
        dashboard_main_layout = QVBoxLayout(dashboard_widget)
        dashboard_main_layout.setContentsMargins(20, 20, 20, 20)
        dashboard_main_layout.setSpacing(0)

        # Create centered container for the grid
        dashboard_container = QWidget()
        dashboard_container.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        dashboard_layout = QGridLayout(dashboard_container)
        dashboard_layout.setContentsMargins(0, 0, 0, 0)
        dashboard_layout.setSpacing(15)

        # Create dashboard cards with icons and content hints
        self.dashboard_cards = self._create_dashboard_cards()

        # Load saved dashboard layout
        self._load_dashboard_layout()

        # Add cards to grid (default 6 columns, flexible rows)
        from PySide6.QtCore import QSettings
        settings = QSettings()
        default_columns = settings.value("dashboard/grid_columns", 6, type=int)

        row, col = 0, 0
        for card_data in self.dashboard_cards:
            card_widget = card_data['widget']
            dashboard_layout.addWidget(card_widget, row, col)
            col += 1
            if col >= default_columns:  # Flexible columns per row
                col = 0
                row += 1

        # Center the dashboard container
        dashboard_main_layout.addWidget(dashboard_container, 0, Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignHCenter)
        dashboard_main_layout.addStretch()

        dashboard_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        main_layout.addWidget(dashboard_widget)

        # Initial card size application (will be corrected by final application)
        from PySide6.QtCore import QSettings
        settings = QSettings()
        saved_card_size = settings.value("dashboard/card_size", "small")
        self.logger.info(f"Applying initial card size after grid layout: {saved_card_size}")

        # Apply the saved card size to all dashboard cards
        if hasattr(self, 'dashboard_cards'):
            size_map = {
                'small': (180, 110),     # Was "Medium"
                'medium': (220, 130),    # Was "Large"
                'large': (260, 150)      # Was "Extra Large"
            }
            width, height = size_map.get(saved_card_size.lower(), (180, 110))
            for card_data in self.dashboard_cards:
                widget = card_data['widget']
                # Use comprehensive size setting to ensure layout respects the size
                widget.setFixedSize(width, height)
                widget.setMinimumSize(width, height)
                widget.setMaximumSize(width, height)
                widget.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
                widget.updateGeometry()
                widget.update()
            self.logger.info(f"Applied initial card size: {saved_card_size} ({width}x{height}) to {len(self.dashboard_cards)} cards")

        # --- Footer Section with Version Indicator ---
        footer_widget = QWidget()
        footer_layout = QHBoxLayout(footer_widget)

        # Main footer label (left side)
        self.footer_label = QLabel()
        self.footer_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.footer_label.setStyleSheet("background-color: #e0e0e0; padding: 5px;")
        footer_layout.addWidget(self.footer_label, 3)  # Give more space to main footer

        # Version indicator (right side)
        self.version_indicator = QLabel()
        self.version_indicator.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.version_indicator.setStyleSheet("background-color: #e0e0e0; padding: 5px; font-weight: bold;")
        footer_layout.addWidget(self.version_indicator, 1)  # Give less space to version indicator

        footer_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        footer_widget.setFixedHeight(50)
        main_layout.addWidget(footer_widget)

        # Set initial text via retranslateUi
        self.retranslateUi()

        # FINAL FIX: Apply card sizes after ALL UI initialization is complete
        # Use QTimer to ensure this happens after the event loop starts
        from PySide6.QtCore import QTimer
        QTimer.singleShot(0, self._apply_final_card_sizes)

        # Remove default page switch
        # self._switch_page("Club")

    def _apply_final_card_sizes(self):
        """Apply card sizes after all UI initialization is complete."""
        from PySide6.QtCore import QSettings
        settings = QSettings()
        saved_card_size = settings.value("dashboard/card_size", "small")

        if hasattr(self, 'dashboard_cards'):
            size_map = {
                'small': (180, 110),     # Was "Medium"
                'medium': (220, 130),    # Was "Large"
                'large': (260, 150)      # Was "Extra Large"
            }
            width, height = size_map.get(saved_card_size.lower(), (180, 110))

            for card_data in self.dashboard_cards:
                widget = card_data['widget']

                # Force the size with maximum enforcement
                widget.setFixedSize(width, height)
                widget.setMinimumSize(width, height)
                widget.setMaximumSize(width, height)
                widget.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)

                # Force immediate geometry update
                widget.updateGeometry()
                widget.update()
                widget.repaint()  # Force immediate repaint

            self.logger.info(f"Final card size application: {saved_card_size} ({width}x{height})")

    # Remove _connect_nav_signals and _switch_page
    # def _connect_nav_signals(self): ...
    # def _switch_page(self, page_name): ...

    # --- Restore separate window opening logic ---
    def open_settings(self):
        win_id = "settings"
        settings_win = self.opened_windows.get(win_id)

        if settings_win is None or not settings_win.isVisible():
            if settings_win is None: # Window doesn't exist or was destroyed
                 settings_win = SettingsWindow()
                 settings_win.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
                 settings_win.destroyed.connect(lambda: self.on_window_destroyed(win_id))
                 self.opened_windows[win_id] = settings_win
                 # Connect signals ONLY when creating the window
                 settings_win.language_changed.connect(self.retranslateUi) # Retranslate main window
                 settings_win.language_changed.connect(self.retranslate_other_windows) # Retranslate other open windows
                 settings_win.language_changed.connect(self.refresh_tooltips) # Refresh tooltips with new language
                 # Connect appearance signals directly to handlers in MainWindow
                 settings_win.theme_changed.connect(self.on_theme_change_requested)
                 settings_win.accent_color_changed.connect(self.on_accent_color_change_requested)
                 settings_win.button_shape_changed.connect(self.on_button_shape_change_requested)
                 settings_win.font_family_changed.connect(self.on_font_family_change_requested)
                 settings_win.font_size_changed.connect(self.on_font_size_change_requested)
                 settings_win.density_changed.connect(self.on_density_change_requested)

                 # Connect tooltip signals
                 settings_win.tooltip_enabled_changed.connect(self.tooltip_manager.set_enabled)
                 settings_win.tooltip_duration_changed.connect(self.tooltip_manager.set_duration)

                 # Connect dashboard signals
                 settings_win.grid_columns_changed.connect(self.on_grid_columns_change_requested)
                 settings_win.card_size_changed.connect(self.on_card_size_change_requested)

            settings_win.show()
            settings_win.activateWindow()
            settings_win.raise_()
        else: # Window exists and is visible
            settings_win.raise_()
            settings_win.activateWindow()

    def open_club(self):
        win_id = "club"

        # If window already exists and is visible, just bring it to front
        club_win = self.opened_windows.get(win_id)
        if club_win is not None and club_win.isVisible():
            club_win.raise_()
            club_win.activateWindow()
            return

        # If window doesn't exist, create it without showing
        if club_win is None:
            try:
                self.logger.info("Creating club window...") # MODIFIED print
                # Tell Qt to disable repaints during initialization
                QApplication.setOverrideCursor(Qt.CursorShape.WaitCursor)

                # Create window but don't show it yet
                club_win = ClubWindow(club_data_manager=self.club_data_manager)
                club_win.setObjectName(win_id)
                club_win.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
                club_win.destroyed.connect(lambda: self.on_window_destroyed(win_id))
                self.opened_windows[win_id] = club_win

                # Connect language_changed signal to retranslateUi
                if hasattr(self, 'language_changed'):
                    self.language_changed.connect(club_win.retranslateUi)
                    self.logger.info("Connected language_changed signal to club_win.retranslateUi")

                # Set initial size to avoid resize flicker
                club_win.resize(700, 550)

                # Apply stylesheet first (this doesn't trigger repaints)
                self._apply_current_stylesheet_to_window(club_win)

                # Disable repaints inside window during initialization
                club_win.setUpdatesEnabled(False)

                # Load data without painting
                try:
                    self.logger.info("Loading club data...") # MODIFIED print
                    club_win._load_club_data()
                    self.logger.info("Club data loaded successfully.") # MODIFIED print
                except Exception as e:
                    self.logger.error(f"Error loading club data: {e}") # MODIFIED print
                    # Continue with showing even if data loading failed

                # Re-enable updates to allow rendering
                club_win.setUpdatesEnabled(True)

                # Now finally show the window
                self.logger.info("Showing club window...") # MODIFIED print
                club_win.show()

                # Restore cursor and tell Qt to process events (renders images correctly)
                QApplication.restoreOverrideCursor()
                QApplication.processEvents()

            except Exception as e:
                self.logger.error(f"Critical error opening club window: {e}")
                if win_id in self.opened_windows:
                    del self.opened_windows[win_id]
                QApplication.restoreOverrideCursor()
        else:
            # Window exists but is hidden - just show it again
            club_win.show()
            club_win.raise_()
            club_win.activateWindow()

    def open_roster(self):
        """Opens the Roster page as a separate window."""
        win_id = "roster"
        roster_win = self.opened_windows.get(win_id)

        if roster_win is None or not roster_win.isVisible():
            if roster_win is None:
                 # RosterPage now accepts club_data_manager and main_window (creates own roster_manager)
                 roster_win = RosterPage(club_data_manager=self.club_data_manager, main_window=self)
                 roster_win.setWindowTitle(self.tr("Roster")) # Set window title
                 roster_win.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
                 roster_win.destroyed.connect(lambda: self.on_window_destroyed(win_id))
                 self.opened_windows[win_id] = roster_win
                 # Apply current stylesheet if needed
                 app_instance = QApplication.instance()
                 if app_instance:
                     roster_win.setStyleSheet(app_instance.styleSheet())
                 # --- ADD: Retranslate immediately on creation --- #
                 roster_win.retranslateUi()
                 # ----------------------------------------------- #

            roster_win.show()
            roster_win.activateWindow()
            roster_win.raise_()
        else:
            roster_win.raise_()
            roster_win.activateWindow()

    def on_window_destroyed(self, win_id):
        """Slot to handle cleanup when a window is destroyed."""
        self.logger.info(f"Window '{win_id}' closed and destroyed.")
        if win_id in self.opened_windows:
            self.opened_windows[win_id] = None # Clear the reference

    # --- Keep appearance change handlers ---
    def on_theme_change_requested(self, theme_mode):
        # ... (Keep implementation, it calls _apply_global_appearance) ...
        self.logger.debug(f"Received theme_changed signal: {theme_mode}")
        try:
            import main as main_module
            main_module.current_theme_mode = theme_mode
            self._apply_global_appearance()
        except (AttributeError, ImportError) as e:
            self.logger.warning(f"Could not update main module for theme change: {e}")
        self.logger.info(f"Theme changed to: {theme_mode}")

    def on_accent_color_change_requested(self, accent_color_id):
        # ... (Keep implementation) ...
        self.logger.debug(f"Received accent_color_changed signal: {accent_color_id}") # MODIFIED print

        # Update the settings directly to ensure it's saved
        from PySide6.QtCore import QSettings
        settings = QSettings()
        settings.setValue("appearance/accent_color", accent_color_id)

        # Try to update main module if available
        try:
            import main as main_module
            main_module.current_accent_color_id = accent_color_id
            self._apply_global_appearance()
        except (AttributeError, ImportError) as e:
            self.logger.warning(f"Could not update main module, refreshing dashboard styling directly: {e}")
            # Fallback: refresh dashboard styling directly
            self._refresh_dashboard_styling()

        self.logger.info(f"Accent color changed to: {accent_color_id}")

    def on_button_shape_change_requested(self, button_shape_id):
        # ... (Keep implementation) ...
        self.logger.debug(f"Received button_shape_changed signal: {button_shape_id}") # MODIFIED print
        try:
            import main as main_module
            main_module.current_button_shape_id = button_shape_id
            self._apply_global_appearance()
        except (AttributeError, ImportError) as e:
            self.logger.warning(f"Could not update main module for button shape change: {e}")
        self.logger.info(f"Button shape changed to: {button_shape_id}")

    def on_font_family_change_requested(self, font_family):
        # ... (Keep implementation) ...
        self.logger.debug(f"Received font_family_changed signal: {font_family}") # MODIFIED print
        try:
            import main as main_module
            main_module.current_font_family = font_family
            self._apply_global_appearance()
        except (AttributeError, ImportError) as e:
            self.logger.warning(f"Could not update main module for font family change: {e}")
        self.logger.info(f"Font family changed to: {font_family}")

    def on_font_size_change_requested(self, font_size):
        # ... (Keep implementation) ...
        self.logger.debug(f"Received font_size_changed signal: {font_size}") # MODIFIED print
        try:
            import main as main_module
            main_module.current_font_size = font_size
            self._apply_global_appearance()
        except (AttributeError, ImportError) as e:
            self.logger.warning(f"Could not update main module for font size change: {e}")
        self.logger.info(f"Font size changed to: {font_size}")

    def on_density_change_requested(self, density):
        # ... (Keep implementation) ...
        self.logger.debug(f"Received density_changed signal: {density}") # MODIFIED print
        try:
            import main as main_module
            main_module.current_density = density
            self._apply_global_appearance()
        except (AttributeError, ImportError) as e:
            self.logger.warning(f"Could not update main module for density change: {e}")
        self.logger.info(f"Density changed to: {density}")

    def on_grid_columns_change_requested(self, columns):
        """Handle grid columns change from settings."""
        self.logger.debug(f"Received grid_columns_changed signal: {columns}")
        # Update the dashboard grid layout
        self._update_dashboard_grid_columns(columns)
        self.logger.info(f"Dashboard grid columns changed to: {columns}")

    def on_card_size_change_requested(self, card_size):
        """Handle card size change from settings."""
        self.logger.debug(f"Received card_size_changed signal: {card_size}")
        # Update the dashboard card sizes
        self._update_dashboard_card_size(card_size)
        self.logger.info(f"Dashboard card size changed to: {card_size}")

    def _update_version_indicator(self):
        """Update the version indicator in the footer with current version level."""
        # Get current version name
        version_name = self.permission_manager.version_name

        # Check if developer mode is enabled
        if self.permission_manager.developer_mode:
            # Show developer mode indicator
            self.version_indicator.setText(self.tr("Version: {0} (Developer Mode)").format(version_name))
            # Use a different color for developer mode
            self.version_indicator.setStyleSheet("background-color: #e0e0e0; padding: 5px; font-weight: bold; color: #ff0000;")
        else:
            # Show regular version indicator
            self.version_indicator.setText(self.tr("Version: {0}").format(version_name))
            # Use default styling
            self.version_indicator.setStyleSheet("background-color: #e0e0e0; padding: 5px; font-weight: bold;")

    def _apply_global_appearance(self):
         """Calls the main module function to apply all current appearance settings."""
         try:
            import main as main_module
            main_module.apply_appearance_settings(
                 main_module.current_theme_mode,
                 main_module.current_accent_color_id,
                 main_module.current_button_shape_id,
                 main_module.current_font_family,
                 main_module.current_font_size,
                 main_module.current_density
            )
            # Apply stylesheet update to all currently open windows
            app_instance = QApplication.instance()
            if app_instance:
                current_stylesheet = app_instance.styleSheet()
                for win_id, window in self.opened_windows.items():
                    if window and window.isVisible(): # Apply only to visible windows
                        # Replace print with log
                        self.logger.debug(f"Applying updated stylesheet to window: {win_id}") # MODIFIED print
                        window.setStyleSheet(current_stylesheet)

            # Refresh dashboard card styling for theme changes
            self._refresh_dashboard_styling()

            self.logger.info("Applied global appearance settings.") # MODIFIED print
         except AttributeError as e:
            self.logger.error(f"Error: Could not access main module attributes: {e}") # MODIFIED print
         except Exception as e:
            self.logger.error(f"Error applying appearance settings: {e}") # MODIFIED print

    def _update_dashboard_grid_columns(self, columns):
        """Update the dashboard grid layout based on new column count."""
        if not hasattr(self, 'dashboard_cards'):
            return

        # Find the dashboard container and layout
        dashboard_container = None
        for card_data in self.dashboard_cards:
            widget = card_data['widget']
            if widget.parent():
                dashboard_container = widget.parent()
                break

        if dashboard_container and hasattr(dashboard_container, 'layout'):
            layout = dashboard_container.layout()
            if isinstance(layout, QGridLayout):
                # Clear existing layout
                for i in reversed(range(layout.count())):
                    layout.itemAt(i).widget().setParent(None)

                # Re-add cards in new grid arrangement with flexible rows
                for i, card_data in enumerate(self.dashboard_cards):
                    row = i // columns
                    col = i % columns
                    layout.addWidget(card_data['widget'], row, col)

                # Calculate total rows needed
                total_cards = len(self.dashboard_cards)
                total_rows = (total_cards + columns - 1) // columns  # Ceiling division

                # Preserve current card sizes by reapplying them
                self._preserve_current_card_sizes()

                self.logger.info(f"Updated dashboard grid to {columns} columns × {total_rows} rows ({total_cards} cards)")

                # Auto-resize window to fit new grid layout
                self._auto_resize_window()

    def _update_dashboard_card_size(self, card_size):
        """Update the dashboard card sizes based on new size setting."""
        if not hasattr(self, 'dashboard_cards'):
            return

        # Define size mappings (removed old small, renamed others)
        size_map = {
            'small': (180, 110),     # Was "Medium"
            'medium': (220, 130),    # Was "Large"
            'large': (260, 150)      # Was "Extra Large"
        }

        width, height = size_map.get(card_size.lower(), (180, 110))  # Default to medium

        # Update all card sizes without content refresh (to prevent corruption)
        for card_data in self.dashboard_cards:
            widget = card_data['widget']
            # Use comprehensive size setting to ensure layout respects the size
            widget.setFixedSize(width, height)
            widget.setMinimumSize(width, height)
            widget.setMaximumSize(width, height)
            widget.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
            widget.updateGeometry()
            widget.update()

        self.logger.info(f"Updated dashboard card size to {card_size} ({width}x{height})")

        # Auto-resize window to fit new card size
        self._auto_resize_window()

    def _preserve_current_card_sizes(self):
        """Preserve current card sizes after grid layout changes."""
        if not hasattr(self, 'dashboard_cards'):
            return

        # Get current card size setting from QSettings
        from PySide6.QtCore import QSettings
        settings = QSettings()
        current_card_size = settings.value("dashboard/card_size", "small")  # Default to new "Small"

        # Define size mappings (same as in _update_dashboard_card_size)
        size_map = {
            'small': (180, 110),     # Was "Medium"
            'medium': (220, 130),    # Was "Large"
            'large': (260, 150)      # Was "Extra Large"
        }

        width, height = size_map.get(current_card_size.lower(), (180, 110))  # Default to medium

        # Reapply the current card size to all cards (no content refresh during grid changes)
        for card_data in self.dashboard_cards:
            widget = card_data['widget']
            # Use comprehensive size setting to ensure layout respects the size
            widget.setFixedSize(width, height)
            widget.setMinimumSize(width, height)
            widget.setMaximumSize(width, height)
            widget.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
            widget.updateGeometry()
            widget.update()

        self.logger.debug(f"Preserved card sizes: {current_card_size} ({width}x{height})")

    def _auto_resize_window(self):
        """Automatically resize window to fit dashboard content optimally."""
        self.logger.debug("🔧 _auto_resize_window() called")
        try:
            if not hasattr(self, 'dashboard_cards') or not self.dashboard_cards:
                self.logger.debug("❌ No dashboard cards found, skipping auto-resize")
                return

            # Get current settings
            from PySide6.QtCore import QSettings
            settings = QSettings()
            columns = settings.value("dashboard/grid_columns", 6, type=int)
            card_size = settings.value("dashboard/card_size", "small")

            # Define card size mappings
            size_map = {
                'small': (180, 110),
                'medium': (220, 130),
                'large': (260, 150)
            }
            card_width, card_height = size_map.get(card_size.lower(), (180, 110))

            # Calculate grid layout
            total_cards = len(self.dashboard_cards)
            total_rows = (total_cards + columns - 1) // columns  # Ceiling division

            # Calculate grid content size
            grid_spacing = 15
            max_cards_in_row = min(columns, total_cards)
            grid_content_width = (max_cards_in_row * card_width) + ((max_cards_in_row - 1) * grid_spacing)
            grid_content_height = (total_rows * card_height) + ((total_rows - 1) * grid_spacing)

            # Add dashboard container margins
            dashboard_margins = 40  # 20px each side
            dashboard_width = grid_content_width + dashboard_margins
            dashboard_height = grid_content_height + dashboard_margins

            # Add UI elements
            header_height = 50
            footer_height = 50

            # Add window chrome and safety margins
            window_chrome_width = 20
            window_chrome_height = 60
            safety_margin_width = 50
            safety_margin_height = 30

            # Calculate optimal window size
            optimal_width = dashboard_width + window_chrome_width + safety_margin_width
            optimal_height = dashboard_height + header_height + footer_height + window_chrome_height + safety_margin_height

            # Get current window size
            current_size = self.size()
            current_width = current_size.width()
            current_height = current_size.height()

            # Calculate size difference
            width_diff = optimal_width - current_width
            height_diff = optimal_height - current_height

            self.logger.debug(f"🔧 Auto-resize calculation:")
            self.logger.debug(f"   Dashboard: {columns} columns × {total_rows} rows, {card_size} cards")
            self.logger.debug(f"   Grid content: {grid_content_width} × {grid_content_height}")
            self.logger.debug(f"   Dashboard area: {dashboard_width} × {dashboard_height}")
            self.logger.debug(f"   Current window: {current_width} × {current_height}")
            self.logger.debug(f"   Optimal window: {optimal_width} × {optimal_height}")
            self.logger.debug(f"   Size difference: {width_diff:+d} × {height_diff:+d}")

            # Only resize if there's a significant difference (more than 5px in either direction)
            resize_threshold = 5  # Reduced threshold for testing
            should_resize = abs(width_diff) > resize_threshold or abs(height_diff) > resize_threshold

            self.logger.debug(f"   Resize threshold: {resize_threshold}px")
            self.logger.debug(f"   Width diff abs: {abs(width_diff)}, Height diff abs: {abs(height_diff)}")
            self.logger.debug(f"   Should resize: {should_resize}")

            if should_resize:
                # Get screen size to ensure we don't exceed screen bounds
                screen = self.screen()
                if screen:
                    screen_geometry = screen.availableGeometry()
                    max_width = screen_geometry.width() - 100  # Leave 100px margin
                    max_height = screen_geometry.height() - 100  # Leave 100px margin

                    # Constrain to screen size
                    final_width = min(optimal_width, max_width)
                    final_height = min(optimal_height, max_height)

                    if final_width != optimal_width or final_height != optimal_height:
                        self.logger.debug(f"   Constrained to screen: {final_width} × {final_height}")
                else:
                    final_width = optimal_width
                    final_height = optimal_height

                # Check current size constraints
                min_size = self.minimumSize()
                max_size = self.maximumSize()
                self.logger.debug(f"🔍 Window size constraints: min={min_size.width()}×{min_size.height()}, max={max_size.width()}×{max_size.height()}")

                # Check if our target size violates constraints
                if final_width < min_size.width() or final_height < min_size.height():
                    self.logger.debug(f"⚠️ Target size violates minimum constraints!")
                    self.logger.debug(f"🔧 Clearing minimum size constraints to allow resize...")
                    # Clear minimum size constraints temporarily
                    self.setMinimumSize(0, 0)

                if final_width > max_size.width() or final_height > max_size.height():
                    self.logger.debug(f"⚠️ Target size violates maximum constraints!")

                # Apply the resize
                self.resize(final_width, final_height)
                self.logger.debug(f"✅ Window resized to: {final_width} × {final_height}")

                # Force update geometry and layout
                self.updateGeometry()
                self.update()

                # Try to find and modify the dashboard container that might be enforcing size
                dashboard_container = None
                central_widget = self.centralWidget()
                if central_widget:
                    # Try to prevent layout from enforcing minimum size
                    central_widget.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
                    central_widget.setMinimumSize(0, 0)

                    # Find dashboard container
                    for child in central_widget.findChildren(QWidget):
                        if hasattr(child, 'layout') and child.layout() and isinstance(child.layout(), QGridLayout):
                            dashboard_container = child
                            break

                    if dashboard_container:
                        self.logger.debug(f"🔍 Found dashboard container, adjusting size policies...")
                        # Temporarily allow the dashboard container to be smaller
                        dashboard_container.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
                        dashboard_container.setMinimumSize(0, 0)
                        dashboard_container.setMaximumSize(16777215, 16777215)

                        # Adjust grid layout constraints
                        grid_layout = dashboard_container.layout()
                        if grid_layout:
                            grid_layout.setSizeConstraint(QGridLayout.SizeConstraint.SetDefaultConstraint)

                    # Force layout update
                    layout = central_widget.layout()
                    if layout:
                        layout.setSizeConstraint(QVBoxLayout.SizeConstraint.SetDefaultConstraint)
                        layout.update()

                # Clear minimum size again (layout might have reset it)
                self.setMinimumSize(0, 0)

                # Try resize again after layout adjustments
                self.resize(final_width, final_height)
                self.logger.debug(f"🔄 Second resize attempt: {final_width} × {final_height}")

                # Check if resize actually took effect
                actual_size = self.size()
                self.logger.debug(f"🔍 Actual window size after resize: {actual_size.width()} × {actual_size.height()}")

                if actual_size.width() != final_width or actual_size.height() != final_height:
                    self.logger.debug(f"⚠️ Resize was overridden! Expected: {final_width}×{final_height}, Got: {actual_size.width()}×{actual_size.height()}")

                    # Try to identify what's constraining the size
                    if actual_size.width() != final_width:
                        self.logger.debug(f"   Width override: {final_width} → {actual_size.width()}")
                    if actual_size.height() != final_height:
                        self.logger.debug(f"   Height override: {final_height} → {actual_size.height()}")

                    # Check if it's a layout constraint
                    central_widget = self.centralWidget()
                    if central_widget:
                        central_min = central_widget.minimumSize()
                        central_max = central_widget.maximumSize()
                        self.logger.debug(f"   Central widget constraints: min={central_min.width()}×{central_min.height()}, max={central_max.width()}×{central_max.height()}")

                # Log the action
                if width_diff > 0 or height_diff > 0:
                    self.logger.info(f"Auto-resized window larger: {current_width}×{current_height} → {final_width}×{final_height}")
                else:
                    self.logger.info(f"Auto-resized window smaller: {current_width}×{current_height} → {final_width}×{final_height}")
            else:
                self.logger.debug(f"⏭️ No resize needed (difference within {resize_threshold}px threshold)")

        except Exception as e:
            self.logger.error(f"Error in auto-resize: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _load_club_logo_icon(self, size=32):
        """Load and resize club logo for dashboard card icon."""
        try:
            # Path to club logo
            logo_path = os.path.join("media", "club_logo", "Club_logo.png")

            if os.path.exists(logo_path):
                # Load and resize the logo
                pixmap = QPixmap(logo_path)
                if not pixmap.isNull():
                    # Scale to desired size while maintaining aspect ratio
                    scaled_pixmap = pixmap.scaled(
                        size, size,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    return QIcon(scaled_pixmap)
                else:
                    self.logger.warning(f"Could not load club logo from {logo_path}")
            else:
                self.logger.info(f"Club logo not found at {logo_path}")
        except Exception as e:
            self.logger.error(f"Error loading club logo: {e}")

        # Return None if logo couldn't be loaded (will use emoji fallback)
        return None

    def refresh_club_logo(self):
        """Refresh the club logo on the dashboard card."""
        if not hasattr(self, 'dashboard_cards'):
            return

        # Find the Club card
        for card_data in self.dashboard_cards:
            if card_data['name'] == 'Club':
                card_widget = card_data['widget']
                layout = card_widget.layout()
                if layout and layout.count() >= 1:
                    icon_label = layout.itemAt(0).widget()  # Icon is first item
                    if icon_label and isinstance(icon_label, QLabel):
                        # Try to load updated club logo
                        club_logo_icon = self._load_club_logo_icon(32)
                        if club_logo_icon:
                            icon_label.setPixmap(club_logo_icon.pixmap(32, 32))
                            icon_label.setStyleSheet("margin-bottom: 5px;")
                            self.logger.info("Club logo refreshed on dashboard")
                        else:
                            # Fallback to emoji if logo not available
                            icon_label.setText('🏢')
                            icon_label.setStyleSheet("font-size: 32px; margin-bottom: 5px;")
                            self.logger.info("Club logo not found, using emoji fallback")
                break

    def retranslateUi(self):
        """Retranslates the main window static elements."""
        from app.utils.tooltip_helper import set_tooltip, apply_tab_tooltips, apply_window_tooltip
        import logging

        logger = logging.getLogger(__name__)
        logger.info("Retranslating MainWindow UI")

        # Apply tooltip to the window itself
        apply_window_tooltip(self)

        self.setWindowTitle(self.tr("FootData"))
        self.header_label.setText(self.tr("FOOT|DATA - Header Placeholder"))
        self.footer_label.setText(self.tr("Footer Information Placeholder"))

        # Update version indicator
        self._update_version_indicator()

        # Get current language
        from PySide6.QtCore import QSettings
        settings = QSettings()
        current_language = settings.value("general/language", "en")

        # Update dashboard cards with translated text
        self._update_dashboard_card_translations()

        # Apply tooltips to dashboard cards
        self._update_dashboard_tooltips()

        # Retranslate menu items
        self._retranslate_menus()

        # Retranslation of open windows is handled in retranslate_other_windows
        self.logger.info("Retranslated MainWindow UI")

    def retranslate_other_windows(self):
        self.logger.debug("MainWindow.retranslate_other_windows called.") # MODIFIED print to logger.debug
        # Iterate through known child windows
        # Use self.opened_windows.items() to get key and value
        for window_name, window in self.opened_windows.items():
            self.logger.debug(f"  Checking window: '{window_name}'") # MODIFIED print to logger.debug
            # Check if window object exists
            if window is None:
                self.logger.debug(f"    Window '{window_name}' is None. Skipping.") # MODIFIED print to logger.debug
                continue # Skip if window reference is None (already closed/destroyed)

            # Check visibility
            is_hidden = window.isHidden()
            self.logger.debug(f"    Window '{window_name}' isHidden = {is_hidden}") # MODIFIED print to logger.debug

            # Check retranslateUi method
            has_retranslate = hasattr(window, 'retranslateUi')
            self.logger.debug(f"    Window '{window_name}' has retranslateUi = {has_retranslate}") # MODIFIED print to logger.debug

            if not is_hidden and has_retranslate:
                self.logger.debug(f"    Attempting to call retranslateUi for '{window_name}'") # MODIFIED print to logger.debug
                try:
                    window.retranslateUi()
                    self.logger.debug(f"    Successfully called retranslateUi for '{window_name}'") # MODIFIED print to logger.debug
                except Exception as e:
                    self.logger.error(f"    ERROR: calling retranslateUi for {window_name}: {e}") # MODIFIED print to logger.error
            elif is_hidden:
                 self.logger.debug(f"    Skipping retranslateUi for '{window_name}' because it is hidden.") # MODIFIED print to logger.debug
            elif not has_retranslate:
                 self.logger.warning(f"    WARNING: Skipping retranslateUi for '{window_name}' because method not found.") # MODIFIED print to logger.warning

        # REMOVED redundant signal emission print here
        # print("language_changed signal emitted.")

    def refresh_tooltips(self):
        """Refresh tooltips after language change."""
        self.logger.info("Refreshing tooltips after language change")

        # Import tooltip helper functions
        from app.utils.tooltip_helper import set_tooltip, apply_tab_tooltips, apply_window_tooltip

        # Apply tooltip to the main window itself
        apply_window_tooltip(self)

        # Refresh tooltips in dashboard cards
        self._update_dashboard_tooltips()

        # Refresh tooltips in open windows
        for window_name, window in self.opened_windows.items():
            if window is None or window.isHidden():
                continue

            # Apply window tooltip
            apply_window_tooltip(window)

            # Apply tab tooltips if window has tabs
            if hasattr(window, 'tab_widget'):
                apply_tab_tooltips(window.tab_widget)
            elif hasattr(window, 'tabs'):
                apply_tab_tooltips(window.tabs)
            elif hasattr(window, 'detail_tabs'):
                apply_tab_tooltips(window.detail_tabs)

            # Check if window has _update_tooltips method
            if hasattr(window, '_update_tooltips'):
                try:
                    self.logger.debug(f"Refreshing tooltips for window: {window_name}")
                    window._update_tooltips()
                except Exception as e:
                    self.logger.error(f"Error refreshing tooltips for {window_name}: {e}")

        self.logger.info("Tooltips refreshed")

    def closeEvent(self, event):
        """Handles the main window close event."""
        # Create a custom message box with translated buttons
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(self.tr("Exit Application"))
        msg_box.setText(self.tr("Are you sure you want to exit FootData?"))
        msg_box.setIcon(QMessageBox.Icon.Question)

        # Create custom buttons with translated text
        yes_button = msg_box.addButton(self.tr("Yes"), QMessageBox.ButtonRole.YesRole)
        no_button = msg_box.addButton(self.tr("No"), QMessageBox.ButtonRole.NoRole)
        msg_box.setDefaultButton(no_button)

        msg_box.exec()

        # Check which button was clicked
        if msg_box.clickedButton() == yes_button:
            self.logger.info("Accepting close event. Application will quit.") # MODIFIED print

            # Save all data explicitly before quitting
            self.logger.info("Saving data before application close...") # MODIFIED print

            # Save club window data and sponsors if open
            if 'club' in self.opened_windows and self.opened_windows['club']:
                club_win = self.opened_windows['club']
                try:
                    if hasattr(club_win, 'save_data'):
                        club_win.save_data()
                        self.logger.info("  Saved club window data") # MODIFIED print

                    # Explicitly save sponsors panel if it exists
                    if hasattr(club_win, 'sponsors_panel') and club_win.sponsors_panel:
                        club_win.sponsors_panel.save_data()
                        self.logger.info("  Saved sponsors panel data") # MODIFIED print
                except Exception as e:
                    self.logger.error(f"  Error saving club/sponsors data: {e}") # MODIFIED print

            # Close the attendance manager
            if hasattr(self, 'attendance_manager'):
                self.logger.info("Closing attendance manager")
                self.attendance_manager.close_db()

            event.accept()
            # Explicitly quit the application to close all windows
            app = QApplication.instance()
            if app:
                 app.quit()
            # No need to explicitly close windows, WA_DeleteOnClose handles them.
            # No need to explicitly close DB managers if handled by window close or aboutToQuit.
        else:
            self.logger.info("Ignoring close event.") # MODIFIED print
            event.ignore()

    def tr(self, text):
        """Helper for translation within MainWindow."""
        return QCoreApplication.translate("MainWindow", text)

    def _create_actions(self):
        """Create actions for the menu bar."""
        # Settings Action
        self.settings_action = QAction(QIcon.fromTheme("preferences-system"), self.tr("&Settings..."), self)
        self.settings_action.setStatusTip(self.tr("Configure application settings"))

        # Club Action
        self.club_action = QAction(QIcon.fromTheme("system-users"), self.tr("&Club Info..."), self)
        self.club_action.setStatusTip(self.tr("View/Edit Club Information"))

        # Roster Action
        self.roster_action = QAction(QIcon.fromTheme("x-office-spreadsheet"), self.tr("&Roster..."), self)
        self.roster_action.setStatusTip(self.tr("Open the Player Roster"))

        # --- ADDED Attendance Action --- #
        self.attendance_action = QAction(QIcon.fromTheme("x-office-calendar"), self.tr("&Attendance..."), self)
        self.attendance_action.setStatusTip(self.tr("Track player attendance"))
        # ------------------------------- #

        # --- ADDED Evaluations Action --- #
        self.evaluations_action = QAction(QIcon.fromTheme("x-office-address-book"), self.tr("&Evaluations..."), self)
        self.evaluations_action.setStatusTip(self.tr("Manage player evaluations"))
        # ------------------------------- #

        # --- ADDED Options Action --- #
        self.options_action = QAction(QIcon.fromTheme("preferences-other"), self.tr("&Options..."), self)
        self.options_action.setStatusTip(self.tr("Configure football rules and nationality zones"))
        # --------------------------- #

        # Exit Action
        self.exit_action = QAction(QIcon.fromTheme("application-exit"), self.tr("E&xit"), self)
        self.exit_action.setShortcut("Ctrl+Q")
        self.exit_action.setStatusTip(self.tr("Exit the application"))

        # Help Action
        self.about_action = QAction(self.tr("&About"), self)
        self.about_action.setStatusTip(self.tr("Show the application's About box"))

        # About Qt Action
        self.about_qt_action = QAction(self.tr("About &Qt"), self)
        self.about_qt_action.setStatusTip(self.tr("Show the Qt library's About box"))

        # Developer Settings Action
        self.open_dev_settings_action = QAction(self.tr("&Developer Settings"), self)
        self.open_dev_settings_action.setShortcut(QKeySequence("Ctrl+Shift+D"))
        self.open_dev_settings_action.setStatusTip(self.tr("Open Developer Settings"))

    def _create_menus(self):
        """Create the main menu bar and add actions."""
        menu_bar = self.menuBar()

        # File Menu
        self.file_menu = menu_bar.addMenu(self.tr("&File"))
        self.file_menu.addAction(self.club_action)
        self.file_menu.addAction(self.roster_action)
        self.file_menu.addAction(self.attendance_action) # Added Attendance Action here
        self.file_menu.addAction(self.evaluations_action) # Added Evaluations Action here
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.settings_action)
        self.file_menu.addAction(self.options_action) # Added Options Action here
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.exit_action)

        # Help Menu
        self.help_menu = menu_bar.addMenu(self.tr("&Help"))
        self.help_menu.addAction(self.about_action)
        self.help_menu.addAction(self.about_qt_action)

        # Developer Menu
        self.dev_menu = menu_bar.addMenu(self.tr("&Developer"))
        self.dev_menu.addAction(self.open_dev_settings_action)

    def _retranslate_menus(self):
        """Update menu titles with current translations."""
        self.logger.info("Retranslating menus")

        # Get the menu bar
        menu_bar = self.menuBar()

        # Check if we have menus
        if not menu_bar or len(menu_bar.actions()) == 0:
            self.logger.warning("No menu bar or empty menu bar found")
            return

        # Update menu titles
        actions = menu_bar.actions()

        # File menu (first menu)
        if len(actions) > 0 and hasattr(self, 'file_menu'):
            file_action = actions[0]
            file_action.setText(self.tr("&File"))

        # Help menu (second menu)
        if len(actions) > 1 and hasattr(self, 'help_menu'):
            help_action = actions[1]
            help_action.setText(self.tr("&Help"))

        # Developer menu (third menu)
        if len(actions) > 2 and hasattr(self, 'dev_menu'):
            dev_action = actions[2]
            dev_action.setText(self.tr("&Developer"))

        # Also update action texts
        if hasattr(self, 'club_action'):
            self.club_action.setText(self.tr("&Club Info..."))

        if hasattr(self, 'roster_action'):
            self.roster_action.setText(self.tr("&Roster..."))

        if hasattr(self, 'attendance_action'):
            self.attendance_action.setText(self.tr("&Attendance..."))

        if hasattr(self, 'settings_action'):
            self.settings_action.setText(self.tr("&Settings..."))

        if hasattr(self, 'options_action'):
            self.options_action.setText(self.tr("&Options..."))

        if hasattr(self, 'exit_action'):
            self.exit_action.setText(self.tr("E&xit"))

        if hasattr(self, 'about_action'):
            self.about_action.setText(self.tr("&About"))

        if hasattr(self, 'about_qt_action'):
            self.about_qt_action.setText(self.tr("About &Qt"))

        if hasattr(self, 'open_dev_settings_action'):
            self.open_dev_settings_action.setText(self.tr("&Developer Settings"))

    def _setup_connections(self):
        """Connect signals and slots for menu actions and button clicks."""
        # Connect menu actions to window opening methods
        self.settings_action.triggered.connect(self.open_settings)
        self.club_action.triggered.connect(self.open_club)
        self.roster_action.triggered.connect(self.open_roster)
        self.attendance_action.triggered.connect(self.open_attendance) # Connect Attendance Action
        self.evaluations_action.triggered.connect(self.open_evaluations) # Connect Evaluations Action
        self.options_action.triggered.connect(self.open_options) # Connect Options Action
        self.exit_action.triggered.connect(self.close)

        # Connect direct button clicks (if they exist in the restored UI)
        if hasattr(self, 'btn_settings'): self.btn_settings.clicked.connect(self.open_settings)
        if hasattr(self, 'btn_club'): self.btn_club.clicked.connect(self.open_club)
        if hasattr(self, 'btn_roster'): self.btn_roster.clicked.connect(self.open_roster)

        # Connect appearance/language signals from settings window
        # This assumes settings window is created/shown via open_settings
        # We might need to establish connections *after* the window is created.
        # Deferring complex signal handling until window creation in open_ methods.

        # Connect Help menu actions
        self.about_action.triggered.connect(self.show_about_dialog)
        self.about_qt_action.triggered.connect(QApplication.aboutQt)

        # Connect Developer menu actions
        self.open_dev_settings_action.triggered.connect(self.open_developer_settings_window)

    # --- ADDED Method to Open Attendance Window --- #
    def open_attendance(self):
        """Opens the Attendance page as a separate window."""
        win_id = "attendance"
        attendance_win = self.opened_windows.get(win_id)

        # Check if Roster window is open to get selected player IDs
        selected_player_ids = []
        roster_win = self.opened_windows.get("roster")
        self.logger.info(f"Roster window found: {roster_win is not None}")

        # DEBUG: Check the player_selections table directly
        try:
            from app.data.roster_manager import RosterManager
            rm = RosterManager()
            # Create a direct SQL query to check the player_selections table
            if rm.conn:
                rm.cursor.execute("SELECT player_id FROM player_selections WHERE selection_type = 'attendance' AND selected = 1")
                rows = rm.cursor.fetchall()
                if rows:
                    direct_ids = [row[0] for row in rows]
                    self.logger.info(f"Direct DB query found {len(direct_ids)} players selected for attendance: {direct_ids}")
                else:
                    self.logger.info("Direct DB query found NO players selected for attendance")
        except Exception as e:
            self.logger.error(f"Error in direct DB query: {e}")

        if roster_win and roster_win.isVisible():
            self.logger.info("Roster window is visible, getting selected players")
            # Get selected players from the roster window
            try:
                # Get players selected for attendance
                self.logger.info("Calling get_players_by_selection('attendance', selected=True)")
                selected_players = roster_win.roster_manager.get_players_by_selection('attendance', selected=True)
                self.logger.info(f"Result from get_players_by_selection: {selected_players}")

                if selected_players:
                    selected_player_ids = [p['player_id'] for p in selected_players]
                    self.logger.info(f"Found {len(selected_player_ids)} players selected for attendance: {selected_player_ids}")
                else:
                    self.logger.info("No players selected for attendance in Roster page")
            except Exception as e:
                self.logger.error(f"Error getting selected players from Roster: {e}")
                import traceback
                self.logger.error(traceback.format_exc())

        if attendance_win is None or not attendance_win.isVisible():
            if attendance_win is None:
                 # Create AttendancePage with no parent to ensure it's a separate window
                 from app.data.roster_manager import RosterManager

                 # Create roster manager but use the shared attendance manager
                 roster_manager = RosterManager()

                 # Pass selected player IDs to the attendance page
                 attendance_win = AttendancePage(
                     roster_manager=roster_manager,
                     attendance_manager=self.attendance_manager,
                     selected_player_ids=selected_player_ids
                 )
                 attendance_win.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
                 attendance_win.destroyed.connect(lambda: self.on_window_destroyed(win_id))

                 # Connect the roster window's selection_changed signal to refresh the attendance window
                 if roster_win and hasattr(roster_win, 'selection_changed'):
                     self.logger.info("Connecting roster selection_changed signal to attendance refresh")
                     try:
                         # Store a reference to the lambda function to prevent it from being garbage collected
                         self._refresh_attendance_lambda = lambda selection_type, selected: self._refresh_attendance_if_needed(selection_type, selected, attendance_win)

                         # Connect the signal to the lambda function
                         roster_win.selection_changed.connect(self._refresh_attendance_lambda)
                         self.logger.info("Signal connection successful")
                     except Exception as e:
                         self.logger.error(f"Error connecting signal: {e}")
                         import traceback
                         self.logger.error(traceback.format_exc())

                 # Ensure the window is a standalone window
                 attendance_win.setWindowFlags(Qt.WindowType.Window)
                 attendance_win.setWindowModality(Qt.WindowModality.NonModal)

                 self.opened_windows[win_id] = attendance_win
                 # Apply current style/font immediately
                 app = QApplication.instance()
                 if app:
                     attendance_win.setStyleSheet(app.styleSheet())
                     attendance_win.setFont(app.font())
                 # Retranslate immediately on creation
                 attendance_win.retranslateUi()

            attendance_win.show()
            attendance_win.activateWindow()
            attendance_win.raise_()
        else:
            # If window is already open, update the selected player IDs
            if selected_player_ids:
                attendance_win.selected_player_ids = selected_player_ids
                attendance_win.refresh_data()  # Use the refresh method to update everything
                self.logger.info(f"Updated attendance window with {len(selected_player_ids)} selected players")

            # Connect the roster window's selection_changed signal to refresh the attendance window
            # (in case it wasn't connected before)
            if roster_win and hasattr(roster_win, 'selection_changed'):
                # Disconnect any existing connections first to avoid duplicates
                try:
                    # Try to disconnect the specific lambda if it exists
                    if hasattr(self, '_refresh_attendance_lambda'):
                        try:
                            roster_win.selection_changed.disconnect(self._refresh_attendance_lambda)
                            self.logger.info("Successfully disconnected existing signal connection")
                        except Exception as e:
                            self.logger.warning(f"Could not disconnect existing signal: {e}")
                except Exception as e:
                    self.logger.warning(f"Error during disconnect: {e}")

                self.logger.info("Reconnecting roster selection_changed signal to attendance refresh")
                try:
                    # Store a reference to the lambda function to prevent it from being garbage collected
                    self._refresh_attendance_lambda = lambda selection_type, selected: self._refresh_attendance_if_needed(selection_type, selected, attendance_win)

                    # Connect the signal to the lambda function
                    roster_win.selection_changed.connect(self._refresh_attendance_lambda)
                    self.logger.info("Signal reconnection successful")
                except Exception as e:
                    self.logger.error(f"Error reconnecting signal: {e}")
                    import traceback
                    self.logger.error(traceback.format_exc())

            attendance_win.raise_()
            attendance_win.activateWindow()
    # ------------------------------------------ #

    # --- ADDED Method to Open Evaluations Window --- #
    def open_evaluations(self):
        """Opens the Evaluations page as a separate window."""
        win_id = "evaluations"
        evaluations_win = self.opened_windows.get(win_id)

        # Check if Roster window is open to get selected player IDs
        selected_player_ids = []
        roster_win = self.opened_windows.get("roster")
        self.logger.info(f"Roster window found: {roster_win is not None}")

        # If roster window is open, get selected player IDs for evaluation
        if roster_win is not None and roster_win.isVisible():
            try:
                # Get player IDs directly from the database
                if roster_win.roster_manager.conn:
                    roster_win.roster_manager.cursor.execute("SELECT player_id FROM player_selections WHERE selection_type = 'evaluation' AND selected = 1")
                    rows = roster_win.roster_manager.cursor.fetchall()
                    if rows:
                        selected_player_ids = [row[0] for row in rows]
                        self.logger.info(f"Found {len(selected_player_ids)} players selected for evaluation")
            except Exception as e:
                self.logger.error(f"Error getting selected player IDs: {e}")

        if evaluations_win is None or not evaluations_win.isVisible():
            if evaluations_win is None:
                 # Create EvaluationsPage with no parent to ensure it's a separate window
                 from app.data.roster_manager import RosterManager

                 # Create roster manager but use the shared evaluation manager
                 roster_manager = RosterManager()

                 # Pass selected player IDs to the evaluations page
                 evaluations_win = EvaluationsPage(
                     roster_manager=roster_manager,
                     evaluation_manager=self.evaluation_manager,
                     selected_player_ids=selected_player_ids
                 )
                 evaluations_win.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
                 evaluations_win.destroyed.connect(lambda: self.on_window_destroyed(win_id))

                 # Ensure the window is a standalone window
                 evaluations_win.setWindowFlags(Qt.WindowType.Window)
                 evaluations_win.setWindowModality(Qt.WindowModality.NonModal)

                 self.opened_windows[win_id] = evaluations_win
                 # Apply current style/font immediately
                 app = QApplication.instance()
                 if app:
                     evaluations_win.setStyleSheet(app.styleSheet())
                     evaluations_win.setFont(app.font())
                 # Retranslate immediately on creation
                 evaluations_win.retranslateUi()

            evaluations_win.show()
            evaluations_win.activateWindow()
            evaluations_win.raise_()
        else:
            # If window is already open, update the selected player IDs
            if selected_player_ids:
                evaluations_win.selected_player_ids = selected_player_ids
                evaluations_win.refresh_data()  # Use the refresh method to update everything
                self.logger.info(f"Updated evaluations window with {len(selected_player_ids)} selected players")

            evaluations_win.raise_()
            evaluations_win.activateWindow()
    # ------------------------------------------ #

    # --- ADDED Method to Open Options Window --- #
    def open_options(self):
        """Opens the Options page as a separate window."""
        win_id = "options"
        options_win = self.opened_windows.get(win_id)

        if options_win is None or not options_win.isVisible():
            if options_win is None:
                 # Create OptionsPage with no parent to ensure it's a separate window
                 options_win = OptionsPage(None)
                 options_win.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
                 options_win.destroyed.connect(lambda: self.on_window_destroyed(win_id))

                 # Ensure the window is a standalone window
                 options_win.setWindowFlags(Qt.WindowType.Window)
                 options_win.setWindowModality(Qt.WindowModality.NonModal)

                 self.opened_windows[win_id] = options_win
                 # Apply current style/font immediately
                 app = QApplication.instance()
                 if app:
                     options_win.setStyleSheet(app.styleSheet())
                     options_win.setFont(app.font())
                 # --- ADD: Retranslate immediately on creation --- #
                 options_win.retranslateUi()
                 # ----------------------------------------------- #

            options_win.show()
            options_win.activateWindow()
            options_win.raise_()
        else:
            options_win.raise_()
            options_win.activateWindow()
    # ------------------------------------------ #

    # --- ADDED Method to Open Matches Window --- #
    def open_matches(self):
        """Opens the Matches page as a separate window."""
        win_id = "matches"
        matches_win = self.opened_windows.get(win_id)

        if matches_win is None or not matches_win.isVisible():
            if matches_win is None:
                 # Create MatchesPage with no parent to ensure it's a separate window
                 matches_win = MatchesPage(None)
                 matches_win.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
                 matches_win.destroyed.connect(lambda: self.on_window_destroyed(win_id))

                 # Ensure the window is a standalone window
                 matches_win.setWindowFlags(Qt.WindowType.Window)
                 matches_win.setWindowModality(Qt.WindowModality.NonModal)

                 self.opened_windows[win_id] = matches_win
                 # Apply current style/font immediately
                 app = QApplication.instance()
                 if app:
                     matches_win.setStyleSheet(app.styleSheet())
                     matches_win.setFont(app.font())
                 # Retranslate immediately on creation if method exists
                 if hasattr(matches_win, 'retranslateUi'):
                     matches_win.retranslateUi()

            matches_win.show()
            matches_win.activateWindow()
            matches_win.raise_()
        else:
            matches_win.raise_()
            matches_win.activateWindow()
    # ------------------------------------------ #

    def _apply_current_stylesheet_to_window(self, window):
        """Apply the current application stylesheet to a child window."""
        app_instance = QApplication.instance()
        if app_instance:
            window.setStyleSheet(app_instance.styleSheet())

    def open_developer_settings_window(self):
        """Opens the Developer Settings window."""
        win_id = "developer_settings"
        # Option 1: Create a new dialog each time (modal or non-modal)
        # If it's a QDialog, it can be shown modally with exec() or non-modally with show()

        # Option 2: Manage a single instance (non-modal)
        if self.developer_settings_window_instance is None or not self.developer_settings_window_instance.isVisible():
            if self.developer_settings_window_instance is None: # Window doesn't exist or was destroyed
                 self.developer_settings_window_instance = DeveloperSettingsWindow(self) # Pass parent
                 # Optional: If it should be deleted when closed and not part of opened_windows tracking
                 # self.developer_settings_window_instance.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
                 # self.developer_settings_window_instance.destroyed.connect(self.on_dev_window_destroyed)

            self.developer_settings_window_instance.show()
            self.developer_settings_window_instance.activateWindow()
            self.developer_settings_window_instance.raise_()
        else: # Window exists and is visible
            self.developer_settings_window_instance.raise_()
            self.developer_settings_window_instance.activateWindow()

    def show_about_dialog(self):
        pass # Add pass to make it a valid empty method for now

    def refresh_tooltips(self):
        """Refresh all tooltips with the current language."""
        self.logger.info("Refreshing tooltips with current language")

        try:
            # Get current language for logging
            settings = QSettings()
            current_language = settings.value("general/language", "en")
            self.logger.info(f"Refreshing tooltips for language: {current_language}")

            # First, refresh tooltips in the main window
            if hasattr(self, 'tooltip_manager') and self.tooltip_manager:
                self.tooltip_manager.refresh_tooltips()
                self.logger.info("Refreshed tooltips in main window")
            else:
                # If no tooltip manager, manually refresh main window tooltips
                from app.utils.tooltip_helper import set_tooltip
                self.logger.info("Manually refreshing main window tooltips")
                set_tooltip(self.btn_settings, "Open application settings")
                set_tooltip(self.btn_club, "View and edit club information")
                set_tooltip(self.btn_roster, "Manage player roster")
                set_tooltip(self.btn_attendance, "Track player attendance")
                set_tooltip(self.btn_evaluations, "Manage player evaluations")
                set_tooltip(self.btn_matches, "Manage matches and competitions")
                set_tooltip(self.btn_options, "Configure football rules and nationality zones")

            # Refresh tooltips in all open windows
            for win_id, window in list(self.opened_windows.items()):
                try:
                    if window and not window.isHidden():
                        self.logger.info(f"Refreshing tooltips for window: {win_id}")

                        # First try using the window's tooltip manager
                        if hasattr(window, 'tooltip_manager') and window.tooltip_manager:
                            window.tooltip_manager.refresh_tooltips()
                            self.logger.info(f"Refreshed tooltips in {win_id} window using tooltip manager")

                        # Then try using the window's refresh_tooltips method
                        elif hasattr(window, 'refresh_tooltips'):
                            window.refresh_tooltips()
                            self.logger.info(f"Called refresh_tooltips on {win_id} window")

                        # Finally try using the window's _update_tooltips method
                        elif hasattr(window, '_update_tooltips'):
                            window._update_tooltips()
                            self.logger.info(f"Called _update_tooltips on {win_id} window")

                        else:
                            self.logger.warning(f"No tooltip refresh method found for {win_id} window")

                except RuntimeError:
                    self.logger.warning(f"Window {win_id} was destroyed during tooltip refresh")
                    # Remove the reference to the destroyed window
                    self.opened_windows[win_id] = None
                except Exception as e:
                    self.logger.error(f"Error refreshing tooltips in {win_id} window: {e}")
                    import traceback
                    self.logger.error(traceback.format_exc())

            self.logger.info("Tooltip refresh complete")
        except Exception as e:
            self.logger.error(f"Error in refresh_tooltips: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def refresh_attendance_window(self, selection_type=None):
        """Refresh the attendance window if it's open.

        This method can be called directly from other components like RosterPage.

        Args:
            selection_type (str, optional): The type of selection that changed.
                                           If 'attendance', will refresh the window.
                                           If None, will refresh regardless of type.
        """
        self.logger.info(f"refresh_attendance_window called with selection_type={selection_type}")

        # Only proceed if selection_type is None or 'attendance'
        if selection_type is not None and selection_type != 'attendance':
            self.logger.info(f"Ignoring refresh for selection_type={selection_type}")
            return

        # Check if attendance window is open
        attendance_win = self.opened_windows.get("attendance")
        if not attendance_win:
            self.logger.info("Attendance window is not open")
            return

        # Check if attendance window is visible
        try:
            is_visible = attendance_win.isVisible()
            self.logger.info(f"Attendance window is visible: {is_visible}")
            if not is_visible:
                return
        except Exception as e:
            self.logger.error(f"Error checking attendance window visibility: {e}")
            return

        # Refresh the attendance window by clicking the refresh button
        self.logger.info("Refreshing attendance window")
        try:
            # First try to click the refresh button
            if hasattr(attendance_win, 'refresh_button'):
                self.logger.info("Clicking attendance_win.refresh_button")
                attendance_win.refresh_button.click()
                self.logger.info("refresh_button clicked successfully")
            # Fallback to calling refresh_data directly if button not available
            elif hasattr(attendance_win, 'refresh_data'):
                self.logger.info("Calling attendance_win.refresh_data()")
                attendance_win.refresh_data()
                self.logger.info("refresh_data() completed successfully")
            else:
                self.logger.error("attendance_win does not have refresh_button or refresh_data method")
        except Exception as e:
            self.logger.error(f"Error refreshing attendance window: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def refresh_evaluations_window(self, selection_type=None):
        """Refresh the evaluations window if it's open.

        This method can be called directly from other components like RosterPage.

        Args:
            selection_type (str, optional): The type of selection that changed.
                                           If 'evaluation', will refresh the window.
                                           If None, will refresh regardless of type.
        """
        self.logger.info(f"refresh_evaluations_window called with selection_type={selection_type}")

        # Only proceed if selection_type is None or 'evaluation'
        if selection_type is not None and selection_type != 'evaluation':
            self.logger.info(f"Ignoring refresh for selection_type={selection_type}")
            return

        # Check if evaluations window is open
        evaluations_win = self.opened_windows.get("evaluations")
        if not evaluations_win:
            self.logger.info("Evaluations window is not open")
            return

        # Check if evaluations window is visible
        try:
            is_visible = evaluations_win.isVisible()
            self.logger.info(f"Evaluations window is visible: {is_visible}")
            if not is_visible:
                return
        except Exception as e:
            self.logger.error(f"Error checking evaluations window visibility: {e}")
            return

        # Refresh the evaluations window
        self.logger.info("Refreshing evaluations window")
        try:
            # Call refresh_data directly
            if hasattr(evaluations_win, 'refresh_data'):
                self.logger.info("Calling evaluations_win.refresh_data()")
                evaluations_win.refresh_data()
                self.logger.info("refresh_data() completed successfully")
            else:
                self.logger.error("evaluations_win does not have refresh_data method")
        except Exception as e:
            self.logger.error(f"Error refreshing evaluations window: {e}")
            import traceback
            self.logger.error(traceback.format_exc())





    # Keep the old method for backward compatibility
    def _refresh_attendance_if_needed(self, selection_type, selected, _):
        """Refresh the attendance window if it's open and the selection type is 'attendance'."""
        self.logger.info(f"_refresh_attendance_if_needed called with selection_type={selection_type}, selected={selected}")

        # Use the new method instead
        self.refresh_attendance_window(selection_type)

    def _developer_settings_closed(self):
        self.logger.debug("Developer Settings window was closed/destroyed.") # ADDED logger.debug
        self.developer_settings_window_instance = None # Clear the reference

# Example run block needs dummy translator
if __name__ == '__main__':
    QCoreApplication.setOrganizationName("MyCompanyOrName")
    QCoreApplication.setOrganizationDomain("mycompany.com")
    QCoreApplication.setApplicationName("FootDataMainTest")
    app = QApplication(sys.argv)
    dummy_translator = QTranslator()
    QCoreApplication.installTranslator(dummy_translator)
    main_win = MainWindow()
    main_win.show()
    sys.exit(app.exec())