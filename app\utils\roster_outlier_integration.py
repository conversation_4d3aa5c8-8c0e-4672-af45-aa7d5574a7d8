"""
Integration utility for adding outlier detection to the roster page.
This module provides functions to highlight outlier values in the roster table.
"""

from PySide6.QtWidgets import QTableWidgetItem
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor

from app.utils.outlier_detection import outlier_detector, OutlierDetector
from app.data.roster_manager import RosterManager


def get_outlier_background_color(value: float, average: float):
    """
    Get the background color for outlier highlighting based on whether
    the value is above or below average.

    Args:
        value: The outlier value
        average: The team average for this field

    Returns:
        QColor for highlighting
    """
    from app.utils.color_constants import get_color

    if value > average:
        return get_color("roster_outlier_high")  # Red for high values
    else:
        return get_color("roster_outlier_low")   # Orange for low values


def get_outlier_border_color():
    """Get the border color for outlier highlighting."""
    return QColor(255, 234, 167)  # Slightly darker yellow border


def apply_outlier_styling(item: QTableWidgetItem, is_outlier: bool):
    """
    Apply outlier styling to a table widget item.

    Args:
        item: The QTableWidgetItem to style
        is_outlier: Whether this item represents an outlier value
    """
    if is_outlier:
        # Set background color for outlier
        item.setBackground(get_outlier_background_color())

        # Set tooltip with warning message
        current_tooltip = item.toolTip()
        outlier_warning = "⚠️ Potential data entry error: Value significantly differs from team average"

        if current_tooltip:
            item.setToolTip(f"{current_tooltip}\n\n{outlier_warning}")
        else:
            item.setToolTip(outlier_warning)
    else:
        # Clear outlier styling
        item.setData(Qt.ItemDataRole.BackgroundRole, None)  # Clear background to default

        # Remove outlier warning from tooltip if present
        current_tooltip = item.toolTip()
        if current_tooltip and "⚠️ Potential data entry error" in current_tooltip:
            # Remove the outlier warning part
            parts = current_tooltip.split("\n\n⚠️ Potential data entry error")
            item.setToolTip(parts[0] if parts[0] else "")


def check_and_highlight_outliers(roster_table, roster_manager: RosterManager):
    """
    Check for outliers in the roster table and apply highlighting.

    Args:
        roster_table: The QTableWidget containing roster data
        roster_manager: RosterManager instance for accessing player data
    """
    try:
        # Get all player data
        players = roster_manager.get_all_players()
        if not players:
            return

        # Detect outliers
        outliers = outlier_detector.detect_outliers(players)

        # Get column mapping for physical fields
        physical_field_columns = {}
        for col in range(roster_table.columnCount()):
            header_item = roster_table.horizontalHeaderItem(col)
            if header_item:
                header_text = header_item.text().lower()

                # Map header text to field names
                field_mapping = {
                    'height': 'height',
                    'weight': 'weight',
                    'waist': 'waist',
                    'hip': 'hip',
                    'neck': 'neck',
                    'wrist': 'wrist',
                    'forearm': 'forearm',
                    'thigh': 'thigh'
                }

                for header_key, field_name in field_mapping.items():
                    if header_key in header_text:
                        physical_field_columns[field_name] = col
                        break

        # Apply highlighting to table cells
        for row in range(roster_table.rowCount()):
            # Get player ID for this row
            player_id_item = None
            for col in range(roster_table.columnCount()):
                header_item = roster_table.horizontalHeaderItem(col)
                if header_item and 'id' in header_item.text().lower():
                    player_id_item = roster_table.item(row, col)
                    break

            if not player_id_item:
                continue

            player_id = player_id_item.text()
            player_outliers = outliers.get(player_id, {})

            # Check each physical field column
            for field_name, col_index in physical_field_columns.items():
                item = roster_table.item(row, col_index)
                if item:
                    is_outlier = player_outliers.get(field_name, False)
                    apply_outlier_styling(item, is_outlier)

    except Exception as e:
        print(f"Error in outlier detection: {e}")


def get_outlier_statistics_text(roster_manager: RosterManager):
    """
    Get a text summary of outlier statistics for all physical fields.

    Args:
        roster_manager: RosterManager instance for accessing player data

    Returns:
        String with outlier statistics summary
    """
    try:
        players = roster_manager.get_all_players()
        if not players:
            return "No player data available for outlier analysis."

        stats_lines = ["Outlier Detection Summary:", "=" * 30]

        for field in OutlierDetector.PHYSICAL_FIELDS:
            field_stats = outlier_detector.get_field_statistics(players, field)

            if field_stats:
                stats_lines.append(f"\n{field.title()}:")
                stats_lines.append(f"  Players: {field_stats['count']}")
                stats_lines.append(f"  Average: {field_stats['mean']:.1f}")
                stats_lines.append(f"  Range: {field_stats['min']:.1f} - {field_stats['max']:.1f}")

                if field_stats['outlier_count'] > 0:
                    stats_lines.append(f"  ⚠️ Outliers: {field_stats['outlier_count']} ({field_stats['outlier_percentage']:.1f}%)")
                else:
                    stats_lines.append(f"  ✓ No outliers detected")

        threshold = outlier_detector.threshold_percentage
        stats_lines.append(f"\nCurrent threshold: {threshold}% deviation from average")

        return "\n".join(stats_lines)

    except Exception as e:
        return f"Error generating outlier statistics: {e}"


def update_outlier_threshold(new_threshold: int):
    """
    Update the outlier detection threshold.

    Args:
        new_threshold: New threshold percentage (10-100)
    """
    outlier_detector.update_threshold(new_threshold)


def get_current_threshold():
    """
    Get the current outlier detection threshold.

    Returns:
        Current threshold percentage
    """
    return outlier_detector.threshold_percentage


def is_physical_field(field_name: str):
    """
    Check if a field name is a physical measurement field.

    Args:
        field_name: Name of the field to check

    Returns:
        True if it's a physical field that should be checked for outliers
    """
    return field_name.lower() in [field.lower() for field in OutlierDetector.PHYSICAL_FIELDS]


def get_outlier_info_for_value(roster_manager: RosterManager, field_name: str, value: float):
    """
    Get outlier information for a specific value.

    Args:
        roster_manager: RosterManager instance
        field_name: Name of the field being checked
        value: Value to check

    Returns:
        Tuple of (is_outlier, description_message)
    """
    try:
        if not is_physical_field(field_name):
            return False, ""

        players = roster_manager.get_all_players()
        if not players:
            return False, ""

        averages = outlier_detector.calculate_team_averages(players)
        average = averages.get(field_name.lower())

        if average is None:
            return False, ""

        return outlier_detector.get_outlier_info(value, average)

    except Exception as e:
        print(f"Error getting outlier info: {e}")
        return False, ""


def create_outlier_warning_message(field_name: str, value: float, average: float, threshold: int):
    """
    Create a user-friendly warning message for an outlier value.

    Args:
        field_name: Name of the field
        value: The outlier value
        average: Team average for this field
        threshold: Current threshold percentage

    Returns:
        Formatted warning message
    """
    percentage_diff = abs(value - average) / average * 100
    direction = "above" if value > average else "below"

    return (
        f"⚠️ Data Entry Warning\n\n"
        f"The {field_name} value ({value}) is {percentage_diff:.1f}% {direction} "
        f"the team average ({average:.1f}).\n\n"
        f"This exceeds the {threshold}% threshold and may indicate a data entry error. "
        f"Please verify this value is correct.\n\n"
        f"Common errors:\n"
        f"• Extra digit (e.g., 1780 instead of 178 for height)\n"
        f"• Missing decimal point\n"
        f"• Wrong unit of measurement"
    )
