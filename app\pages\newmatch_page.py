from PySide6.QtWidgets import (
    Q<PERSON><PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton, QSplitter,
    QGroupBox, QScrollArea, QFormLayout, QLineEdit, QDateEdit,
    QTimeEdit, QComboBox, QSpinBox, QLabel, QRadioButton, QCheckBox, QMessageBox, QGridLayout, QDoubleSpinBox, QTextEdit, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QDialog, QListWidget, QListWidgetItem, QAbstractItemView, QSizePolicy, QMenu
)
from PySide6.QtGui import QAction
from PySide6.QtCore import Qt, QCoreApplication, QSettings, QDate, QTime, QTimer, QSize, Signal, QLocale, QEvent
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QPainterPath
from app.widgets.collapsible_group_box import CollapsibleGroupBox
from app.utils.color_constants import get_color
from app.utils.debug_utils import window_init_debug, progress_validation_debug
from app.utils.locale_utils import apply_locale_to_date_edit, update_all_date_edits_locale
from app.data.club_data_manager import ClubDataManager
from app.data.roster_manager import RosterManager


class ProgressTriangleButton(QPushButton):
    """Custom triangle button that matches the CollapsibleGroupBox style."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._collapsed = False
        self.setFlat(True)
        self.setFixedSize(16, 16)
        self.setCursor(Qt.CursorShape.PointingHandCursor)

    def set_collapsed(self, collapsed):
        """Set the collapsed state and update appearance."""
        self._collapsed = collapsed
        self.update()  # Trigger repaint

    def sizeHint(self):
        """Override sizeHint for a small square button."""
        return QSize(16, 16)

    def paintEvent(self, event):
        """Custom paint event to draw the collapse/expand arrow (same as CollapsibleGroupBox)."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Define the arrow path (same as CollapsibleGroupBox)
        path = QPainterPath()
        if self._collapsed:
            # Right-pointing arrow (collapsed)
            path.moveTo(4, 4)
            path.lineTo(12, 8)
            path.lineTo(4, 12)
            path.lineTo(4, 4)
        else:
            # Down-pointing arrow (expanded)
            path.moveTo(4, 4)
            path.lineTo(12, 4)
            path.lineTo(8, 12)
            path.lineTo(4, 4)

        # Draw the arrow (same style as CollapsibleGroupBox)
        painter.setPen(QPen(QColor(100, 100, 100), 1.5))
        painter.setBrush(QBrush(QColor(220, 220, 220)))
        painter.drawPath(path)

        painter.end()


class SplitCellWidget(QWidget):
    """Custom widget for Goals and Assists columns with invisible left/right click areas."""

    value_changed = Signal(int)  # Signal emitted when value changes

    def __init__(self, initial_value=0, parent=None):
        super().__init__(parent)
        self.value = initial_value
        # Don't set fixed height - let it adapt to table row height

        # Create layout with zero spacing
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)  # No margins at all
        layout.setSpacing(0)  # No spacing between widgets

        # Create left button (decrease) - 30% of width
        self.left_button = QPushButton()
        self.left_button.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: #888888;
                font-size: 10px;
                font-weight: bold;
                padding: 0px;
                margin: 0px;
                min-width: 12px;
            }
            QPushButton:hover {
                background: rgba(200, 200, 200, 0.3);
            }
            QPushButton:pressed {
                background: rgba(200, 200, 200, 0.5);
            }
        """)
        self.left_button.setText("−")  # Minus symbol for decrease
        self.left_button.clicked.connect(self._decrease_value)
        # Allow button to expand vertically to fill cell height
        self.left_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Create center label (value display) - 40% of width
        self.value_label = QLabel(str(self.value))
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setStyleSheet("""
            QLabel {
                background: transparent;
                color: black;
                padding: 0px;
                margin: 0px;
                min-width: 16px;
            }
        """)
        # Ensure the label can expand vertically to center content
        self.value_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Create right button (increase) - 30% of width
        self.right_button = QPushButton()
        self.right_button.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: #888888;
                font-size: 10px;
                font-weight: bold;
                padding: 0px;
                margin: 0px;
                min-width: 12px;
            }
            QPushButton:hover {
                background: rgba(200, 200, 200, 0.3);
            }
            QPushButton:pressed {
                background: rgba(200, 200, 200, 0.5);
            }
        """)
        self.right_button.setText("+")  # Plus symbol for increase
        self.right_button.clicked.connect(self._increase_value)
        # Allow button to expand vertically to fill cell height
        self.right_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Add widgets to layout with proportional stretch (30%, 40%, 30%)
        layout.addWidget(self.left_button, 3)   # 30% - left button
        layout.addWidget(self.value_label, 4)   # 40% - center value
        layout.addWidget(self.right_button, 3)  # 30% - right button

    def _decrease_value(self):
        """Decrease value by 1 (minimum 0)."""
        if self.value > 0:
            self.value -= 1
            self.value_label.setText(str(self.value))
            self.value_changed.emit(self.value)

    def _increase_value(self):
        """Increase value by 1."""
        print(f"DEBUG: SplitCellWidget _increase_value called, current value: {self.value}")

        # Debug parent hierarchy
        parent = self.parent()
        print(f"DEBUG: Direct parent: {type(parent)} - {parent}")
        if parent:
            grandparent = parent.parent()
            print(f"DEBUG: Grandparent: {type(grandparent)} - {grandparent}")
            if grandparent:
                great_grandparent = grandparent.parent()
                print(f"DEBUG: Great-grandparent: {type(great_grandparent)} - {great_grandparent}")

        # Try to find the NewMatchPage by walking up the parent hierarchy
        current_widget = self
        newmatch_page = None
        depth = 0
        while current_widget and depth < 10:  # Safety limit
            current_widget = current_widget.parent()
            depth += 1
            print(f"DEBUG: Level {depth}: {type(current_widget)} - {current_widget}")
            if hasattr(current_widget, '_validate_player_can_have_events'):
                newmatch_page = current_widget
                print(f"DEBUG: Found NewMatchPage at level {depth}")
                break

        if newmatch_page:
            print("DEBUG: Found NewMatchPage with validation method")
            # Get the row of this widget by checking the table
            row = None
            table = newmatch_page.players_table
            print(f"DEBUG: Searching through {table.rowCount()} rows and {table.columnCount()} columns")

            for r in range(table.rowCount()):
                for c in range(table.columnCount()):
                    cell_widget = table.cellWidget(r, c)
                    if cell_widget == self:
                        row = r
                        print(f"DEBUG: Found widget at row {r}, column {c}")
                        break
                if row is not None:
                    break

            print(f"DEBUG: SplitCellWidget increase clicked, found row: {row}")

            if row is not None:
                # Validate if player can have events
                validation_result = newmatch_page._validate_player_can_have_events(row)
                print(f"DEBUG: Validation result for row {row}: {validation_result}")
                if not validation_result:
                    print(f"DEBUG: Validation failed for row {row}, blocking increase")
                    return  # Don't increase if player didn't play
                else:
                    print(f"DEBUG: Validation passed for row {row}, allowing increase")
            else:
                print("DEBUG: Could not find row for widget, allowing increase (fallback)")
        else:
            print("DEBUG: No NewMatchPage found, allowing increase (fallback)")

        self.value += 1
        self.value_label.setText(str(self.value))
        self.value_changed.emit(self.value)

    def set_value(self, value):
        """Set the current value."""
        new_value = max(0, int(value))

        # If trying to set a non-zero value, validate that player can have events
        if new_value > 0:
            # Try to find the NewMatchPage by walking up the parent hierarchy
            current_widget = self
            newmatch_page = None
            depth = 0
            while current_widget and depth < 10:  # Safety limit
                current_widget = current_widget.parent()
                depth += 1
                if hasattr(current_widget, '_validate_player_can_have_events'):
                    newmatch_page = current_widget
                    break

            if newmatch_page:
                # Get the row of this widget by checking the table
                row = None
                table = newmatch_page.players_table
                for r in range(table.rowCount()):
                    for c in range(table.columnCount()):
                        cell_widget = table.cellWidget(r, c)
                        if cell_widget == self:
                            row = r
                            break
                    if row is not None:
                        break

                if row is not None:
                    # Validate if player can have events
                    if not newmatch_page._validate_player_can_have_events(row):
                        print(f"DEBUG: set_value blocked for row {row}, keeping value at 0")
                        new_value = 0  # Force to 0 if player didn't play

        self.value = new_value
        self.value_label.setText(str(self.value))

    def get_value(self):
        """Get the current value."""
        return self.value


class NewMatchPage(QWidget):
    """A page for creating a new match."""

    # Signal emitted when the window is closed
    window_closed = Signal()

    def __init__(self, parent=None, club_data_manager=None):
        super().__init__(parent)
        self.setObjectName("NewMatchPage")
        self.setWindowTitle(self.tr("New Match"))
        # Set window flags to create a standalone window
        self.setWindowFlags(Qt.WindowType.Window)
        # Ensure it behaves as a normal window that doesn't block other windows
        self.setWindowModality(Qt.WindowModality.NonModal)

        # Hide window during construction to prevent flash
        self.setVisible(False)

        # Use provided club data manager or create new one
        self.club_data_manager = club_data_manager if club_data_manager else ClubDataManager()

        # Initialize roster manager
        self.roster_manager = RosterManager()

        # Initialize UI
        self._init_ui()

        # Restore window state immediately during construction but before showing
        # This prevents the flash by setting correct size before window becomes visible
        self._restore_window_state()

        # Set up field validation after UI is created
        QTimer.singleShot(100, self._setup_field_validation)

    def _init_ui(self):
        """Initialize the user interface."""
        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create top section with buttons (no border)
        self._create_top_section(main_layout)

        # Create vertical splitter for left and right panels
        self._create_splitter_section(main_layout)

    def _create_top_section(self, main_layout):
        """Create the top section with basic buttons."""
        # Create horizontal layout for buttons
        button_layout = QHBoxLayout()

        # Create buttons for new match actions
        self.save_match_btn = QPushButton(self.tr("Save Match"))
        self.cancel_btn = QPushButton(self.tr("Cancel"))

        # Add buttons to layout
        button_layout.addWidget(self.save_match_btn)
        button_layout.addWidget(self.cancel_btn)

        # Add spacer to push buttons to the left and separate from right buttons
        button_layout.addStretch()

        # Player management buttons (initially hidden, aligned to right side of splitter)
        self.add_player_btn = QPushButton(self.tr("Add Player"))
        self.add_player_btn.hide()  # Initially hidden
        button_layout.addWidget(self.add_player_btn)

        self.remove_player_btn = QPushButton(self.tr("Remove Player"))
        self.remove_player_btn.hide()  # Initially hidden
        button_layout.addWidget(self.remove_player_btn)

        self.clear_all_subs_btn = QPushButton(self.tr("Clear All Subs"))
        self.clear_all_subs_btn.hide()  # Initially hidden
        button_layout.addWidget(self.clear_all_subs_btn)

        # Connect button signals
        self.save_match_btn.clicked.connect(self._save_match)
        self.cancel_btn.clicked.connect(self._cancel)
        self.add_player_btn.clicked.connect(self._on_add_player_clicked)
        self.remove_player_btn.clicked.connect(self._on_remove_player_clicked)
        self.clear_all_subs_btn.clicked.connect(self._on_clear_all_subs_clicked)

        # Add button layout to main layout
        main_layout.addLayout(button_layout)

    def _create_splitter_section(self, main_layout):
        """Create the vertical splitter with left and right panels."""
        # Create vertical splitter
        self.splitter = QSplitter(Qt.Orientation.Horizontal)

        # Create left panel with sections
        left_panel = self._create_left_panel()

        # Create right panel with tabs
        right_panel = self._create_right_panel()

        # Add panels to splitter
        self.splitter.addWidget(left_panel)
        self.splitter.addWidget(right_panel)

        # Set initial splitter proportions (50/50)
        self.splitter.setSizes([400, 600])

        # Connect splitter signal to save state when moved
        self.splitter.splitterMoved.connect(self._save_splitter_state)

        # Add splitter to main layout
        main_layout.addWidget(self.splitter)

    def _create_left_panel(self):
        """Create the left panel with sections."""
        # Create scroll area for the left panel
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.Shape.NoFrame)

        # Create content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # Create sections
        sections = [
            "New Match Progress",
            "Information",
            "Teams and Score",
            "Statistics",
            "Advanced Statistics",
            "Venue",
            "Officials"
        ]

        for section_name in sections:
            section_group = QGroupBox(self.tr(section_name))

            if section_name == "New Match Progress":
                # Create New Match Progress section with status indicators
                section_layout = self._create_progress_section()
            elif section_name == "Information":
                # Create Information section with form fields
                section_layout = self._create_information_section()
            elif section_name == "Teams and Score":
                # Create Teams and Score section with form fields
                section_layout = self._create_teams_score_section()
            elif section_name == "Statistics":
                # Create Statistics section using CollapsibleGroupBox
                self.stats_group = CollapsibleGroupBox(self.tr("Match Statistics"))
                stats_content = self._create_statistics_section()
                self.stats_group.add_widget(stats_content)
                content_layout.addWidget(self.stats_group)
                continue  # Skip the regular section_group creation
            elif section_name == "Advanced Statistics":
                # Create Advanced Statistics section using CollapsibleGroupBox
                self.adv_stats_group = CollapsibleGroupBox(self.tr("Advanced Match Statistics"))
                adv_stats_content = self._create_advanced_statistics_section()
                self.adv_stats_group.add_widget(adv_stats_content)
                content_layout.addWidget(self.adv_stats_group)
                continue  # Skip the regular section_group creation
            elif section_name == "Venue":
                # Create Venue section using CollapsibleGroupBox
                self.venue_group = CollapsibleGroupBox(self.tr("Venue"))
                venue_content = self._create_venue_section()
                self.venue_group.add_widget(venue_content)
                content_layout.addWidget(self.venue_group)
                continue  # Skip the regular section_group creation
            elif section_name == "Officials":
                # Create Officials section using CollapsibleGroupBox
                self.officials_group = CollapsibleGroupBox(self.tr("Officials"))
                officials_content = self._create_officials_section()
                self.officials_group.add_widget(officials_content)
                content_layout.addWidget(self.officials_group)
                continue  # Skip the regular section_group creation
            else:
                # Add placeholder content for other sections
                section_layout = QVBoxLayout()
                placeholder_widget = QWidget()
                placeholder_widget.setMinimumHeight(50)
                placeholder_widget.setStyleSheet("background-color: #ffffff; border: 1px solid #ddd;")
                section_layout.addWidget(placeholder_widget)

            section_group.setLayout(section_layout)
            content_layout.addWidget(section_group)

        # Add stretch to push sections to top
        content_layout.addStretch()

        # Set the content widget to the scroll area
        scroll_area.setWidget(content_widget)

        return scroll_area

    def _create_right_panel(self):
        """Create the right panel with tabs."""
        # Create tab widget
        tab_widget = QTabWidget()

        # Create tabs
        tabs = [
            ("Players", self._create_players_tab()),
            ("Opponent", self._create_opponent_tab()),
            ("Timeline", self._create_timeline_tab()),
            ("Tactics", self._create_tactics_tab()),
            ("Staff", self._create_staff_tab())
        ]

        # Add tabs to widget and store tab indices
        self.players_tab_index = -1
        for i, (tab_name, tab_content) in enumerate(tabs):
            tab_index = tab_widget.addTab(tab_content, self.tr(tab_name))
            # Store the Players tab index for later reference
            if tab_name == "Players":
                self.players_tab_index = tab_index

        # Initially hide the Players tab (will be shown when Basic Information is complete)
        if self.players_tab_index >= 0:
            tab_widget.setTabVisible(self.players_tab_index, False)

        # Connect tab change signal to show/hide player management buttons
        tab_widget.currentChanged.connect(self._on_tab_changed)

        # Store reference to tab widget for later use
        self.tab_widget = tab_widget

        return tab_widget

    def _create_players_tab(self):
        """Create the Players tab content with player table."""
        players_widget = QWidget()
        players_layout = QVBoxLayout(players_widget)
        players_layout.setContentsMargins(5, 5, 5, 5)

        # Create players table
        self.players_table = QTableWidget()
        self.players_table.setColumnCount(77)  # Added all advanced statistics columns + 12 performance columns

        # Define column headers and their properties
        self.column_headers = [
            self.tr("Lineup"),
            self.tr("No"),
            self.tr("Player"),
            self.tr("Pos"),
            self.tr("Events"),
            self.tr("C"),
            self.tr("Sub Out"),
            self.tr("Sub In"),
            self.tr("Min"),
            self.tr("Y"),
            self.tr("R"),
            self.tr("Y1"),
            self.tr("Y2"),
            self.tr("Rr"),
            self.tr("R1"),
            self.tr("Goals"),
            self.tr("Goals min"),
            self.tr("Assists"),
            self.tr("Assists min"),
            self.tr("Inj"),
            self.tr("InjR"),
            # Advanced Statistics columns (hidden by default)
            self.tr("Attempts"),
            self.tr("On Target"),
            self.tr("X-Goals"),
            self.tr("X-Assists"),
            self.tr("Shot Creating Actions"),
            self.tr("Goal Creating Actions"),
            self.tr("Passes Short"),
            self.tr("Passes Short Completed"),
            self.tr("Passes Short %"),
            self.tr("Passes Medium"),
            self.tr("Passes Medium Completed"),
            self.tr("Passes Medium %"),
            self.tr("Passes Long"),
            self.tr("Passes Long Completed"),
            self.tr("Passes Long %"),
            self.tr("Passes"),
            self.tr("Passes Completed"),
            self.tr("Passes %"),
            self.tr("Key Passes"),
            self.tr("Through Balls"),
            self.tr("Passes 3/3"),
            self.tr("Passes PA"),
            self.tr("Progressive Passes"),
            self.tr("Switch Play"),
            self.tr("Passes Offside"),
            self.tr("Passes Blocked"),
            self.tr("Total Crosses"),
            self.tr("Crosses to Penalty Area"),
            self.tr("Total Passing Distance"),
            self.tr("Progressive Passing Distance"),
            self.tr("Progressive Carries"),
            self.tr("Take-ons Attempts"),
            self.tr("Take-ons Successful"),
            self.tr("Aerial Duels Won"),
            self.tr("Aerial Duels Lost"),
            self.tr("Aerial Duels Won %"),
            self.tr("Touches"),
            self.tr("Tackles"),
            self.tr("Tackles Won"),
            self.tr("Tackles %"),
            self.tr("Interceptions"),
            self.tr("Clearances"),
            self.tr("Opp Shots Blocked"),
            self.tr("Opp Passes Blocked"),
            self.tr("Mistakes Lead to Opp Shot"),
            # Performance columns (hidden by default)
            self.tr("Passing"),
            self.tr("Creativity"),
            self.tr("Dribbling-Carrying"),
            self.tr("Marking"),
            self.tr("Positioning"),
            self.tr("Aerial"),
            self.tr("Speed"),
            self.tr("Stamina"),
            self.tr("Power"),
            self.tr("Decision-Making"),
            self.tr("Concentration"),
            self.tr("Work-Rate")
        ]
        self.players_table.setHorizontalHeaderLabels(self.column_headers)

        # Configure table properties
        self.players_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.players_table.setAlternatingRowColors(True)
        self.players_table.setSortingEnabled(False)  # Disable header sorting

        # Set up header for draggable column widths and allow reordering
        header = self.players_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)  # Allow dragging to resize
        header.setSectionsMovable(True)  # Allow column reordering

        # Set up context menu for column headers
        header.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        header.customContextMenuRequested.connect(self._show_header_context_menu)

        # Set initial column widths
        self.players_table.setColumnWidth(self._get_column_index("Lineup"), 60)   # Lineup checkbox
        self.players_table.setColumnWidth(self._get_column_index("No"), 50)       # Shirt number
        self.players_table.setColumnWidth(self._get_column_index("Player"), 200)  # Player name
        self.players_table.setColumnWidth(self._get_column_index("Pos"), 60)      # Position
        self.players_table.setColumnWidth(self._get_column_index("Events"), 70)   # Events
        self.players_table.setColumnWidth(self._get_column_index("C"), 40)        # Captain
        self.players_table.setColumnWidth(self._get_column_index("Sub Out"), 70)  # Sub Out
        self.players_table.setColumnWidth(self._get_column_index("Sub In"), 70)   # Sub In
        self.players_table.setColumnWidth(self._get_column_index("Min"), 60)      # Minutes played
        self.players_table.setColumnWidth(self._get_column_index("Y"), 40)        # Yellow cards
        self.players_table.setColumnWidth(self._get_column_index("R"), 40)        # Red cards
        self.players_table.setColumnWidth(self._get_column_index("Y1"), 50)       # 1st yellow minute
        self.players_table.setColumnWidth(self._get_column_index("Y2"), 50)       # 2nd yellow minute
        self.players_table.setColumnWidth(self._get_column_index("Rr"), 60)       # Red card reason
        self.players_table.setColumnWidth(self._get_column_index("R1"), 50)       # Red card minute
        self.players_table.setColumnWidth(self._get_column_index("Goals"), 40)      # Goals - proportional layout
        self.players_table.setColumnWidth(self._get_column_index("Goals min"), 80) # Goals minutes - comma separated
        self.players_table.setColumnWidth(self._get_column_index("Assists"), 40)   # Assists - proportional layout
        self.players_table.setColumnWidth(self._get_column_index("Assists min"), 80) # Assists minutes - comma separated
        self.players_table.setColumnWidth(self._get_column_index("Inj"), 50)       # Injury minute
        self.players_table.setColumnWidth(self._get_column_index("InjR"), 120)     # Injury reason

        # Set column widths for advanced statistics columns
        advanced_stats_columns = [
            ("Attempts", 70), ("On Target", 70), ("X-Goals", 70), ("X-Assists", 70),
            ("Shot Creating Actions", 80), ("Goal Creating Actions", 80),
            ("Passes Short", 80), ("Passes Short Completed", 80), ("Passes Short %", 70),
            ("Passes Medium", 80), ("Passes Medium Completed", 80), ("Passes Medium %", 70),
            ("Passes Long", 80), ("Passes Long Completed", 80), ("Passes Long %", 70),
            ("Passes", 70), ("Passes Completed", 80), ("Passes %", 70),
            ("Key Passes", 80), ("Through Balls", 80), ("Passes 3/3", 70),
            ("Passes PA", 70), ("Progressive Passes", 80), ("Switch Play", 80),
            ("Passes Offside", 80), ("Passes Blocked", 80), ("Total Crosses", 80),
            ("Crosses to Penalty Area", 90), ("Total Passing Distance", 90),
            ("Progressive Passing Distance", 100), ("Progressive Carries", 80),
            ("Take-ons Attempts", 80), ("Take-ons Successful", 80),
            ("Aerial Duels Won", 80), ("Aerial Duels Lost", 80), ("Aerial Duels Won %", 80),
            ("Touches", 70), ("Tackles", 70), ("Tackles Won", 80), ("Tackles %", 70),
            ("Interceptions", 80), ("Clearances", 80), ("Opp Shots Blocked", 90),
            ("Opp Passes Blocked", 90), ("Mistakes Lead to Opp Shot", 100)
        ]

        for col_name, width in advanced_stats_columns:
            col_index = self._get_column_index(col_name)
            if col_index >= 0:
                self.players_table.setColumnWidth(col_index, width)

        # Hide all advanced statistics columns by default (they can be shown via header context menu)
        for col_name, _ in advanced_stats_columns:
            col_index = self._get_column_index(col_name)
            if col_index >= 0:
                self.players_table.setColumnHidden(col_index, True)

        # Set column widths for performance columns
        performance_columns = [
            ("Passing", 70), ("Creativity", 70), ("Dribbling-Carrying", 90),
            ("Marking", 70), ("Positioning", 80), ("Aerial", 70),
            ("Speed", 70), ("Stamina", 70), ("Power", 70),
            ("Decision-Making", 90), ("Concentration", 80), ("Work-Rate", 80)
        ]

        for col_name, width in performance_columns:
            col_index = self._get_column_index(col_name)
            if col_index >= 0:
                self.players_table.setColumnWidth(col_index, width)

        # Hide all performance columns by default (they can be shown via header context menu)
        for col_name, _ in performance_columns:
            col_index = self._get_column_index(col_name)
            if col_index >= 0:
                self.players_table.setColumnHidden(col_index, True)

        # Restore column state (visibility and widths) from settings
        self._restore_column_state()

        # Connect signal to save column state when widths are manually resized
        header = self.players_table.horizontalHeader()
        header.sectionResized.connect(self._on_column_resized)

        # Store references to split cell widgets for Goals and Assists columns
        self.goals_widgets = {}  # row -> widget mapping
        self.assists_widgets = {}  # row -> widget mapping

        # Add table to layout
        players_layout.addWidget(self.players_table)

        # Add counters below the table
        counters_layout = QHBoxLayout()

        # X-a-side label (shows the competition's player limit)
        x_a_side = self._get_competition_x_a_side()
        self.x_a_side_label = QLabel(self.tr(f"{x_a_side}-a-side"))
        self.x_a_side_label.setStyleSheet("font-weight: bold; color: #2196F3; padding: 5px; border: 1px solid #2196F3; border-radius: 3px;")
        counters_layout.addWidget(self.x_a_side_label)

        self.lineup_counter_label = QLabel(self.tr("Lineup = 0"))
        self.lineup_counter_label.setStyleSheet("font-weight: bold; color: #333; padding: 5px;")
        counters_layout.addWidget(self.lineup_counter_label)

        self.subs_counter_label = QLabel(self.tr("Subs = 0"))
        self.subs_counter_label.setStyleSheet("font-weight: bold; color: #666; padding: 5px;")
        counters_layout.addWidget(self.subs_counter_label)

        # Competition rule labels
        min_req_start = self._get_competition_min_req_start()
        self.min_req_start_label = QLabel(self.tr(f"Min req start = {min_req_start}"))
        self.min_req_start_label.setStyleSheet("font-weight: bold; color: #FF9800; padding: 5px;")
        counters_layout.addWidget(self.min_req_start_label)

        min_dur_match = self._get_competition_min_dur_match()
        self.min_dur_match_label = QLabel(self.tr(f"Min dur match = {min_dur_match}"))
        self.min_dur_match_label.setStyleSheet("font-weight: bold; color: #FF9800; padding: 5px;")
        counters_layout.addWidget(self.min_dur_match_label)

        max_subs = self._get_competition_max_subs()
        self.max_subs_label = QLabel(self.tr(f"Max subs = {max_subs}"))
        self.max_subs_label.setStyleSheet("font-weight: bold; color: #FF9800; padding: 5px;")
        counters_layout.addWidget(self.max_subs_label)

        allow_sub_sub = self._get_competition_allow_sub_sub()
        sub_sub_text = "true" if allow_sub_sub else "false"
        self.allow_sub_sub_label = QLabel(self.tr(f"Allow sub-sub {sub_sub_text}"))
        self.allow_sub_sub_label.setStyleSheet("font-weight: bold; color: #FF9800; padding: 5px;")
        counters_layout.addWidget(self.allow_sub_sub_label)

        counters_layout.addStretch()  # Push counters to the left

        players_layout.addLayout(counters_layout)

        # Connect table changes to update counters and sorting
        self.players_table.itemChanged.connect(self._on_table_item_changed)

        # Connect table clicks for substitution handling
        self.players_table.clicked.connect(self._on_players_table_clicked)

        # Connect table double-clicks for yellow card handling
        self.players_table.doubleClicked.connect(self._on_players_table_double_clicked)

        # Enable custom context menu for right-click
        self.players_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.players_table.customContextMenuRequested.connect(self._on_players_table_right_clicked)

        return players_widget

    def _get_column_index(self, header_name):
        """Get column index by header name for robust column identification."""
        try:
            logical_index = self.column_headers.index(self.tr(header_name))
            # Convert logical index to visual index if columns have been reordered
            header = self.players_table.horizontalHeader()
            visual_index = header.visualIndex(logical_index)
            if header_name == "Goals min":
                window_init_debug(f"Column '{header_name}' - logical: {logical_index}, visual: {visual_index}")
            return visual_index
        except ValueError:
            window_init_debug(f"Warning: Column '{header_name}' not found in headers")
            window_init_debug(f"Available headers: {self.column_headers}")
            return -1

    def _create_opponent_tab(self):
        """Create the Opponent tab content."""
        opponent_widget = QWidget()
        opponent_layout = QVBoxLayout(opponent_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Opponent tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        opponent_layout.addWidget(placeholder_label)

        return opponent_widget

    def _create_timeline_tab(self):
        """Create the Timeline tab content."""
        timeline_widget = QWidget()
        timeline_layout = QVBoxLayout(timeline_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Timeline tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        timeline_layout.addWidget(placeholder_label)

        return timeline_widget

    def _create_tactics_tab(self):
        """Create the Tactics tab content."""
        tactics_widget = QWidget()
        tactics_layout = QVBoxLayout(tactics_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Tactics tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        tactics_layout.addWidget(placeholder_label)

        return tactics_widget

    def _create_staff_tab(self):
        """Create the Staff tab content."""
        staff_widget = QWidget()
        staff_layout = QVBoxLayout(staff_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Staff tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        staff_layout.addWidget(placeholder_label)

        return staff_widget

    def _on_tab_changed(self, index):
        """Handle tab change to show/hide player management buttons."""
        # Update player button visibility based on current tab and basic info completion
        self._update_player_button_visibility()

    def _update_players_tab_visibility(self):
        """Update Players tab visibility based on Basic Information completion status."""
        if not hasattr(self, 'tab_widget') or not hasattr(self, 'players_tab_index'):
            return

        if self.players_tab_index < 0:
            return

        # Show Players tab only when Basic Information is complete (green)
        if hasattr(self, 'basic_info_complete') and self.basic_info_complete:
            self.tab_widget.setTabVisible(self.players_tab_index, True)
            # Automatically switch to Players tab when it becomes available
            self.tab_widget.setCurrentIndex(self.players_tab_index)
        else:
            self.tab_widget.setTabVisible(self.players_tab_index, False)

    def _update_player_button_visibility(self):
        """Update player button visibility based on current tab (simplified since tab is only visible when allowed)."""
        if not hasattr(self, 'tab_widget') or not hasattr(self, 'add_player_btn') or not hasattr(self, 'remove_player_btn') or not hasattr(self, 'clear_all_subs_btn'):
            return

        # Get current tab text
        current_index = self.tab_widget.currentIndex()
        if current_index < 0:
            return

        current_tab_text = self.tab_widget.tabText(current_index)

        # Show player management buttons only when Players tab is active
        # (No need to check Basic Information since Players tab is only visible when it's complete)
        if current_tab_text == self.tr("Players"):
            self.add_player_btn.show()
            self.remove_player_btn.show()
            self.clear_all_subs_btn.show()
        else:
            self.add_player_btn.hide()
            self.remove_player_btn.hide()
            self.clear_all_subs_btn.hide()

    def _create_progress_section(self):
        """Create the New Match Progress section with collapsible subsections."""
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(5)  # Compact spacing

        # Create Basic Information collapsible subsection
        self._create_basic_info_subsection(progress_layout)

        # Create Players Data collapsible subsection
        self._create_players_data_subsection(progress_layout)

        # Add stretch to push content to top
        progress_layout.addStretch()

        return progress_layout

    def _create_basic_info_subsection(self, parent_layout):
        """Create the Basic Information collapsible subsection."""
        # Create header with triangle, title, and status
        basic_info_header = QWidget()
        basic_info_header_layout = QHBoxLayout(basic_info_header)
        basic_info_header_layout.setContentsMargins(0, 0, 0, 0)
        basic_info_header_layout.setSpacing(5)

        # Collapse/expand triangle (same style as Match Statistics)
        self.basic_info_triangle = ProgressTriangleButton()
        self.basic_info_triangle.clicked.connect(self._toggle_basic_info_section)

        # Title label with status color
        self.basic_info_title = QLabel(self.tr("Basic Information"))
        self.basic_info_title.setStyleSheet("font-weight: bold; color: red;")  # Default red

        # Add to header layout (no checkmark for subsection titles)
        basic_info_header_layout.addWidget(self.basic_info_triangle)
        basic_info_header_layout.addWidget(self.basic_info_title)
        basic_info_header_layout.addStretch()

        # Make header clickable (but not the triangle button area)
        basic_info_header.mousePressEvent = lambda event: self._toggle_basic_info_section() if event.x() > 20 else None
        basic_info_header.setCursor(Qt.CursorShape.PointingHandCursor)

        # Create content widget for status indicators
        self.basic_info_content = QWidget()
        basic_info_content_layout = QGridLayout(self.basic_info_content)
        basic_info_content_layout.setContentsMargins(20, 5, 0, 5)  # Indent content
        basic_info_content_layout.setSpacing(2)  # Compact spacing

        # Define status fields
        status_fields = [
            ("date", self.tr("Date")),
            ("time", self.tr("Time")),
            ("competition", self.tr("Competition")),
            ("season", self.tr("Season")),
            ("opponent", self.tr("Opponent"))
        ]

        # Create status labels for each field
        self.progress_status_labels = {}
        serious_fields = {'date', 'competition', 'season', 'opponent'}  # These need X marks

        for row, (field_key, field_name) in enumerate(status_fields):
            # Create status label with field name
            status_label = QLabel(field_name)

            # Determine initial color based on field type
            if field_key in serious_fields:
                # Serious validation - start with red
                serious_color = get_color("match_validation_serious").name()
                status_label.setStyleSheet(f"color: {serious_color}; font-size: 11px;")
            else:
                # Warning validation - start with orange
                warning_color = get_color("match_validation_warning").name()
                status_label.setStyleSheet(f"color: {warning_color}; font-size: 11px;")

            # Create checkmark label (initially hidden)
            checkmark_label = QLabel("✓")
            ok_color = get_color("match_validation_ok").name()
            checkmark_label.setStyleSheet(f"color: {ok_color}; font-weight: bold; font-size: 12px;")
            checkmark_label.hide()  # Initially hidden

            # Add to grid
            basic_info_content_layout.addWidget(status_label, row, 0)
            basic_info_content_layout.addWidget(checkmark_label, row, 1)

            # Store references
            status_info = {
                'label': status_label,
                'checkmark': checkmark_label
            }

            # Add X mark for serious validations
            if field_key in serious_fields:
                xmark_label = QLabel("✗")
                xmark_label.setStyleSheet(f"color: {serious_color}; font-weight: bold; font-size: 12px;")
                xmark_label.show()  # Initially shown for serious validations
                basic_info_content_layout.addWidget(xmark_label, row, 1)  # Same position as checkmark
                status_info['xmark'] = xmark_label

            self.progress_status_labels[field_key] = status_info

        # Add header and content to parent layout
        parent_layout.addWidget(basic_info_header)
        parent_layout.addWidget(self.basic_info_content)

        # Track expanded state
        self.basic_info_expanded = True

        # Track completion status for basic information
        self.basic_info_complete = False

        # Track substitution pairs
        self.substitution_pairs = []  # List of dicts: {'sub_out_player_id': id, 'sub_in_player_id': id, 'minute': int}

    def _create_players_data_subsection(self, parent_layout):
        """Create the Players Data collapsible subsection."""
        # Create header with triangle, title, and status
        players_data_header = QWidget()
        players_data_header_layout = QHBoxLayout(players_data_header)
        players_data_header_layout.setContentsMargins(0, 0, 0, 0)
        players_data_header_layout.setSpacing(5)

        # Collapse/expand triangle (same style as Match Statistics)
        self.players_data_triangle = ProgressTriangleButton()
        self.players_data_triangle.clicked.connect(self._toggle_players_data_section)

        # Title label with status color
        self.players_data_title = QLabel(self.tr("Players Data"))
        self.players_data_title.setStyleSheet("font-weight: bold; color: green;")  # Green (no requirements yet)

        # Add to header layout (no checkmark for subsection titles)
        players_data_header_layout.addWidget(self.players_data_triangle)
        players_data_header_layout.addWidget(self.players_data_title)
        players_data_header_layout.addStretch()

        # Make header clickable (but not the triangle button area)
        players_data_header.mousePressEvent = lambda event: self._toggle_players_data_section() if event.x() > 20 else None
        players_data_header.setCursor(Qt.CursorShape.PointingHandCursor)

        # Create content widget
        self.players_data_content = QWidget()
        players_data_content_layout = QGridLayout(self.players_data_content)
        players_data_content_layout.setContentsMargins(20, 5, 0, 5)  # Indent content
        players_data_content_layout.setSpacing(2)  # Compact spacing

        # Starting lineup validation (SERIOUS)
        min_req_start = self._get_competition_min_req_start()
        self.starting_lineup_label = QLabel(self.tr(f"Starting lineup ≥ {min_req_start}"))
        serious_color = get_color("match_validation_serious").name()
        self.starting_lineup_label.setStyleSheet(f"color: {serious_color}; font-size: 11px;")  # Default red

        # Create checkmark label (initially hidden)
        self.starting_lineup_checkmark = QLabel("✓")
        ok_color = get_color("match_validation_ok").name()
        self.starting_lineup_checkmark.setStyleSheet(f"color: {ok_color}; font-weight: bold; font-size: 12px;")
        self.starting_lineup_checkmark.hide()  # Initially hidden

        # Create X mark label for serious validation (initially shown)
        self.starting_lineup_xmark = QLabel("✗")
        self.starting_lineup_xmark.setStyleSheet(f"color: {serious_color}; font-weight: bold; font-size: 12px;")
        self.starting_lineup_xmark.show()  # Initially shown (serious validation)

        # Captain set (WARNING)
        self.captain_set_label = QLabel(self.tr("Captain set"))
        warning_color = get_color("match_validation_warning").name()
        self.captain_set_label.setStyleSheet(f"color: {warning_color}; font-size: 11px;")  # Default warning

        self.captain_set_checkmark = QLabel("✓")
        ok_color = get_color("match_validation_ok").name()
        self.captain_set_checkmark.setStyleSheet(f"color: {ok_color}; font-weight: bold; font-size: 12px;")
        self.captain_set_checkmark.hide()  # Initially hidden

        self.captain_set_warning = QLabel("⚠")
        self.captain_set_warning.setStyleSheet(f"color: {warning_color}; font-weight: bold; font-size: 12px;")
        self.captain_set_warning.show()  # Initially shown (warning validation)

        # Goalkeeper (WARNING)
        self.goalkeeper_label = QLabel(self.tr("Goalkeeper"))
        self.goalkeeper_label.setStyleSheet(f"color: {warning_color}; font-size: 11px;")  # Default warning

        self.goalkeeper_checkmark = QLabel("✓")
        self.goalkeeper_checkmark.setStyleSheet(f"color: {ok_color}; font-weight: bold; font-size: 12px;")
        self.goalkeeper_checkmark.hide()  # Initially hidden

        self.goalkeeper_warning = QLabel("⚠")
        self.goalkeeper_warning.setStyleSheet(f"color: {warning_color}; font-weight: bold; font-size: 12px;")
        self.goalkeeper_warning.show()  # Initially shown (warning validation)

        # Goals assigned (WARNING)
        self.goals_assigned_label = QLabel(self.tr("Goals assigned"))
        self.goals_assigned_label.setStyleSheet(f"color: {warning_color}; font-size: 11px;")  # Default warning

        self.goals_assigned_checkmark = QLabel("✓")
        self.goals_assigned_checkmark.setStyleSheet(f"color: {ok_color}; font-weight: bold; font-size: 12px;")
        self.goals_assigned_checkmark.hide()  # Initially hidden

        self.goals_assigned_warning = QLabel("⚠")
        self.goals_assigned_warning.setStyleSheet(f"color: {warning_color}; font-weight: bold; font-size: 12px;")
        self.goals_assigned_warning.show()  # Initially shown (warning validation)

        # Goals minutes (SERIOUS)
        self.goals_minutes_label = QLabel(self.tr("Goals minutes"))
        # Default green (no goals = complete) - will be set properly in validation
        ok_color = get_color("match_validation_ok").name()
        self.goals_minutes_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")

        self.goals_minutes_checkmark = QLabel("✓")
        self.goals_minutes_checkmark.setStyleSheet(f"color: {ok_color}; font-weight: bold; font-size: 12px;")
        self.goals_minutes_checkmark.show()  # Initially shown (no goals = complete)

        self.goals_minutes_xmark = QLabel("✗")
        serious_color = get_color("match_validation_serious").name()
        self.goals_minutes_xmark.setStyleSheet(f"color: {serious_color}; font-weight: bold; font-size: 12px;")
        self.goals_minutes_xmark.hide()  # Initially hidden

        # Assists minutes (SERIOUS)
        self.assists_minutes_label = QLabel(self.tr("Assists minutes"))
        # Default green (no assists = complete) - will be set properly in validation
        self.assists_minutes_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")

        self.assists_minutes_checkmark = QLabel("✓")
        self.assists_minutes_checkmark.setStyleSheet(f"color: {ok_color}; font-weight: bold; font-size: 12px;")
        self.assists_minutes_checkmark.show()  # Initially shown (no assists = complete)

        self.assists_minutes_xmark = QLabel("✗")
        self.assists_minutes_xmark.setStyleSheet(f"color: {serious_color}; font-weight: bold; font-size: 12px;")
        self.assists_minutes_xmark.hide()  # Initially hidden

        # Add to grid
        players_data_content_layout.addWidget(self.starting_lineup_label, 0, 0)
        players_data_content_layout.addWidget(self.starting_lineup_checkmark, 0, 1)
        players_data_content_layout.addWidget(self.starting_lineup_xmark, 0, 1)  # Same position as checkmark
        players_data_content_layout.addWidget(self.captain_set_label, 1, 0)
        players_data_content_layout.addWidget(self.captain_set_checkmark, 1, 1)
        players_data_content_layout.addWidget(self.captain_set_warning, 1, 1)  # Same position as checkmark
        players_data_content_layout.addWidget(self.goalkeeper_label, 2, 0)
        players_data_content_layout.addWidget(self.goalkeeper_checkmark, 2, 1)
        players_data_content_layout.addWidget(self.goalkeeper_warning, 2, 1)  # Same position as checkmark
        players_data_content_layout.addWidget(self.goals_assigned_label, 3, 0)
        players_data_content_layout.addWidget(self.goals_assigned_checkmark, 3, 1)
        players_data_content_layout.addWidget(self.goals_assigned_warning, 3, 1)  # Same position as checkmark
        players_data_content_layout.addWidget(self.goals_minutes_label, 4, 0)
        players_data_content_layout.addWidget(self.goals_minutes_checkmark, 4, 1)
        players_data_content_layout.addWidget(self.goals_minutes_xmark, 4, 1)  # Same position as checkmark
        players_data_content_layout.addWidget(self.assists_minutes_label, 5, 0)
        players_data_content_layout.addWidget(self.assists_minutes_checkmark, 5, 1)
        players_data_content_layout.addWidget(self.assists_minutes_xmark, 5, 1)  # Same position as checkmark

        # Add header and content to parent layout
        parent_layout.addWidget(players_data_header)
        parent_layout.addWidget(self.players_data_content)

        # Track expanded state
        self.players_data_expanded = True

    def _toggle_basic_info_section(self):
        """Toggle the Basic Information section visibility."""
        self.basic_info_expanded = not self.basic_info_expanded

        if self.basic_info_expanded:
            self.basic_info_content.show()
            self.basic_info_triangle.set_collapsed(False)
        else:
            self.basic_info_content.hide()
            self.basic_info_triangle.set_collapsed(True)

    def _toggle_players_data_section(self):
        """Toggle the Players Data section visibility."""
        self.players_data_expanded = not self.players_data_expanded

        if self.players_data_expanded:
            self.players_data_content.show()
            self.players_data_triangle.set_collapsed(False)
        else:
            self.players_data_content.hide()
            self.players_data_triangle.set_collapsed(True)

    def _create_information_section(self):
        """Create the Information section with editable form fields."""
        form_layout = QFormLayout()

        # Match ID (auto-generated, read-only)
        self.match_id_field = QLineEdit()
        self.match_id_field.setReadOnly(True)
        self.match_id_field.setPlaceholderText(self.tr("Auto-generated"))
        form_layout.addRow(self.tr("Match ID:"), self.match_id_field)

        # Date
        self.date_field = QDateEdit()
        # Apply locale based on user's language setting
        apply_locale_to_date_edit(self.date_field)
        # Defer calendar popup setting to prevent flash during initialization
        self.date_field.setDate(QDate.currentDate())
        form_layout.addRow(self.tr("Date:"), self.date_field)

        # Time
        self.time_field = QTimeEdit()
        self.time_field.setTime(QTime(18, 0))  # Default to 6:00 PM
        form_layout.addRow(self.tr("Time:"), self.time_field)

        # Competition (dropdown from Options)
        self.competition_field = QComboBox()
        self.competition_field.setEditable(False)
        self._load_competitions()
        form_layout.addRow(self.tr("Competition:"), self.competition_field)

        # Season (auto-generated dropdown)
        self.season_field = QComboBox()
        self.season_field.setEditable(False)
        self._load_seasons()
        form_layout.addRow(self.tr("Season:"), self.season_field)

        # Matchday
        self.matchday_field = QSpinBox()
        self.matchday_field.setRange(1, 999)
        self.matchday_field.setValue(1)
        form_layout.addRow(self.tr("Matchday:"), self.matchday_field)

        # Round
        self.round_field = QSpinBox()
        self.round_field.setRange(1, 999)
        self.round_field.setValue(1)
        form_layout.addRow(self.tr("Round:"), self.round_field)

        return form_layout

    def _create_teams_score_section(self):
        """Create the Teams and Score section with editable form fields."""
        form_layout = QFormLayout()

        # Home/Away radio buttons
        home_away_widget = QWidget()
        home_away_layout = QHBoxLayout(home_away_widget)
        home_away_layout.setContentsMargins(0, 0, 0, 0)

        self.home_radio = QRadioButton(self.tr("Home"))
        self.away_radio = QRadioButton(self.tr("Away"))
        self.home_radio.setChecked(True)  # Default to Home

        home_away_layout.addWidget(self.home_radio)
        home_away_layout.addWidget(self.away_radio)
        home_away_layout.addStretch()

        form_layout.addRow(self.tr("Home/Away:"), home_away_widget)

        # Opponent
        self.opponent_field = QLineEdit()
        self.opponent_field.setPlaceholderText(self.tr("Enter opponent name..."))
        form_layout.addRow(self.tr("Opponent:"), self.opponent_field)

        # Full Time Score
        score_widget = QWidget()
        score_layout = QHBoxLayout(score_widget)
        score_layout.setContentsMargins(0, 0, 0, 0)

        self.home_score_field = QSpinBox()
        self.home_score_field.setRange(0, 99)
        self.home_score_field.setValue(0)

        score_separator = QLabel("-")
        score_separator.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.away_score_field = QSpinBox()
        self.away_score_field.setRange(0, 99)
        self.away_score_field.setValue(0)

        score_layout.addWidget(self.home_score_field)
        score_layout.addWidget(score_separator)
        score_layout.addWidget(self.away_score_field)
        score_layout.addStretch()

        form_layout.addRow(self.tr("Full Time Score:"), score_widget)

        # Half Time checkbox
        self.halftime_checkbox = QCheckBox(self.tr("Half Time"))
        self.halftime_checkbox.toggled.connect(self._on_halftime_toggled)
        form_layout.addRow("", self.halftime_checkbox)

        # Half Time Score (initially hidden)
        self.halftime_score_widget = QWidget()
        halftime_score_layout = QHBoxLayout(self.halftime_score_widget)
        halftime_score_layout.setContentsMargins(0, 0, 0, 0)

        self.halftime_home_score_field = QSpinBox()
        self.halftime_home_score_field.setRange(0, 99)
        self.halftime_home_score_field.setValue(0)

        halftime_separator = QLabel("-")
        halftime_separator.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.halftime_away_score_field = QSpinBox()
        self.halftime_away_score_field.setRange(0, 99)
        self.halftime_away_score_field.setValue(0)

        halftime_score_layout.addWidget(self.halftime_home_score_field)
        halftime_score_layout.addWidget(halftime_separator)
        halftime_score_layout.addWidget(self.halftime_away_score_field)
        halftime_score_layout.addStretch()

        self.halftime_score_widget.setVisible(False)  # Initially hidden
        form_layout.addRow(self.tr("Half Time Score:"), self.halftime_score_widget)

        # Extra Time checkbox
        self.extratime_checkbox = QCheckBox(self.tr("Extra Time"))
        self.extratime_checkbox.toggled.connect(self._on_extratime_toggled)
        form_layout.addRow("", self.extratime_checkbox)

        # Extra Time Score (initially hidden)
        self.extratime_score_widget = QWidget()
        extratime_score_layout = QHBoxLayout(self.extratime_score_widget)
        extratime_score_layout.setContentsMargins(0, 0, 0, 0)

        self.extratime_home_score_field = QSpinBox()
        self.extratime_home_score_field.setRange(0, 99)
        self.extratime_home_score_field.setValue(0)

        extratime_separator = QLabel("-")
        extratime_separator.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.extratime_away_score_field = QSpinBox()
        self.extratime_away_score_field.setRange(0, 99)
        self.extratime_away_score_field.setValue(0)

        extratime_score_layout.addWidget(self.extratime_home_score_field)
        extratime_score_layout.addWidget(extratime_separator)
        extratime_score_layout.addWidget(self.extratime_away_score_field)
        extratime_score_layout.addStretch()

        self.extratime_score_widget.setVisible(False)  # Initially hidden
        form_layout.addRow(self.tr("Extra Time Score:"), self.extratime_score_widget)

        # Penalty checkbox
        self.penalty_checkbox = QCheckBox(self.tr("Penalty"))
        self.penalty_checkbox.toggled.connect(self._on_penalty_toggled)
        form_layout.addRow("", self.penalty_checkbox)

        # Penalty Score (initially hidden)
        self.penalty_score_widget = QWidget()
        penalty_score_layout = QHBoxLayout(self.penalty_score_widget)
        penalty_score_layout.setContentsMargins(0, 0, 0, 0)

        self.penalty_home_score_field = QSpinBox()
        self.penalty_home_score_field.setRange(0, 99)
        self.penalty_home_score_field.setValue(0)

        penalty_separator = QLabel("-")
        penalty_separator.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.penalty_away_score_field = QSpinBox()
        self.penalty_away_score_field.setRange(0, 99)
        self.penalty_away_score_field.setValue(0)

        penalty_score_layout.addWidget(self.penalty_home_score_field)
        penalty_score_layout.addWidget(penalty_separator)
        penalty_score_layout.addWidget(self.penalty_away_score_field)
        penalty_score_layout.addStretch()

        self.penalty_score_widget.setVisible(False)  # Initially hidden
        form_layout.addRow(self.tr("Penalty Score:"), self.penalty_score_widget)

        # Opponents own goals
        self.opponents_own_goals_field = QSpinBox()
        self.opponents_own_goals_field.setRange(0, 99)
        self.opponents_own_goals_field.setValue(0)
        self.opponents_own_goals_field.setToolTip(self.tr("Goals scored by the opponent into their own net (count toward our score)"))
        form_layout.addRow(self.tr("Opponents own goals:"), self.opponents_own_goals_field)

        # Result (read-only, calculated)
        self.result_label = QLabel(self.tr("--"))
        self.result_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(self.tr("Result:"), self.result_label)

        # Match duration (read-only, from competition settings)
        self.match_duration_label = QLabel(self.tr("90"))
        self.match_duration_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(self.tr("Match duration:"), self.match_duration_label)

        # Stoppage time input
        self.stoppage_time_field = QSpinBox()
        self.stoppage_time_field.setRange(0, 30)
        self.stoppage_time_field.setValue(0)
        self.stoppage_time_field.setSuffix(" min")
        self.stoppage_time_field.valueChanged.connect(self._update_total_duration)
        form_layout.addRow(self.tr("Stoppage time:"), self.stoppage_time_field)

        # ET duration (initially hidden)
        self.et_duration_field = QSpinBox()
        self.et_duration_field.setRange(0, 60)
        self.et_duration_field.setValue(30)
        self.et_duration_field.setSuffix(" min")
        self.et_duration_field.valueChanged.connect(self._update_total_duration)
        self.et_duration_field.setVisible(False)  # Initially hidden
        form_layout.addRow(self.tr("ET duration:"), self.et_duration_field)

        # Total duration (read-only, calculated)
        self.total_duration_label = QLabel(self.tr("90"))
        self.total_duration_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        form_layout.addRow(self.tr("Total duration:"), self.total_duration_label)

        # Connect score changes to update result
        self._connect_result_updates()

        # Calculate initial result with default values
        self._update_result()

        # Update initial duration values
        self._update_match_duration()
        self._update_total_duration()

        return form_layout

    def _create_statistics_section(self):
        """Create the Statistics section content."""
        stats_content = QWidget()
        stats_layout = QVBoxLayout(stats_content)

        # Create header with team names
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Empty label for the first column (stat name)
        header_layout.addWidget(QLabel(""), 1)

        # Team name labels
        self.team_name_label = QLabel(self._get_club_name())
        self.opponent_name_label = QLabel(self.tr("Opponent"))

        # Set alignment and style for team name labels
        for label in [self.team_name_label, self.opponent_name_label]:
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            font = label.font()
            font.setBold(True)
            label.setFont(font)

        header_layout.addWidget(self.team_name_label, 1)
        header_layout.addWidget(self.opponent_name_label, 1)

        stats_layout.addWidget(header_widget)

        # Create grid for statistics
        stats_grid_widget = QWidget()
        stats_grid = QGridLayout(stats_grid_widget)
        stats_grid.setContentsMargins(0, 0, 0, 0)

        # Define statistics fields
        stat_fields = [
            "final_score",  # New field for final score
            "attempts",
            "on_target",
            "passes",
            "completed_passes",
            "fouls",
            "yellow_cards",
            "red_cards",
            "offsides",
            "corners",
            "possession",
            "xgoals"
        ]

        # Define display names for statistics
        stat_display_names = {
            "final_score": self.tr("Final Score"),
            "attempts": self.tr("Attempts"),
            "on_target": self.tr("On Target"),
            "passes": self.tr("Passes"),
            "completed_passes": self.tr("Completed Passes"),
            "fouls": self.tr("Fouls Committed"),
            "yellow_cards": self.tr("Yellow Cards"),
            "red_cards": self.tr("Red Cards"),
            "offsides": self.tr("Offsides"),
            "corners": self.tr("Corners"),
            "possession": self.tr("Possession (%)"),
            "xgoals": self.tr("xGoals")
        }

        # Create spinboxes for each statistic
        self.team_stats = {}
        self.opponent_stats = {}

        # Add each statistic to the grid
        for row, field in enumerate(stat_fields):
            # Add label
            label = QLabel(stat_display_names[field])

            # Make the Final Score label bold and more visible
            if field == "final_score":
                font = label.font()
                font.setBold(True)
                font.setPointSize(font.pointSize() + 1)  # Make it slightly larger
                label.setFont(font)

            stats_grid.addWidget(label, row, 0)

            # Special handling for Final Score field
            if field == "final_score":
                # Create labels instead of spinboxes for final score
                team_score_label = QLabel("0")
                opponent_score_label = QLabel("0")

                # Style the score labels
                for score_label in [team_score_label, opponent_score_label]:
                    score_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    font = score_label.font()
                    font.setBold(True)
                    score_label.setFont(font)

                # Add labels to the grid
                stats_grid.addWidget(team_score_label, row, 1)
                stats_grid.addWidget(opponent_score_label, row, 2)

                # Store references to the labels
                self.team_stats[field] = team_score_label
                self.opponent_stats[field] = opponent_score_label

                continue  # Skip the rest of the loop for this field

            # Create spinboxes with appropriate ranges and settings
            # Use QDoubleSpinBox for xGoals, QSpinBox for others
            if field == "xgoals":
                team_spinbox = QDoubleSpinBox()
                opponent_spinbox = QDoubleSpinBox()
                # Set decimal precision and range for xGoals
                team_spinbox.setDecimals(2)
                opponent_spinbox.setDecimals(2)
                team_spinbox.setRange(0, 10.0)
                opponent_spinbox.setRange(0, 10.0)
                team_spinbox.setSingleStep(0.1)
                opponent_spinbox.setSingleStep(0.1)
            else:
                team_spinbox = QSpinBox()
                opponent_spinbox = QSpinBox()

            # Set ranges based on the statistic type
            if field == "possession":
                team_spinbox.setRange(0, 100)
                opponent_spinbox.setRange(0, 100)
                team_spinbox.setSuffix("%")
                opponent_spinbox.setSuffix("%")

                # Connect signals for possession to ensure they sum to 100%
                team_spinbox.valueChanged.connect(lambda value, sb=opponent_spinbox: sb.setValue(100 - value))
                opponent_spinbox.valueChanged.connect(lambda value, sb=team_spinbox: sb.setValue(100 - value))

                # Set default values
                team_spinbox.setValue(50)
                opponent_spinbox.setValue(50)
            elif field == "on_target":
                # On target must be <= Attempts, so we need to connect to the attempts field
                team_spinbox.setRange(0, 0)  # Will be updated when attempts changes
                opponent_spinbox.setRange(0, 0)  # Will be updated when attempts changes
            elif field in ["yellow_cards", "red_cards"]:
                # Reasonable max for cards
                team_spinbox.setRange(0, 10)
                opponent_spinbox.setRange(0, 10)
            elif field == "attempts":
                # Set a reasonable max for attempts
                team_spinbox.setRange(0, 99)
                opponent_spinbox.setRange(0, 99)
            else:
                # For other statistics, use appropriate ranges
                team_spinbox.setRange(0, 99)
                opponent_spinbox.setRange(0, 99)

            # Add spinboxes to the grid
            stats_grid.addWidget(team_spinbox, row, 1)
            stats_grid.addWidget(opponent_spinbox, row, 2)

            # Store references to the spinboxes
            self.team_stats[field] = team_spinbox
            self.opponent_stats[field] = opponent_spinbox

        # Add the grid to the layout
        stats_layout.addWidget(stats_grid_widget)

        # Connect validation logic after all fields are created
        self._connect_statistics_validation()

        # Update initial team labels and final score
        QTimer.singleShot(100, self._update_team_labels)
        QTimer.singleShot(100, self._update_final_score)

        return stats_content

    def _connect_statistics_validation(self):
        """Connect validation logic for statistics fields."""
        # Connect attempts fields to on_target fields to ensure on_target <= attempts
        if "attempts" in self.team_stats and "on_target" in self.team_stats:
            # Update the on_target max value when attempts changes
            def update_team_on_target_max(value):
                self.team_stats["on_target"].setMaximum(value)
                # If current on_target value is greater than new max, adjust it
                if self.team_stats["on_target"].value() > value:
                    self.team_stats["on_target"].setValue(value)

            # Connect the signals
            self.team_stats["attempts"].valueChanged.connect(update_team_on_target_max)
            # Initialize with current attempts value
            update_team_on_target_max(self.team_stats["attempts"].value())

        if "attempts" in self.opponent_stats and "on_target" in self.opponent_stats:
            # Update the on_target max value when attempts changes
            def update_opponent_on_target_max(value):
                self.opponent_stats["on_target"].setMaximum(value)
                # If current on_target value is greater than new max, adjust it
                if self.opponent_stats["on_target"].value() > value:
                    self.opponent_stats["on_target"].setValue(value)

            # Connect the signals
            self.opponent_stats["attempts"].valueChanged.connect(update_opponent_on_target_max)
            # Initialize with current attempts value
            update_opponent_on_target_max(self.opponent_stats["attempts"].value())

        # Connect passes fields to completed_passes fields to ensure completed_passes <= passes
        if "passes" in self.team_stats and "completed_passes" in self.team_stats:
            # Update the completed_passes max value when passes changes
            def update_team_completed_passes_max(value):
                self.team_stats["completed_passes"].setMaximum(value)
                # If current completed_passes value is greater than new max, adjust it
                if self.team_stats["completed_passes"].value() > value:
                    self.team_stats["completed_passes"].setValue(value)

            # Connect the signals
            self.team_stats["passes"].valueChanged.connect(update_team_completed_passes_max)
            # Initialize with current passes value
            update_team_completed_passes_max(self.team_stats["passes"].value())

        if "passes" in self.opponent_stats and "completed_passes" in self.opponent_stats:
            # Update the completed_passes max value when passes changes
            def update_opponent_completed_passes_max(value):
                self.opponent_stats["completed_passes"].setMaximum(value)
                # If current completed_passes value is greater than new max, adjust it
                if self.opponent_stats["completed_passes"].value() > value:
                    self.opponent_stats["completed_passes"].setValue(value)

            # Connect the signals
            self.opponent_stats["passes"].valueChanged.connect(update_opponent_completed_passes_max)
            # Initialize with current passes value
            update_opponent_completed_passes_max(self.opponent_stats["passes"].value())

    def _create_advanced_statistics_section(self):
        """Create the Advanced Statistics section content."""
        adv_stats_content = QWidget()
        adv_stats_layout = QVBoxLayout(adv_stats_content)

        # Create header with team names (reuse the same team names from basic stats)
        adv_header_widget = QWidget()
        adv_header_layout = QHBoxLayout(adv_header_widget)
        adv_header_layout.setContentsMargins(0, 0, 0, 0)

        # Empty label for the first column (stat name)
        adv_header_layout.addWidget(QLabel(""), 1)

        # Team name labels (create new labels with the same text)
        self.adv_team_name_label = QLabel(self._get_club_name())
        self.adv_opponent_name_label = QLabel(self.tr("Opponent"))

        # Set alignment and style for team name labels
        for label in [self.adv_team_name_label, self.adv_opponent_name_label]:
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            font = label.font()
            font.setBold(True)
            label.setFont(font)

        adv_header_layout.addWidget(self.adv_team_name_label, 1)
        adv_header_layout.addWidget(self.adv_opponent_name_label, 1)

        adv_stats_layout.addWidget(adv_header_widget)

        # Create grid for advanced statistics
        adv_stats_grid_widget = QWidget()
        adv_stats_grid = QGridLayout(adv_stats_grid_widget)
        adv_stats_grid.setContentsMargins(0, 0, 0, 0)

        # Define advanced statistics fields
        adv_stat_fields = [
            "crosses",
            "touches",
            "tackles",
            "interceptions",
            "aerials_won",
            "clearances",
            "long_balls",
            "saves"
        ]

        # Define display names for advanced statistics
        adv_stat_display_names = {
            "crosses": self.tr("Crosses"),
            "touches": self.tr("Touches"),
            "tackles": self.tr("Tackles"),
            "interceptions": self.tr("Interceptions"),
            "aerials_won": self.tr("Aerials Won"),
            "clearances": self.tr("Clearances"),
            "long_balls": self.tr("Long Balls"),
            "saves": self.tr("Saves")
        }

        # Create spinboxes for each advanced statistic
        self.team_adv_stats = {}
        self.opponent_adv_stats = {}

        # Add each advanced statistic to the grid
        for row, field in enumerate(adv_stat_fields):
            # Add label
            label = QLabel(adv_stat_display_names[field])
            adv_stats_grid.addWidget(label, row, 0)

            # Create spinboxes for team and opponent
            team_spinbox = QSpinBox()
            opponent_spinbox = QSpinBox()

            # Set unlimited ranges for all advanced statistics
            team_spinbox.setRange(0, 999999)
            opponent_spinbox.setRange(0, 999999)

            # Add spinboxes to the grid
            adv_stats_grid.addWidget(team_spinbox, row, 1)
            adv_stats_grid.addWidget(opponent_spinbox, row, 2)

            # Store references to the spinboxes
            self.team_adv_stats[field] = team_spinbox
            self.opponent_adv_stats[field] = opponent_spinbox

        # Add the grid to the layout
        adv_stats_layout.addWidget(adv_stats_grid_widget)

        # Update initial team labels
        QTimer.singleShot(100, self._update_advanced_team_labels)

        return adv_stats_content

    def _create_venue_section(self):
        """Create the Venue section with form fields."""
        venue_content = QWidget()
        form_layout = QFormLayout(venue_content)

        # Stadium Name (auto-filled if home)
        self.venue_stadium_field = QLineEdit()
        self.venue_stadium_field.setPlaceholderText(self.tr("Enter stadium name..."))
        form_layout.addRow(self.tr("Stadium:"), self.venue_stadium_field)

        # City (auto-filled if home)
        self.venue_city_field = QLineEdit()
        self.venue_city_field.setPlaceholderText(self.tr("Enter city..."))
        form_layout.addRow(self.tr("City:"), self.venue_city_field)

        # Country (default from club, but user can change)
        self.venue_country_field = QComboBox()
        self.venue_country_field.setEditable(True)
        self._load_countries()
        form_layout.addRow(self.tr("Country:"), self.venue_country_field)

        # Attendance
        self.venue_attendance_field = QSpinBox()
        self.venue_attendance_field.setRange(0, 999999)
        self.venue_attendance_field.setValue(0)
        self.venue_attendance_field.setSuffix(self.tr(" people"))
        form_layout.addRow(self.tr("Attendance:"), self.venue_attendance_field)

        # Weather
        self.venue_weather_field = QComboBox()
        self.venue_weather_field.addItems([
            self.tr("Select weather..."),
            self.tr("Sunny"),
            self.tr("Partly Cloudy"),
            self.tr("Cloudy"),
            self.tr("Overcast"),
            self.tr("Light Rain"),
            self.tr("Rain"),
            self.tr("Heavy Rain"),
            self.tr("Drizzle"),
            self.tr("Snow"),
            self.tr("Light Snow"),
            self.tr("Heavy Snow"),
            self.tr("Fog"),
            self.tr("Windy"),
            self.tr("Hot"),
            self.tr("Cold"),
            self.tr("Humid"),
            self.tr("Clear")
        ])
        form_layout.addRow(self.tr("Weather:"), self.venue_weather_field)

        # Broadcast
        self.venue_broadcast_field = QLineEdit()
        self.venue_broadcast_field.setPlaceholderText(self.tr("e.g., ESPN, Sky Sports, etc."))
        form_layout.addRow(self.tr("Broadcast:"), self.venue_broadcast_field)

        # Connect home/away changes to update venue fields
        QTimer.singleShot(100, self._update_venue_fields)

        return venue_content

    def _create_officials_section(self):
        """Create the Officials section with referee fields."""
        officials_content = QWidget()
        form_layout = QFormLayout(officials_content)

        # Referee fields (1-6)
        self.referee_fields = {}
        for i in range(1, 7):
            referee_field = QLineEdit()
            referee_field.setPlaceholderText(self.tr(f"Enter referee {i} name..."))
            form_layout.addRow(self.tr(f"Referee {i}:"), referee_field)
            self.referee_fields[f"referee_{i}"] = referee_field

        # Referee rating (1-10, default 5)
        self.referee_rating_field = QSpinBox()
        self.referee_rating_field.setRange(1, 10)
        self.referee_rating_field.setValue(5)  # Default value
        self.referee_rating_field.setSuffix("/10")
        form_layout.addRow(self.tr("Referee Rating:"), self.referee_rating_field)

        # Comments
        self.referee_comments_field = QTextEdit()
        self.referee_comments_field.setPlaceholderText(self.tr("Enter comments about referees..."))
        self.referee_comments_field.setMaximumHeight(80)  # Limit height
        form_layout.addRow(self.tr("Comments:"), self.referee_comments_field)

        return officials_content

    def _get_club_name(self):
        """Get club name from club data manager."""
        try:
            club_name = self.club_data_manager.get_data("club_name", "")
            return club_name if club_name else self.tr("My Team")
        except Exception as e:
            print(f"ERROR: Failed to get club name: {e}")
            return self.tr("My Team")

    def _setup_field_validation(self):
        """Set up validation for required fields."""
        # Enable calendar popup after window is shown to prevent flash during initialization
        if hasattr(self, 'date_field'):
            self.date_field.setCalendarPopup(True)

        # Connect field change signals to validation and progress updates
        if hasattr(self, 'opponent_field'):
            self.opponent_field.textChanged.connect(self._validate_required_fields)
            self.opponent_field.textChanged.connect(self._update_progress_status)
        if hasattr(self, 'date_field'):
            self.date_field.dateChanged.connect(self._validate_required_fields)
            self.date_field.dateChanged.connect(self._update_progress_status)
        if hasattr(self, 'time_field'):
            self.time_field.timeChanged.connect(self._update_progress_status)
        if hasattr(self, 'competition_field'):
            self.competition_field.currentTextChanged.connect(self._validate_required_fields)
            self.competition_field.currentTextChanged.connect(self._update_progress_status)
            self.competition_field.currentTextChanged.connect(self._update_competition_labels)
        if hasattr(self, 'season_field'):
            self.season_field.currentTextChanged.connect(self._validate_required_fields)
            self.season_field.currentTextChanged.connect(self._update_progress_status)

        # Also connect opponent field to update team labels
        if hasattr(self, 'opponent_field'):
            self.opponent_field.textChanged.connect(self._update_team_labels)

        # Initial progress status update
        self._update_progress_status()

        # Initial players data validation
        self._update_all_players_data_validation()

        # Validate and clean up any invalid player events
        QTimer.singleShot(200, self._validate_and_clean_all_player_events)

    def _validate_required_fields(self):
        """Validate required fields and highlight with red border if empty."""
        # Define required fields and their validation logic
        required_fields = {
            'opponent_field': lambda: self.opponent_field.text().strip() != "",
            'competition_field': lambda: self.competition_field.currentText() != self.tr("Select Competition...") and self.competition_field.currentText().strip() != "",
            'season_field': lambda: self.season_field.currentText() != self.tr("Select Season...") and self.season_field.currentText().strip() != ""
        }

        # Validate each field
        for field_name, validation_func in required_fields.items():
            if hasattr(self, field_name):
                field = getattr(self, field_name)
                is_valid = validation_func()

                if is_valid:
                    # Remove red border
                    field.setStyleSheet("")
                else:
                    # Add red border
                    field.setStyleSheet("border: 1px solid red;")

    def _update_team_labels(self):
        """Update team name labels with club name and opponent name."""
        club_name = self._get_club_name()
        opponent_name = self.opponent_field.text().strip() if hasattr(self, 'opponent_field') and self.opponent_field.text().strip() else self.tr("Opponent")

        # Update main statistics labels
        if hasattr(self, 'team_name_label'):
            self.team_name_label.setText(club_name)
        if hasattr(self, 'opponent_name_label'):
            self.opponent_name_label.setText(opponent_name)

        # Update advanced statistics labels
        if hasattr(self, 'adv_team_name_label'):
            self.adv_team_name_label.setText(club_name)
        if hasattr(self, 'adv_opponent_name_label'):
            self.adv_opponent_name_label.setText(opponent_name)

    def _update_advanced_team_labels(self):
        """Update advanced statistics team labels."""
        self._update_team_labels()

    def _update_progress_status(self):
        """Update the progress status indicators based on field completion."""
        if not hasattr(self, 'progress_status_labels'):
            return

        # Define validation logic for each progress field
        progress_validations = {
            'date': lambda: True,  # Date always has a default value
            'time': lambda: True,  # Time always has a default value
            'competition': lambda: (hasattr(self, 'competition_field') and
                                  self.competition_field.currentText() != self.tr("Select Competition...") and
                                  self.competition_field.currentText().strip() != ""),
            'season': lambda: (hasattr(self, 'season_field') and
                             self.season_field.currentText() != self.tr("Select Season...") and
                             self.season_field.currentText().strip() != ""),
            'opponent': lambda: (hasattr(self, 'opponent_field') and
                               self.opponent_field.text().strip() != "")
        }

        # Define which validations are serious (need X marks) vs warnings
        serious_validations = {'date', 'competition', 'season', 'opponent'}  # These are serious
        warning_validations = {'time'}  # These are warnings

        # Update each progress status and track completion
        all_complete = True
        for field_key, validation_func in progress_validations.items():
            if field_key in self.progress_status_labels:
                is_complete = validation_func()
                status_info = self.progress_status_labels[field_key]

                if is_complete:
                    # Field is complete - show green text and checkmark
                    ok_color = get_color("match_validation_ok").name()
                    status_info['label'].setStyleSheet(f"color: {ok_color}; font-weight: bold; font-size: 11px;")
                    status_info['checkmark'].show()
                    # Hide X mark if it exists
                    if 'xmark' in status_info:
                        status_info['xmark'].hide()
                else:
                    # Field is incomplete - determine if serious or warning
                    if field_key in serious_validations:
                        # Serious issue - show red text and X mark
                        serious_color = get_color("match_validation_serious").name()
                        status_info['label'].setStyleSheet(f"color: {serious_color}; font-size: 11px;")
                        status_info['checkmark'].hide()
                        if 'xmark' in status_info:
                            status_info['xmark'].show()
                    else:
                        # Warning - show orange text, no X mark
                        warning_color = get_color("match_validation_warning").name()
                        status_info['label'].setStyleSheet(f"color: {warning_color}; font-size: 11px;")
                        status_info['checkmark'].hide()
                        if 'xmark' in status_info:
                            status_info['xmark'].hide()
                    all_complete = False

        # Update Basic Information subsection title and checkmark
        self._update_basic_info_subsection_status(all_complete)

    def _update_basic_info_subsection_status(self, all_complete):
        """Update the Basic Information subsection title color and track completion status."""
        if hasattr(self, 'basic_info_title'):
            # Update completion status
            self.basic_info_complete = all_complete

            if all_complete:
                # All fields complete - green title
                self.basic_info_title.setStyleSheet("font-weight: bold; color: green;")
            else:
                # At least one field incomplete - red title
                self.basic_info_title.setStyleSheet("font-weight: bold; color: red;")

            # Update Players tab visibility based on completion status
            self._update_players_tab_visibility()

            # Update player button visibility if Players tab is currently active
            self._update_player_button_visibility()

    def _load_countries(self):
        """Load countries list for venue country field."""
        countries = [
            self.tr("Select country..."),
            "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Argentina", "Armenia", "Australia",
            "Austria", "Azerbaijan", "Bahrain", "Bangladesh", "Belarus", "Belgium", "Belize", "Benin",
            "Bhutan", "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei", "Bulgaria",
            "Burkina Faso", "Burundi", "Cambodia", "Cameroon", "Canada", "Cape Verde", "Central African Republic",
            "Chad", "Chile", "China", "Colombia", "Comoros", "Congo", "Costa Rica", "Croatia", "Cuba",
            "Cyprus", "Czech Republic", "Denmark", "Djibouti", "Dominica", "Dominican Republic", "Ecuador",
            "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Ethiopia", "Fiji", "Finland",
            "France", "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala",
            "Guinea", "Guinea-Bissau", "Guyana", "Haiti", "Honduras", "Hungary", "Iceland", "India",
            "Indonesia", "Iran", "Iraq", "Ireland", "Israel", "Italy", "Jamaica", "Japan", "Jordan",
            "Kazakhstan", "Kenya", "Kiribati", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon",
            "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg", "Madagascar",
            "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Mauritania", "Mauritius",
            "Mexico", "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique",
            "Myanmar", "Namibia", "Nauru", "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger",
            "Nigeria", "North Korea", "North Macedonia", "Norway", "Oman", "Pakistan", "Palau", "Panama",
            "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Poland", "Portugal", "Qatar", "Romania",
            "Russia", "Rwanda", "Saint Kitts and Nevis", "Saint Lucia", "Saint Vincent and the Grenadines",
            "Samoa", "San Marino", "Sao Tome and Principe", "Saudi Arabia", "Senegal", "Serbia", "Seychelles",
            "Sierra Leone", "Singapore", "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa",
            "South Korea", "South Sudan", "Spain", "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland",
            "Syria", "Taiwan", "Tajikistan", "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tonga",
            "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan", "Tuvalu", "Uganda", "Ukraine",
            "United Arab Emirates", "United Kingdom", "United States", "Uruguay", "Uzbekistan", "Vanuatu",
            "Vatican City", "Venezuela", "Vietnam", "Yemen", "Zambia", "Zimbabwe"
        ]

        self.venue_country_field.addItems(countries)

        # Set default country from club settings (placeholder for now)
        # TODO: Load from club settings when available
        default_country = self._get_club_default_country()
        if default_country:
            index = self.venue_country_field.findText(default_country)
            if index >= 0:
                self.venue_country_field.setCurrentIndex(index)

    def _get_club_data_manager(self):
        """Get or create a shared club data manager instance."""
        if not hasattr(self, '_club_data_manager'):
            from app.data.club_data_manager import ClubDataManager
            self._club_data_manager = ClubDataManager()
            print("DEBUG: Created shared ClubDataManager instance")
        return self._club_data_manager

    def _get_club_default_country(self):
        """Get default country from club settings."""
        try:
            club_manager = self._get_club_data_manager()
            country_name = club_manager.get_data("country", "")
            print(f"DEBUG: Retrieved club country: '{country_name}'")
            return country_name if country_name else None
        except Exception as e:
            print(f"ERROR: Failed to get club country: {e}")
            return None


    def _load_competitions(self):
        """Load competitions from Options page data."""
        try:
            # Clear existing items
            self.competition_field.clear()

            # Add default placeholder
            self.competition_field.addItem(self.tr("Select Competition..."))

            # Load competitions from settings using the same structure as Options page
            settings = QSettings()
            settings.beginGroup("football_competitions")

            # Get number of competitions
            count = settings.value("count", 0, int)

            competitions_loaded = 0
            if count > 0:
                # Load each competition using the correct key format
                for i in range(count):
                    # Get competition name using the correct key format
                    name_key = f"{i}/name"
                    name = settings.value(name_key, "", str)

                    if name and name.strip():
                        self.competition_field.addItem(name)
                        competitions_loaded += 1

            settings.endGroup()

        except Exception as e:
            print(f"Error loading competitions: {e}")
            # Just show placeholder on error
            self.competition_field.clear()
            self.competition_field.addItem(self.tr("Select Competition..."))

    def _load_seasons(self):
        """Load seasons based on Options → Dates."""
        # TODO: Calculate seasons from Options → Dates
        # For now, add placeholder seasons
        current_year = QDate.currentDate().year()
        seasons = []

        # Generate 3 seasons around current year
        for i in range(-1, 2):
            start_year = current_year + i
            end_year = start_year + 1
            season = f"{start_year}-{end_year}"
            seasons.append(season)

        self.season_field.addItems(seasons)

        # Set default to current season (middle item)
        self.season_field.setCurrentIndex(1)

    def _on_halftime_toggled(self, checked):
        """Handle half time checkbox toggle."""
        self.halftime_score_widget.setVisible(checked)

        if not checked:
            # Clear scores when disabled
            self.halftime_home_score_field.setValue(0)
            self.halftime_away_score_field.setValue(0)

        # Update result
        self._update_result()

    def _on_extratime_toggled(self, checked):
        """Handle extra time checkbox toggle."""
        self.extratime_score_widget.setVisible(checked)

        # Show/hide ET duration field
        if hasattr(self, 'et_duration_field'):
            self.et_duration_field.setVisible(checked)

        if not checked:
            # Clear scores when disabled
            self.extratime_home_score_field.setValue(0)
            self.extratime_away_score_field.setValue(0)

        # Update result and total duration
        self._update_result()
        if hasattr(self, '_update_total_duration'):
            self._update_total_duration()

    def _on_penalty_toggled(self, checked):
        """Handle penalty checkbox toggle."""
        self.penalty_score_widget.setVisible(checked)

        if checked:
            # Only show extra time dialog if extra time is not already enabled
            if not self.extratime_checkbox.isChecked():
                reply = QMessageBox.question(
                    self,
                    self.tr("Extra Time Confirmation"),
                    self.tr("Although it is not always necessary to play extra time before penalties, "
                           "it is common in many competitions. Would you like to enable extra time as well?"),
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    self.extratime_checkbox.setChecked(True)
        else:
            # Clear scores when disabled
            self.penalty_home_score_field.setValue(0)
            self.penalty_away_score_field.setValue(0)

        # Update result when penalty is toggled
        self._update_result()

    def _connect_result_updates(self):
        """Connect all score fields and checkboxes to update result."""
        # Connect score field changes
        self.home_score_field.valueChanged.connect(self._update_result)
        self.away_score_field.valueChanged.connect(self._update_result)
        self.halftime_home_score_field.valueChanged.connect(self._update_result)
        self.halftime_away_score_field.valueChanged.connect(self._update_result)
        self.extratime_home_score_field.valueChanged.connect(self._update_result)
        self.extratime_away_score_field.valueChanged.connect(self._update_result)
        self.penalty_home_score_field.valueChanged.connect(self._update_result)
        self.penalty_away_score_field.valueChanged.connect(self._update_result)

        # Connect score changes to final score updates in statistics
        self.home_score_field.valueChanged.connect(self._update_final_score)
        self.away_score_field.valueChanged.connect(self._update_final_score)
        self.extratime_home_score_field.valueChanged.connect(self._update_final_score)
        self.extratime_away_score_field.valueChanged.connect(self._update_final_score)
        self.penalty_home_score_field.valueChanged.connect(self._update_final_score)
        self.penalty_away_score_field.valueChanged.connect(self._update_final_score)

        # Connect score changes to goals assigned validation
        self.home_score_field.valueChanged.connect(self._update_all_players_data_validation)
        self.away_score_field.valueChanged.connect(self._update_all_players_data_validation)

        # Connect opponents own goals to validation
        self.opponents_own_goals_field.valueChanged.connect(self._update_all_players_data_validation)

        # Connect radio button changes
        self.home_radio.toggled.connect(self._update_result)
        self.away_radio.toggled.connect(self._update_result)
        self.home_radio.toggled.connect(self._update_team_labels)
        self.away_radio.toggled.connect(self._update_team_labels)
        self.home_radio.toggled.connect(self._update_advanced_team_labels)
        self.away_radio.toggled.connect(self._update_advanced_team_labels)

        # Connect radio button changes to goals assigned validation
        self.home_radio.toggled.connect(self._update_all_players_data_validation)
        self.away_radio.toggled.connect(self._update_all_players_data_validation)

        # Connect opponent field changes
        self.opponent_field.textChanged.connect(self._update_team_labels)
        self.opponent_field.textChanged.connect(self._update_advanced_team_labels)

        # Connect home/away changes to venue fields
        self.home_radio.toggled.connect(self._update_venue_fields)
        self.away_radio.toggled.connect(self._update_venue_fields)

        # Connect checkbox changes
        self.halftime_checkbox.toggled.connect(self._update_result)
        self.extratime_checkbox.toggled.connect(self._update_result)
        self.penalty_checkbox.toggled.connect(self._update_result)

    def _update_result(self):
        """Calculate and update the result display."""
        # Get current scores
        home_ft = self.home_score_field.value()
        away_ft = self.away_score_field.value()

        # Determine final scores based on what's enabled
        if self.penalty_checkbox.isChecked():
            # Match decided by penalties
            home_final = self.penalty_home_score_field.value()
            away_final = self.penalty_away_score_field.value()
            finish_type = "PK"

            # Show ET score if extra time was played
            if self.extratime_checkbox.isChecked():
                et_home = self.extratime_home_score_field.value()
                et_away = self.extratime_away_score_field.value()
                score_display = f"ET score: {et_home}-{et_away}"
            else:
                score_display = f"FT score: {home_ft}-{away_ft}"

        elif self.extratime_checkbox.isChecked():
            # Match decided in extra time
            home_final = self.extratime_home_score_field.value()
            away_final = self.extratime_away_score_field.value()
            finish_type = "ET"
            score_display = f"FT score: {home_ft}-{away_ft}"
        else:
            # Match decided in full time
            home_final = home_ft
            away_final = away_ft
            finish_type = "FT"
            score_display = ""

        # Determine result based on home/away and final scores
        is_home = self.home_radio.isChecked()

        if home_final > away_final:
            # Home team won
            if is_home:
                result = "Win"
                color = "green"
            else:
                result = "Loss"
                color = "red"
        elif home_final < away_final:
            # Away team won
            if is_home:
                result = "Loss"
                color = "red"
            else:
                result = "Win"
                color = "green"
        else:
            # Draw
            result = "Draw"
            color = "orange"

        # Format result text
        if finish_type == "FT":
            result_text = result
        else:
            result_text = f"{result} (on {finish_type})"

        # Add score display if applicable
        if score_display:
            result_text += f" - {score_display}"

        # Update label with color
        self.result_label.setText(result_text)
        self.result_label.setStyleSheet(f"font-weight: bold; color: {color};")



    def _save_match(self):
        """Save the new match."""
        try:
            # Collect all match data
            match_data = {
                'date': self.date_field.date().toString(Qt.DateFormat.ISODate),
                'time': self.time_field.time().toString(Qt.DateFormat.ISODate),
                'competition_id': self.competition_field.currentText(),
                'season': self.season_field.currentText(),
                'matchday': self.matchday_field.value(),
                'round': self.round_field.value(),
                'is_home': self.home_radio.isChecked(),
                'opponent': self.opponent_field.text(),
                'team_score': self.home_score_field.value(),
                'opponent_score': self.away_score_field.value(),
                'ht_team_score': self.halftime_home_score_field.value() if self.halftime_checkbox.isChecked() else 0,
                'ht_opponent_score': self.halftime_away_score_field.value() if self.halftime_checkbox.isChecked() else 0,
                'et_played': self.extratime_checkbox.isChecked(),
                'et_team_score': self.extratime_home_score_field.value() if self.extratime_checkbox.isChecked() else 0,
                'et_opponent_score': self.extratime_away_score_field.value() if self.extratime_checkbox.isChecked() else 0,
                'et_duration': self.et_duration_field.value() if self.extratime_checkbox.isChecked() else 0,
                'penalties_played': self.penalty_checkbox.isChecked(),
                'penalties_team_score': self.penalty_home_score_field.value() if self.penalty_checkbox.isChecked() else 0,
                'penalties_opponent_score': self.penalty_away_score_field.value() if self.penalty_checkbox.isChecked() else 0,
                'opponents_own_goals': self.opponents_own_goals_field.value(),
                'match_duration': self._get_competition_duration(),
                'stoppage_time': self.stoppage_time_field.value()
            }

            # TODO: Integrate with MatchesManager to save to database
            window_init_debug("Match data to save:")
            window_init_debug(f"  Basic info: {match_data['date']} vs {match_data['opponent']}")
            window_init_debug(f"  Score: {match_data['team_score']}-{match_data['opponent_score']}")

            # Show success message
            QMessageBox.information(self, self.tr("Success"),
                                  self.tr("Match data prepared for saving.\n(Database integration pending)"))

        except Exception as e:
            QMessageBox.critical(self, self.tr("Error"),
                               self.tr(f"Error preparing match data: {str(e)}"))
            window_init_debug(f"Error in _save_match: {e}")

    def _update_team_labels(self):
        """Update team labels in statistics based on home/away selection and opponent name."""
        if not hasattr(self, 'team_name_label') or not hasattr(self, 'opponent_name_label'):
            return

        opponent_name = self.opponent_field.text().strip()
        if not opponent_name:
            opponent_name = self.tr("Opponent")

        if self.home_radio.isChecked():
            # We are home team
            self.team_name_label.setText(self.tr("My Team"))
            self.opponent_name_label.setText(opponent_name)
        else:
            # We are away team
            self.team_name_label.setText(opponent_name)
            self.opponent_name_label.setText(self.tr("My Team"))

    def _update_final_score(self):
        """Update the final score in the Match Statistics section."""
        # Check if the final score labels exist
        if not hasattr(self, 'team_stats') or not hasattr(self, 'opponent_stats'):
            return
        if "final_score" not in self.team_stats or "final_score" not in self.opponent_stats:
            return

        # Get final scores based on what's enabled
        if self.penalty_checkbox.isChecked():
            team_final = self.penalty_home_score_field.value() if self.home_radio.isChecked() else self.penalty_away_score_field.value()
            opponent_final = self.penalty_away_score_field.value() if self.home_radio.isChecked() else self.penalty_home_score_field.value()
        elif self.extratime_checkbox.isChecked():
            team_final = self.extratime_home_score_field.value() if self.home_radio.isChecked() else self.extratime_away_score_field.value()
            opponent_final = self.extratime_away_score_field.value() if self.home_radio.isChecked() else self.extratime_home_score_field.value()
        else:
            team_final = self.home_score_field.value() if self.home_radio.isChecked() else self.away_score_field.value()
            opponent_final = self.away_score_field.value() if self.home_radio.isChecked() else self.home_score_field.value()

        # Update the final score labels
        self.team_stats["final_score"].setText(str(team_final))
        self.opponent_stats["final_score"].setText(str(opponent_final))

    def _update_advanced_team_labels(self):
        """Update team labels in advanced statistics based on home/away selection and opponent name."""
        if not hasattr(self, 'adv_team_name_label') or not hasattr(self, 'adv_opponent_name_label'):
            return

        opponent_name = self.opponent_field.text().strip()
        if not opponent_name:
            opponent_name = self.tr("Opponent")

        if self.home_radio.isChecked():
            # We are home team
            self.adv_team_name_label.setText(self.tr("My Team"))
            self.adv_opponent_name_label.setText(opponent_name)
        else:
            # We are away team
            self.adv_team_name_label.setText(opponent_name)
            self.adv_opponent_name_label.setText(self.tr("My Team"))

    def _update_venue_fields(self):
        """Update venue fields based on home/away selection and club settings."""
        if not hasattr(self, 'venue_stadium_field') or not hasattr(self, 'venue_city_field'):
            return

        if self.home_radio.isChecked():
            # Home match - auto-fill stadium and city from club settings
            stadium_name = self._get_club_stadium_name()
            city_name = self._get_club_city_name()

            if stadium_name:
                self.venue_stadium_field.setText(stadium_name)
                self.venue_stadium_field.setReadOnly(True)
                self.venue_stadium_field.setStyleSheet("background-color: #f0f0f0;")
                self.venue_stadium_field.setPlaceholderText("")
            else:
                self.venue_stadium_field.setText("")
                self.venue_stadium_field.setReadOnly(False)
                self.venue_stadium_field.setStyleSheet("")
                self.venue_stadium_field.setPlaceholderText(self.tr("No stadium name in club settings"))

            if city_name:
                self.venue_city_field.setText(city_name)
                self.venue_city_field.setReadOnly(True)
                self.venue_city_field.setStyleSheet("background-color: #f0f0f0;")
                self.venue_city_field.setPlaceholderText("")
            else:
                self.venue_city_field.setText("")
                self.venue_city_field.setReadOnly(False)
                self.venue_city_field.setStyleSheet("")
                self.venue_city_field.setPlaceholderText(self.tr("No city in club settings"))
        else:
            # Away match - allow manual entry
            self.venue_stadium_field.clear()
            self.venue_stadium_field.setReadOnly(False)
            self.venue_stadium_field.setStyleSheet("")
            self.venue_stadium_field.setPlaceholderText("")

            self.venue_city_field.clear()
            self.venue_city_field.setReadOnly(False)
            self.venue_city_field.setStyleSheet("")
            self.venue_city_field.setPlaceholderText("")

    def _get_club_stadium_name(self):
        """Get club stadium name from settings."""
        try:
            club_manager = self._get_club_data_manager()
            stadium_name = club_manager.get_data("stadium_name", "")
            print(f"DEBUG: Retrieved club stadium name: '{stadium_name}'")
            return stadium_name if stadium_name else None
        except Exception as e:
            print(f"ERROR: Failed to get club stadium name: {e}")
            return None

    def _get_club_city_name(self):
        """Get club city name from settings."""
        try:
            club_manager = self._get_club_data_manager()
            city_name = club_manager.get_data("city", "")
            print(f"DEBUG: Retrieved club city name: '{city_name}'")
            return city_name if city_name else None
        except Exception as e:
            print(f"ERROR: Failed to get club city name: {e}")
            return None

    def _cancel(self):
        """Cancel creating new match (placeholder)."""
        print("Cancel button clicked - placeholder")
        # Close the window
        self.close()

    def _restore_window_state(self):
        """Restore window size and splitter position from settings."""
        settings = QSettings()

        # Restore window size
        size = settings.value("newmatch_page/window_size")
        if size:
            self.resize(size)
        else:
            # Set default size if no saved size
            self.resize(1200, 800)

        # Restore splitter position
        splitter_state = settings.value("newmatch_page/splitter_state")
        if splitter_state and hasattr(self, 'splitter'):
            self.splitter.restoreState(splitter_state)

    def _save_window_state(self):
        """Save window size and splitter position to settings."""
        settings = QSettings()
        settings.setValue("newmatch_page/window_size", self.size())
        settings.setValue("newmatch_page/splitter_state", self.splitter.saveState())

    def _save_splitter_state(self):
        """Save splitter position when moved."""
        settings = QSettings()
        settings.setValue("newmatch_page/splitter_state", self.splitter.saveState())

    def closeEvent(self, event):
        """Handle window close event to save state."""
        self._save_window_state()
        super().closeEvent(event)

    def _on_add_player_clicked(self):
        """Handle Add Player button click - open player selection popup."""
        # Create player selection dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Select Players"))
        dialog.setMinimumSize(500, 400)
        dialog.setModal(True)

        dialog_layout = QVBoxLayout(dialog)

        # Create instruction label
        instruction_label = QLabel(self.tr("Select players from the matchday roster:"))
        instruction_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        dialog_layout.addWidget(instruction_label)

        # Create player list with multiple selection mode
        player_list = QListWidget()
        player_list.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)

        # Get players with match_day selection enabled
        players = self.roster_manager.get_players_by_selection("match_day", True)

        if not players:
            # Show message if no players are available
            no_players_label = QLabel(self.tr("No players available for matchday selection.\nPlease set matchday selection for players in the Roster page."))
            no_players_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_players_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
            dialog_layout.addWidget(no_players_label)
        else:
            # Sort players by position (GK, DF, MF, AT) then by last name
            position_order = {"GK": 0, "DF": 1, "MF": 2, "AT": 3}
            players.sort(key=lambda p: (position_order.get(p.get("position", ""), 4), p.get("last_name", "")))

            # Add players to the list
            for player in players:
                # Format: "Last Name, First Name (Position) - #Number"
                player_name = f"{player.get('last_name', '')} {player.get('first_name', '')}"
                position = player.get('position', '')
                shirt_number = player.get('shirt_number', '')

                display_text = f"{player_name.strip()}"
                if position:
                    display_text += f" ({position})"
                if shirt_number:
                    display_text += f" - #{shirt_number}"

                player_list.addItem(display_text)
                # Store player data in user role
                player_list.item(player_list.count() - 1).setData(Qt.ItemDataRole.UserRole, player)

            dialog_layout.addWidget(player_list)

        # Create button layout
        button_layout = QHBoxLayout()

        # Selection helper buttons
        select_all_btn = QPushButton(self.tr("Select All"))
        clear_selection_btn = QPushButton(self.tr("Clear Selection"))

        # Action buttons
        add_selected_btn = QPushButton(self.tr("Add Selected"))
        cancel_btn = QPushButton(self.tr("Cancel"))

        # Add buttons to layout
        button_layout.addWidget(select_all_btn)
        button_layout.addWidget(clear_selection_btn)
        button_layout.addStretch()
        button_layout.addWidget(add_selected_btn)
        button_layout.addWidget(cancel_btn)

        dialog_layout.addLayout(button_layout)

        # Connect button signals
        def select_all():
            for i in range(player_list.count()):
                player_list.item(i).setSelected(True)

        def clear_selection():
            for i in range(player_list.count()):
                player_list.item(i).setSelected(False)

        def add_selected_players():
            selected_items = player_list.selectedItems()

            if selected_items:
                # Temporarily disconnect signals to prevent sorting during batch add
                self.players_table.itemChanged.disconnect()

                # Add all players first without sorting
                for item in selected_items:
                    player_data = item.data(Qt.ItemDataRole.UserRole)
                    if player_data:
                        self._add_player_to_table(player_data, sort_after_add=False)

                # Reconnect signals
                self.players_table.itemChanged.connect(self._on_table_item_changed)

                # Update counters and sort once after all players are added
                self._update_counters()
                self._sort_players_table()

                # Clear selection to show players were added, but keep dialog open
                player_list.clearSelection()
            else:
                QMessageBox.information(dialog, self.tr("No Selection"),
                                      self.tr("Please select at least one player to add."))

        select_all_btn.clicked.connect(select_all)
        clear_selection_btn.clicked.connect(clear_selection)
        add_selected_btn.clicked.connect(add_selected_players)
        cancel_btn.clicked.connect(dialog.reject)

        # Show dialog
        dialog.exec()

    def _on_clear_all_subs_clicked(self):
        """Handle Clear All Subs button click - clear all substitution data."""
        # Check if there are any substitutions to clear
        if not self.substitution_pairs:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                self.tr("No Substitutions"),
                self.tr("There are no substitutions to clear.")
            )
            return

        # Validate that clearing all substitutions won't create orphaned goals/assists
        validation_result = self._validate_clear_all_substitutions()
        if not validation_result['can_clear']:
            # Show warning about orphaned events
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Cannot Clear All Substitutions"),
                validation_result['message']
            )
            return

        # Show confirmation dialog
        from PySide6.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self,
            self.tr("Clear All Substitutions"),
            self.tr("Are you sure you want to clear all substitution data?\n\n"
                   "This will remove all Sub In and Sub Out data for all players."),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self._clear_all_substitutions()

    def _clear_all_substitutions(self):
        """Clear all substitution data from the table and tracking list."""
        # Get column indices
        sub_out_col = self._get_column_index("Sub Out")
        sub_in_col = self._get_column_index("Sub In")

        # Clear all Sub Out and Sub In cells
        for row in range(self.players_table.rowCount()):
            sub_out_item = self.players_table.item(row, sub_out_col)
            sub_in_item = self.players_table.item(row, sub_in_col)

            if sub_out_item:
                sub_out_item.setText("")
            if sub_in_item:
                sub_in_item.setText("")

        # Clear the substitution pairs tracking list
        cleared_count = len(self.substitution_pairs)
        self.substitution_pairs.clear()

        # Update minutes for all players
        self._update_all_player_minutes()

        # Print confirmation
        print(f"Cleared all substitutions: {cleared_count} substitution(s) removed")

    def _on_remove_player_clicked(self):
        """Handle Remove Player button click - remove selected players from table."""
        selected_rows = []
        for i in range(self.players_table.rowCount()):
            if self.players_table.item(i, 0) and self.players_table.item(i, 0).isSelected():
                selected_rows.append(i)

        if not selected_rows:
            # If no rows selected, check if any row is currently highlighted
            current_row = self.players_table.currentRow()
            if current_row >= 0:
                selected_rows = [current_row]

        if not selected_rows:
            QMessageBox.information(self, self.tr("No Selection"),
                                  self.tr("Please select a player to remove."))
            return

        # Check if removing these players would cause lineup issues BEFORE any confirmation
        validation_result = self._validate_player_removal(selected_rows)
        if not validation_result['can_remove']:
            QMessageBox.warning(
                self,
                self.tr("Cannot Remove Players"),
                validation_result['message']
            )
            return

        # Confirm removal
        if len(selected_rows) == 1:
            message = self.tr("Are you sure you want to remove the selected player?")
        else:
            message = self.tr(f"Are you sure you want to remove {len(selected_rows)} selected players?")

        reply = QMessageBox.question(self, self.tr("Confirm Removal"), message,
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            # Remove rows in reverse order to maintain indices
            for row in sorted(selected_rows, reverse=True):
                self.players_table.removeRow(row)

            # Update counters and sort table after removal
            self._update_counters()
            self._sort_players_table()
            self._update_all_players_data_validation()

    def _validate_player_removal(self, selected_rows):
        """
        Validate if removing the selected players would cause lineup issues or data loss.
        Returns dict with 'can_remove' (bool) and 'message' (str).
        """
        # First check for players with existing data that would be lost
        players_with_data = []

        for row in selected_rows:
            player_item = self.players_table.item(row, self._get_column_index("Player"))
            player_name = player_item.text() if player_item else f"Player row {row}"

            player_data_issues = []

            # Check for substitution data
            sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
            sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))
            sub_in_value = sub_in_item.text().strip() if sub_in_item else ""
            sub_out_value = sub_out_item.text().strip() if sub_out_item else ""

            if sub_in_value and sub_in_value != "0":
                player_data_issues.append(f"Sub In: {sub_in_value}")
            if sub_out_value and sub_out_value != "0":
                player_data_issues.append(f"Sub Out: {sub_out_value}")

            # Check for goals and assists
            if row in self.goals_widgets:
                goals_count = self.goals_widgets[row].get_value()
                if goals_count > 0:
                    player_data_issues.append(f"Goals: {goals_count}")

            if row in self.assists_widgets:
                assists_count = self.assists_widgets[row].get_value()
                if assists_count > 0:
                    player_data_issues.append(f"Assists: {assists_count}")

            # Check for yellow cards
            y_item = self.players_table.item(row, self._get_column_index("Y"))
            if y_item and y_item.text().strip() and int(y_item.text().strip() or "0") > 0:
                player_data_issues.append(f"Yellow cards: {y_item.text()}")

            # Check for red cards
            r_item = self.players_table.item(row, self._get_column_index("R"))
            if r_item and r_item.text().strip() and int(r_item.text().strip() or "0") > 0:
                player_data_issues.append(f"Red card: {r_item.text()}")

            # Check for injury data
            inj_item = self.players_table.item(row, self._get_column_index("Inj"))
            if inj_item and inj_item.text().strip() and int(inj_item.text().strip() or "0") > 0:
                player_data_issues.append("Injury data")

            if player_data_issues:
                players_with_data.append({
                    'name': player_name,
                    'data': player_data_issues
                })

        # If any players have data, prevent removal
        if players_with_data:
            message_parts = [self.tr("Cannot remove the following players because they have match data that would be lost:")]
            message_parts.append("")

            for player_info in players_with_data:
                data_text = ", ".join(player_info['data'])
                message_parts.append(f"• {player_info['name']}: {data_text}")

            message_parts.append("")
            message_parts.append(self.tr("Please clear their match data first (goals, assists, cards, substitutions) before removing them."))

            return {
                'can_remove': False,
                'message': "\n".join(message_parts)
            }

        # Now check for lineup issues
        # Get current lineup count
        current_lineup_count = 0
        lineup_col = self._get_column_index("Lineup")

        # Count players currently in lineup, excluding those to be removed
        lineup_players_to_remove = 0
        for row in range(self.players_table.rowCount()):
            if row in selected_rows:
                # This player will be removed - check if they're in lineup
                lineup_item = self.players_table.item(row, lineup_col)
                if lineup_item and lineup_item.checkState() == Qt.CheckState.Checked:
                    lineup_players_to_remove += 1
            else:
                # This player will remain - check if they're in lineup
                lineup_item = self.players_table.item(row, lineup_col)
                if lineup_item and lineup_item.checkState() == Qt.CheckState.Checked:
                    current_lineup_count += 1

        # Calculate lineup count after removal
        final_lineup_count = current_lineup_count  # Already excludes removed players

        # Get minimum required to start
        min_req_start = self._get_competition_min_req_start()

        # Check if removal would cause lineup to fall below minimum
        if final_lineup_count < min_req_start:
            # Create detailed error message
            player_count = len(selected_rows)
            if player_count == 1:
                if lineup_players_to_remove == 1:
                    message = self.tr(
                        "Cannot remove this player because they are in the starting lineup.\n\n"
                        "Removing them would leave {0} players in the lineup, but the minimum required is {1}.\n\n"
                        "Please uncheck their Lineup status first, or add more players to the lineup."
                    ).format(final_lineup_count, min_req_start)
                else:
                    message = self.tr(
                        "Cannot remove this player at this time.\n\n"
                        "The current lineup has {0} players, but the minimum required is {1}.\n\n"
                        "Please add more players to the lineup first."
                    ).format(current_lineup_count, min_req_start)
            else:
                if lineup_players_to_remove > 0:
                    message = self.tr(
                        "Cannot remove {0} players because {1} of them are in the starting lineup.\n\n"
                        "Removing them would leave {2} players in the lineup, but the minimum required is {3}.\n\n"
                        "Please uncheck their Lineup status first, or add more players to the lineup."
                    ).format(player_count, lineup_players_to_remove, final_lineup_count, min_req_start)
                else:
                    message = self.tr(
                        "Cannot remove {0} players at this time.\n\n"
                        "The current lineup has {1} players, but the minimum required is {2}.\n\n"
                        "Please add more players to the lineup first."
                    ).format(player_count, current_lineup_count, min_req_start)

            return {
                'can_remove': False,
                'message': message
            }

        # Removal is allowed
        return {
            'can_remove': True,
            'message': ''
        }

    def _get_competition_x_a_side(self):
        """Get the X-a-side value for the current competition."""
        # Check if competition field exists
        if not hasattr(self, 'competition_field') or not self.competition_field:
            return 11  # Default value

        # Get the selected competition name from the competition field
        competition_name = self.competition_field.currentText()

        if not competition_name:
            return 11  # Default value

        # Load competitions from settings
        settings = QSettings()
        settings.beginGroup("football_competitions")

        # Get number of competitions
        count = settings.value("count", 0, int)

        # Find the competition by name and get x_a_side value
        for i in range(count):
            name = settings.value(f"{i}/name", "")
            if name == competition_name:
                x_a_side = settings.value(f"{i}/x_a_side", 11, int)
                settings.endGroup()
                return x_a_side

        settings.endGroup()
        return 11  # Default value if not found

    def _get_competition_min_req_start(self):
        """Get the minimum required to start value for the current competition."""
        if not hasattr(self, 'competition_field') or not self.competition_field:
            return 9  # Default value

        competition_name = self.competition_field.currentText()
        if not competition_name:
            return 9  # Default value

        settings = QSettings()
        settings.beginGroup("football_competitions")
        count = settings.value("count", 0, int)

        for i in range(count):
            name = settings.value(f"{i}/name", "")
            if name == competition_name:
                min_req_start = settings.value(f"{i}/min_req_start", 9, int)
                settings.endGroup()
                return min_req_start

        settings.endGroup()
        return 9  # Default value if not found

    def _get_competition_min_dur_match(self):
        """Get the minimum during match value for the current competition."""
        if not hasattr(self, 'competition_field') or not self.competition_field:
            return 7  # Default value

        competition_name = self.competition_field.currentText()
        if not competition_name:
            return 7  # Default value

        settings = QSettings()
        settings.beginGroup("football_competitions")
        count = settings.value("count", 0, int)

        for i in range(count):
            name = settings.value(f"{i}/name", "")
            if name == competition_name:
                min_dur_match = settings.value(f"{i}/min_dur_match", 7, int)
                settings.endGroup()
                return min_dur_match

        settings.endGroup()
        return 7  # Default value if not found

    def _get_competition_max_subs(self):
        """Get the maximum substitutions value for the current competition."""
        if not hasattr(self, 'competition_field') or not self.competition_field:
            return 5  # Default value

        competition_name = self.competition_field.currentText()
        if not competition_name:
            return 5  # Default value

        settings = QSettings()
        settings.beginGroup("football_competitions")
        count = settings.value("count", 0, int)

        for i in range(count):
            name = settings.value(f"{i}/name", "")
            if name == competition_name:
                max_subs = settings.value(f"{i}/max_subs", 5, int)
                settings.endGroup()
                return max_subs

        settings.endGroup()
        return 5  # Default value if not found

    def _get_competition_allow_sub_sub(self):
        """Get the allow substituted substitute value for the current competition."""
        if not hasattr(self, 'competition_field') or not self.competition_field:
            return False  # Default value

        competition_name = self.competition_field.currentText()
        if not competition_name:
            return False  # Default value

        settings = QSettings()
        settings.beginGroup("football_competitions")
        count = settings.value("count", 0, int)

        for i in range(count):
            name = settings.value(f"{i}/name", "")
            if name == competition_name:
                allow_sub_sub = settings.value(f"{i}/allow_sub_sub", False, bool)
                settings.endGroup()
                return allow_sub_sub

        settings.endGroup()
        return False  # Default value if not found

    def _get_competition_duration(self):
        """Get the duration value for the current competition."""
        if not hasattr(self, 'competition_field') or not self.competition_field:
            return 90  # Default value

        competition_name = self.competition_field.currentText()
        if not competition_name:
            return 90  # Default value

        settings = QSettings()
        settings.beginGroup("football_competitions")
        count = settings.value("count", 0, int)

        for i in range(count):
            name = settings.value(f"{i}/name", "")
            if name == competition_name:
                duration = settings.value(f"{i}/duration", 90, int)
                settings.endGroup()
                return duration

        settings.endGroup()
        return 90  # Default value if not found

    def _update_match_duration(self):
        """Update the match duration label based on the selected competition."""
        duration = self._get_competition_duration()
        if hasattr(self, 'match_duration_label'):
            self.match_duration_label.setText(f"{duration} min")
        self._update_total_duration()

    def _update_total_duration(self):
        """Calculate and update the total duration label."""
        if not hasattr(self, 'match_duration_label') or not hasattr(self, 'total_duration_label'):
            return

        # Get match duration
        duration_text = self.match_duration_label.text().replace(" min", "")
        try:
            match_duration = int(duration_text)
        except (ValueError, AttributeError):
            match_duration = 90

        # Get stoppage time
        stoppage_time = 0
        if hasattr(self, 'stoppage_time_field'):
            stoppage_time = self.stoppage_time_field.value()

        # Get ET duration if extra time is enabled
        et_duration = 0
        if hasattr(self, 'et_duration_field') and hasattr(self, 'extratime_checkbox'):
            if self.extratime_checkbox.isChecked():
                et_duration = self.et_duration_field.value()

        # Calculate total
        total_duration = match_duration + stoppage_time + et_duration

        # Update label
        self.total_duration_label.setText(f"{total_duration} min")

    def _on_table_item_changed(self, item):
        """Handle table item changes (lineup, captain checkbox changes)."""
        if not item:
            return

        column_index = item.column()
        lineup_col = self._get_column_index("Lineup")
        captain_col = self._get_column_index("C")

        # Handle Lineup column changes
        if column_index == lineup_col:
            # First check if player has substitution data - prevent changes if they do
            row = item.row()
            sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))
            sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))

            sub_out_value = sub_out_item.text().strip() if sub_out_item else ""
            sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

            # Check if player has substitution data, goals, or assists - prevent lineup changes if they do
            has_substitution_data = sub_out_value or (sub_in_value and sub_in_value != "0")

            # Check if player has goals or assists
            player_goals = self._get_player_goals_count(row)
            player_assists = self._get_player_assists_count(row)
            has_goals_or_assists = player_goals > 0 or player_assists > 0

            if has_substitution_data or has_goals_or_assists:
                # Get player name for the message
                name_item = self.players_table.item(row, self._get_column_index("Player"))
                player_name = name_item.text() if name_item else "Player"

                # Block signals to prevent recursive calls during revert
                self.players_table.blockSignals(True)

                # Revert the change
                item.setCheckState(Qt.CheckState.Unchecked if item.checkState() == Qt.CheckState.Checked else Qt.CheckState.Checked)

                # Re-enable signals
                self.players_table.blockSignals(False)

                # Create detailed warning message
                reasons = []
                if has_substitution_data:
                    reasons.append("substitution data")
                if player_goals > 0:
                    goals_text = f"{player_goals} goal{'s' if player_goals != 1 else ''}"
                    reasons.append(goals_text)
                if player_assists > 0:
                    assists_text = f"{player_assists} assist{'s' if player_assists != 1 else ''}"
                    reasons.append(assists_text)

                reason_text = ", ".join(reasons)

                # Show warning message
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(
                    self,
                    self.tr("Cannot Change Lineup Status"),
                    self.tr("Cannot change lineup status for {0} because they have {1}. "
                           "Clear this data first if you need to change their lineup status.").format(player_name, reason_text)
                )
                return  # Exit early, don't proceed with other checks or sorting

            # Check if user is trying to enable lineup for too many players
            if item.checkState() == Qt.CheckState.Checked:
                # Count current lineup players
                lineup_count = 0
                for check_row in range(self.players_table.rowCount()):
                    lineup_item = self.players_table.item(check_row, lineup_col)
                    if lineup_item and lineup_item.checkState() == Qt.CheckState.Checked:
                        lineup_count += 1

                # Get the X-a-side limit
                x_a_side = self._get_competition_x_a_side()

                # If we exceed the limit, show message and revert
                if lineup_count > x_a_side:
                    # Block signals to prevent recursive calls during revert
                    self.players_table.blockSignals(True)

                    # Revert the change
                    item.setCheckState(Qt.CheckState.Unchecked)

                    # Re-enable signals
                    self.players_table.blockSignals(False)

                    QMessageBox.warning(
                        self,
                        self.tr("Lineup Limit Exceeded"),
                        self.tr(f"Cannot have more than {x_a_side} players in the lineup for this competition.")
                    )
                    return

            # Check if user is trying to disable lineup and would go below minimum required
            elif item.checkState() == Qt.CheckState.Unchecked:
                # When player is removed from lineup, clear their goals/assists if they have no sub in
                row = item.row()
                sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
                sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

                # If player has no sub in data, they won't be playing, so clear goals/assists
                if not sub_in_value or sub_in_value == "0":
                    self._clear_player_events_for_non_playing(row)

                # Count current lineup players (excluding the one being unchecked)
                lineup_count = 0
                for row in range(self.players_table.rowCount()):
                    lineup_item = self.players_table.item(row, lineup_col)
                    if lineup_item and lineup_item.checkState() == Qt.CheckState.Checked and lineup_item != item:
                        lineup_count += 1

                # Get the minimum required to start
                min_req_start = self._get_competition_min_req_start()

                # If we would go below minimum, show message and revert
                if lineup_count < min_req_start:
                    # Block signals to prevent recursive calls during revert
                    self.players_table.blockSignals(True)

                    # Revert the change
                    item.setCheckState(Qt.CheckState.Checked)

                    # Re-enable signals
                    self.players_table.blockSignals(False)

                    QMessageBox.warning(
                        self,
                        self.tr("Minimum Players Required"),
                        self.tr(f"Cannot have fewer than {min_req_start} players in the starting lineup for this competition.")
                    )
                    return

            self._update_counters()
            self._sort_players_table()
            self._update_all_players_data_validation()

        # Handle Captain column changes
        elif column_index == captain_col:
            if item.checkState() == Qt.CheckState.Checked:
                # Only one captain allowed - uncheck all other captain checkboxes
                current_row = item.row()
                for row in range(self.players_table.rowCount()):
                    if row != current_row:
                        captain_item = self.players_table.item(row, captain_col)
                        if captain_item and captain_item.checkState() == Qt.CheckState.Checked:
                            captain_item.setCheckState(Qt.CheckState.Unchecked)

            # Update captain validation
            self._update_all_players_data_validation()

        # Handle advanced statistics column changes
        elif column_index == self._get_column_index("Attempts"):
            self._validate_attempts_field(item)
        elif column_index == self._get_column_index("On Target"):
            self._validate_on_target_field(item)
        elif self._is_advanced_stats_column(column_index):
            self._validate_advanced_stats_field(item, column_index)

    def _validate_attempts_field(self, item):
        """Validate the Attempts field."""
        # Block signals to prevent recursion
        self.players_table.blockSignals(True)

        try:
            row = item.row()
            text = item.text().strip()

            # If empty, set to 0
            if not text:
                item.setText("0")
                return

            try:
                # Try to convert to integer
                value = int(text)

                # Ensure value is non-negative
                if value < 0:
                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr("Attempts must be a non-negative integer.")
                    )
                    item.setText("0")
                    return

                # Check if player has played (only if value > 0)
                if value > 0:
                    has_played = self._validate_player_can_have_events(row, "attempts")
                    if not has_played:
                        item.setText("0")
                        return

                # Update On Target maximum if needed
                on_target_item = self.players_table.item(row, self._get_column_index("On Target"))
                if on_target_item:
                    try:
                        on_target_value = int(on_target_item.text().strip() or "0")
                        if on_target_value > value:
                            on_target_item.setText(str(value))
                    except ValueError:
                        on_target_item.setText("0")

            except ValueError:
                # Not a valid integer, reset to 0
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("Attempts must be a non-negative integer.")
                )
                item.setText("0")
        finally:
            # Re-enable signals
            self.players_table.blockSignals(False)

    def _validate_on_target_field(self, item):
        """Validate the On Target field."""
        # Block signals to prevent recursion
        self.players_table.blockSignals(True)

        try:
            row = item.row()
            text = item.text().strip()

            # If empty, set to 0
            if not text:
                item.setText("0")
                return

            try:
                # Try to convert to integer
                value = int(text)

                # Ensure value is non-negative
                if value < 0:
                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr("On Target must be a non-negative integer.")
                    )
                    item.setText("0")
                    return

                # Check if player has played (only if value > 0)
                if value > 0:
                    has_played = self._validate_player_can_have_events(row, "on target shots")
                    if not has_played:
                        item.setText("0")
                        return

                # Validate: on-target cannot be greater than attempts
                attempts_item = self.players_table.item(row, self._get_column_index("Attempts"))
                attempts_value = 0
                if attempts_item:
                    try:
                        attempts_value = int(attempts_item.text().strip() or "0")
                    except ValueError:
                        attempts_value = 0

                if value > attempts_value:
                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr("On Target shots cannot be greater than total attempts.")
                    )
                    # Set on-target equal to attempts
                    item.setText(str(attempts_value))

            except ValueError:
                # Not a valid integer, reset to 0
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("On Target must be a non-negative integer.")
                )
                item.setText("0")
        finally:
            # Re-enable signals
            self.players_table.blockSignals(False)

    def _is_advanced_stats_column(self, column_index):
        """Check if a column index belongs to advanced statistics."""
        advanced_stats_columns = [
            "X-Goals", "X-Assists", "Shot Creating Actions", "Goal Creating Actions",
            "Passes Short", "Passes Short Completed", "Passes Medium", "Passes Medium Completed",
            "Passes Long", "Passes Long Completed", "Passes", "Passes Completed",
            "Key Passes", "Through Balls", "Passes 3/3", "Passes PA", "Progressive Passes", "Switch Play",
            "Passes Offside", "Passes Blocked", "Total Crosses", "Crosses to Penalty Area", "Total Passing Distance",
            "Progressive Passing Distance", "Progressive Carries", "Take-ons Attempts", "Take-ons Successful",
            "Aerial Duels Won", "Aerial Duels Lost", "Touches", "Tackles", "Tackles Won",
            "Interceptions", "Clearances", "Opp Shots Blocked", "Opp Passes Blocked", "Mistakes Lead to Opp Shot"
        ]

        for col_name in advanced_stats_columns:
            if column_index == self._get_column_index(col_name):
                return True
        return False

    def _validate_advanced_stats_field(self, item, column_index):
        """Validate advanced statistics fields."""
        # Block signals to prevent recursion
        self.players_table.blockSignals(True)

        try:
            row = item.row()
            text = item.text().strip()

            # Get column name for specific validation
            column_name = self.column_headers[column_index]

            # If empty, set to 0
            if not text:
                item.setText("0")
                return

            # Handle percentage columns (read-only, auto-calculated)
            if "%" in column_name:
                self._calculate_percentage_field(item, column_index)
                return

            # Handle decimal fields (X-Goals, X-Assists, distances)
            if column_name in ["X-Goals", "X-Assists", "Total Passing Distance", "Progressive Passing Distance"]:
                try:
                    value = float(text)
                    if value < 0:
                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Value"),
                            self.tr(f"{column_name} must be a non-negative number.")
                        )
                        item.setText("0")
                        return

                    # Check if player has played (only if value > 0)
                    if value > 0:
                        has_played = self._validate_player_can_have_events(row, column_name.lower())
                        if not has_played:
                            item.setText("0")
                            return

                except ValueError:
                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr(f"{column_name} must be a valid number.")
                    )
                    item.setText("0")
            else:
                # Handle integer fields
                try:
                    value = int(text)
                    if value < 0:
                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Value"),
                            self.tr(f"{column_name} must be a non-negative integer.")
                        )
                        item.setText("0")
                        return

                    # Check if player has played (only if value > 0)
                    if value > 0:
                        has_played = self._validate_player_can_have_events(row, column_name.lower())
                        if not has_played:
                            item.setText("0")
                            return

                except ValueError:
                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr(f"{column_name} must be a non-negative integer.")
                    )
                    item.setText("0")

            # Handle specific field validations
            self._validate_specific_advanced_stats(item, column_index)

            # Auto-update total passes if short/medium/long passes change
            if column_name in ["Passes Short", "Passes Medium", "Passes Long"]:
                self._update_total_passes(row)
            elif column_name in ["Passes Short Completed", "Passes Medium Completed", "Passes Long Completed"]:
                self._update_total_passes_completed(row)

        finally:
            # Re-enable signals
            self.players_table.blockSignals(False)

    def _calculate_percentage_field(self, item, column_index):
        """Calculate percentage fields automatically."""
        row = item.row()
        column_name = self.column_headers[column_index]

        if column_name == "Passes Short %":
            self._calculate_passes_percentage(row, "Passes Short", "Passes Short Completed", "Passes Short %")
        elif column_name == "Passes Medium %":
            self._calculate_passes_percentage(row, "Passes Medium", "Passes Medium Completed", "Passes Medium %")
        elif column_name == "Passes Long %":
            self._calculate_passes_percentage(row, "Passes Long", "Passes Long Completed", "Passes Long %")
        elif column_name == "Passes %":
            self._calculate_passes_percentage(row, "Passes", "Passes Completed", "Passes %")
        elif column_name == "Tackles %":
            self._calculate_passes_percentage(row, "Tackles", "Tackles Won", "Tackles %")
        elif column_name == "Aerial Duels Won %":
            self._calculate_aerial_duels_percentage(row)

    def _calculate_passes_percentage(self, row, total_col, completed_col, percentage_col):
        """Calculate passing percentage."""
        total_item = self.players_table.item(row, self._get_column_index(total_col))
        completed_item = self.players_table.item(row, self._get_column_index(completed_col))
        percentage_item = self.players_table.item(row, self._get_column_index(percentage_col))

        if total_item and completed_item and percentage_item:
            try:
                total = int(total_item.text() or "0")
                completed = int(completed_item.text() or "0")

                if total > 0:
                    percentage = (completed / total) * 100
                    percentage_item.setText(f"{percentage:.1f}%")
                else:
                    percentage_item.setText("0.0%")
            except ValueError:
                percentage_item.setText("0.0%")

    def _calculate_aerial_duels_percentage(self, row):
        """Calculate aerial duels won percentage."""
        won_item = self.players_table.item(row, self._get_column_index("Aerial Duels Won"))
        lost_item = self.players_table.item(row, self._get_column_index("Aerial Duels Lost"))
        percentage_item = self.players_table.item(row, self._get_column_index("Aerial Duels Won %"))

        if won_item and lost_item and percentage_item:
            try:
                won = int(won_item.text() or "0")
                lost = int(lost_item.text() or "0")
                total = won + lost

                if total > 0:
                    percentage = (won / total) * 100
                    percentage_item.setText(f"{percentage:.1f}%")
                else:
                    percentage_item.setText("0.0%")
            except ValueError:
                percentage_item.setText("0.0%")

    def _validate_specific_advanced_stats(self, item, column_index):
        """Handle specific validation rules for advanced statistics."""
        row = item.row()
        column_name = self.column_headers[column_index]

        # Validate completed passes don't exceed total passes
        if "Completed" in column_name:
            total_col = column_name.replace(" Completed", "")
            total_item = self.players_table.item(row, self._get_column_index(total_col))
            if total_item:
                try:
                    completed = int(item.text() or "0")
                    total = int(total_item.text() or "0")
                    if completed > total:
                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Value"),
                            self.tr(f"Completed {total_col.lower()} cannot exceed total {total_col.lower()}.")
                        )
                        item.setText(str(total))
                except ValueError:
                    pass

        # Validate take-ons successful don't exceed attempts
        if column_name == "Take-ons Successful":
            attempts_item = self.players_table.item(row, self._get_column_index("Take-ons Attempts"))
            if attempts_item:
                try:
                    successful = int(item.text() or "0")
                    attempts = int(attempts_item.text() or "0")
                    if successful > attempts:
                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Value"),
                            self.tr("Successful take-ons cannot exceed total attempts.")
                        )
                        item.setText(str(attempts))
                except ValueError:
                    pass

    def _update_total_passes(self, row):
        """Update total passes based on short + medium + long passes."""
        short_item = self.players_table.item(row, self._get_column_index("Passes Short"))
        medium_item = self.players_table.item(row, self._get_column_index("Passes Medium"))
        long_item = self.players_table.item(row, self._get_column_index("Passes Long"))
        total_item = self.players_table.item(row, self._get_column_index("Passes"))

        if short_item and medium_item and long_item and total_item:
            try:
                short = int(short_item.text() or "0")
                medium = int(medium_item.text() or "0")
                long = int(long_item.text() or "0")
                calculated_total = short + medium + long

                current_total = int(total_item.text() or "0")

                # If user has manually edited total passes, ask for confirmation
                if current_total != 0 and current_total != calculated_total:
                    reply = QMessageBox.question(
                        self,
                        self.tr("Update Total Passes"),
                        self.tr(f"Total passes is currently {current_total}, but calculated total from short+medium+long is {calculated_total}.\n\n"
                               f"Do you want to update total passes to {calculated_total}?\n\n"
                               f"Click 'Yes' to auto-calculate, 'No' to keep manual value."),
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.Yes
                    )

                    if reply == QMessageBox.StandardButton.Yes:
                        total_item.setText(str(calculated_total))
                        # Also update percentage
                        self._calculate_passes_percentage(row, "Passes", "Passes Completed", "Passes %")
                else:
                    # Auto-update if total is 0 or matches calculated
                    total_item.setText(str(calculated_total))
                    # Also update percentage
                    self._calculate_passes_percentage(row, "Passes", "Passes Completed", "Passes %")

            except ValueError:
                pass

    def _update_total_passes_completed(self, row):
        """Update total passes completed based on short + medium + long passes completed."""
        short_item = self.players_table.item(row, self._get_column_index("Passes Short Completed"))
        medium_item = self.players_table.item(row, self._get_column_index("Passes Medium Completed"))
        long_item = self.players_table.item(row, self._get_column_index("Passes Long Completed"))
        total_item = self.players_table.item(row, self._get_column_index("Passes Completed"))

        if short_item and medium_item and long_item and total_item:
            try:
                short = int(short_item.text() or "0")
                medium = int(medium_item.text() or "0")
                long = int(long_item.text() or "0")
                calculated_total = short + medium + long

                current_total = int(total_item.text() or "0")

                # If user has manually edited total passes completed, ask for confirmation
                if current_total != 0 and current_total != calculated_total:
                    reply = QMessageBox.question(
                        self,
                        self.tr("Update Total Passes Completed"),
                        self.tr(f"Total passes completed is currently {current_total}, but calculated total from short+medium+long is {calculated_total}.\n\n"
                               f"Do you want to update total passes completed to {calculated_total}?\n\n"
                               f"Click 'Yes' to auto-calculate, 'No' to keep manual value."),
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.Yes
                    )

                    if reply == QMessageBox.StandardButton.Yes:
                        total_item.setText(str(calculated_total))
                        # Also update percentage
                        self._calculate_passes_percentage(row, "Passes", "Passes Completed", "Passes %")
                else:
                    # Auto-update if total is 0 or matches calculated
                    total_item.setText(str(calculated_total))
                    # Also update percentage
                    self._calculate_passes_percentage(row, "Passes", "Passes Completed", "Passes %")

            except ValueError:
                pass

    def _update_counters(self):
        """Update both lineup and subs counter displays."""
        lineup_count = 0
        subs_count = 0
        lineup_col = self._get_column_index("Lineup")

        for row in range(self.players_table.rowCount()):
            lineup_item = self.players_table.item(row, lineup_col)
            if lineup_item:
                if lineup_item.checkState() == Qt.CheckState.Checked:
                    lineup_count += 1
                else:
                    subs_count += 1

        self.lineup_counter_label.setText(self.tr(f"Lineup = {lineup_count}"))
        self.subs_counter_label.setText(self.tr(f"Subs = {subs_count}"))

        # Update players data validation
        self._update_players_data_validation()

    def _update_x_a_side_label(self):
        """Update the X-a-side label based on current competition."""
        if hasattr(self, 'x_a_side_label'):
            x_a_side = self._get_competition_x_a_side()
            self.x_a_side_label.setText(self.tr(f"{x_a_side}-a-side"))

    def _update_competition_labels(self):
        """Update all competition-based labels when competition changes."""
        self._update_x_a_side_label()
        self._update_match_duration()

        if hasattr(self, 'min_req_start_label'):
            min_req_start = self._get_competition_min_req_start()
            self.min_req_start_label.setText(self.tr(f"Min req start = {min_req_start}"))

        if hasattr(self, 'min_dur_match_label'):
            min_dur_match = self._get_competition_min_dur_match()
            self.min_dur_match_label.setText(self.tr(f"Min dur match = {min_dur_match}"))

        if hasattr(self, 'max_subs_label'):
            max_subs = self._get_competition_max_subs()
            self.max_subs_label.setText(self.tr(f"Max subs = {max_subs}"))

        if hasattr(self, 'allow_sub_sub_label'):
            allow_sub_sub = self._get_competition_allow_sub_sub()
            sub_sub_text = "true" if allow_sub_sub else "false"
            self.allow_sub_sub_label.setText(self.tr(f"Allow sub-sub {sub_sub_text}"))

        # Update starting lineup validation label
        if hasattr(self, 'starting_lineup_label'):
            min_req_start = self._get_competition_min_req_start()
            self.starting_lineup_label.setText(self.tr(f"Starting lineup ≥ {min_req_start}"))

        # Update validation status
        self._update_players_data_validation()

    def _update_players_data_validation(self):
        """Update Players Data validation status based on starting lineup count."""
        if not hasattr(self, 'starting_lineup_label') or not hasattr(self, 'starting_lineup_checkmark') or not hasattr(self, 'starting_lineup_xmark'):
            return

        # Count current lineup players
        lineup_count = 0
        if hasattr(self, 'players_table'):
            lineup_col = self._get_column_index("Lineup")
            for row in range(self.players_table.rowCount()):
                lineup_item = self.players_table.item(row, lineup_col)
                if lineup_item and lineup_item.checkState() == Qt.CheckState.Checked:
                    lineup_count += 1

        # Get minimum required to start
        min_req_start = self._get_competition_min_req_start()

        # Check if validation passes
        validation_passes = lineup_count >= min_req_start

        if validation_passes:
            # Show green with checkmark, hide X mark
            ok_color = get_color("match_validation_ok").name()
            self.starting_lineup_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")
            self.starting_lineup_checkmark.show()
            self.starting_lineup_xmark.hide()
            # Update Players Data title to green
            if hasattr(self, 'players_data_title'):
                self.players_data_title.setStyleSheet(f"font-weight: bold; color: {ok_color};")
        else:
            # Show red with X mark, hide checkmark (serious validation)
            serious_color = get_color("match_validation_serious").name()
            self.starting_lineup_label.setStyleSheet(f"color: {serious_color}; font-size: 11px;")
            self.starting_lineup_checkmark.hide()
            self.starting_lineup_xmark.show()
            # Update Players Data title to red
            if hasattr(self, 'players_data_title'):
                self.players_data_title.setStyleSheet(f"font-weight: bold; color: {serious_color};")

    def _sort_players_table(self):
        """Sort players table by lineup status (true first) then by position (GK, DF, MF, AT)."""
        if self.players_table.rowCount() == 0:
            return

        # Temporarily disconnect signals to prevent recursive calls during sorting
        self.players_table.itemChanged.disconnect()

        # Get column indices
        lineup_col = self._get_column_index("Lineup")
        number_col = self._get_column_index("No")
        name_col = self._get_column_index("Player")
        position_col = self._get_column_index("Pos")
        events_col = self._get_column_index("Events")
        captain_col = self._get_column_index("C")
        sub_out_col = self._get_column_index("Sub Out")
        sub_in_col = self._get_column_index("Sub In")
        min_col = self._get_column_index("Min")
        y_col = self._get_column_index("Y")
        r_col = self._get_column_index("R")
        y1_col = self._get_column_index("Y1")
        y2_col = self._get_column_index("Y2")
        rr_col = self._get_column_index("Rr")
        r1_col = self._get_column_index("R1")
        goals_col = self._get_column_index("Goals")
        goals_min_col = self._get_column_index("Goals min")
        assists_col = self._get_column_index("Assists")
        assists_min_col = self._get_column_index("Assists min")
        inj_col = self._get_column_index("Inj")
        injr_col = self._get_column_index("InjR")

        # Get all advanced statistics column indices
        advanced_stats_cols = {}
        advanced_stats_columns = [
            "Attempts", "On Target", "X-Goals", "X-Assists", "Shot Creating Actions", "Goal Creating Actions",
            "Passes Short", "Passes Short Completed", "Passes Short %", "Passes Medium", "Passes Medium Completed",
            "Passes Medium %", "Passes Long", "Passes Long Completed", "Passes Long %", "Passes", "Passes Completed",
            "Passes %", "Key Passes", "Through Balls", "Passes 3/3", "Passes PA", "Progressive Passes", "Switch Play",
            "Passes Offside", "Passes Blocked", "Total Crosses", "Crosses to Penalty Area", "Total Passing Distance",
            "Progressive Passing Distance", "Progressive Carries", "Take-ons Attempts", "Take-ons Successful",
            "Aerial Duels Won", "Aerial Duels Lost", "Aerial Duels Won %", "Touches", "Tackles", "Tackles Won",
            "Tackles %", "Interceptions", "Clearances", "Opp Shots Blocked", "Opp Passes Blocked", "Mistakes Lead to Opp Shot"
        ]

        for col_name in advanced_stats_columns:
            advanced_stats_cols[col_name] = self._get_column_index(col_name)

        # Collect all player data
        players_data = []
        for row in range(self.players_table.rowCount()):
            lineup_item = self.players_table.item(row, lineup_col)
            number_item = self.players_table.item(row, number_col)
            name_item = self.players_table.item(row, name_col)
            position_item = self.players_table.item(row, position_col)
            events_item = self.players_table.item(row, events_col)
            captain_item = self.players_table.item(row, captain_col)
            sub_out_item = self.players_table.item(row, sub_out_col)
            sub_in_item = self.players_table.item(row, sub_in_col)
            min_item = self.players_table.item(row, min_col)
            y_item = self.players_table.item(row, y_col)
            r_item = self.players_table.item(row, r_col)
            y1_item = self.players_table.item(row, y1_col)
            y2_item = self.players_table.item(row, y2_col)
            rr_item = self.players_table.item(row, rr_col)
            r1_item = self.players_table.item(row, r1_col)
            goals_min_item = self.players_table.item(row, goals_min_col)
            assists_min_item = self.players_table.item(row, assists_min_col)
            inj_item = self.players_table.item(row, inj_col)
            injr_item = self.players_table.item(row, injr_col)

            # Get all advanced statistics items
            advanced_stats_items = {}
            for col_name in advanced_stats_columns:
                col_index = advanced_stats_cols[col_name]
                if col_index >= 0:
                    advanced_stats_items[col_name] = self.players_table.item(row, col_index)

            # Get values from split cell widgets instead of items
            goals_widget = self.players_table.cellWidget(row, goals_col)
            assists_widget = self.players_table.cellWidget(row, assists_col)

            if lineup_item and name_item and position_item:
                is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
                position = position_item.text()

                # Define position order for sorting
                position_order = {"GK": 0, "DF": 1, "MF": 2, "AT": 3}
                position_sort_key = position_order.get(position, 4)

                players_data.append({
                    'lineup': is_lineup,
                    'position_sort_key': position_sort_key,
                    'position': position,
                    'number': number_item.text() if number_item else "",
                    'name': name_item.text(),
                    'player_id': name_item.data(Qt.ItemDataRole.UserRole),
                    'events': events_item.text() if events_item else "",
                    'is_captain': captain_item.checkState() == Qt.CheckState.Checked if captain_item else False,
                    'sub_out': sub_out_item.text() if sub_out_item else "",
                    'sub_in': sub_in_item.text() if sub_in_item else "",
                    'minutes': self._calculate_player_minutes(row),
                    'yellow_cards': y_item.text() if y_item else "0",
                    'red_cards': r_item.text() if r_item else "0",
                    'y1_minute': y1_item.text() if y1_item else "",
                    'y2_minute': y2_item.text() if y2_item else "",
                    'red_reason': rr_item.text() if rr_item else "",
                    'r1_minute': r1_item.text() if r1_item else "",
                    'goals': str(goals_widget.get_value()) if goals_widget else "0",
                    'goals_min': goals_min_item.text() if goals_min_item else "",
                    'assists': str(assists_widget.get_value()) if assists_widget else "0",
                    'assists_min': assists_min_item.text() if assists_min_item else "",
                    'injury_minute': inj_item.text() if inj_item else "",
                    'injury_reason': injr_item.text() if injr_item else "",
                    'lineup_flags': lineup_item.flags(),
                    'lineup_alignment': lineup_item.textAlignment(),
                    'captain_flags': captain_item.flags() if captain_item else Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled,
                    'captain_alignment': captain_item.textAlignment() if captain_item else Qt.AlignmentFlag.AlignCenter
                })

                # Add all advanced statistics data
                for col_name in advanced_stats_columns:
                    item = advanced_stats_items.get(col_name)
                    players_data[-1][col_name.lower().replace(' ', '_').replace('-', '_')] = item.text() if item else "0"

        # Sort by lineup status (True first) then by position order
        players_data.sort(key=lambda x: (not x['lineup'], x['position_sort_key']))

        # Clear table and repopulate with sorted data
        self.players_table.setRowCount(0)

        for player_data in players_data:
            row = self.players_table.rowCount()
            self.players_table.insertRow(row)

            # Lineup column (checkbox)
            lineup_item = QTableWidgetItem()
            lineup_item.setFlags(player_data['lineup_flags'])
            lineup_item.setCheckState(Qt.CheckState.Checked if player_data['lineup'] else Qt.CheckState.Unchecked)
            lineup_item.setTextAlignment(player_data['lineup_alignment'])
            self.players_table.setItem(row, lineup_col, lineup_item)

            # Shirt Number column
            number_item = QTableWidgetItem(player_data['number'])
            number_item.setFlags(number_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            number_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, number_col, number_item)

            # Player Name column
            name_item = QTableWidgetItem(player_data['name'])
            name_item.setFlags(name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            name_item.setData(Qt.ItemDataRole.UserRole, player_data['player_id'])
            self.players_table.setItem(row, name_col, name_item)

            # Position column
            position_item = QTableWidgetItem(player_data['position'])
            position_item.setFlags(position_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            position_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, position_col, position_item)

            # Events column (placeholder for now)
            events_item = QTableWidgetItem(player_data['events'])
            events_item.setFlags(events_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only for now
            events_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, events_col, events_item)

            # Captain column (checkbox)
            captain_item = QTableWidgetItem()
            captain_item.setFlags(player_data['captain_flags'])
            captain_item.setCheckState(Qt.CheckState.Checked if player_data['is_captain'] else Qt.CheckState.Unchecked)
            captain_item.setTextAlignment(player_data['captain_alignment'])
            self.players_table.setItem(row, captain_col, captain_item)

            # Sub Out column (placeholder for now)
            sub_out_item = QTableWidgetItem(player_data['sub_out'])
            sub_out_item.setFlags(sub_out_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only for now
            sub_out_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, sub_out_col, sub_out_item)

            # Sub In column (placeholder for now)
            sub_in_item = QTableWidgetItem(player_data['sub_in'])
            sub_in_item.setFlags(sub_in_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only for now
            sub_in_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, sub_in_col, sub_in_item)

            # Min column (calculated minutes played)
            min_item = QTableWidgetItem(str(player_data['minutes']))
            min_item.setFlags(min_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            min_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, min_col, min_item)

            # Y column (yellow cards)
            y_item = QTableWidgetItem(player_data['yellow_cards'])
            y_item.setFlags(y_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
            y_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, y_col, y_item)

            # R column (red cards)
            r_item = QTableWidgetItem(player_data['red_cards'])
            r_item.setFlags(r_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
            r_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, r_col, r_item)

            # Y1 column (1st yellow minute)
            y1_item = QTableWidgetItem(player_data['y1_minute'])
            y1_item.setFlags(y1_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            y1_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, y1_col, y1_item)

            # Y2 column (2nd yellow minute)
            y2_item = QTableWidgetItem(player_data['y2_minute'])
            y2_item.setFlags(y2_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            y2_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, y2_col, y2_item)

            # Rr column (red card reason)
            rr_item = QTableWidgetItem(player_data['red_reason'])
            rr_item.setFlags(rr_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            rr_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, rr_col, rr_item)

            # R1 column (red card minute)
            r1_item = QTableWidgetItem(player_data['r1_minute'])
            r1_item.setFlags(r1_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            r1_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, r1_col, r1_item)

            # Goals column (with split cell widget)
            goals_value = int(player_data['goals']) if player_data['goals'] and player_data['goals'].isdigit() else 0
            self._create_split_cell_widget(row, goals_col, goals_value)

            # Goals min column
            goals_min_item = QTableWidgetItem(player_data['goals_min'])
            goals_min_item.setFlags(goals_min_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            goals_min_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, goals_min_col, goals_min_item)

            # Assists column (with split cell widget)
            assists_value = int(player_data['assists']) if player_data['assists'] and player_data['assists'].isdigit() else 0
            self._create_split_cell_widget(row, assists_col, assists_value)

            # Assists min column
            assists_min_item = QTableWidgetItem(player_data['assists_min'])
            assists_min_item.setFlags(assists_min_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            assists_min_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, assists_min_col, assists_min_item)

            # Inj column (injury minute)
            inj_col = self._get_column_index("Inj")
            inj_item = QTableWidgetItem(player_data['injury_minute'])
            inj_item.setFlags(inj_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            inj_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, inj_col, inj_item)

            # InjR column (injury reason)
            injr_col = self._get_column_index("InjR")
            injr_item = QTableWidgetItem(player_data['injury_reason'])
            injr_item.setFlags(injr_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            injr_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, injr_col, injr_item)

            # Attempts column (editable)
            attempts_col = self._get_column_index("Attempts")
            attempts_item = QTableWidgetItem(player_data['attempts'])
            attempts_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, attempts_col, attempts_item)

            # Add all advanced statistics columns
            for col_name in advanced_stats_columns:
                col_index = advanced_stats_cols[col_name]
                if col_index >= 0:
                    data_key = col_name.lower().replace(' ', '_').replace('-', '_')
                    item = QTableWidgetItem(player_data.get(data_key, "0"))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.players_table.setItem(row, col_index, item)

        # Reconnect signals
        self.players_table.itemChanged.connect(self._on_table_item_changed)

    def _create_split_cell_widget(self, row, column, initial_value=0):
        """Create a split cell widget for Goals or Assists columns."""
        widget = SplitCellWidget(initial_value, self.players_table)

        # Connect value change signal to update data
        def on_value_changed(new_value):
            # Store the value in a way that can be retrieved during sorting
            widget.stored_value = new_value

            # Update validation when goals or assists change
            if column == self._get_column_index("Goals"):
                # Handle goals reduction - remove excess minutes if needed
                self._handle_goals_reduction(row, new_value)
                self._update_all_players_data_validation()
            elif column == self._get_column_index("Assists"):
                # Handle assists reduction - remove excess minutes if needed
                self._handle_assists_reduction(row, new_value)
                self._update_all_players_data_validation()

        widget.value_changed.connect(on_value_changed)

        # Set the widget in the table
        self.players_table.setCellWidget(row, column, widget)

        # Store reference for later access
        if column == self._get_column_index("Goals"):
            self.goals_widgets[row] = widget
        elif column == self._get_column_index("Assists"):
            self.assists_widgets[row] = widget

        return widget

    def _add_player_to_table(self, player_data, sort_after_add=True):
        """Add a player to the players table.

        Args:
            player_data (dict): Player data dictionary
            sort_after_add (bool): Whether to sort and update counters after adding (default True)
        """
        # Get column indices
        lineup_col = self._get_column_index("Lineup")
        number_col = self._get_column_index("No")
        name_col = self._get_column_index("Player")
        position_col = self._get_column_index("Pos")
        events_col = self._get_column_index("Events")
        captain_col = self._get_column_index("C")
        sub_out_col = self._get_column_index("Sub Out")
        sub_in_col = self._get_column_index("Sub In")
        min_col = self._get_column_index("Min")
        y_col = self._get_column_index("Y")
        r_col = self._get_column_index("R")
        y1_col = self._get_column_index("Y1")
        y2_col = self._get_column_index("Y2")
        rr_col = self._get_column_index("Rr")
        r1_col = self._get_column_index("R1")
        goals_col = self._get_column_index("Goals")
        assists_col = self._get_column_index("Assists")

        # Check if player is already in the table
        player_id = player_data.get('player_id')
        for row in range(self.players_table.rowCount()):
            existing_item = self.players_table.item(row, name_col)  # Player name column
            if existing_item and existing_item.data(Qt.ItemDataRole.UserRole) == player_id:
                QMessageBox.information(self, self.tr("Player Already Added"),
                                      self.tr("This player is already in the match squad."))
                return

        # Add new row
        row = self.players_table.rowCount()
        self.players_table.insertRow(row)

        # Get x-a-side value to determine if this player should be in lineup
        x_a_side = self._get_competition_x_a_side()
        current_lineup_count = 0

        # Count current lineup players
        for existing_row in range(row):  # Only count existing rows
            existing_lineup_item = self.players_table.item(existing_row, lineup_col)
            if existing_lineup_item and existing_lineup_item.checkState() == Qt.CheckState.Checked:
                current_lineup_count += 1

        # Lineup column (checkbox)
        lineup_item = QTableWidgetItem()
        lineup_item.setFlags(lineup_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)

        # Automatically set lineup=true for first x-a-side players
        if current_lineup_count < x_a_side:
            lineup_item.setCheckState(Qt.CheckState.Checked)
        else:
            lineup_item.setCheckState(Qt.CheckState.Unchecked)

        lineup_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, lineup_col, lineup_item)

        # Shirt Number column
        shirt_number = player_data.get('shirt_number', '')
        number_item = QTableWidgetItem(str(shirt_number) if shirt_number else "")
        number_item.setFlags(number_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
        number_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, number_col, number_item)

        # Player Name column (Last Name + First Name)
        player_name = f"{player_data.get('last_name', '')} {player_data.get('first_name', '')}"
        name_item = QTableWidgetItem(player_name.strip())
        name_item.setFlags(name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
        name_item.setData(Qt.ItemDataRole.UserRole, player_id)  # Store player ID
        self.players_table.setItem(row, name_col, name_item)

        # Position column
        position = player_data.get('position', '')
        position_item = QTableWidgetItem(position)
        position_item.setFlags(position_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
        position_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, position_col, position_item)

        # Events column (placeholder for now)
        events_item = QTableWidgetItem("")
        events_item.setFlags(events_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only for now
        events_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, events_col, events_item)

        # Captain column (checkbox)
        captain_item = QTableWidgetItem()
        captain_item.setFlags(captain_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
        captain_item.setCheckState(Qt.CheckState.Unchecked)  # Default not captain
        captain_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, captain_col, captain_item)

        # Sub Out column (placeholder for now)
        sub_out_item = QTableWidgetItem("")
        sub_out_item.setFlags(sub_out_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only for now
        sub_out_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, sub_out_col, sub_out_item)

        # Sub In column (placeholder for now)
        sub_in_item = QTableWidgetItem("")
        sub_in_item.setFlags(sub_in_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only for now
        sub_in_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, sub_in_col, sub_in_item)

        # Min column (calculated minutes played)
        min_item = QTableWidgetItem("0")  # Default to 0 for new players
        min_item.setFlags(min_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
        min_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, min_col, min_item)

        # Y column (yellow cards)
        y_item = QTableWidgetItem("0")  # Default to 0 for new players
        y_item.setFlags(y_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
        y_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, y_col, y_item)

        # R column (red cards)
        r_item = QTableWidgetItem("0")  # Default to 0 for new players
        r_item.setFlags(r_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
        r_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, r_col, r_item)

        # Y1 column (1st yellow minute)
        y1_item = QTableWidgetItem("")  # Default to empty for new players
        y1_item.setFlags(y1_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
        y1_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, y1_col, y1_item)

        # Y2 column (2nd yellow minute)
        y2_item = QTableWidgetItem("")  # Default to empty for new players
        y2_item.setFlags(y2_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
        y2_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, y2_col, y2_item)

        # Rr column (red card reason)
        rr_item = QTableWidgetItem("")  # Default to empty for new players
        rr_item.setFlags(rr_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
        rr_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, rr_col, rr_item)

        # R1 column (red card minute)
        r1_item = QTableWidgetItem("")  # Default to empty for new players
        r1_item.setFlags(r1_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
        r1_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, r1_col, r1_item)

        # Goals column (with split cell widget)
        self._create_split_cell_widget(row, goals_col, 0)

        # Goals min column (clickable for minute entry)
        goals_min_col = self._get_column_index("Goals min")
        goals_min_item = QTableWidgetItem("")  # Default to empty for new players
        goals_min_item.setFlags(goals_min_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
        goals_min_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        print(f"DEBUG: Creating Goals min item for row {row}, col {goals_min_col}")
        self.players_table.setItem(row, goals_min_col, goals_min_item)

        # Verify the item was set
        verify_item = self.players_table.item(row, goals_min_col)
        print(f"DEBUG: Verification - Goals min item exists: {verify_item is not None}")

        # Assists column (with split cell widget)
        self._create_split_cell_widget(row, assists_col, 0)

        # Assists min column (clickable for minute entry)
        assists_min_col = self._get_column_index("Assists min")
        assists_min_item = QTableWidgetItem("")  # Default to empty for new players
        assists_min_item.setFlags(assists_min_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
        assists_min_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, assists_min_col, assists_min_item)

        # Inj column (injury minute)
        inj_col = self._get_column_index("Inj")
        inj_item = QTableWidgetItem("")  # Default to empty for new players
        inj_item.setFlags(inj_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
        inj_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, inj_col, inj_item)

        # InjR column (injury reason)
        injr_col = self._get_column_index("InjR")
        injr_item = QTableWidgetItem("")  # Default to empty for new players
        injr_item.setFlags(injr_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
        injr_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, injr_col, injr_item)

        # Attempts column (editable)
        attempts_col = self._get_column_index("Attempts")
        attempts_item = QTableWidgetItem("0")  # Default to 0 for new players
        attempts_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.players_table.setItem(row, attempts_col, attempts_item)

        # Add all advanced statistics columns with default values
        advanced_stats_columns_list = [
            "Attempts", "On Target", "X-Goals", "X-Assists", "Shot Creating Actions", "Goal Creating Actions",
            "Passes Short", "Passes Short Completed", "Passes Short %", "Passes Medium", "Passes Medium Completed",
            "Passes Medium %", "Passes Long", "Passes Long Completed", "Passes Long %", "Passes", "Passes Completed",
            "Passes %", "Key Passes", "Through Balls", "Passes 3/3", "Passes PA", "Progressive Passes", "Switch Play",
            "Passes Offside", "Passes Blocked", "Total Crosses", "Crosses to Penalty Area", "Total Passing Distance",
            "Progressive Passing Distance", "Progressive Carries", "Take-ons Attempts", "Take-ons Successful",
            "Aerial Duels Won", "Aerial Duels Lost", "Aerial Duels Won %", "Touches", "Tackles", "Tackles Won",
            "Tackles %", "Interceptions", "Clearances", "Opp Shots Blocked", "Opp Passes Blocked", "Mistakes Lead to Opp Shot"
        ]

        for col_name in advanced_stats_columns_list:
            col_index = self._get_column_index(col_name)
            if col_index >= 0:
                item = QTableWidgetItem("0")  # Default to 0 for new players
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.players_table.setItem(row, col_index, item)

        # Add all performance columns with default values (clickable for popup)
        performance_columns_list = [
            "Passing", "Creativity", "Dribbling-Carrying",
            "Marking", "Positioning", "Aerial",
            "Speed", "Stamina", "Power",
            "Decision-Making", "Concentration", "Work-Rate"
        ]

        for col_name in performance_columns_list:
            col_index = self._get_column_index(col_name)
            if col_index >= 0:
                item = QTableWidgetItem("0")  # Default to 0 for new players
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only (click-based)
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.players_table.setItem(row, col_index, item)

        # Update counters and sort table after adding player (if requested)
        if sort_after_add:
            self._update_counters()
            self._sort_players_table()

        # Update all players data validation
        self._update_all_players_data_validation()

    def _on_players_table_clicked(self, index):
        """Handle clicks on the players table for substitution functionality."""
        if not index.isValid():
            return

        row = index.row()
        column = index.column()

        # Check if click is on Sub Out, Sub In, Y, R, Goals min, Assists min, Inj, or Performance columns
        sub_out_col = self._get_column_index("Sub Out")
        sub_in_col = self._get_column_index("Sub In")
        y_col = self._get_column_index("Y")
        r_col = self._get_column_index("R")
        goals_min_col = self._get_column_index("Goals min")
        assists_min_col = self._get_column_index("Assists min")
        inj_col = self._get_column_index("Inj")

        # Performance columns
        performance_columns = [
            "Passing", "Creativity", "Dribbling-Carrying",
            "Marking", "Positioning", "Aerial",
            "Speed", "Stamina", "Power",
            "Decision-Making", "Concentration", "Work-Rate"
        ]

        if column == sub_out_col:
            self._handle_sub_out_click(row)
        elif column == sub_in_col:
            self._handle_sub_in_click(row)
        elif column == y_col:
            self._handle_yellow_card_click(row)
        elif column == r_col:
            self._handle_red_card_click(row)
        elif column == goals_min_col:
            self._handle_goals_min_click(row)
        elif column == assists_min_col:
            self._handle_assists_min_click(row)
        elif column == inj_col:
            self._handle_injury_click(row)
        else:
            # Check if click is on a performance column
            column_header = self.column_headers[column] if column < len(self.column_headers) else ""
            if column_header in performance_columns:
                self._handle_performance_click(row, column, column_header)

    def _on_players_table_double_clicked(self, index):
        """Handle double-clicks on the players table for yellow card functionality."""
        if not index.isValid():
            return

        row = index.row()
        column = index.column()

        # Check if double-click is on Y column
        y_col = self._get_column_index("Y")

        if column == y_col:
            self._handle_yellow_card_double_click(row)

    def _handle_yellow_card_double_click(self, row):
        """Handle double-clicks on Y (Yellow cards) column - same as single click for this implementation."""
        # According to the requirements, double-click should have the same behavior as single click
        # for yellow cards, so we just call the single click handler
        self._handle_yellow_card_click(row)

    def _handle_sub_out_click(self, row):
        """Handle clicks on Sub Out column."""
        # Get player information
        lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
        sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
        name_item = self.players_table.item(row, self._get_column_index("Player"))

        if not lineup_item or not name_item:
            return

        is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
        sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

        # Rule: Only players who actually played can be substituted out
        # - Players who started (lineup=true) can be subbed out
        # - Players who came in as substitute (sub_in > 0) can be subbed out
        # - Players who never played (lineup=false AND sub_in=empty/zero) cannot be subbed out
        if not is_lineup and (not sub_in_value or sub_in_value == "0"):
            return

        # Check if player already has a sub out value - don't allow if they do
        sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))
        if sub_out_item and sub_out_item.text().strip():
            # Player already has sub out value, ignore click
            return

        # Check substitution limit before allowing new substitution
        if not self._can_make_substitution():
            return

        # Valid click - show player selection popup for substitution in
        self._show_sub_in_player_selection(row)

    def _handle_sub_in_click(self, row):
        """Handle clicks on Sub In column."""
        # Get player information
        lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
        sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
        name_item = self.players_table.item(row, self._get_column_index("Player"))

        if not lineup_item or not name_item:
            return

        is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
        sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

        # Rule: Only unused substitutes can be substituted in
        # - Players who didn't start (lineup=false) AND have no sub_in data can be subbed in
        if is_lineup or (sub_in_value and sub_in_value != "0"):
            return

        # Check substitution limit before allowing new substitution
        if not self._can_make_substitution():
            return

        # Valid click - show player selection popup for substitution out
        self._show_sub_out_player_selection(row)

    def _can_make_substitution(self):
        """Check if a new substitution can be made based on competition limits."""
        # Get the maximum substitutions allowed
        max_subs = self._get_competition_max_subs()

        # Count current substitutions (only paired substitutions, not "no replacement")
        current_subs = 0
        for substitution in self.substitution_pairs:
            if not substitution.get('no_replacement', False):
                current_subs += 1

        # Check if we can make another substitution
        if current_subs >= max_subs:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Substitution Limit Reached"),
                self.tr("Cannot make more substitutions. Maximum allowed: {0}\n\n"
                       "Note: Players substituted out without replacement do not count towards this limit.").format(max_subs)
            )
            return False

        return True

    def _handle_yellow_card_click(self, row):
        """Handle clicks on Y (Yellow cards) column with 3-state cycling and minute popups."""
        y_col = self._get_column_index("Y")
        r_col = self._get_column_index("R")

        y_item = self.players_table.item(row, y_col)
        r_item = self.players_table.item(row, r_col)

        if not y_item:
            return

        # Get current yellow card value
        current_y = y_item.text().strip()
        if not current_y or current_y == "":
            current_y = "0"

        # Cycle through values: 0 → 1 → 2 → 0
        if current_y == "0":
            new_y = "1"
        elif current_y == "1":
            new_y = "2"
        else:  # current_y == "2" or any other value
            new_y = "0"

        # Update yellow card value
        y_item.setText(new_y)

        # Handle minute popups and related data based on new value
        if new_y == "1":
            # Show minute popup for 1st yellow card
            self._show_yellow_card_minute_popup(row, 1)
        elif new_y == "2":
            # Show minute popup for 2nd yellow card
            self._show_yellow_card_minute_popup(row, 2)
            # Auto-set red card and reason
            if r_item:
                r_item.setText("1")
            self._set_red_card_reason(row, "2Y")
        elif new_y == "0":
            # Clear all yellow and red card data when going back to 0
            self._clear_yellow_red_card_data(row)

    def _handle_red_card_click(self, row):
        """Handle clicks on R (Red cards) column with 2-state toggle and minute popup."""
        r_col = self._get_column_index("R")
        y_col = self._get_column_index("Y")

        r_item = self.players_table.item(row, r_col)
        y_item = self.players_table.item(row, y_col)

        if not r_item:
            return

        # Get current red card and yellow card values
        current_r = r_item.text().strip()
        current_y = y_item.text().strip() if y_item else "0"
        if not current_r or current_r == "":
            current_r = "0"

        # Toggle between 0 and 1
        if current_r == "0":
            new_r = "1"
        else:  # current_r == "1" or any other value
            new_r = "0"

        # Update red card value
        r_item.setText(new_r)

        # Handle minute popup and related data
        if new_r == "1":
            # Only show minute popup if Y is not 2 (direct red card)
            if current_y != "2":
                self._show_red_card_minute_popup(row)
                # Set reason for direct red card
                self._set_red_card_reason(row, "1R")
        else:
            # Clear red card data when toggling off
            self._clear_red_card_data(row)

    def _show_yellow_card_minute_popup(self, row, card_number):
        """Show minute selection popup for yellow card."""
        from app.widgets.minute_selector_widget import MinuteSelectorPopup

        # Get match duration for available minutes
        match_duration = self._get_total_match_duration()

        # Filter available minutes based on card number
        if card_number == 2:
            # For 2nd yellow card, only allow minutes >= 1st yellow card minute
            y1_col = self._get_column_index("Y1")
            y1_item = self.players_table.item(row, y1_col)
            if y1_item and y1_item.text().strip():
                try:
                    first_yellow_minute = int(y1_item.text().strip())
                    # Available minutes start from the 1st yellow minute
                    available_minutes = list(range(first_yellow_minute, match_duration + 1))
                except ValueError:
                    # If Y1 is invalid, use all minutes
                    available_minutes = list(range(1, match_duration + 1))
            else:
                # If no Y1 minute, use all minutes
                available_minutes = list(range(1, match_duration + 1))
        else:
            # For 1st yellow card, use all minutes
            available_minutes = list(range(1, match_duration + 1))

        # Create and show minute selector popup
        popup = MinuteSelectorPopup(available_minutes, 0, self)
        popup.minute_selected.connect(
            lambda minute: self._set_yellow_card_minute(row, card_number, minute)
        )

        # Position popup near the clicked cell
        y_col = self._get_column_index("Y")
        y_item = self.players_table.item(row, y_col)
        if y_item:
            # Get the visual rect of the item
            rect = self.players_table.visualItemRect(y_item)
            # Map to global coordinates
            global_pos = self.players_table.mapToGlobal(rect.bottomLeft())
            popup.show_at_position(global_pos)
        else:
            popup.show()

    def _show_red_card_minute_popup(self, row):
        """Show minute selection popup for red card."""
        from app.widgets.minute_selector_widget import MinuteSelectorPopup

        # Get match duration for available minutes
        match_duration = self._get_total_match_duration()

        # Check if player has Y=1 to filter available minutes
        y_col = self._get_column_index("Y")
        y1_col = self._get_column_index("Y1")
        y_item = self.players_table.item(row, y_col)
        y1_item = self.players_table.item(row, y1_col)

        current_y = y_item.text().strip() if y_item else "0"

        if current_y == "1" and y1_item and y1_item.text().strip():
            # Player has Y=1, red card minute must be >= yellow card minute
            try:
                yellow_minute = int(y1_item.text().strip())
                # Available minutes start from the yellow card minute
                available_minutes = list(range(yellow_minute, match_duration + 1))
            except ValueError:
                # If Y1 is invalid, use all minutes
                available_minutes = list(range(1, match_duration + 1))
        else:
            # No yellow card restriction, use all minutes
            available_minutes = list(range(1, match_duration + 1))

        # Create and show minute selector popup
        popup = MinuteSelectorPopup(available_minutes, 0, self)
        popup.minute_selected.connect(
            lambda minute: self._set_red_card_minute(row, minute)
        )

        # Position popup near the clicked cell
        r_col = self._get_column_index("R")
        r_item = self.players_table.item(row, r_col)
        if r_item:
            # Get the visual rect of the item
            rect = self.players_table.visualItemRect(r_item)
            # Map to global coordinates
            global_pos = self.players_table.mapToGlobal(rect.bottomLeft())
            popup.show_at_position(global_pos)
        else:
            popup.show()

    def _handle_performance_click(self, row, column, column_header):
        """Handle clicks on Performance columns with popup value selector."""
        # Validate if player can have performance values
        has_played = self._validate_player_can_have_events(row, column_header.lower())

        # Get current value
        item = self.players_table.item(row, column)
        current_value = int(item.text()) if item and item.text().isdigit() else 0

        if not has_played and current_value == 0:
            # Player didn't play and trying to set a value > 0
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Invalid Performance Rating"),
                self.tr("Player was not playing during the match. Cannot set performance rating for {0}.").format(column_header)
            )
            return

        # Show value selector popup (0-10)
        self._show_performance_value_popup(row, column, column_header, current_value)

    def _show_performance_value_popup(self, row, column, column_header, current_value):
        """Show value selection popup for performance rating (0-10)."""
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QGridLayout, QPushButton, QLabel, QFrame
        from PySide6.QtCore import Qt

        # Create custom dialog for performance values (without minute symbols)
        dialog = QDialog(self)
        dialog.setWindowTitle(f"{column_header} Rating")
        dialog.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowTitleHint | Qt.WindowType.WindowCloseButtonHint)
        dialog.setModal(True)
        dialog.setFixedSize(240, 180)

        # Main layout
        main_layout = QVBoxLayout(dialog)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # Header label
        header_label = QLabel(f"Select {column_header} Rating")
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_label.setStyleSheet("font-weight: bold; font-size: 12px; color: #333;")
        main_layout.addWidget(header_label)

        # Grid layout for value buttons (no frame to save space)
        grid_layout = QGridLayout()
        grid_layout.setSpacing(3)
        grid_layout.setContentsMargins(5, 5, 5, 5)

        # Create buttons for values 0-10 in a 4x3 grid (0-3, 4-7, 8-10)
        for value in range(11):
            row_pos = value // 4
            col_pos = value % 4

            button = QPushButton(str(value))
            button.setFixedSize(35, 30)
            button.setObjectName(f"performance_button_{value}")

            # Style the button
            if value == current_value:
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #2196F3;
                        color: white;
                        border: 2px solid #1976D2;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 14px;
                    }
                    QPushButton:hover {
                        background-color: #1976D2;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                """)
            else:
                button.setStyleSheet("""
                    QPushButton {
                        background-color: white;
                        color: #333;
                        border: 1px solid #ccc;
                        border-radius: 6px;
                        font-size: 14px;
                    }
                    QPushButton:hover {
                        background-color: #e3f2fd;
                        border-color: #2196F3;
                    }
                    QPushButton:pressed {
                        background-color: #bbdefb;
                    }
                """)

            # Connect button click to set value and close dialog
            button.clicked.connect(lambda checked, v=value: self._set_performance_value_and_close(row, column, v, dialog))

            grid_layout.addWidget(button, row_pos, col_pos)

        main_layout.addLayout(grid_layout)

        # Position dialog very close to the clicked cell
        item = self.players_table.item(row, column)
        if item:
            # Get the visual rect of the item
            rect = self.players_table.visualItemRect(item)
            # Map to global coordinates relative to the table
            table_global_pos = self.players_table.mapToGlobal(rect.topLeft())

            # Position dialog directly next to the cell (very close)
            x = table_global_pos.x() + rect.width() + 5  # Just 5 pixels to the right
            y = table_global_pos.y() - 10  # Slightly above the cell

            # Ensure dialog stays within screen bounds
            screen_geometry = dialog.screen().availableGeometry()
            if x + dialog.width() > screen_geometry.right():
                x = table_global_pos.x() - dialog.width() - 5  # Position to the left instead
            if y < screen_geometry.top():
                y = table_global_pos.y() + rect.height() + 5  # Position below instead
            if y + dialog.height() > screen_geometry.bottom():
                y = screen_geometry.bottom() - dialog.height() - 10

            dialog.move(x, y)
        else:
            # Center on screen if no item found
            dialog.move(dialog.screen().availableGeometry().center() - dialog.rect().center())

        dialog.exec()

    def _set_performance_value_and_close(self, row, column, value, dialog):
        """Set the performance value for a player and close the dialog."""
        try:
            # Get or create the table item
            item = self.players_table.item(row, column)
            if not item:
                item = QTableWidgetItem()
                self.players_table.setItem(row, column, item)

            # Set the value as plain text (no minute symbol)
            item.setText(str(value))
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # Store the value as data for later retrieval
            item.setData(Qt.ItemDataRole.UserRole, value)

            # Apply styling based on value
            if value == 0:
                item.setBackground(QColor("#f5f5f5"))  # Light gray for 0
            else:
                item.setBackground(QColor("#ffffff"))  # White for other values

            print(f"DEBUG: Set performance value {value} for row {row}, column {column}")

        except Exception as e:
            print(f"ERROR: Failed to set performance value: {e}")

        # Close the dialog
        dialog.accept()

    def _set_performance_value(self, row, column, value):
        """Set the performance value for a player."""
        item = self.players_table.item(row, column)
        if item:
            item.setText(str(value))

    def _set_yellow_card_minute(self, row, card_number, minute):
        """Set the minute for a yellow card."""
        if card_number == 1:
            y1_col = self._get_column_index("Y1")
            y1_item = self.players_table.item(row, y1_col)
            if y1_item:
                y1_item.setText(str(minute))
        elif card_number == 2:
            y2_col = self._get_column_index("Y2")
            y2_item = self.players_table.item(row, y2_col)
            if y2_item:
                y2_item.setText(str(minute))

    def _set_red_card_minute(self, row, minute):
        """Set the minute for a red card."""
        r1_col = self._get_column_index("R1")
        r1_item = self.players_table.item(row, r1_col)
        if r1_item:
            r1_item.setText(str(minute))

    def _set_red_card_reason(self, row, reason):
        """Set the reason for a red card."""
        rr_col = self._get_column_index("Rr")
        rr_item = self.players_table.item(row, rr_col)
        if rr_item:
            rr_item.setText(reason)

    def _clear_yellow_red_card_data(self, row):
        """Clear all yellow and red card data when Y goes from 2 to 0 or 1."""
        # Get column indices
        y1_col = self._get_column_index("Y1")
        y2_col = self._get_column_index("Y2")
        rr_col = self._get_column_index("Rr")
        r1_col = self._get_column_index("R1")
        r_col = self._get_column_index("R")

        # Clear Y1 and Y2 minutes
        y1_item = self.players_table.item(row, y1_col)
        y2_item = self.players_table.item(row, y2_col)
        if y1_item:
            y1_item.setText("")
        if y2_item:
            y2_item.setText("")

        # Clear red card data
        rr_item = self.players_table.item(row, rr_col)
        r1_item = self.players_table.item(row, r1_col)
        r_item = self.players_table.item(row, r_col)
        if rr_item:
            rr_item.setText("")
        if r1_item:
            r1_item.setText("")
        if r_item:
            r_item.setText("0")

    def _clear_red_card_data(self, row):
        """Clear red card data when R is toggled off."""
        # Get column indices
        rr_col = self._get_column_index("Rr")
        r1_col = self._get_column_index("R1")

        # Clear red card reason and minute
        rr_item = self.players_table.item(row, rr_col)
        r1_item = self.players_table.item(row, r1_col)
        if rr_item:
            rr_item.setText("")
        if r1_item:
            r1_item.setText("")

    def _calculate_player_minutes(self, row):
        """Calculate the total minutes played by a player."""
        # Get player information
        lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
        sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
        sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))

        if not lineup_item:
            return 0

        is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
        sub_in_value = sub_in_item.text().strip() if sub_in_item else ""
        sub_out_value = sub_out_item.text().strip() if sub_out_item else ""

        # Get total match duration
        total_duration = self._get_total_match_duration()

        # Calculate minutes based on player's participation
        if is_lineup:
            # Player started the match
            if sub_out_value and sub_out_value != "0":
                # Player was substituted out
                try:
                    sub_out_minute = int(sub_out_value)
                    return min(sub_out_minute, total_duration)
                except ValueError:
                    return total_duration  # If invalid sub out value, assume played full match
            else:
                # Player played the full match
                return total_duration
        else:
            # Player didn't start
            if sub_in_value and sub_in_value != "0":
                # Player was substituted in
                try:
                    sub_in_minute = int(sub_in_value)
                    if sub_out_value and sub_out_value != "0":
                        # Player was also substituted out
                        try:
                            sub_out_minute = int(sub_out_value)
                            # Minutes played = sub_out_minute - sub_in_minute
                            minutes_played = max(0, sub_out_minute - sub_in_minute)
                            return min(minutes_played, total_duration - sub_in_minute)
                        except ValueError:
                            # Invalid sub out value, assume played from sub in to end
                            return max(0, total_duration - sub_in_minute)
                    else:
                        # Player came in and played until the end
                        return max(0, total_duration - sub_in_minute)
                except ValueError:
                    return 0  # Invalid sub in value
            else:
                # Player never played
                return 0

    def _update_all_player_minutes(self):
        """Update the minutes column for all players."""
        min_col = self._get_column_index("Min")
        for row in range(self.players_table.rowCount()):
            minutes = self._calculate_player_minutes(row)
            min_item = self.players_table.item(row, min_col)
            if min_item:
                min_item.setText(str(minutes))

    def _on_players_table_right_clicked(self, position):
        """Handle right-click on the players table for clearing substitutions."""
        # Get the item at the clicked position
        item = self.players_table.itemAt(position)
        if not item:
            print("DEBUG: Right-click - no item found at position")
            return

        row = item.row()
        column = item.column()

        print(f"DEBUG: Right-click at row {row}, column {column}")

        # Check if right-click is on Sub Out, Sub In, Y, R, Goals min, or Assists min column
        sub_out_col = self._get_column_index("Sub Out")
        sub_in_col = self._get_column_index("Sub In")
        y_col = self._get_column_index("Y")
        r_col = self._get_column_index("R")
        goals_min_col = self._get_column_index("Goals min")
        assists_min_col = self._get_column_index("Assists min")

        print(f"DEBUG: Goals min column index: {goals_min_col}")
        print(f"DEBUG: Assists min column index: {assists_min_col}")

        if column == sub_out_col:
            self._show_sub_out_context_menu(row, position)
        elif column == sub_in_col:
            self._show_sub_in_context_menu(row, position)
        elif column == y_col:
            self._show_yellow_card_context_menu(row, position)
        elif column == r_col:
            self._show_red_card_context_menu(row, position)
        elif column == goals_min_col:
            print(f"DEBUG: Right-click on Goals min column")
            self._show_goals_min_context_menu(row, position)
        elif column == assists_min_col:
            print(f"DEBUG: Right-click on Assists min column")
            self._show_assists_min_context_menu(row, position)
        else:
            print(f"DEBUG: Right-click on unhandled column {column}")

    def _show_sub_out_context_menu(self, row, position):
        """Show context menu for Sub Out column actions."""
        # Get player information
        name_item = self.players_table.item(row, self._get_column_index("Player"))
        lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
        sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))
        sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))

        if not name_item or not lineup_item:
            return

        is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
        sub_out_value = sub_out_item.text().strip() if sub_out_item else ""
        sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

        # Create context menu
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)

        # Check if player has substitution data to clear
        has_sub_data = sub_out_value or (sub_in_value and sub_in_value != "0")

        # Check if player is currently playing (can be subbed out without replacement)
        player_is_playing = is_lineup or (sub_in_value and sub_in_value != "0")
        player_not_subbed_out = not sub_out_value

        # Add "Clear Substitution" action if there's data to clear
        if has_sub_data:
            clear_action = context_menu.addAction(self.tr("Clear Substitution"))
            clear_action.triggered.connect(lambda: self._handle_clear_substitution_menu_action(row))

        # Add "Sub Out Without Replacement" action if player is playing and not already subbed out
        if player_is_playing and player_not_subbed_out:
            # Add separator if we already have clear action
            if has_sub_data:
                context_menu.addSeparator()

            sub_out_action = context_menu.addAction(self.tr("Sub Out Without Replacement"))
            sub_out_action.triggered.connect(lambda: self._handle_sub_out_without_replacement_menu_action(row))

        # Only show menu if we have actions
        if context_menu.actions():
            global_pos = self.players_table.mapToGlobal(position)
            context_menu.exec(global_pos)

    def _show_sub_in_context_menu(self, row, position):
        """Show context menu for Sub In column actions."""
        # Get player information
        name_item = self.players_table.item(row, self._get_column_index("Player"))
        sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))
        sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))

        if not name_item:
            return

        sub_out_value = sub_out_item.text().strip() if sub_out_item else ""
        sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

        # Check if there's any substitution data to clear
        has_sub_data = sub_out_value or (sub_in_value and sub_in_value != "0")

        if not has_sub_data:
            return  # Nothing to clear, don't show menu

        # Create context menu
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)

        # Add "Clear Substitution" action
        clear_action = context_menu.addAction(self.tr("Clear Substitution"))
        clear_action.triggered.connect(lambda: self._handle_clear_substitution_menu_action(row))

        # Show the menu at the cursor position
        global_pos = self.players_table.mapToGlobal(position)
        context_menu.exec(global_pos)

    def _handle_clear_substitution_menu_action(self, row):
        """Handle the 'Clear Substitution' menu action."""
        # Get player information
        name_item = self.players_table.item(row, self._get_column_index("Player"))

        if not name_item:
            return

        player_name = name_item.text()

        # Validate that clearing this substitution won't create orphaned goals/assists
        validation_result = self._validate_substitution_clearing(row)
        if not validation_result['can_clear']:
            # Show warning about orphaned events
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Cannot Clear Substitution"),
                validation_result['message']
            )
            return

        # Show confirmation dialog
        from PySide6.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self,
            self.tr("Clear Substitution"),
            self.tr("Clear substitution data for {0}?\n\nThis will remove both Sub In and Sub Out data for all related players.").format(player_name),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self._clear_substitution_data(row)

    def _handle_sub_out_without_replacement_menu_action(self, row):
        """Handle the 'Sub Out Without Replacement' menu action."""
        # Directly show minute selection popup (no confirmation needed)
        self._show_minute_selection_no_replacement(row)

    def _clear_substitution_data(self, clicked_row):
        """Clear substitution data for the clicked player and their substitution partner."""
        # Get the clicked player's data
        clicked_player_id = None
        name_item = self.players_table.item(clicked_row, self._get_column_index("Player"))
        if name_item:
            clicked_player_id = name_item.data(Qt.ItemDataRole.UserRole)

        # Find and remove the substitution pair from our tracking list
        substitution_to_remove = None
        for substitution in self.substitution_pairs:
            if (substitution['sub_out_row'] == clicked_row or
                substitution['sub_in_row'] == clicked_row or
                substitution['sub_out_player_id'] == clicked_player_id or
                substitution['sub_in_player_id'] == clicked_player_id):
                substitution_to_remove = substitution
                break

        if substitution_to_remove:
            # Clear both players' substitution data
            sub_out_row = substitution_to_remove['sub_out_row']
            sub_in_row = substitution_to_remove['sub_in_row']

            # Clear Sub Out data
            if sub_out_row is not None:
                sub_out_item = self.players_table.item(sub_out_row, self._get_column_index("Sub Out"))
                if sub_out_item:
                    sub_out_item.setText("")

            # Clear Sub In data (only if there was a replacement)
            if sub_in_row is not None and not substitution_to_remove.get('no_replacement'):
                sub_in_item = self.players_table.item(sub_in_row, self._get_column_index("Sub In"))
                if sub_in_item:
                    sub_in_item.setText("")

            # Remove from tracking list
            self.substitution_pairs.remove(substitution_to_remove)

            # Print confirmation
            if substitution_to_remove.get('no_replacement'):
                sub_out_name = self.players_table.item(sub_out_row, self._get_column_index("Player")).text()
                print(f"Cleared substitution: {sub_out_name} OUT (no replacement) at minute {substitution_to_remove['minute']}")
            else:
                sub_out_name = self.players_table.item(sub_out_row, self._get_column_index("Player")).text()
                sub_in_name = self.players_table.item(sub_in_row, self._get_column_index("Player")).text()
                print(f"Cleared substitution: {sub_out_name} OUT, {sub_in_name} IN at minute {substitution_to_remove['minute']}")
        else:
            # Fallback: just clear the clicked cell if no pair found
            sub_out_item = self.players_table.item(clicked_row, self._get_column_index("Sub Out"))
            sub_in_item = self.players_table.item(clicked_row, self._get_column_index("Sub In"))

            if sub_out_item:
                sub_out_item.setText("")
            if sub_in_item:
                sub_in_item.setText("")

        # Update minutes for all players after clearing substitution
        self._update_all_player_minutes()

    def _show_sub_in_player_selection(self, sub_out_row):
        """Show popup to select a player for substitution in."""
        # Get the position of the player being substituted out for position matching
        sub_out_position = ""
        position_item = self.players_table.item(sub_out_row, self._get_column_index("Pos"))
        if position_item:
            sub_out_position = position_item.text().strip()

        # Get available players for substitution in (unused substitutes only)
        available_players = []

        for row in range(self.players_table.rowCount()):
            lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
            name_item = self.players_table.item(row, self._get_column_index("Player"))
            number_item = self.players_table.item(row, self._get_column_index("No"))
            position_item = self.players_table.item(row, self._get_column_index("Pos"))
            sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))

            if lineup_item and name_item and number_item and position_item:
                is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
                sub_in_value = sub_in_item.text().strip() if sub_in_item else ""
                player_position = position_item.text().strip()

                # Only non-lineup players who haven't already been subbed in can be subbed in
                if not is_lineup and (not sub_in_value or sub_in_value == "0"):
                    player_data = {
                        'row': row,
                        'name': name_item.text(),
                        'number': number_item.text(),
                        'position': player_position,
                        'same_position': player_position == sub_out_position,
                        'player_id': name_item.data(Qt.ItemDataRole.UserRole)
                    }
                    available_players.append(player_data)

        if not available_players:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                self.tr("No Available Players"),
                self.tr("No substitute players are available for substitution.")
            )
            return

        # Create and show player selection dialog
        self._create_player_selection_dialog(sub_out_row, available_players, sub_out_position)

    def _create_player_selection_dialog(self, sub_out_row, available_players, sub_out_position):
        """Create and show the player selection dialog."""

        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Select Player to Substitute In"))
        dialog.setMinimumSize(350, 400)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # Instruction label with position info
        sub_out_name = self.players_table.item(sub_out_row, self._get_column_index("Player")).text()
        instruction_text = self.tr(f"Select a player to substitute in for {sub_out_name} ({sub_out_position}):")
        instruction_label = QLabel(instruction_text)
        instruction_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(instruction_label)

        # Position hint label
        hint_label = QLabel(self.tr("Players in the same position are highlighted"))
        hint_label.setStyleSheet("color: #666; font-size: 11px; margin-bottom: 5px;")
        layout.addWidget(hint_label)

        # Player list
        player_list = QListWidget()

        # Add special option for substituting out without replacement
        no_replacement_item = QListWidgetItem(self.tr("⚠️ Substitute out without replacement"))
        no_replacement_item.setData(Qt.ItemDataRole.UserRole, {'no_replacement': True})
        no_replacement_item.setBackground(QColor("#FFF3E0"))  # Light orange background
        no_replacement_item.setForeground(QColor("#E65100"))  # Dark orange text
        no_replacement_item.setToolTip(self.tr("Player will be substituted out without anyone coming in"))
        player_list.addItem(no_replacement_item)

        # Add separator (visual only)
        separator_item = QListWidgetItem("─" * 40)
        separator_item.setFlags(separator_item.flags() & ~Qt.ItemFlag.ItemIsSelectable)
        separator_item.setBackground(QColor("#F5F5F5"))
        separator_item.setForeground(QColor("#999"))
        player_list.addItem(separator_item)

        for player_data in available_players:
            item_text = f"{player_data['number']} - {player_data['name']} ({player_data['position']})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, player_data)

            # Apply light highlighting for same position players
            if player_data['same_position']:
                item.setBackground(QColor("#E8F5E8"))  # Light green background
                item.setForeground(QColor("#2E7D32"))  # Darker green text

            player_list.addItem(item)

        layout.addWidget(player_list)

        # Buttons
        button_layout = QHBoxLayout()
        cancel_btn = QPushButton(self.tr("Cancel"))
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # Handle player selection (no confirmation button, direct selection)
        def on_player_selected(item):
            selected_player_data = item.data(Qt.ItemDataRole.UserRole)

            # Check if this is the separator item (not selectable)
            if not (item.flags() & Qt.ItemFlag.ItemIsSelectable):
                return

            dialog.accept()

            # Check if user selected "no replacement" option
            if selected_player_data and selected_player_data.get('no_replacement'):
                self._show_minute_selection_no_replacement(sub_out_row)
            else:
                self._show_minute_selection(sub_out_row, selected_player_data)

        player_list.itemClicked.connect(on_player_selected)

        dialog.exec()

    def _show_minute_selection_no_replacement(self, sub_out_row):
        """Show minute selection popup for substitution out without replacement."""
        from app.widgets.minute_selector_widget import MinuteSelectorPopup

        # Get valid minutes for substitution (excluding minutes before player's events)
        available_minutes = self._get_valid_substitution_minutes(sub_out_row)

        if not available_minutes:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Cannot Substitute"),
                self.tr("Player cannot be substituted out because they have events (goals/assists) that would occur after the substitution. Please clear the player's events first if you need to substitute them out earlier.")
            )
            return

        # Create and show minute selector popup with no initial selection (0 means no selection)
        popup = MinuteSelectorPopup(available_minutes, 0, self)
        popup.minute_selected.connect(
            lambda minute: self._complete_substitution_no_replacement(sub_out_row, minute)
        )

        # Position popup near the clicked cell
        sub_out_item = self.players_table.item(sub_out_row, self._get_column_index("Sub Out"))
        if sub_out_item:
            # Get the visual rect of the item
            rect = self.players_table.visualItemRect(sub_out_item)
            # Map to global coordinates
            global_pos = self.players_table.mapToGlobal(rect.bottomLeft())
            popup.show_at_position(global_pos)
        else:
            popup.show()

    def _complete_substitution_no_replacement(self, sub_out_row, minute):
        """Complete the substitution out without replacement."""
        # Get sub out player information
        sub_out_name_item = self.players_table.item(sub_out_row, self._get_column_index("Player"))
        if not sub_out_name_item:
            return

        sub_out_player_id = sub_out_name_item.data(Qt.ItemDataRole.UserRole)

        # Update Sub Out column for the player being substituted out
        sub_out_item = self.players_table.item(sub_out_row, self._get_column_index("Sub Out"))
        if sub_out_item:
            sub_out_item.setText(str(minute))

        # Store the substitution (no sub in player)
        substitution_pair = {
            'sub_out_player_id': sub_out_player_id,
            'sub_in_player_id': None,  # No replacement
            'sub_out_row': sub_out_row,
            'sub_in_row': None,  # No replacement
            'minute': minute,
            'no_replacement': True
        }
        self.substitution_pairs.append(substitution_pair)

        # Add substitution information to the timeline tab
        self._add_substitution_to_timeline_no_replacement(substitution_pair)

        # Update minutes for all players
        self._update_all_player_minutes()

    def _add_substitution_to_timeline_no_replacement(self, substitution_pair):
        """Add substitution out without replacement to timeline."""
        sub_out_name = self.players_table.item(substitution_pair['sub_out_row'], self._get_column_index("Player")).text()
        print(f"Substitution at minute {substitution_pair['minute']}: {sub_out_name} OUT (no replacement)")

    def _show_sub_out_player_selection(self, sub_in_row):
        """Show popup to select a player for substitution out (reverse flow)."""
        # Get the position of the player being substituted in for position matching
        sub_in_position = ""
        position_item = self.players_table.item(sub_in_row, self._get_column_index("Pos"))
        if position_item:
            sub_in_position = position_item.text().strip()

        # Get available players for substitution out (players currently playing)
        available_players = []

        for row in range(self.players_table.rowCount()):
            lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
            name_item = self.players_table.item(row, self._get_column_index("Player"))
            number_item = self.players_table.item(row, self._get_column_index("No"))
            position_item = self.players_table.item(row, self._get_column_index("Pos"))
            sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
            sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))

            if lineup_item and name_item and number_item and position_item:
                is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
                sub_in_value = sub_in_item.text().strip() if sub_in_item else ""
                sub_out_value = sub_out_item.text().strip() if sub_out_item else ""
                player_position = position_item.text().strip()

                # Only players currently playing can be subbed out:
                # - Started (lineup=true) OR came in as substitute (sub_in > 0)
                # - AND don't already have sub_out value
                player_is_playing = is_lineup or (sub_in_value and sub_in_value != "0")
                player_not_subbed_out = not sub_out_value

                if player_is_playing and player_not_subbed_out:
                    player_data = {
                        'row': row,
                        'name': name_item.text(),
                        'number': number_item.text(),
                        'position': player_position,
                        'same_position': player_position == sub_in_position,
                        'player_id': name_item.data(Qt.ItemDataRole.UserRole)
                    }
                    available_players.append(player_data)

        if not available_players:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                self.tr("No Available Players"),
                self.tr("No players are available to be substituted out.")
            )
            return

        # Create and show player selection dialog
        self._create_sub_out_player_selection_dialog(sub_in_row, available_players, sub_in_position)

    def _create_sub_out_player_selection_dialog(self, sub_in_row, available_players, sub_in_position):
        """Create and show the player selection dialog for substitution out."""

        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Select Player to Substitute Out"))
        dialog.setMinimumSize(350, 400)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # Instruction label with position info
        sub_in_name = self.players_table.item(sub_in_row, self._get_column_index("Player")).text()
        instruction_text = self.tr(f"Select a player to substitute out for {sub_in_name} ({sub_in_position}):")
        instruction_label = QLabel(instruction_text)
        instruction_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(instruction_label)

        # Position hint label
        hint_label = QLabel(self.tr("Players in the same position are highlighted"))
        hint_label.setStyleSheet("color: #666; font-size: 11px; margin-bottom: 5px;")
        layout.addWidget(hint_label)

        # Player list
        player_list = QListWidget()

        for player_data in available_players:
            item_text = f"{player_data['number']} - {player_data['name']} ({player_data['position']})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, player_data)

            # Apply light highlighting for same position players
            if player_data['same_position']:
                item.setBackground(QColor("#E8F5E8"))  # Light green background
                item.setForeground(QColor("#2E7D32"))  # Darker green text

            player_list.addItem(item)

        layout.addWidget(player_list)

        # Buttons
        button_layout = QHBoxLayout()
        cancel_btn = QPushButton(self.tr("Cancel"))
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # Handle player selection (no confirmation button, direct selection)
        def on_player_selected(item):
            selected_player_data = item.data(Qt.ItemDataRole.UserRole)
            dialog.accept()
            self._show_minute_selection_reverse(sub_in_row, selected_player_data)

        player_list.itemClicked.connect(on_player_selected)

        dialog.exec()

    def _show_minute_selection_reverse(self, sub_in_row, sub_out_player_data):
        """Show minute selection popup for the reverse substitution flow."""
        from app.widgets.minute_selector_widget import MinuteSelectorPopup

        # Find the row of the player being substituted out
        sub_out_row = None
        player_col = self._get_column_index("Player")
        for row in range(self.players_table.rowCount()):
            player_item = self.players_table.item(row, player_col)
            if player_item and player_item.text() == sub_out_player_data['name']:
                sub_out_row = row
                break

        if sub_out_row is None:
            # Fallback to basic minutes if we can't find the player
            match_duration = self._get_total_match_duration()
            available_minutes = list(range(1, match_duration + 1))
        else:
            # Get valid minutes for substitution (excluding minutes before player's events)
            available_minutes = self._get_valid_substitution_minutes(sub_out_row)

            if not available_minutes:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(
                    self,
                    self.tr("Cannot Substitute"),
                    self.tr("Player cannot be substituted out because they have events (goals/assists) that would occur after the substitution. Please clear the player's events first if you need to substitute them out earlier.")
                )
                return

        # Also validate Sub In minutes for the player being substituted in
        sub_in_valid_minutes = self._get_valid_sub_in_minutes(sub_in_row)

        # Find intersection of valid minutes for both players
        available_minutes = [minute for minute in available_minutes if minute in sub_in_valid_minutes]

        if not available_minutes:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Cannot Substitute"),
                self.tr("No valid minutes available for this substitution. The player being substituted in has a Sub Out time that conflicts with the available substitution minutes.")
            )
            return

        # Create and show minute selector popup with no initial selection (0 means no selection)
        popup = MinuteSelectorPopup(available_minutes, 0, self)
        popup.minute_selected.connect(
            lambda minute: self._complete_substitution_reverse(sub_in_row, sub_out_player_data, minute)
        )

        # Position popup near the clicked cell
        sub_in_item = self.players_table.item(sub_in_row, self._get_column_index("Sub In"))
        if sub_in_item:
            # Get the visual rect of the item
            rect = self.players_table.visualItemRect(sub_in_item)
            # Map to global coordinates
            global_pos = self.players_table.mapToGlobal(rect.bottomLeft())
            popup.show_at_position(global_pos)
        else:
            popup.show()

    def _complete_substitution_reverse(self, sub_in_row, sub_out_player_data, minute):
        """Complete the reverse substitution by updating both players and tracking the pair."""
        # Get sub in player information
        sub_in_name_item = self.players_table.item(sub_in_row, self._get_column_index("Player"))
        if not sub_in_name_item:
            return

        sub_in_player_id = sub_in_name_item.data(Qt.ItemDataRole.UserRole)
        sub_out_row = sub_out_player_data['row']
        sub_out_player_id = sub_out_player_data['player_id']

        # Update Sub In column for the player being substituted in
        sub_in_item = self.players_table.item(sub_in_row, self._get_column_index("Sub In"))
        if sub_in_item:
            sub_in_item.setText(str(minute))

        # Update Sub Out column for the player being substituted out
        sub_out_item = self.players_table.item(sub_out_row, self._get_column_index("Sub Out"))
        if sub_out_item:
            sub_out_item.setText(str(minute))

        # Store the substitution pair
        substitution_pair = {
            'sub_out_player_id': sub_out_player_id,
            'sub_in_player_id': sub_in_player_id,
            'sub_out_row': sub_out_row,
            'sub_in_row': sub_in_row,
            'minute': minute
        }
        self.substitution_pairs.append(substitution_pair)

        # Add substitution information to the timeline tab (placeholder for now)
        self._add_substitution_to_timeline(substitution_pair)

        # Update minutes for all players
        self._update_all_player_minutes()

    def _show_minute_selection(self, sub_out_row, sub_in_player_data):
        """Show minute selection popup for the substitution."""
        from app.widgets.minute_selector_widget import MinuteSelectorPopup

        # Get valid minutes for substitution (excluding minutes before player's events)
        available_minutes = self._get_valid_substitution_minutes(sub_out_row)

        if not available_minutes:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Cannot Substitute"),
                self.tr("Player cannot be substituted out because they have events (goals/assists) that would occur after the substitution. Please clear the player's events first if you need to substitute them out earlier.")
            )
            return

        # Also validate Sub In minutes for the player being substituted in
        sub_in_row = sub_in_player_data['row']
        sub_in_valid_minutes = self._get_valid_sub_in_minutes(sub_in_row)

        # Find intersection of valid minutes for both players
        available_minutes = [minute for minute in available_minutes if minute in sub_in_valid_minutes]

        if not available_minutes:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Cannot Substitute"),
                self.tr("No valid minutes available for this substitution. The player being substituted in has a Sub Out time that conflicts with the available substitution minutes.")
            )
            return

        # Create and show minute selector popup with no initial selection (0 means no selection)
        popup = MinuteSelectorPopup(available_minutes, 0, self)
        popup.minute_selected.connect(
            lambda minute: self._complete_substitution(sub_out_row, sub_in_player_data, minute)
        )

        # Position popup near the clicked cell
        sub_out_item = self.players_table.item(sub_out_row, self._get_column_index("Sub Out"))
        if sub_out_item:
            # Get the visual rect of the item
            rect = self.players_table.visualItemRect(sub_out_item)
            # Map to global coordinates
            global_pos = self.players_table.mapToGlobal(rect.bottomLeft())
            popup.show_at_position(global_pos)
        else:
            popup.show()

    def _complete_substitution(self, sub_out_row, sub_in_player_data, minute):
        """Complete the substitution by updating both players and tracking the pair."""
        # Get sub out player information
        sub_out_name_item = self.players_table.item(sub_out_row, self._get_column_index("Player"))
        if not sub_out_name_item:
            return

        sub_out_player_id = sub_out_name_item.data(Qt.ItemDataRole.UserRole)
        sub_in_row = sub_in_player_data['row']
        sub_in_player_id = sub_in_player_data['player_id']

        # Update Sub Out column for the player being substituted out
        sub_out_item = self.players_table.item(sub_out_row, self._get_column_index("Sub Out"))
        if sub_out_item:
            sub_out_item.setText(str(minute))

        # Update Sub In column for the player being substituted in
        sub_in_item = self.players_table.item(sub_in_row, self._get_column_index("Sub In"))
        if sub_in_item:
            sub_in_item.setText(str(minute))

        # Store the substitution pair
        substitution_pair = {
            'sub_out_player_id': sub_out_player_id,
            'sub_in_player_id': sub_in_player_id,
            'sub_out_row': sub_out_row,
            'sub_in_row': sub_in_row,
            'minute': minute
        }
        self.substitution_pairs.append(substitution_pair)

        # Add substitution information to the timeline tab (placeholder for now)
        self._add_substitution_to_timeline(substitution_pair)

        # Update minutes for all players
        self._update_all_player_minutes()

    def _add_substitution_to_timeline(self, substitution_pair):
        """Add substitution information to the timeline tab (placeholder implementation)."""
        # This is a placeholder - in the future this could update a timeline widget
        # For now, we'll just print the substitution info
        sub_out_name = self.players_table.item(substitution_pair['sub_out_row'], self._get_column_index("Player")).text()
        sub_in_name = self.players_table.item(substitution_pair['sub_in_row'], self._get_column_index("Player")).text()

        print(f"Substitution at minute {substitution_pair['minute']}: {sub_out_name} OUT, {sub_in_name} IN")

    def _show_yellow_card_context_menu(self, row, position):
        """Show context menu for Yellow Card column actions."""
        from PySide6.QtWidgets import QMenu

        # Create context menu
        menu = QMenu(self)

        # Add menu items
        first_yellow_action = menu.addAction(self.tr("1st yellow card"))
        second_yellow_action = menu.addAction(self.tr("2nd yellow cards"))
        clear_yellow_action = menu.addAction(self.tr("Clear yellow cards"))

        # Connect actions
        first_yellow_action.triggered.connect(lambda: self._set_first_yellow_card(row))
        second_yellow_action.triggered.connect(lambda: self._set_second_yellow_cards(row))
        clear_yellow_action.triggered.connect(lambda: self._clear_yellow_cards(row))

        # Convert position to global coordinates
        global_pos = self.players_table.mapToGlobal(position)

        # Show the context menu
        menu.exec(global_pos)

    def _show_red_card_context_menu(self, row, position):
        """Show context menu for Red Card column actions."""
        from PySide6.QtWidgets import QMenu

        # Create context menu
        menu = QMenu(self)

        # Add menu items
        red_card_action = menu.addAction(self.tr("Red card"))
        clear_red_action = menu.addAction(self.tr("Clear red"))

        # Connect actions
        red_card_action.triggered.connect(lambda: self._set_red_card(row))
        clear_red_action.triggered.connect(lambda: self._clear_red_card(row))

        # Convert position to global coordinates
        global_pos = self.players_table.mapToGlobal(position)

        # Show the context menu
        menu.exec(global_pos)

    def _set_first_yellow_card(self, row):
        """Set 1st yellow card - popup minutes window, clear 2nd card and 2Y red if exists."""
        # Get column indices
        y_col = self._get_column_index("Y")
        r_col = self._get_column_index("R")
        rr_col = self._get_column_index("Rr")

        # Set Y to 1
        y_item = self.players_table.item(row, y_col)
        if y_item:
            y_item.setText("1")

        # Clear 2nd yellow and red card data if it was from 2 yellows
        self._clear_second_yellow_and_2y_red(row)

        # Show minute popup for 1st yellow
        self._show_yellow_card_minute_popup(row, 1)

    def _set_second_yellow_cards(self, row):
        """Set 2nd yellow cards - popup minutes window 2 times for both cards."""
        # Get column indices
        y_col = self._get_column_index("Y")
        r_col = self._get_column_index("R")

        # Set Y to 2
        y_item = self.players_table.item(row, y_col)
        if y_item:
            y_item.setText("2")

        # Set R to 1 and reason to 2Y
        r_item = self.players_table.item(row, r_col)
        if r_item:
            r_item.setText("1")
        self._set_red_card_reason(row, "2Y")

        # Show minute popup for 1st yellow first
        self._show_first_then_second_yellow_popup(row)

    def _clear_yellow_cards(self, row):
        """Clear yellow cards data, R, and R1 if reason is 2Y."""
        # Get column indices
        y_col = self._get_column_index("Y")
        r_col = self._get_column_index("R")
        rr_col = self._get_column_index("Rr")

        # Check if red card reason is 2Y
        rr_item = self.players_table.item(row, rr_col)
        is_2y_red = rr_item and rr_item.text().strip() == "2Y"

        # Clear yellow card data
        self._clear_yellow_red_card_data(row)

        # If red was from 2Y, also clear red card
        if is_2y_red:
            r_item = self.players_table.item(row, r_col)
            if r_item:
                r_item.setText("0")

    def _set_red_card(self, row):
        """Set red card - popup minutes window for R1 and add value 1R in Rr."""
        # Get column indices
        r_col = self._get_column_index("R")

        # Set R to 1
        r_item = self.players_table.item(row, r_col)
        if r_item:
            r_item.setText("1")

        # Set reason to 1R
        self._set_red_card_reason(row, "1R")

        # Show minute popup for red card (with yellow card validation)
        self._show_red_card_minute_popup(row)

    def _clear_red_card(self, row):
        """Clear red card - remove R and R1 if reason is 1R."""
        # Get column indices
        r_col = self._get_column_index("R")
        rr_col = self._get_column_index("Rr")

        # Check if red card reason is 1R
        rr_item = self.players_table.item(row, rr_col)
        is_1r_red = rr_item and rr_item.text().strip() == "1R"

        # Only clear if reason is 1R (don't clear 2Y reds)
        if is_1r_red:
            # Clear red card data
            self._clear_red_card_data(row)

            # Set R to 0
            r_item = self.players_table.item(row, r_col)
            if r_item:
                r_item.setText("0")

    def _clear_second_yellow_and_2y_red(self, row):
        """Clear 2nd yellow card data and red card if it was from 2 yellows."""
        # Get column indices
        y2_col = self._get_column_index("Y2")
        r_col = self._get_column_index("R")
        rr_col = self._get_column_index("Rr")
        r1_col = self._get_column_index("R1")

        # Clear Y2 minute
        y2_item = self.players_table.item(row, y2_col)
        if y2_item:
            y2_item.setText("")

        # Check if red card reason is 2Y and clear it
        rr_item = self.players_table.item(row, rr_col)
        if rr_item and rr_item.text().strip() == "2Y":
            # Clear red card from 2 yellows
            r_item = self.players_table.item(row, r_col)
            if r_item:
                r_item.setText("0")
            rr_item.setText("")

            # Clear R1 minute
            r1_item = self.players_table.item(row, r1_col)
            if r1_item:
                r1_item.setText("")

    def _show_first_then_second_yellow_popup(self, row):
        """Show minute popup for 1st yellow, then 2nd yellow."""
        from app.widgets.minute_selector_widget import MinuteSelectorPopup

        # Get match duration for available minutes
        match_duration = self._get_total_match_duration()
        available_minutes = list(range(1, match_duration + 1))

        # Create and show minute selector popup for 1st yellow
        popup = MinuteSelectorPopup(available_minutes, 0, self)
        popup.minute_selected.connect(
            lambda minute: self._set_first_yellow_then_show_second(row, minute)
        )

        # Position popup near the clicked cell
        y_col = self._get_column_index("Y")
        y_item = self.players_table.item(row, y_col)
        if y_item:
            rect = self.players_table.visualItemRect(y_item)
            global_pos = self.players_table.mapToGlobal(rect.bottomLeft())
            popup.show_at_position(global_pos)
        else:
            popup.show()

    def _set_first_yellow_then_show_second(self, row, first_minute):
        """Set 1st yellow minute and then show popup for 2nd yellow."""
        # Set Y1 minute
        self._set_yellow_card_minute(row, 1, first_minute)

        # Now show popup for 2nd yellow (with filtered minutes)
        self._show_yellow_card_minute_popup(row, 2)

    def _get_total_match_duration(self):
        """Get the total match duration including stoppage time and extra time."""
        # Get base match duration from competition settings
        base_duration = 90  # Default
        if hasattr(self, 'competition_field') and self.competition_field:
            competition_name = self.competition_field.currentText()
            if competition_name:
                settings = QSettings()
                settings.beginGroup("football_competitions")
                count = settings.value("count", 0, int)

                for i in range(count):
                    name = settings.value(f"{i}/name", "")
                    if name == competition_name:
                        base_duration = settings.value(f"{i}/match_duration", 90, int)
                        break

                settings.endGroup()

        # Add stoppage time if available
        stoppage_time = 0
        if hasattr(self, 'stoppage_time_field') and self.stoppage_time_field:
            stoppage_time = self.stoppage_time_field.value()

        # Add extra time if enabled
        extra_time = 0
        if hasattr(self, 'extratime_checkbox') and hasattr(self, 'et_duration_field'):
            if self.extratime_checkbox.isChecked():
                extra_time = self.et_duration_field.value()

        return base_duration + stoppage_time + extra_time

    def _handle_goals_min_click(self, row):
        """Handle clicks on Goals min column to open sequential minute popups."""
        # Check if player actually played before allowing goal minutes
        if not self._validate_player_can_have_events(row):
            return

        # Get the number of goals for this player
        goals_count = self._get_player_goals_count(row)

        print(f"DEBUG: Goals min clicked for row {row}, goals_count: {goals_count}")

        if goals_count == 0:
            print("DEBUG: No goals to enter minutes for")
            return  # No goals to enter minutes for

        # Start the sequential popup process
        self._show_goals_minute_sequence(row, goals_count)

    def _handle_assists_min_click(self, row):
        """Handle clicks on Assists min column - show minute entry popup for assists."""
        # Check if player actually played before allowing assist minutes
        if not self._validate_player_can_have_events(row):
            return

        print(f"DEBUG: Assists min clicked for row {row}")

        # Get the number of assists for this player
        assists_count = self._get_player_assists_count(row)

        print(f"DEBUG: Assists min clicked for row {row}, assists_count: {assists_count}")

        if assists_count == 0:
            print("DEBUG: No assists to enter minutes for")
            return

        # Show the assists minute sequence popup
        self._show_assists_minute_sequence(row, assists_count)

    def _handle_injury_click(self, row):
        """Handle clicks on Inj column - show minute selection popup for injury."""
        print(f"DEBUG: Injury clicked for row {row}")

        # Get match duration for available minutes
        match_duration = self._get_total_match_duration()
        available_minutes = list(range(1, match_duration + 1))

        # Create and show minute selector popup
        from app.widgets.minute_selector_widget import MinuteSelectorPopup
        popup = MinuteSelectorPopup(available_minutes, 0, self)
        popup.minute_selected.connect(
            lambda minute: self._set_injury_minute_and_show_reason(row, minute)
        )

        # Position popup near the clicked cell
        inj_item = self.players_table.item(row, self._get_column_index("Inj"))
        if inj_item:
            # Get the visual rect of the item
            rect = self.players_table.visualItemRect(inj_item)
            # Map to global coordinates
            global_pos = self.players_table.mapToGlobal(rect.bottomLeft())
            popup.show_at_position(global_pos)
        else:
            popup.show()

    def _set_injury_minute_and_show_reason(self, row, minute):
        """Set injury minute and show reason input dialog."""
        print(f"DEBUG: Setting injury minute {minute} for row {row}")

        # Set the injury minute
        inj_item = self.players_table.item(row, self._get_column_index("Inj"))
        if inj_item:
            inj_item.setText(str(minute))

        # Show injury reason dialog
        self._show_injury_reason_dialog(row)

    def _show_injury_reason_dialog(self, row):
        """Show dialog to input injury reason."""
        from PySide6.QtWidgets import QInputDialog

        # Get current injury reason if any
        injr_item = self.players_table.item(row, self._get_column_index("InjR"))
        current_reason = injr_item.text() if injr_item else ""

        # Show input dialog
        reason, ok = QInputDialog.getText(
            self,
            self.tr("Injury Reason"),
            self.tr("Enter injury reason (e.g., broken arm, muscle strain):"),
            text=current_reason
        )

        if ok and reason.strip():
            # Set the injury reason
            if injr_item:
                injr_item.setText(reason.strip())
        elif ok and not reason.strip():
            # User clicked OK but entered empty text - clear the reason
            if injr_item:
                injr_item.setText("")

    def _get_player_goals_count(self, row):
        """Get the number of goals scored by a player."""
        goals_col = self._get_column_index("Goals")
        if row in self.goals_widgets:
            # Get value from split cell widget
            return self.goals_widgets[row].get_value()
        return 0

    def _get_player_assists_count(self, row):
        """Get the number of assists by a player."""
        assists_col = self._get_column_index("Assists")
        if row in self.assists_widgets:
            # Get value from split cell widget
            return self.assists_widgets[row].get_value()
        return 0

    def _show_goals_minute_sequence(self, row, goals_count):
        """Show sequential minute popups for each goal."""
        # Get existing goal minutes
        existing_minutes = self._get_existing_goal_minutes(row)

        # Extend the list to match goals_count if needed
        while len(existing_minutes) < goals_count:
            existing_minutes.append(None)

        # Find all missing goal minutes
        missing_goals = []
        for i in range(goals_count):
            if i >= len(existing_minutes) or existing_minutes[i] is None:
                missing_goals.append(i + 1)  # Goal numbers are 1-based

        print(f"DEBUG: Goals count: {goals_count}, existing_minutes: {existing_minutes}, missing_goals: {missing_goals}")

        if missing_goals:
            # Start with the first missing goal
            self._show_single_goal_minute_popup(row, missing_goals[0], goals_count, existing_minutes, missing_goals)

    def _get_existing_goal_minutes(self, row):
        """Get existing goal minutes as a list."""
        goals_min_col = self._get_column_index("Goals min")
        goals_min_item = self.players_table.item(row, goals_min_col)

        if not goals_min_item or not goals_min_item.text().strip():
            return []

        # Parse comma-separated minutes
        minutes_text = goals_min_item.text().strip()
        if not minutes_text:
            return []

        minutes = []
        for minute_str in minutes_text.split(','):
            minute_str = minute_str.strip()
            if minute_str:
                try:
                    minutes.append(int(minute_str))
                except ValueError:
                    minutes.append(None)  # Invalid minute
            else:
                minutes.append(None)  # Empty minute

        return minutes

    def _show_single_goal_minute_popup(self, row, goal_number, total_goals, existing_minutes, missing_goals=None):
        """Show minute selection popup for a single goal."""
        from app.widgets.minute_selector_widget import MinuteSelectorPopup

        # Get valid minutes when player was playing
        valid_minutes = self._get_player_valid_minutes(row)

        if not valid_minutes:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Invalid Goal"),
                self.tr("Player was not playing during the match. Cannot enter goal minutes.")
            )
            return

        # Create popup with title showing goal number
        popup = MinuteSelectorPopup(valid_minutes, 0, self)
        popup.setWindowTitle(self.tr(f"Enter minute for Goal {goal_number} of {total_goals}"))

        # Connect to handle the selected minute
        popup.minute_selected.connect(
            lambda minute: self._set_goal_minute(row, goal_number, minute, total_goals, existing_minutes, missing_goals)
        )

        # Position popup near the clicked cell
        goals_min_col = self._get_column_index("Goals min")

        # Use table geometry to calculate position even if item doesn't exist
        try:
            # Get the cell rectangle using row and column indices
            cell_rect = self.players_table.visualRect(self.players_table.model().index(row, goals_min_col))
            global_pos = self.players_table.mapToGlobal(cell_rect.bottomLeft())
            print(f"DEBUG: Positioning popup at {global_pos} (calculated from cell rect)")
            popup.show_at_position(global_pos)
        except Exception as e:
            print(f"DEBUG: Error calculating position: {e}, showing popup at default position")
            popup.show()

    def _get_player_valid_minutes(self, row):
        """Calculate valid minutes when player was actually playing."""
        # Get match duration
        match_duration = self._get_total_match_duration()

        # Get player data
        lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
        sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
        sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))
        r_item = self.players_table.item(row, self._get_column_index("R"))
        r1_item = self.players_table.item(row, self._get_column_index("R1"))

        # Check lineup status
        is_lineup = lineup_item.checkState() == Qt.CheckState.Checked if lineup_item else False

        # Get substitution data
        sub_in_minute = None
        if sub_in_item and sub_in_item.text().strip():
            try:
                sub_in_minute = int(sub_in_item.text().strip())
            except ValueError:
                pass

        sub_out_minute = None
        if sub_out_item and sub_out_item.text().strip():
            try:
                sub_out_minute = int(sub_out_item.text().strip())
            except ValueError:
                pass

        # Get red card data
        has_red = r_item.text() == "1" if r_item else False
        red_minute = None
        if has_red and r1_item and r1_item.text().strip():
            try:
                red_minute = int(r1_item.text().strip())
            except ValueError:
                pass

        # Calculate playing period
        if is_lineup:
            # Player started the match
            start_minute = 1
            if sub_out_minute:
                end_minute = sub_out_minute
            elif has_red and red_minute:
                end_minute = red_minute
            else:
                end_minute = match_duration
        else:
            # Player didn't start
            if sub_in_minute:
                start_minute = sub_in_minute
                if sub_out_minute:
                    end_minute = sub_out_minute
                elif has_red and red_minute:
                    end_minute = red_minute
                else:
                    end_minute = match_duration
            else:
                return []  # Never played

        # Return valid minutes range
        return list(range(start_minute, end_minute + 1))

    def _set_goal_minute(self, row, goal_number, minute, total_goals, existing_minutes, missing_goals=None):
        """Set the minute for a specific goal and continue with next missing goal if needed."""
        # Extend existing_minutes list if needed
        while len(existing_minutes) < goal_number:
            existing_minutes.append(None)

        # Set the minute for this goal
        existing_minutes[goal_number - 1] = minute

        # Update the display
        self._update_goals_min_display(row, existing_minutes)

        # Update all players data validation
        self._update_all_players_data_validation()

        # Continue with next missing goal if there are more
        if missing_goals:
            # Find the current goal in the missing_goals list
            try:
                current_index = missing_goals.index(goal_number)
                # Check if there's a next missing goal
                if current_index + 1 < len(missing_goals):
                    next_missing_goal = missing_goals[current_index + 1]
                    print(f"DEBUG: Continuing with next missing goal: {next_missing_goal}")
                    self._show_single_goal_minute_popup(row, next_missing_goal, total_goals, existing_minutes, missing_goals)
                else:
                    print(f"DEBUG: All missing goals completed")
            except ValueError:
                print(f"DEBUG: Goal {goal_number} not found in missing_goals list")
        else:
            # Fallback to old behavior (continue with next sequential goal)
            next_goal = goal_number + 1
            if next_goal <= total_goals:
                self._show_single_goal_minute_popup(row, next_goal, total_goals, existing_minutes)

    def _update_goals_min_display(self, row, minutes_list):
        """Update the Goals min column display with comma-separated minutes."""
        goals_min_col = self._get_column_index("Goals min")

        print(f"DEBUG: Updating goals min display for row {row}, minutes_list: {minutes_list}")
        print(f"DEBUG: Goals min column index: {goals_min_col}")
        print(f"DEBUG: Table has {self.players_table.rowCount()} rows, {self.players_table.columnCount()} columns")

        # Try to get the item
        goals_min_item = self.players_table.item(row, goals_min_col)
        print(f"DEBUG: Retrieved item: {goals_min_item}")

        if goals_min_item:
            # Filter out None values, convert to integers, sort, and create comma-separated string
            valid_minutes = [m for m in minutes_list if m is not None]
            valid_minutes.sort()  # Sort minutes in ascending order
            display_text = ", ".join(str(m) for m in valid_minutes)
            print(f"DEBUG: Setting display text: '{display_text}'")
            goals_min_item.setText(display_text)
        else:
            print(f"DEBUG: No goals_min_item found for row {row}, col {goals_min_col}")
            # Try to create the item if it doesn't exist
            goals_min_item = QTableWidgetItem("")
            goals_min_item.setFlags(goals_min_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            goals_min_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, goals_min_col, goals_min_item)

            # Now set the text with sorted minutes
            valid_minutes = [m for m in minutes_list if m is not None]
            valid_minutes.sort()  # Sort minutes in ascending order
            display_text = ", ".join(str(m) for m in valid_minutes)
            goals_min_item.setText(display_text)
            print(f"DEBUG: Created new item and set text: '{display_text}'")

    def _show_goals_min_context_menu(self, row, position):
        """Show context menu for Goals min column actions."""
        from PySide6.QtWidgets import QMenu

        print(f"DEBUG: Showing goals min context menu for row {row}")

        context_menu = QMenu(self)

        # Add "Clear goal minutes" action
        clear_action = context_menu.addAction(self.tr("Clear goal minutes"))
        clear_action.triggered.connect(lambda: self._clear_goal_minutes(row))

        # Add "Open minutes table" action
        open_action = context_menu.addAction(self.tr("Open minutes table"))
        open_action.triggered.connect(lambda: self._handle_goals_min_click(row))

        # Show context menu at the clicked position
        global_pos = self.players_table.mapToGlobal(position)
        context_menu.exec(global_pos)

    def _clear_goal_minutes(self, row):
        """Clear all goal minutes for a player."""
        goals_min_col = self._get_column_index("Goals min")
        goals_min_item = self.players_table.item(row, goals_min_col)

        if goals_min_item:
            goals_min_item.setText("")

        # Update all players data validation after clearing
        self._update_all_players_data_validation()

    def _show_assists_min_context_menu(self, row, position):
        """Show context menu for Assists min column actions."""
        from PySide6.QtWidgets import QMenu

        print(f"DEBUG: Showing assists min context menu for row {row}")

        context_menu = QMenu(self)

        # Add "Clear assist minutes" action
        clear_action = context_menu.addAction(self.tr("Clear assist minutes"))
        clear_action.triggered.connect(lambda: self._clear_assist_minutes(row))

        # Add "Open minutes table" action
        open_action = context_menu.addAction(self.tr("Open minutes table"))
        open_action.triggered.connect(lambda: self._handle_assists_min_click(row))

        # Show context menu at the clicked position
        global_pos = self.players_table.mapToGlobal(position)
        context_menu.exec(global_pos)

    def _clear_assist_minutes(self, row):
        """Clear all assist minutes for a player."""
        assists_min_col = self._get_column_index("Assists min")
        assists_min_item = self.players_table.item(row, assists_min_col)

        if assists_min_item:
            assists_min_item.setText("")

        # Update all players data validation after clearing
        self._update_all_players_data_validation()

    def _handle_goals_reduction(self, row, new_goals_count):
        """Handle goals reduction by removing excess minutes (keep earliest minutes)."""
        # Get existing goal minutes
        existing_minutes = self._get_existing_goal_minutes(row)

        if len(existing_minutes) > new_goals_count:
            print(f"DEBUG: Goals reduced from {len(existing_minutes)} to {new_goals_count}, removing excess minutes")

            # Filter out None values and sort by minute value
            valid_minutes = [(i, minute) for i, minute in enumerate(existing_minutes) if minute is not None]
            valid_minutes.sort(key=lambda x: x[1])  # Sort by minute value (earliest first)

            # Keep only the earliest minutes up to new_goals_count
            minutes_to_keep = valid_minutes[:new_goals_count]

            # Create new minutes list with only the kept minutes
            new_minutes = [None] * new_goals_count
            for i, (original_index, minute) in enumerate(minutes_to_keep):
                if i < new_goals_count:
                    new_minutes[i] = minute

            print(f"DEBUG: Kept earliest {len(minutes_to_keep)} minutes: {[m[1] for m in minutes_to_keep]}")

            # Update the display with the reduced minutes
            self._update_goals_min_display(row, new_minutes)

    def _handle_assists_reduction(self, row, new_assists_count):
        """Handle when assists are reduced - remove excess minutes."""
        # Get existing assist minutes
        existing_minutes = self._get_existing_assist_minutes(row)

        if len(existing_minutes) <= new_assists_count:
            # No reduction needed
            return

        print(f"DEBUG: Reducing assists from {len(existing_minutes)} to {new_assists_count}")

        # Keep only the first new_assists_count minutes
        if new_assists_count == 0:
            # Clear all minutes
            new_minutes = []
        else:
            # Keep the first new_assists_count minutes, removing the latest ones
            minutes_to_keep = []
            for i, minute in enumerate(existing_minutes):
                if minute is not None and len(minutes_to_keep) < new_assists_count:
                    minutes_to_keep.append((i, minute))

            # Create new minutes list with only the kept minutes
            new_minutes = [None] * new_assists_count
            for i, (original_index, minute) in enumerate(minutes_to_keep):
                if i < new_assists_count:
                    new_minutes[i] = minute

        print(f"DEBUG: New assists minutes: {new_minutes}")

        # Update the display with the reduced minutes
        self._update_assists_min_display(row, new_minutes)

    def _show_assists_minute_sequence(self, row, assists_count):
        """Show sequential minute popups for each assist."""
        # Get existing assist minutes
        existing_minutes = self._get_existing_assist_minutes(row)

        # Extend the list to match assists_count if needed
        while len(existing_minutes) < assists_count:
            existing_minutes.append(None)

        # Find missing assists (those without minutes)
        missing_assists = []
        for i in range(assists_count):
            if i >= len(existing_minutes) or existing_minutes[i] is None:
                missing_assists.append(i + 1)  # 1-based numbering

        print(f"DEBUG: Missing assists: {missing_assists}")

        if missing_assists:
            # Start with the first missing assist
            self._show_single_assist_minute_popup(row, missing_assists[0], assists_count, existing_minutes, missing_assists)
        else:
            print("DEBUG: All assists already have minutes")

    def _show_single_assist_minute_popup(self, row, assist_number, total_assists, existing_minutes, missing_assists=None):
        """Show minute selection popup for a single assist."""
        from app.widgets.minute_selector_widget import MinuteSelectorPopup

        # Get valid minutes when player was actually playing
        valid_minutes = self._get_player_valid_minutes(row)

        if not valid_minutes:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(
                self,
                self.tr("Invalid Assist"),
                self.tr("Player was not playing during the match. Cannot enter assist minutes.")
            )
            return

        # Create popup with title showing assist number
        popup = MinuteSelectorPopup(valid_minutes, 0, self)
        popup.setWindowTitle(self.tr(f"Enter minute for Assist {assist_number} of {total_assists}"))

        # Connect to handle the selected minute
        popup.minute_selected.connect(
            lambda minute: self._set_assist_minute(row, assist_number, minute, total_assists, existing_minutes, missing_assists)
        )

        # Position popup near the clicked cell
        assists_min_col = self._get_column_index("Assists min")

        # Use table geometry to calculate position
        try:
            cell_rect = self.players_table.visualRect(self.players_table.model().index(row, assists_min_col))
            global_pos = self.players_table.mapToGlobal(cell_rect.bottomLeft())
            popup.show_at_position(global_pos)
        except Exception as e:
            print(f"DEBUG: Error calculating position: {e}, showing popup at default position")
            popup.show()

    def _get_existing_assist_minutes(self, row):
        """Get existing assist minutes for a player as a list."""
        assists_min_col = self._get_column_index("Assists min")
        assists_min_item = self.players_table.item(row, assists_min_col)

        if assists_min_item and assists_min_item.text().strip():
            # Parse comma-separated minutes
            minutes_text = assists_min_item.text().strip()
            try:
                minutes = [int(m.strip()) for m in minutes_text.split(",") if m.strip()]
                return minutes
            except ValueError:
                return []
        return []

    def _get_player_latest_event_minute(self, row):
        """Get the latest minute when player had any event (goal or assist)."""
        latest_minute = None

        # Get goal minutes
        goal_minutes = self._get_existing_goal_minutes(row)
        valid_goal_minutes = [m for m in goal_minutes if m is not None]

        # Get assist minutes
        assist_minutes = self._get_existing_assist_minutes(row)
        valid_assist_minutes = [m for m in assist_minutes if m is not None]

        # Combine all event minutes
        all_event_minutes = valid_goal_minutes + valid_assist_minutes

        if all_event_minutes:
            latest_minute = max(all_event_minutes)

        return latest_minute

    def _get_valid_substitution_minutes(self, row):
        """Get valid minutes for substitution out, excluding minutes before player's events and sub in."""
        # Get match duration for base available minutes
        match_duration = self._get_total_match_duration()
        base_minutes = list(range(1, match_duration + 1))

        # Get the player's sub in minute
        sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
        sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

        # Determine the minimum minute for substitution out
        min_sub_out_minute = 1  # Default minimum

        # If player was substituted in, they can only be subbed out AFTER that minute
        if sub_in_value and sub_in_value != "0":
            try:
                sub_in_minute = int(sub_in_value)
                min_sub_out_minute = max(min_sub_out_minute, sub_in_minute + 1)  # Must be after sub in
                print(f"DEBUG: Player row {row} was subbed in at minute {sub_in_minute}, min sub out: {min_sub_out_minute}")
            except ValueError:
                pass

        # Get the latest minute when player had any event (goals/assists)
        latest_event_minute = self._get_player_latest_event_minute(row)

        if latest_event_minute is not None:
            # Player can be substituted out at the same minute or after their latest event
            min_sub_out_minute = max(min_sub_out_minute, latest_event_minute)
            print(f"DEBUG: Player row {row} latest event at minute {latest_event_minute}, adjusted min sub out: {min_sub_out_minute}")

        # Filter minutes based on the minimum required minute
        valid_minutes = [minute for minute in base_minutes if minute >= min_sub_out_minute]

        print(f"DEBUG: Player row {row} valid sub out minutes: {len(valid_minutes)} (from {min(valid_minutes) if valid_minutes else 'none'} to {max(valid_minutes) if valid_minutes else 'none'})")

        return valid_minutes

    def _get_valid_sub_in_minutes(self, row):
        """Get valid minutes for substitution in, excluding minutes after player's sub out."""
        # Get match duration for base available minutes
        match_duration = self._get_total_match_duration()
        base_minutes = list(range(1, match_duration + 1))

        # Get the player's sub out minute
        sub_out_item = self.players_table.item(row, self._get_column_index("Sub Out"))
        sub_out_value = sub_out_item.text().strip() if sub_out_item else ""

        # Determine the maximum minute for substitution in
        max_sub_in_minute = match_duration  # Default maximum

        # If player has a sub out minute, they can only be subbed in BEFORE that minute
        if sub_out_value and sub_out_value != "0":
            try:
                sub_out_minute = int(sub_out_value)
                max_sub_in_minute = min(max_sub_in_minute, sub_out_minute - 1)  # Must be before sub out
                print(f"DEBUG: Player row {row} will be subbed out at minute {sub_out_minute}, max sub in: {max_sub_in_minute}")
            except ValueError:
                pass

        # Filter minutes based on the maximum allowed minute
        valid_minutes = [minute for minute in base_minutes if minute <= max_sub_in_minute]

        print(f"DEBUG: Player row {row} valid sub in minutes: {len(valid_minutes)} (from {min(valid_minutes) if valid_minutes else 'none'} to {max(valid_minutes) if valid_minutes else 'none'})")

        return valid_minutes

    def _validate_player_can_have_events(self, row, event_type="goals or assists"):
        """Validate if a player can have events (must have played in the match)."""
        # Get player's lineup status and substitution data
        lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
        sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))
        player_item = self.players_table.item(row, self._get_column_index("Player"))

        player_name = player_item.text() if player_item else f"Player row {row}"

        if not lineup_item:
            print(f"DEBUG: No lineup item for {player_name}")
            return False

        is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
        sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

        print(f"DEBUG: Validating {player_name} - Lineup: {is_lineup}, Sub In: '{sub_in_value}'")

        # Player can have events if:
        # 1. They were in the starting lineup, OR
        # 2. They were substituted in (sub_in > 0)
        player_played = is_lineup or (sub_in_value and sub_in_value != "0")

        print(f"DEBUG: Player {player_name} played: {player_played}")

        if not player_played:
            # Show warning message
            from PySide6.QtWidgets import QMessageBox

            QMessageBox.warning(
                self,
                self.tr("Player Did Not Play"),
                self.tr(f"{player_name} did not play in this match (not in lineup and never substituted in). Players who didn't play cannot have {event_type}.")
            )
            return False

        return True

    def _validate_substitution_clearing(self, clicked_row):
        """Validate that clearing a substitution won't create orphaned goals/assists.

        Returns:
            dict: {'can_clear': bool, 'message': str}
        """
        print(f"DEBUG: Validating substitution clearing for row {clicked_row}")

        # Get the clicked player's data
        clicked_player_id = None
        name_item = self.players_table.item(clicked_row, self._get_column_index("Player"))
        if name_item:
            clicked_player_id = name_item.data(Qt.ItemDataRole.UserRole)
            clicked_player_name = name_item.text()
        else:
            return {'can_clear': False, 'message': self.tr("Player information not found.")}

        # Find the substitution pair involving this player
        substitution_pair = None
        for substitution in self.substitution_pairs:
            if (substitution['sub_out_row'] == clicked_row or
                substitution['sub_in_row'] == clicked_row or
                substitution['sub_out_player_id'] == clicked_player_id or
                substitution['sub_in_player_id'] == clicked_player_id):
                substitution_pair = substitution
                break

        if not substitution_pair:
            print(f"DEBUG: No substitution pair found for row {clicked_row}")
            return {'can_clear': True, 'message': ''}

        # Get both players involved in the substitution
        sub_out_row = substitution_pair['sub_out_row']
        sub_in_row = substitution_pair['sub_in_row']

        print(f"DEBUG: Found substitution pair - sub_out_row: {sub_out_row}, sub_in_row: {sub_in_row}")

        # Check both players for goals/assists that would become orphaned
        players_with_orphaned_events = []

        # Check sub_out player (player being substituted out)
        if sub_out_row is not None:
            orphaned_events = self._check_player_orphaned_events_after_sub_clear(sub_out_row, 'sub_out')
            if orphaned_events:
                sub_out_name_item = self.players_table.item(sub_out_row, self._get_column_index("Player"))
                sub_out_name = sub_out_name_item.text() if sub_out_name_item else f"Player {sub_out_row}"
                players_with_orphaned_events.append({
                    'name': sub_out_name,
                    'events': orphaned_events
                })

        # Check sub_in player (player being substituted in)
        if sub_in_row is not None:
            orphaned_events = self._check_player_orphaned_events_after_sub_clear(sub_in_row, 'sub_in')
            if orphaned_events:
                sub_in_name_item = self.players_table.item(sub_in_row, self._get_column_index("Player"))
                sub_in_name = sub_in_name_item.text() if sub_in_name_item else f"Player {sub_in_row}"
                players_with_orphaned_events.append({
                    'name': sub_in_name,
                    'events': orphaned_events
                })

        if players_with_orphaned_events:
            # Create detailed error message
            message_parts = [self.tr("Cannot clear this substitution because the following players would have orphaned goals/assists:")]
            message_parts.append("")

            for player_info in players_with_orphaned_events:
                events_text = ", ".join(player_info['events'])
                message_parts.append(f"• {player_info['name']}: {events_text}")

            message_parts.append("")
            message_parts.append(self.tr("Please clear the goals/assists first, or ensure these players have other ways to be on the field (lineup or other substitutions)."))

            return {
                'can_clear': False,
                'message': "\n".join(message_parts)
            }

        return {'can_clear': True, 'message': ''}

    def _check_player_orphaned_events_after_sub_clear(self, row, player_role):
        """Check if a player would have orphaned events after substitution clearing.

        Args:
            row: Player's row in the table
            player_role: 'sub_out' or 'sub_in'

        Returns:
            list: List of orphaned event descriptions
        """
        orphaned_events = []

        # Get player's current status
        lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
        is_lineup = lineup_item.checkState() == Qt.CheckState.Checked if lineup_item else False

        # Get player's goals and assists
        player_goals = self._get_player_goals_count(row)
        player_assists = self._get_player_assists_count(row)

        print(f"DEBUG: Checking row {row} ({player_role}) - lineup: {is_lineup}, goals: {player_goals}, assists: {player_assists}")

        # If player is in lineup, they can always have events (even after sub clearing)
        if is_lineup:
            print(f"DEBUG: Player {row} is in lineup, no orphaned events")
            return orphaned_events

        # If player is not in lineup, they need substitution to have events
        # After clearing this substitution, they would have no way to be on field
        if player_goals > 0:
            goals_text = f"{player_goals} goal{'s' if player_goals != 1 else ''}"
            orphaned_events.append(goals_text)

        if player_assists > 0:
            assists_text = f"{player_assists} assist{'s' if player_assists != 1 else ''}"
            orphaned_events.append(assists_text)

        print(f"DEBUG: Player {row} would have orphaned events: {orphaned_events}")
        return orphaned_events

    def _validate_clear_all_substitutions(self):
        """Validate that clearing all substitutions won't create orphaned goals/assists.

        Returns:
            dict: {'can_clear': bool, 'message': str}
        """
        print("DEBUG: Validating clear all substitutions")

        players_with_orphaned_events = []

        # Check all players who are not in lineup but have goals/assists
        for row in range(self.players_table.rowCount()):
            lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
            is_lineup = lineup_item.checkState() == Qt.CheckState.Checked if lineup_item else False

            # Skip players in lineup (they can always have events)
            if is_lineup:
                continue

            # Check if player has goals/assists
            player_goals = self._get_player_goals_count(row)
            player_assists = self._get_player_assists_count(row)

            if player_goals > 0 or player_assists > 0:
                # This player is not in lineup but has events - they would be orphaned
                name_item = self.players_table.item(row, self._get_column_index("Player"))
                player_name = name_item.text() if name_item else f"Player {row}"

                orphaned_events = []
                if player_goals > 0:
                    goals_text = f"{player_goals} goal{'s' if player_goals != 1 else ''}"
                    orphaned_events.append(goals_text)

                if player_assists > 0:
                    assists_text = f"{player_assists} assist{'s' if player_assists != 1 else ''}"
                    orphaned_events.append(assists_text)

                players_with_orphaned_events.append({
                    'name': player_name,
                    'events': orphaned_events
                })

                print(f"DEBUG: Player {player_name} (row {row}) would have orphaned events: {orphaned_events}")

        if players_with_orphaned_events:
            # Create detailed error message
            message_parts = [self.tr("Cannot clear all substitutions because the following players would have orphaned goals/assists:")]
            message_parts.append("")

            for player_info in players_with_orphaned_events:
                events_text = ", ".join(player_info['events'])
                message_parts.append(f"• {player_info['name']}: {events_text}")

            message_parts.append("")
            message_parts.append(self.tr("Please clear the goals/assists first, or add these players to the starting lineup."))

            return {
                'can_clear': False,
                'message': "\n".join(message_parts)
            }

        return {'can_clear': True, 'message': ''}

    def _clear_player_events_for_non_playing(self, row):
        """Clear goals and assists for a player who is not playing (lineup=false, sub_in=empty)."""
        print(f"DEBUG: Clearing events for non-playing player at row {row}")

        # Clear goals
        if row in self.goals_widgets:
            goals_widget = self.goals_widgets[row]
            if goals_widget.get_value() > 0:
                print(f"DEBUG: Clearing {goals_widget.get_value()} goals for row {row}")
                goals_widget.set_value(0)

        # Clear assists
        if row in self.assists_widgets:
            assists_widget = self.assists_widgets[row]
            if assists_widget.get_value() > 0:
                print(f"DEBUG: Clearing {assists_widget.get_value()} assists for row {row}")
                assists_widget.set_value(0)

        # Clear goal minutes
        goals_min_col = self._get_column_index("Goals min")
        goals_min_item = self.players_table.item(row, goals_min_col)
        if goals_min_item and goals_min_item.text().strip():
            print(f"DEBUG: Clearing goal minutes for row {row}")
            goals_min_item.setText("")

        # Clear assist minutes
        assists_min_col = self._get_column_index("Assists min")
        assists_min_item = self.players_table.item(row, assists_min_col)
        if assists_min_item and assists_min_item.text().strip():
            print(f"DEBUG: Clearing assist minutes for row {row}")
            assists_min_item.setText("")

        # Update validation after clearing
        self._update_all_players_data_validation()

    def _validate_and_clean_all_player_events(self):
        """Validate all players and clean up invalid goals/assists for non-playing players."""
        print("DEBUG: Validating and cleaning all player events")

        for row in range(self.players_table.rowCount()):
            # Check if player actually played
            lineup_item = self.players_table.item(row, self._get_column_index("Lineup"))
            sub_in_item = self.players_table.item(row, self._get_column_index("Sub In"))

            if not lineup_item:
                continue

            is_lineup = lineup_item.checkState() == Qt.CheckState.Checked
            sub_in_value = sub_in_item.text().strip() if sub_in_item else ""

            # Player didn't play if not in lineup and no sub in
            player_played = is_lineup or (sub_in_value and sub_in_value != "0")

            if not player_played:
                # Check if player has any goals or assists
                has_goals = row in self.goals_widgets and self.goals_widgets[row].get_value() > 0
                has_assists = row in self.assists_widgets and self.assists_widgets[row].get_value() > 0

                if has_goals or has_assists:
                    player_item = self.players_table.item(row, self._get_column_index("Player"))
                    player_name = player_item.text() if player_item else f"Player row {row}"
                    print(f"DEBUG: Found invalid events for non-playing player {player_name}, clearing...")
                    self._clear_player_events_for_non_playing(row)

    def _set_assist_minute(self, row, assist_number, minute, total_assists, existing_minutes, missing_assists=None):
        """Set the minute for a specific assist and continue with next missing assist if needed."""
        # Extend existing_minutes list if needed
        while len(existing_minutes) < assist_number:
            existing_minutes.append(None)

        # Set the minute for this assist
        existing_minutes[assist_number - 1] = minute

        # Update the display
        self._update_assists_min_display(row, existing_minutes)

        # Update all players data validation
        self._update_all_players_data_validation()

        # Continue with next missing assist if there are more
        if missing_assists:
            try:
                current_index = missing_assists.index(assist_number)
                if current_index + 1 < len(missing_assists):
                    next_missing_assist = missing_assists[current_index + 1]
                    print(f"DEBUG: Continuing with next missing assist: {next_missing_assist}")
                    self._show_single_assist_minute_popup(row, next_missing_assist, total_assists, existing_minutes, missing_assists)
                else:
                    print(f"DEBUG: All missing assists completed")
            except ValueError:
                print(f"DEBUG: Assist {assist_number} not found in missing_assists list")

    def _update_assists_min_display(self, row, minutes_list):
        """Update the Assists min column display with comma-separated minutes."""
        assists_min_col = self._get_column_index("Assists min")

        # Try to get the item
        assists_min_item = self.players_table.item(row, assists_min_col)

        if assists_min_item:
            # Filter out None values and create comma-separated string
            valid_minutes = [str(m) for m in minutes_list if m is not None]
            display_text = ", ".join(valid_minutes)
            assists_min_item.setText(display_text)
        else:
            # Try to create the item if it doesn't exist
            assists_min_item = QTableWidgetItem("")
            assists_min_item.setFlags(assists_min_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            assists_min_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.players_table.setItem(row, assists_min_col, assists_min_item)

            # Now set the text
            valid_minutes = [str(m) for m in minutes_list if m is not None]
            display_text = ", ".join(valid_minutes)
            assists_min_item.setText(display_text)

    def _update_all_players_data_validation(self):
        """Update all Players Data validation statuses."""
        self._update_captain_set_validation()
        self._update_goalkeeper_validation()
        self._update_goals_assigned_validation()
        self._update_goals_minutes_validation()
        self._update_assists_minutes_validation()

    def _update_captain_set_validation(self):
        """Update the Captain set validation status."""
        if not hasattr(self, 'captain_set_label') or not hasattr(self, 'captain_set_checkmark') or not hasattr(self, 'captain_set_warning'):
            return

        # Check if any player has captain status (C column checked)
        captain_found = False
        c_col = self._get_column_index("C")

        for row in range(self.players_table.rowCount()):
            c_item = self.players_table.item(row, c_col)
            if c_item and c_item.checkState() == Qt.CheckState.Checked:
                captain_found = True
                break

        if captain_found:
            # OK - use green color, show checkmark, hide warning
            ok_color = get_color("match_validation_ok").name()
            self.captain_set_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")
            self.captain_set_checkmark.show()
            self.captain_set_warning.hide()
        else:
            # Warning - use orange color, show warning symbol, hide checkmark
            warning_color = get_color("match_validation_warning").name()
            self.captain_set_label.setStyleSheet(f"color: {warning_color}; font-size: 11px;")
            self.captain_set_checkmark.hide()
            self.captain_set_warning.show()

    def _update_goalkeeper_validation(self):
        """Update the Goalkeeper validation status."""
        if not hasattr(self, 'goalkeeper_label') or not hasattr(self, 'goalkeeper_checkmark') or not hasattr(self, 'goalkeeper_warning'):
            return

        # Check if there's a GK in the starting lineup
        gk_in_lineup = False
        lineup_col = self._get_column_index("Lineup")
        pos_col = self._get_column_index("Pos")

        for row in range(self.players_table.rowCount()):
            lineup_item = self.players_table.item(row, lineup_col)
            pos_item = self.players_table.item(row, pos_col)

            if (lineup_item and lineup_item.checkState() == Qt.CheckState.Checked and
                pos_item and pos_item.text().strip().upper() == "GK"):
                gk_in_lineup = True
                break

        if gk_in_lineup:
            # OK - use green color, show checkmark, hide warning
            ok_color = get_color("match_validation_ok").name()
            self.goalkeeper_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")
            self.goalkeeper_checkmark.show()
            self.goalkeeper_warning.hide()
        else:
            # Warning - use orange color, show warning symbol, hide checkmark
            warning_color = get_color("match_validation_warning").name()
            self.goalkeeper_label.setStyleSheet(f"color: {warning_color}; font-size: 11px;")
            self.goalkeeper_checkmark.hide()
            self.goalkeeper_warning.show()

    def _update_goals_assigned_validation(self):
        """Update the Goals assigned validation status."""
        if not hasattr(self, 'goals_assigned_label') or not hasattr(self, 'goals_assigned_checkmark') or not hasattr(self, 'goals_assigned_warning'):
            return

        # Get total player goals
        total_player_goals = 0
        for row in range(self.players_table.rowCount()):
            player_goals = self._get_player_goals_count(row)
            total_player_goals += player_goals

        # Get team score (our goals)
        team_goals = 0
        if self.home_radio.isChecked():
            team_goals = self.home_score_field.value()
        else:
            team_goals = self.away_score_field.value()

        # Add opponents own goals to total player goals (these count toward our score but aren't assigned to players)
        opponents_own_goals = self.opponents_own_goals_field.value() if hasattr(self, 'opponents_own_goals_field') else 0
        total_goals_for_comparison = total_player_goals + opponents_own_goals

        print(f"DEBUG: Goals assigned validation - player_goals: {total_player_goals}, opponents_own_goals: {opponents_own_goals}, total_for_comparison: {total_goals_for_comparison}, team_goals: {team_goals}")

        if total_goals_for_comparison == team_goals:
            # OK - use green color, show checkmark, hide warning
            ok_color = get_color("match_validation_ok").name()
            self.goals_assigned_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")
            self.goals_assigned_checkmark.show()
            self.goals_assigned_warning.hide()
        else:
            # Warning - use orange color, show warning symbol, hide checkmark
            warning_color = get_color("match_validation_warning").name()
            self.goals_assigned_label.setStyleSheet(f"color: {warning_color}; font-size: 11px;")
            self.goals_assigned_checkmark.hide()
            self.goals_assigned_warning.show()

    def _update_goals_minutes_validation(self):
        """Update the Goals minutes validation status."""
        if not hasattr(self, 'goals_minutes_label') or not hasattr(self, 'goals_minutes_checkmark') or not hasattr(self, 'goals_minutes_xmark'):
            return

        # Count total goals and goals with minutes
        total_goals = 0
        goals_with_minutes = 0

        # Also update individual cell highlighting
        for row in range(self.players_table.rowCount()):
            # Get goals count for this player
            player_goals = self._get_player_goals_count(row)
            total_goals += player_goals

            if player_goals > 0:
                # Get existing goal minutes for this player
                existing_minutes = self._get_existing_goal_minutes(row)
                # Count how many goals have minutes (non-None values)
                player_goals_with_minutes = len([m for m in existing_minutes if m is not None])
                goals_with_minutes += player_goals_with_minutes

                # Update cell highlighting based on completeness
                self._update_goals_min_cell_highlighting(row, player_goals, player_goals_with_minutes)
            else:
                # No goals - clear any highlighting
                self._update_goals_min_cell_highlighting(row, 0, 0)

        print(f"DEBUG: Goals minutes validation - total_goals: {total_goals}, goals_with_minutes: {goals_with_minutes}")

        # Update validation status
        if total_goals == 0:
            # No goals to validate - show as complete (green)
            ok_color = get_color("match_validation_ok").name()
            self.goals_minutes_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")
            self.goals_minutes_checkmark.show()
            self.goals_minutes_xmark.hide()
        elif goals_with_minutes == total_goals:
            # All goals have minutes - show as complete (green)
            ok_color = get_color("match_validation_ok").name()
            self.goals_minutes_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")
            self.goals_minutes_checkmark.show()
            self.goals_minutes_xmark.hide()
        else:
            # Some goals missing minutes - show as serious issue (red with X mark)
            serious_color = get_color("match_validation_serious").name()
            self.goals_minutes_label.setStyleSheet(f"color: {serious_color}; font-size: 11px;")
            self.goals_minutes_checkmark.hide()
            self.goals_minutes_xmark.show()

    def _update_goals_min_cell_highlighting(self, row, player_goals, player_goals_with_minutes):
        """Update the subtle highlighting for Goals min cells based on completeness."""
        goals_min_col = self._get_column_index("Goals min")
        goals_min_item = self.players_table.item(row, goals_min_col)

        if goals_min_item:
            if player_goals == 0:
                # No goals - no highlighting needed
                goals_min_item.setBackground(QColor("white"))
            elif player_goals_with_minutes == player_goals:
                # All goals have minutes - no highlighting (complete)
                goals_min_item.setBackground(QColor("white"))
            else:
                # Some goals missing minutes - configurable highlight color
                highlight_color = get_color("match_missing_minutes_highlight")
                goals_min_item.setBackground(highlight_color)

    def _update_assists_minutes_validation(self):
        """Update the Assists minutes validation status."""
        if not hasattr(self, 'assists_minutes_label') or not hasattr(self, 'assists_minutes_checkmark') or not hasattr(self, 'assists_minutes_xmark'):
            return

        # Count total assists and assists with minutes
        total_assists = 0
        assists_with_minutes = 0

        # Also update individual cell highlighting
        for row in range(self.players_table.rowCount()):
            # Get assists count for this player
            player_assists = self._get_player_assists_count(row)
            total_assists += player_assists

            if player_assists > 0:
                # Get existing assist minutes for this player
                existing_minutes = self._get_existing_assist_minutes(row)
                # Count how many assists have minutes (non-None values)
                player_assists_with_minutes = len([m for m in existing_minutes if m is not None])
                assists_with_minutes += player_assists_with_minutes

                # Update cell highlighting based on completeness
                self._update_assists_min_cell_highlighting(row, player_assists, player_assists_with_minutes)
            else:
                # No assists - clear any highlighting
                self._update_assists_min_cell_highlighting(row, 0, 0)

        print(f"DEBUG: Assists minutes validation - total_assists: {total_assists}, assists_with_minutes: {assists_with_minutes}")

        # Update validation status
        if total_assists == 0:
            # No assists to validate - show as complete (green)
            ok_color = get_color("match_validation_ok").name()
            self.assists_minutes_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")
            self.assists_minutes_checkmark.show()
            self.assists_minutes_xmark.hide()
        elif assists_with_minutes == total_assists:
            # All assists have minutes - show as complete (green)
            ok_color = get_color("match_validation_ok").name()
            self.assists_minutes_label.setStyleSheet(f"color: {ok_color}; font-size: 11px;")
            self.assists_minutes_checkmark.show()
            self.assists_minutes_xmark.hide()
        else:
            # Some assists missing minutes - show as serious issue (red with X mark)
            serious_color = get_color("match_validation_serious").name()
            self.assists_minutes_label.setStyleSheet(f"color: {serious_color}; font-size: 11px;")
            self.assists_minutes_checkmark.hide()
            self.assists_minutes_xmark.show()

    def _update_assists_min_cell_highlighting(self, row, player_assists, player_assists_with_minutes):
        """Update the subtle highlighting for Assists min cells based on completeness."""
        assists_min_col = self._get_column_index("Assists min")
        assists_min_item = self.players_table.item(row, assists_min_col)

        if assists_min_item:
            if player_assists == 0:
                # No assists - no highlighting needed
                assists_min_item.setBackground(QColor("white"))
            elif player_assists_with_minutes == player_assists:
                # All assists have minutes - no highlighting (complete)
                assists_min_item.setBackground(QColor("white"))
            else:
                # Some assists missing minutes - configurable highlight color
                highlight_color = get_color("match_missing_minutes_highlight")
                assists_min_item.setBackground(highlight_color)

    def _show_header_context_menu(self, pos):
        """Show context menu for player table header."""
        # Create context menu
        menu = QMenu(self)

        # Add column visibility options
        menu.addSection(self.tr("Show/Hide Columns"))

        # Add group for advanced stats
        advanced_stats_menu = menu.addMenu(self.tr("Advanced Stats"))

        # Define advanced statistics categories and their columns
        advanced_stats_categories = {
            "Shooting": ["Attempts", "On Target", "X-Goals"],
            "Creating": ["X-Assists", "Shot Creating Actions", "Goal Creating Actions"],
            "Passing Short": ["Passes Short", "Passes Short Completed", "Passes Short %"],
            "Passing Medium": ["Passes Medium", "Passes Medium Completed", "Passes Medium %"],
            "Passing Long": ["Passes Long", "Passes Long Completed", "Passes Long %"],
            "Passing Total": ["Passes", "Passes Completed", "Passes %"],
            "Passing Advanced": ["Key Passes", "Through Balls", "Passes 3/3", "Passes PA",
                               "Progressive Passes", "Switch Play", "Passes Offside", "Passes Blocked"],
            "Crossing": ["Total Crosses", "Crosses to Penalty Area"],
            "Distance": ["Total Passing Distance", "Progressive Passing Distance", "Progressive Carries"],
            "Dribbling": ["Take-ons Attempts", "Take-ons Successful"],
            "Aerial": ["Aerial Duels Won", "Aerial Duels Lost", "Aerial Duels Won %"],
            "Ball Control": ["Touches"],
            "Defending": ["Tackles", "Tackles Won", "Tackles %", "Interceptions", "Clearances"],
            "Blocking": ["Opp Shots Blocked", "Opp Passes Blocked"],
            "Errors": ["Mistakes Lead to Opp Shot"]
        }

        # Add "Show/Hide All Advanced Stats" option
        show_all_action = QAction(self.tr("Show All Advanced Stats"), self)
        show_all_action.triggered.connect(lambda: self._toggle_all_advanced_stats(True))
        advanced_stats_menu.addAction(show_all_action)

        hide_all_action = QAction(self.tr("Hide All Advanced Stats"), self)
        hide_all_action.triggered.connect(lambda: self._toggle_all_advanced_stats(False))
        advanced_stats_menu.addAction(hide_all_action)

        advanced_stats_menu.addSeparator()

        # Add category submenus
        for category_name, columns in advanced_stats_categories.items():
            if len(columns) == 1:
                # For single-item categories, add directly to main menu instead of submenu
                header_name = columns[0]
                col_index = self._get_column_index(header_name)
                if col_index >= 0:
                    action = QAction(self.tr(header_name), self)
                    action.setCheckable(True)
                    action.setChecked(not self.players_table.isColumnHidden(col_index))
                    action.setData(col_index)
                    action.triggered.connect(self._toggle_column_visibility)
                    advanced_stats_menu.addAction(action)
            else:
                # For multi-item categories, create submenu
                category_menu = advanced_stats_menu.addMenu(self.tr(category_name))

                # Add "Show/Hide All" options for this category
                show_category_action = QAction(self.tr(f"Show All {category_name}"), self)
                show_category_action.setData((category_name, columns, True))
                show_category_action.triggered.connect(self._toggle_category_columns)
                category_menu.addAction(show_category_action)

                hide_category_action = QAction(self.tr(f"Hide All {category_name}"), self)
                hide_category_action.setData((category_name, columns, False))
                hide_category_action.triggered.connect(self._toggle_category_columns)
                category_menu.addAction(hide_category_action)

                category_menu.addSeparator()

                for header_name in columns:
                    col_index = self._get_column_index(header_name)
                    if col_index >= 0:
                        action = QAction(self.tr(header_name), self)
                        action.setCheckable(True)
                        action.setChecked(not self.players_table.isColumnHidden(col_index))
                        action.setData(col_index)
                        action.triggered.connect(self._toggle_column_visibility)
                        category_menu.addAction(action)

        # Add group for performance stats
        performance_menu = menu.addMenu(self.tr("Performance"))

        # Define performance categories and their columns
        performance_categories = {
            "Attack": ["Passing", "Creativity", "Dribbling-Carrying"],
            "Defense": ["Marking", "Positioning", "Aerial"],
            "Physical Condition": ["Speed", "Stamina", "Power"],
            "Mental Ability": ["Decision-Making", "Concentration", "Work-Rate"]
        }

        # Add "Show/Hide All Performance" option
        show_all_performance_action = QAction(self.tr("Show All Performance"), self)
        show_all_performance_action.triggered.connect(lambda: self._toggle_all_performance_stats(True))
        performance_menu.addAction(show_all_performance_action)

        hide_all_performance_action = QAction(self.tr("Hide All Performance"), self)
        hide_all_performance_action.triggered.connect(lambda: self._toggle_all_performance_stats(False))
        performance_menu.addAction(hide_all_performance_action)

        performance_menu.addSeparator()

        # Add performance category submenus
        for category_name, columns in performance_categories.items():
            # Create submenu for each category
            category_menu = performance_menu.addMenu(self.tr(category_name))

            # Add "Show/Hide All" options for this category
            show_category_action = QAction(self.tr(f"Show All {category_name}"), self)
            show_category_action.setData((category_name, columns, True))
            show_category_action.triggered.connect(self._toggle_category_columns)
            category_menu.addAction(show_category_action)

            hide_category_action = QAction(self.tr(f"Hide All {category_name}"), self)
            hide_category_action.setData((category_name, columns, False))
            hide_category_action.triggered.connect(self._toggle_category_columns)
            category_menu.addAction(hide_category_action)

            category_menu.addSeparator()

            for header_name in columns:
                col_index = self._get_column_index(header_name)
                if col_index >= 0:
                    action = QAction(self.tr(header_name), self)
                    action.setCheckable(True)
                    action.setChecked(not self.players_table.isColumnHidden(col_index))
                    action.setData(col_index)
                    action.triggered.connect(self._toggle_column_visibility)
                    category_menu.addAction(action)

        # Show menu at cursor position
        header = self.players_table.horizontalHeader()
        menu.exec(header.mapToGlobal(pos))

    def _toggle_column_visibility(self):
        """Toggle visibility of a column."""
        action = self.sender()
        if action:
            column = action.data()
            is_visible = action.isChecked()
            self.players_table.setColumnHidden(column, not is_visible)
            # Save column state after change
            self._save_column_state()

    def _toggle_all_advanced_stats(self, show):
        """Show or hide all advanced statistics columns."""
        # Get all advanced statistics column names
        advanced_stats_columns = [
            "Attempts", "On Target", "X-Goals", "X-Assists", "Shot Creating Actions", "Goal Creating Actions",
            "Passes Short", "Passes Short Completed", "Passes Short %", "Passes Medium", "Passes Medium Completed",
            "Passes Medium %", "Passes Long", "Passes Long Completed", "Passes Long %", "Passes", "Passes Completed",
            "Passes %", "Key Passes", "Through Balls", "Passes 3/3", "Passes PA", "Progressive Passes", "Switch Play",
            "Passes Offside", "Passes Blocked", "Total Crosses", "Crosses to Penalty Area", "Total Passing Distance",
            "Progressive Passing Distance", "Progressive Carries", "Take-ons Attempts", "Take-ons Successful",
            "Aerial Duels Won", "Aerial Duels Lost", "Aerial Duels Won %", "Touches", "Tackles", "Tackles Won",
            "Tackles %", "Interceptions", "Clearances", "Opp Shots Blocked", "Opp Passes Blocked", "Mistakes Lead to Opp Shot"
        ]

        # Toggle visibility for all advanced stats columns
        for col_name in advanced_stats_columns:
            col_index = self._get_column_index(col_name)
            if col_index >= 0:
                self.players_table.setColumnHidden(col_index, not show)

        # Save column state after bulk change
        self._save_column_state()

    def _toggle_all_performance_stats(self, show):
        """Show or hide all performance statistics columns."""
        # Get all performance statistics column names
        performance_stats_columns = [
            "Passing", "Creativity", "Dribbling-Carrying",
            "Marking", "Positioning", "Aerial",
            "Speed", "Stamina", "Power",
            "Decision-Making", "Concentration", "Work-Rate"
        ]

        # Toggle visibility for all performance stats columns
        for col_name in performance_stats_columns:
            col_index = self._get_column_index(col_name)
            if col_index >= 0:
                self.players_table.setColumnHidden(col_index, not show)

        # Save column state after bulk change
        self._save_column_state()

    def _toggle_category_columns(self):
        """Show or hide all columns in a specific category."""
        action = self.sender()
        if action:
            data = action.data()
            if data and len(data) == 3:
                category_name, columns, show = data

                # Toggle visibility for all columns in this category
                for col_name in columns:
                    col_index = self._get_column_index(col_name)
                    if col_index >= 0:
                        self.players_table.setColumnHidden(col_index, not show)

                # Save column state after bulk change
                self._save_column_state()

    def _save_column_state(self):
        """Save column visibility and width state to settings."""
        settings = QSettings()

        # Save column visibility state
        column_visibility = {}
        for i in range(self.players_table.columnCount()):
            column_name = self.column_headers[i] if i < len(self.column_headers) else f"Column_{i}"
            column_visibility[column_name] = not self.players_table.isColumnHidden(i)

        settings.setValue("NewMatchPage/ColumnVisibility", column_visibility)

        # Save column widths
        column_widths = {}
        for i in range(self.players_table.columnCount()):
            column_name = self.column_headers[i] if i < len(self.column_headers) else f"Column_{i}"
            column_widths[column_name] = self.players_table.columnWidth(i)

        settings.setValue("NewMatchPage/ColumnWidths", column_widths)

    def _restore_column_state(self):
        """Restore column visibility and width state from settings."""
        settings = QSettings()

        # Restore column visibility
        column_visibility = settings.value("NewMatchPage/ColumnVisibility", {})
        if isinstance(column_visibility, dict):
            for i in range(self.players_table.columnCount()):
                column_name = self.column_headers[i] if i < len(self.column_headers) else f"Column_{i}"
                if column_name in column_visibility:
                    is_visible = column_visibility[column_name]
                    self.players_table.setColumnHidden(i, not is_visible)

        # Restore column widths
        column_widths = settings.value("NewMatchPage/ColumnWidths", {})
        if isinstance(column_widths, dict):
            for i in range(self.players_table.columnCount()):
                column_name = self.column_headers[i] if i < len(self.column_headers) else f"Column_{i}"
                if column_name in column_widths:
                    width = column_widths[column_name]
                    if isinstance(width, int) and width > 0:
                        self.players_table.setColumnWidth(i, width)

    def _on_column_resized(self, logical_index, old_size, new_size):
        """Handle column resize event to save new width."""
        # Save column state when user manually resizes columns
        self._save_column_state()

    def changeEvent(self, event):
        """Handle change events, including language changes."""
        if event.type() == QEvent.Type.LanguageChange:
            # Update all date edit widgets to use the new language locale
            update_all_date_edits_locale(self)

        super().changeEvent(event)  # Call base class implementation

    def closeEvent(self, event):
        """Handle window close event to save state and emit signal."""
        # Save window state
        self._save_window_state()

        # Emit signal to notify that window is closing
        self.window_closed.emit()

        # Accept the close event
        super().closeEvent(event)

    def tr(self, text):
        """Translation method."""
        return QCoreApplication.translate("NewMatchPage", text)
