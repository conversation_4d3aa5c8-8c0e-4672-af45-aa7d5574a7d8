<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="zh_CN">
<context>
    <name></name>
    <message>
        <location filename="../app/pages/options_page.py" line="4008"/>
        <source>Add Competition</source>
        <translation>添加比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4027"/>
        <location filename="../app/pages/options_page.py" line="4198"/>
        <source>Competition:</source>
        <translation>比赛:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4033"/>
        <location filename="../app/pages/options_page.py" line="4204"/>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="153"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="153"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="153"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="153"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="153"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="153"/>
        <source>Start Date:</source>
        <translation type="unfinished">开始日期:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4039"/>
        <location filename="../app/pages/options_page.py" line="4210"/>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="154"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="154"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="154"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="154"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="154"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="154"/>
        <source>End Date:</source>
        <translation type="unfinished">结束日期:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4044"/>
        <location filename="../app/pages/options_page.py" line="4215"/>
        <location filename="../app/pages/options_page.py" line="4461"/>
        <location filename="../app/pages/options_page.py" line="4663"/>
        <source>National</source>
        <translation>国家级</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4045"/>
        <location filename="../app/pages/options_page.py" line="4216"/>
        <location filename="../app/pages/options_page.py" line="4462"/>
        <location filename="../app/pages/options_page.py" line="4664"/>
        <source>International</source>
        <translation>国际级</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4046"/>
        <location filename="../app/pages/options_page.py" line="4217"/>
        <location filename="../app/pages/options_page.py" line="4463"/>
        <location filename="../app/pages/options_page.py" line="4665"/>
        <source>Local</source>
        <translation>地方级</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4048"/>
        <location filename="../app/pages/options_page.py" line="4063"/>
        <location filename="../app/pages/options_page.py" line="4219"/>
        <location filename="../app/pages/options_page.py" line="4235"/>
        <location filename="../app/pages/options_page.py" line="4465"/>
        <location filename="../app/pages/options_page.py" line="4478"/>
        <location filename="../app/pages/options_page.py" line="4667"/>
        <location filename="../app/pages/options_page.py" line="4681"/>
        <source>Other</source>
        <translation type="unfinished">Other</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4050"/>
        <location filename="../app/pages/options_page.py" line="4222"/>
        <location filename="../app/pages/options_page.py" line="4499"/>
        <location filename="../app/pages/options_page.py" line="4720"/>
        <source>Type:</source>
        <translation>类型:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4055"/>
        <location filename="../app/pages/options_page.py" line="4227"/>
        <location filename="../app/pages/options_page.py" line="4470"/>
        <location filename="../app/pages/options_page.py" line="4673"/>
        <source>League</source>
        <translation>联赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4056"/>
        <location filename="../app/pages/options_page.py" line="4228"/>
        <location filename="../app/pages/options_page.py" line="4471"/>
        <location filename="../app/pages/options_page.py" line="4674"/>
        <source>League+playoffs/playouts</source>
        <translation>联赛+季后赛/保级赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4057"/>
        <location filename="../app/pages/options_page.py" line="4229"/>
        <location filename="../app/pages/options_page.py" line="4472"/>
        <location filename="../app/pages/options_page.py" line="4675"/>
        <source>Group+Knockouts</source>
        <translation>小组赛+淘汰赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4058"/>
        <location filename="../app/pages/options_page.py" line="4230"/>
        <location filename="../app/pages/options_page.py" line="4473"/>
        <location filename="../app/pages/options_page.py" line="4676"/>
        <source>Knockouts</source>
        <translation>淘汰赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4059"/>
        <location filename="../app/pages/options_page.py" line="4231"/>
        <location filename="../app/pages/options_page.py" line="4474"/>
        <location filename="../app/pages/options_page.py" line="4677"/>
        <source>Tournament</source>
        <translation>锦标赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4060"/>
        <location filename="../app/pages/options_page.py" line="4232"/>
        <location filename="../app/pages/options_page.py" line="4475"/>
        <location filename="../app/pages/options_page.py" line="4678"/>
        <source>Preparation matches</source>
        <translation>热身赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4061"/>
        <location filename="../app/pages/options_page.py" line="4233"/>
        <location filename="../app/pages/options_page.py" line="4476"/>
        <location filename="../app/pages/options_page.py" line="4679"/>
        <source>Charity</source>
        <translation>慈善赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4065"/>
        <location filename="../app/pages/options_page.py" line="4238"/>
        <location filename="../app/pages/options_page.py" line="4500"/>
        <location filename="../app/pages/options_page.py" line="4721"/>
        <source>Structure:</source>
        <translation>结构:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4081"/>
        <location filename="../app/pages/options_page.py" line="4257"/>
        <source>High</source>
        <translation>高</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4082"/>
        <location filename="../app/pages/options_page.py" line="4258"/>
        <source>Middle</source>
        <translation>中</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4084"/>
        <location filename="../app/pages/options_page.py" line="4260"/>
        <source>Low</source>
        <translation>低</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4085"/>
        <location filename="../app/pages/options_page.py" line="4262"/>
        <source>Priority:</source>
        <translation>优先级:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4090"/>
        <location filename="../app/pages/options_page.py" line="4268"/>
        <location filename="../app/pages/options_page.py" line="4501"/>
        <location filename="../app/pages/options_page.py" line="4722"/>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="159"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="159"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="159"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="159"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="159"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="159"/>
        <source>Notes:</source>
        <translation>备注:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4104"/>
        <location filename="../app/pages/options_page.py" line="4113"/>
        <location filename="../app/pages/options_page.py" line="4282"/>
        <location filename="../app/pages/options_page.py" line="4291"/>
        <source>Invalid Dates</source>
        <translation>无效日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4106"/>
        <location filename="../app/pages/options_page.py" line="4284"/>
        <source>Start date cannot be after end date.</source>
        <translation>开始日期不能晚于结束日期。</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4115"/>
        <location filename="../app/pages/options_page.py" line="4293"/>
        <source>Competition dates must be within the season dates.</source>
        <translation>比赛日期必须在赛季日期内。</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4122"/>
        <location filename="../app/pages/options_page.py" line="4300"/>
        <source>Missing Competition</source>
        <translation>缺少比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4124"/>
        <location filename="../app/pages/options_page.py" line="4302"/>
        <source>Please select a competition from the dropdown list.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4157"/>
        <location filename="../app/pages/options_page.py" line="4327"/>
        <location filename="../app/pages/options_page.py" line="4632"/>
        <location filename="../app/pages/options_page.py" line="4839"/>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="17"/>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="223"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="17"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="223"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="17"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="223"/>
        <source>No Selection</source>
        <translation>未选择</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4159"/>
        <location filename="../app/pages/options_page.py" line="4634"/>
        <source>Please select a competition to edit.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4171"/>
        <source>Edit Competition</source>
        <translation>编辑比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4329"/>
        <location filename="../app/pages/options_page.py" line="4841"/>
        <source>Please select a competition to remove.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4339"/>
        <location filename="../app/pages/options_page.py" line="4911"/>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="233"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="233"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="233"/>
        <source>Confirm Deletion</source>
        <translation>确认删除</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4340"/>
        <source>Are you sure you want to remove the competition &apos;{competition_name}&apos;?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4447"/>
        <source>Add Football Competition</source>
        <translation>添加足球比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4492"/>
        <location filename="../app/pages/options_page.py" line="4697"/>
        <source>No Logo</source>
        <translation>无标志</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4497"/>
        <location filename="../app/pages/options_page.py" line="4718"/>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="152"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="152"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="152"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="152"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="152"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="152"/>
        <source>Name:</source>
        <translation>名称:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4498"/>
        <location filename="../app/pages/options_page.py" line="4719"/>
        <source>Organization:</source>
        <translation>组织:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4507"/>
        <location filename="../app/pages/options_page.py" line="4728"/>
        <source>Logo:</source>
        <translation type="unfinished">队徽:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4526"/>
        <location filename="../app/pages/options_page.py" line="4747"/>
        <source>Select Logo Image</source>
        <translation>选择标志图像</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4529"/>
        <location filename="../app/pages/options_page.py" line="4750"/>
        <source>PNG Files (*.png)</source>
        <translation>PNG文件 (*.png)</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4539"/>
        <location filename="../app/pages/options_page.py" line="4760"/>
        <source>File Too Large</source>
        <translation>文件太大</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4540"/>
        <location filename="../app/pages/options_page.py" line="4761"/>
        <source>The logo file must be less than 100KB. Current size: {:.1f}KB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4549"/>
        <location filename="../app/pages/options_page.py" line="4770"/>
        <source>Image Too Large</source>
        <translation>图像太大</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4550"/>
        <location filename="../app/pages/options_page.py" line="4771"/>
        <source>The logo dimensions must be at most 200x200 pixels. Current size: {}x{}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4649"/>
        <source>Edit Football Competition</source>
        <translation>编辑足球比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4714"/>
        <source>Upload Logo</source>
        <translation>上传标志</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4717"/>
        <source>ID:</source>
        <translation>ID:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4860"/>
        <source>Competition In Use</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4866"/>
        <source>The competition &apos;{}&apos; is currently used in {} entries in:

Page: Options
Tab: Dates
Sub-tab: Season
Section: Competitions

Please remove these entries first before deleting the competition.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4872"/>
        <source>Go to Dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4886"/>
        <source>Dates</source>
        <translation>日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="4912"/>
        <source>Are you sure you want to delete the competition &apos;{}&apos;?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="18"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="18"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="18"/>
        <source>Please select a microcycle to edit.</source>
        <translation type="unfinished">请选择要编辑的小周期。</translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="26"/>
        <source>Edit Transition Microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="46"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="46"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="46"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="36"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="36"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="36"/>
        <source>End date must be after start date</source>
        <translation type="unfinished">结束日期必须在开始日期之后</translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="55"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="55"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="55"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="45"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="45"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="45"/>
        <source>Start date must be within macrocycle dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="60"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="60"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="60"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="50"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="50"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="50"/>
        <source>End date must be within macrocycle dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="71"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="61"/>
        <source>Transition Mesocycle must start after the last Competition Mesocycle ends ({})</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="85"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="85"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="85"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="74"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="74"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="74"/>
        <source>Microcycle dates cannot overlap with existing microcycles in this mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="155"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="155"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="155"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="155"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="155"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="155"/>
        <source>Target 1:</source>
        <translation>目标1:</translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="156"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="156"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="156"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="156"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="156"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="156"/>
        <source>Target 2:</source>
        <translation>目标2:</translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="157"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="157"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="157"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="157"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="157"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="157"/>
        <source>Target 3:</source>
        <translation>目标3:</translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="158"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="158"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="158"/>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="158"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="158"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="158"/>
        <source>Intensity:</source>
        <translation>强度:</translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="224"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="224"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="224"/>
        <source>Please select a microcycle to remove.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods2.py" line="234"/>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="234"/>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="234"/>
        <source>Are you sure you want to remove this microcycle?</source>
        <translation>您确定要删除此微周期吗？</translation>
    </message>
    <message>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="26"/>
        <source>Edit Competition Microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/competition_mesocycle_methods2.py" line="71"/>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="61"/>
        <source>Competition Mesocycle must start after the last Basic Mesocycle ends ({})</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="26"/>
        <source>Edit Basic Microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/basic_mesocycle_methods2.py" line="71"/>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="61"/>
        <source>Basic Mesocycle must start after the last Preparation Mesocycle ends ({})</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods.py" line="16"/>
        <source>Add Transition Microcycle</source>
        <translation type="unfinished">添加过渡小周期</translation>
    </message>
    <message>
        <location filename="../app/pages/competition_mesocycle_methods.py" line="16"/>
        <source>Add Competition Microcycle</source>
        <translation type="unfinished">添加比赛小周期</translation>
    </message>
    <message>
        <location filename="../app/pages/basic_mesocycle_methods.py" line="16"/>
        <source>Add Basic Microcycle</source>
        <translation type="unfinished">添加基础小周期</translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods3.py" line="19"/>
        <location filename="../app/pages/competition_mesocycle_methods3.py" line="19"/>
        <location filename="../app/pages/basic_mesocycle_methods3.py" line="19"/>
        <source>Microcycle Overlap Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/transition_mesocycle_methods3.py" line="20"/>
        <location filename="../app/pages/competition_mesocycle_methods3.py" line="20"/>
        <location filename="../app/pages/basic_mesocycle_methods3.py" line="20"/>
        <source>Error: Cannot save because there are overlapping microcycles in your schedule:

{}

Please fix the overlaps before saving.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/bar_chart_method.py" line="63"/>
        <location filename="../app/dialogs/periodization_chart_dialog_part4.py" line="4"/>
        <location filename="../app/dialogs/periodization_chart_dialog_part4.py" line="115"/>
        <location filename="../app/dialogs/periodization_chart_dialog_part3.py" line="80"/>
        <location filename="../app/dialogs/periodization_chart_dialog_part2.py" line="93"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete4.py" line="4"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete4.py" line="115"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete3.py" line="80"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete2.py" line="93"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated4.py" line="63"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated3.py" line="55"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated2.py" line="93"/>
        <source>Intensity</source>
        <translation type="unfinished">强度</translation>
    </message>
    <message>
        <location filename="../app/dialogs/bar_chart_method.py" line="89"/>
        <location filename="../app/dialogs/periodization_chart_dialog_part4.py" line="141"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete4.py" line="141"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated4.py" line="89"/>
        <source>Microcycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_part2.py" line="16"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete2.py" line="16"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated2.py" line="16"/>
        <source>Date</source>
        <translation type="unfinished">日期</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_part2.py" line="21"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete2.py" line="21"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated2.py" line="21"/>
        <source>Intensity (%)</source>
        <translation type="unfinished">强度 (%)</translation>
    </message>
</context>
<context>
    <name>ClubWindow</name>
    <message>
        <location filename="../app/windows/club_window.py" line="381"/>
        <location filename="../app/windows/club_window.py" line="2477"/>
        <source>Club Name:</source>
        <translation>俱乐部名称:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="382"/>
        <location filename="../app/windows/club_window.py" line="2478"/>
        <source>Short Name:</source>
        <translation>简称:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="383"/>
        <location filename="../app/windows/club_window.py" line="2479"/>
        <source>Nickname:</source>
        <translation>昵称:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="384"/>
        <location filename="../app/windows/club_window.py" line="2480"/>
        <source>Year Founded:</source>
        <translation>成立年份:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="385"/>
        <location filename="../app/windows/club_window.py" line="2481"/>
        <source>City:</source>
        <translation>城市:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="386"/>
        <location filename="../app/windows/club_window.py" line="2482"/>
        <source>Country:</source>
        <translation>国家:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="387"/>
        <location filename="../app/windows/club_window.py" line="2487"/>
        <source>Region/Continent:</source>
        <translation>地区/大洲:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2090"/>
        <source>Error creating logo directory!</source>
        <translation>创建徽标目录时出错！</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2095"/>
        <location filename="../app/windows/club_window.py" line="2673"/>
        <source>PNG Images (*.png)</source>
        <translation>PNG 图片 (*.png)</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2097"/>
        <source>Select Logo Image</source>
        <translation>选择徽标图片</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2114"/>
        <source>Error copying logo!</source>
        <translation>复制徽标时出错！</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="465"/>
        <location filename="../app/windows/club_window.py" line="2498"/>
        <source>Stadium Name:</source>
        <translation>球场名称:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="390"/>
        <location filename="../app/windows/club_window.py" line="2015"/>
        <location filename="../app/windows/club_window.py" line="3428"/>
        <source>No Logo</source>
        <translation>无徽标</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="402"/>
        <location filename="../app/windows/club_window.py" line="2017"/>
        <location filename="../app/windows/club_window.py" line="2548"/>
        <location filename="../app/windows/club_window.py" line="3430"/>
        <source>Remove Logo</source>
        <translation>移除标志</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="466"/>
        <location filename="../app/windows/club_window.py" line="2499"/>
        <source>Capacity:</source>
        <translation>容量:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="467"/>
        <location filename="../app/windows/club_window.py" line="2500"/>
        <source>Seating Capacity:</source>
        <translation>座位容量:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="468"/>
        <location filename="../app/windows/club_window.py" line="2501"/>
        <source>Surface Type:</source>
        <translation>场地类型:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="469"/>
        <location filename="../app/windows/club_window.py" line="2502"/>
        <source>Year Built:</source>
        <translation>建造年份:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="470"/>
        <location filename="../app/windows/club_window.py" line="2506"/>
        <source>Stadium Owner:</source>
        <translation>球场所有者:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="453"/>
        <location filename="../app/windows/club_window.py" line="2527"/>
        <source>--- Select Surface ---</source>
        <translation>--- 选择场地类型 ---</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="460"/>
        <location filename="../app/windows/club_window.py" line="2537"/>
        <source>--- Select Owner ---</source>
        <translation>--- 选择所有者 ---</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="473"/>
        <location filename="../app/windows/club_window.py" line="2073"/>
        <location filename="../app/windows/club_window.py" line="3486"/>
        <source>No Stadium Image</source>
        <translation>无球场图片</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2006"/>
        <location filename="../app/windows/club_window.py" line="2111"/>
        <location filename="../app/windows/club_window.py" line="2558"/>
        <location filename="../app/windows/club_window.py" line="3419"/>
        <source>Logo: {}</source>
        <translation>徽标: {}</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2151"/>
        <source>Error creating stadium directory!</source>
        <translation>创建球场目录时出错！</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2155"/>
        <source>Image Files (*.png *.jpg *.jpeg)</source>
        <translation>图像文件 (*.png *.jpg *.jpeg)</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2157"/>
        <source>Select Stadium Image</source>
        <translation>选择球场图片</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2064"/>
        <location filename="../app/windows/club_window.py" line="2170"/>
        <location filename="../app/windows/club_window.py" line="2565"/>
        <location filename="../app/windows/club_window.py" line="3477"/>
        <source>Stadium Image: {}</source>
        <translation>球场图片: {}</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2173"/>
        <source>Error copying stadium image!</source>
        <translation>复制球场图片时出错！</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="845"/>
        <location filename="../app/windows/club_window.py" line="4573"/>
        <source>1st Color:</source>
        <translation>第一颜色：</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="846"/>
        <location filename="../app/windows/club_window.py" line="4576"/>
        <source>2nd Color:</source>
        <translation>第二颜色：</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="847"/>
        <location filename="../app/windows/club_window.py" line="4579"/>
        <source>3rd Color:</source>
        <translation>第三颜色：</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="829"/>
        <source>Colors</source>
        <translation>颜色</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="531"/>
        <location filename="../app/windows/club_window.py" line="4286"/>
        <source>Contact Information</source>
        <translation>联系信息</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="543"/>
        <location filename="../app/windows/club_window.py" line="4290"/>
        <source>Address Line 1:</source>
        <translation>地址行 1:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="544"/>
        <location filename="../app/windows/club_window.py" line="4291"/>
        <source>Address Line 2:</source>
        <translation>地址行 2:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="545"/>
        <location filename="../app/windows/club_window.py" line="4292"/>
        <source>City/Town:</source>
        <translation>城市/镇:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="546"/>
        <location filename="../app/windows/club_window.py" line="4293"/>
        <source>State/Province:</source>
        <translation>州/省:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="547"/>
        <location filename="../app/windows/club_window.py" line="4294"/>
        <source>Postal Code:</source>
        <translation>邮政编码:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="548"/>
        <location filename="../app/windows/club_window.py" line="4295"/>
        <source>Phone:</source>
        <translation>电话：</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="549"/>
        <location filename="../app/windows/club_window.py" line="4296"/>
        <source>Email:</source>
        <translation>电子邮件：</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="550"/>
        <location filename="../app/windows/club_window.py" line="4299"/>
        <source>Website:</source>
        <translation>网站:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="585"/>
        <location filename="../app/windows/club_window.py" line="657"/>
        <location filename="../app/windows/club_window.py" line="735"/>
        <location filename="../app/windows/club_window.py" line="4310"/>
        <location filename="../app/windows/club_window.py" line="4401"/>
        <location filename="../app/windows/club_window.py" line="4483"/>
        <source>Add</source>
        <translation>添加</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="588"/>
        <location filename="../app/windows/club_window.py" line="660"/>
        <location filename="../app/windows/club_window.py" line="738"/>
        <location filename="../app/windows/club_window.py" line="4218"/>
        <location filename="../app/windows/club_window.py" line="4240"/>
        <location filename="../app/windows/club_window.py" line="4275"/>
        <location filename="../app/windows/club_window.py" line="4316"/>
        <location filename="../app/windows/club_window.py" line="4407"/>
        <location filename="../app/windows/club_window.py" line="4489"/>
        <source>Search...</source>
        <translation>搜索...</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="589"/>
        <location filename="../app/windows/club_window.py" line="661"/>
        <location filename="../app/windows/club_window.py" line="739"/>
        <location filename="../app/windows/club_window.py" line="4216"/>
        <location filename="../app/windows/club_window.py" line="4238"/>
        <location filename="../app/windows/club_window.py" line="4273"/>
        <location filename="../app/windows/club_window.py" line="4318"/>
        <location filename="../app/windows/club_window.py" line="4409"/>
        <location filename="../app/windows/club_window.py" line="4491"/>
        <source>Clear</source>
        <translation>清除</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="591"/>
        <location filename="../app/windows/club_window.py" line="664"/>
        <location filename="../app/windows/club_window.py" line="741"/>
        <location filename="../app/windows/club_window.py" line="4212"/>
        <location filename="../app/windows/club_window.py" line="4234"/>
        <location filename="../app/windows/club_window.py" line="4269"/>
        <location filename="../app/windows/club_window.py" line="4320"/>
        <location filename="../app/windows/club_window.py" line="4411"/>
        <location filename="../app/windows/club_window.py" line="4493"/>
        <source>Filter</source>
        <translation>筛选</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="592"/>
        <location filename="../app/windows/club_window.py" line="670"/>
        <location filename="../app/windows/club_window.py" line="742"/>
        <location filename="../app/windows/club_window.py" line="4214"/>
        <location filename="../app/windows/club_window.py" line="4236"/>
        <location filename="../app/windows/club_window.py" line="4271"/>
        <location filename="../app/windows/club_window.py" line="4322"/>
        <location filename="../app/windows/club_window.py" line="4413"/>
        <location filename="../app/windows/club_window.py" line="4495"/>
        <source>Clear Filter</source>
        <translation>清除筛选</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="597"/>
        <location filename="../app/windows/club_window.py" line="676"/>
        <location filename="../app/windows/club_window.py" line="747"/>
        <location filename="../app/windows/club_window.py" line="4210"/>
        <location filename="../app/windows/club_window.py" line="4232"/>
        <location filename="../app/windows/club_window.py" line="4267"/>
        <location filename="../app/windows/club_window.py" line="4314"/>
        <location filename="../app/windows/club_window.py" line="4405"/>
        <location filename="../app/windows/club_window.py" line="4487"/>
        <source>Search:</source>
        <translation>搜索:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="778"/>
        <location filename="../app/windows/club_window.py" line="4155"/>
        <location filename="../app/windows/club_window.py" line="4209"/>
        <location filename="../app/windows/club_window.py" line="4231"/>
        <location filename="../app/windows/club_window.py" line="4260"/>
        <location filename="../app/windows/club_window.py" line="4334"/>
        <location filename="../app/windows/club_window.py" line="4345"/>
        <location filename="../app/windows/club_window.py" line="4427"/>
        <location filename="../app/windows/club_window.py" line="4513"/>
        <source>-- Select Type --</source>
        <translation>-- 选择类型 --</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="830"/>
        <source>1st Kit</source>
        <translation>第一套球衣</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="831"/>
        <source>2nd Kit</source>
        <translation>第二套球衣</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="832"/>
        <source>3rd Kit</source>
        <translation>第三套球衣</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="833"/>
        <source>1st GK Kit</source>
        <translation>第一套守门员球衣</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="834"/>
        <source>2nd GK Kit</source>
        <translation>第二套守门员球衣</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="899"/>
        <location filename="../app/windows/club_window.py" line="4590"/>
        <source>Images</source>
        <translation>图片</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="909"/>
        <location filename="../app/windows/club_window.py" line="4608"/>
        <source>Front</source>
        <translation>正面</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="4155"/>
        <source>(Unassigned)</source>
        <translation>（未分配）</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="586"/>
        <location filename="../app/windows/club_window.py" line="658"/>
        <location filename="../app/windows/club_window.py" line="736"/>
        <location filename="../app/windows/club_window.py" line="966"/>
        <location filename="../app/windows/club_window.py" line="1102"/>
        <location filename="../app/windows/club_window.py" line="4312"/>
        <location filename="../app/windows/club_window.py" line="4403"/>
        <location filename="../app/windows/club_window.py" line="4485"/>
        <location filename="../app/windows/club_window.py" line="4622"/>
        <location filename="../app/windows/club_window.py" line="4630"/>
        <source>Remove</source>
        <translation>移除</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1004"/>
        <location filename="../app/windows/club_window.py" line="4612"/>
        <source>Back</source>
        <translation>背面</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1135"/>
        <location filename="../app/windows/club_window.py" line="4594"/>
        <source>Settings</source>
        <translation>设置</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1152"/>
        <location filename="../app/windows/club_window.py" line="4598"/>
        <source>Name</source>
        <translation>名称</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1183"/>
        <location filename="../app/windows/club_window.py" line="1238"/>
        <location filename="../app/windows/club_window.py" line="4647"/>
        <location filename="../app/windows/club_window.py" line="4666"/>
        <source>Font Color:</source>
        <translation>字体颜色:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1184"/>
        <location filename="../app/windows/club_window.py" line="1239"/>
        <location filename="../app/windows/club_window.py" line="4648"/>
        <location filename="../app/windows/club_window.py" line="4667"/>
        <source>Font Family:</source>
        <translation>字体系列:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1185"/>
        <location filename="../app/windows/club_window.py" line="1240"/>
        <location filename="../app/windows/club_window.py" line="4649"/>
        <location filename="../app/windows/club_window.py" line="4668"/>
        <source>Font Size:</source>
        <translation>字体大小:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1186"/>
        <location filename="../app/windows/club_window.py" line="1241"/>
        <location filename="../app/windows/club_window.py" line="4650"/>
        <location filename="../app/windows/club_window.py" line="4669"/>
        <source>Vertical Offset:</source>
        <translation>垂直偏移:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1191"/>
        <location filename="../app/windows/club_window.py" line="1246"/>
        <location filename="../app/windows/club_window.py" line="4651"/>
        <location filename="../app/windows/club_window.py" line="4670"/>
        <source>Enable Outline:</source>
        <translation>启用轮廓:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1197"/>
        <location filename="../app/windows/club_window.py" line="1252"/>
        <location filename="../app/windows/club_window.py" line="4652"/>
        <location filename="../app/windows/club_window.py" line="4671"/>
        <source>Outline Color:</source>
        <translation>轮廓颜色:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1204"/>
        <location filename="../app/windows/club_window.py" line="1259"/>
        <location filename="../app/windows/club_window.py" line="4655"/>
        <location filename="../app/windows/club_window.py" line="4674"/>
        <source>Outline Thickness:</source>
        <translation>轮廓粗细:</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1207"/>
        <location filename="../app/windows/club_window.py" line="4602"/>
        <source>Number</source>
        <translation>号码</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2645"/>
        <source>No Image</source>
        <translation>无图片</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2675"/>
        <source>Select Kit Image ({side})</source>
        <translation>选择球衣图片 ({side})</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2689"/>
        <source>Error: File size exceeds {max_kb}KB.</source>
        <translation>错误：文件大小超过 {max_kb}KB。</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2699"/>
        <source>Error: Cannot read image file.</source>
        <translation>错误：无法读取图像文件。</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2705"/>
        <source>Error: Dimensions exceed {dim}x{dim}px.</source>
        <translation>错误：尺寸超过 {dim}x{dim}px。</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2719"/>
        <source>Error copying image!</source>
        <translation>复制图像时出错！</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2741"/>
        <source>Error removing image!</source>
        <translation>移除图像时出错！</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="485"/>
        <location filename="../app/windows/club_window.py" line="2075"/>
        <location filename="../app/windows/club_window.py" line="2552"/>
        <location filename="../app/windows/club_window.py" line="3488"/>
        <source>Remove Stadium Image</source>
        <translation>移除球场图片</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="948"/>
        <location filename="../app/windows/club_window.py" line="1085"/>
        <location filename="../app/windows/club_window.py" line="2546"/>
        <location filename="../app/windows/club_window.py" line="2550"/>
        <location filename="../app/windows/club_window.py" line="4618"/>
        <location filename="../app/windows/club_window.py" line="4626"/>
        <source>Upload</source>
        <translation>上传</translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="306"/>
        <source>Club Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1877"/>
        <location filename="../app/windows/club_window.py" line="3586"/>
        <location filename="../app/windows/club_window.py" line="3898"/>
        <source>Cannot Remove Staff Member</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="1880"/>
        <location filename="../app/windows/club_window.py" line="3589"/>
        <location filename="../app/windows/club_window.py" line="3901"/>
        <source>Cannot remove staff member &apos;{name}&apos; because they are assigned to the following team group(s): {groups}. Please remove the staff member from these groups first.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2010"/>
        <location filename="../app/windows/club_window.py" line="2014"/>
        <location filename="../app/windows/club_window.py" line="2068"/>
        <location filename="../app/windows/club_window.py" line="2072"/>
        <location filename="../app/windows/club_window.py" line="2560"/>
        <location filename="../app/windows/club_window.py" line="2567"/>
        <location filename="../app/windows/club_window.py" line="3423"/>
        <location filename="../app/windows/club_window.py" line="3427"/>
        <location filename="../app/windows/club_window.py" line="3481"/>
        <location filename="../app/windows/club_window.py" line="3485"/>
        <source>(PNG, &lt;500KB, &lt;300x300px)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2458"/>
        <source>General Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2472"/>
        <source>Stadium Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2518"/>
        <location filename="../app/windows/club_window.py" line="4156"/>
        <location filename="../app/windows/club_window.py" line="4337"/>
        <location filename="../app/windows/club_window.py" line="4349"/>
        <location filename="../app/windows/club_window.py" line="4431"/>
        <source>--- Select Country ---</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="2639"/>
        <location filename="../app/windows/club_window.py" line="2667"/>
        <source>(PNG, &lt;{max_kb}KB, &lt;{dim}x{dim}px)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="4582"/>
        <source>FOOT|DATA</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/club_window.py" line="4636"/>
        <location filename="../app/windows/club_window.py" line="4640"/>
        <source>(PNG, &lt;300KB, &lt;300x300px)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CoachingStaffListDialog</name>
    <message>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="28"/>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="36"/>
        <source>Coaching Staff List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="59"/>
        <source>Save List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="75"/>
        <source>Save Coaching Staff List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="78"/>
        <source>JSON Files (*.json);;All Files (*)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="89"/>
        <source>Success</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="90"/>
        <source>Coaching staff list saved successfully to {path}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="95"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../app/dialogs/coaching_staff_list_dialog.py" line="96"/>
        <source>Failed to save coaching staff list: {error}</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DateChangeConfirmationDialog</name>
    <message>
        <location filename="../app/pages/options_page.py" line="52"/>
        <source>Confirm Date Changes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="68"/>
        <source>Changing the season dates will affect other date ranges in the application. Please review the changes below and select how you want to proceed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="84"/>
        <source>Season Date Changes:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="94"/>
        <location filename="../app/pages/options_page.py" line="120"/>
        <source>Old range: {0} to {1}
New range: {2} to {3}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="110"/>
        <source>Macrocycle Date Changes:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="136"/>
        <source>Microcycle Date Changes:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="148"/>
        <source>{0}: {1} to {2} → {3} to {4}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="164"/>
        <source>Options</source>
        <translation type="unfinished">选项</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="167"/>
        <source>Adjust all dates to maintain relationships</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="171"/>
        <source>Automatically adjust all dependent dates to maintain their relative positions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="173"/>
        <source>Keep dates where possible</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="176"/>
        <source>Only adjust dates that would be invalid with the new season dates</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DeveloperSettingsWindow</name>
    <message>
        <location filename="../app/windows/developer_settings_window.py" line="45"/>
        <location filename="../app/windows/developer_settings_window.py" line="135"/>
        <source>Developer Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/developer_settings_window.py" line="52"/>
        <location filename="../app/windows/developer_settings_window.py" line="138"/>
        <source>Component Log Levels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/developer_settings_window.py" line="98"/>
        <location filename="../app/windows/developer_settings_window.py" line="141"/>
        <source>Close</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="../app/windows/developer_settings_window.py" line="136"/>
        <source>Developer settings will appear here.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MainWindow</name>
    <message>
        <location filename="../app/windows/main_window.py" line="515"/>
        <source>Are you sure you want to exit FootData?</source>
        <translation>Are you sure you want to exit FootData?</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="514"/>
        <source>Exit Application</source>
        <translation>Exit Application</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="520"/>
        <source>No</source>
        <translation>No</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="578"/>
        <source>Configure football rules and nationality zones</source>
        <translation>Configure football rules and nationality zones</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="405"/>
        <source>Footer Information Placeholder</source>
        <translation>Footer Information Placeholder</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="403"/>
        <source>FootData</source>
        <translation>FootData</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="570"/>
        <source>View/Edit Club Information</source>
        <translation>View/Edit Club Information</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="519"/>
        <source>Yes</source>
        <translation>Yes</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="298"/>
        <source>Roster</source>
        <translation>Roster</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="574"/>
        <source>Open the Player Roster</source>
        <translation>Open the Player Roster</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="404"/>
        <source>FOOT|DATA - Header Placeholder</source>
        <translation>FOOT|DATA - Header Placeholder</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="566"/>
        <source>Configure application settings</source>
        <translation>Configure application settings</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="420"/>
        <source>Main Content Area - Pages will load here.</source>
        <translation>Main Content Area - Pages will load here.</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="584"/>
        <source>Exit the application</source>
        <translation>Exit the application</translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="565"/>
        <location filename="../app/windows/main_window.py" line="660"/>
        <source>&amp;Settings...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="569"/>
        <location filename="../app/windows/main_window.py" line="654"/>
        <source>&amp;Club Info...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="573"/>
        <location filename="../app/windows/main_window.py" line="657"/>
        <source>&amp;Roster...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="577"/>
        <location filename="../app/windows/main_window.py" line="663"/>
        <source>&amp;Options...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="582"/>
        <location filename="../app/windows/main_window.py" line="666"/>
        <source>E&amp;xit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="587"/>
        <location filename="../app/windows/main_window.py" line="669"/>
        <source>&amp;About</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="588"/>
        <source>Show the application&apos;s About box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="591"/>
        <location filename="../app/windows/main_window.py" line="672"/>
        <source>About &amp;Qt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="592"/>
        <source>Show the Qt library&apos;s About box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="595"/>
        <location filename="../app/windows/main_window.py" line="675"/>
        <source>&amp;Developer Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="597"/>
        <source>Open Developer Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="604"/>
        <location filename="../app/windows/main_window.py" line="640"/>
        <source>&amp;File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="614"/>
        <location filename="../app/windows/main_window.py" line="645"/>
        <source>&amp;Help</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/main_window.py" line="619"/>
        <location filename="../app/windows/main_window.py" line="650"/>
        <source>&amp;Developer</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>OptionsPage</name>
    <message>
        <location filename="../app/pages/options_page.py" line="841"/>
        <location filename="../app/pages/options_page.py" line="2268"/>
        <source>Season</source>
        <translation>赛季</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="405"/>
        <location filename="../app/pages/options_page.py" line="842"/>
        <location filename="../app/pages/options_page.py" line="2270"/>
        <location filename="../app/pages/options_page.py" line="2305"/>
        <source>Periodization</source>
        <translation>周期化</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="425"/>
        <location filename="../app/pages/options_page.py" line="2311"/>
        <source>Start Macrocycle:</source>
        <translation>开始宏周期:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="426"/>
        <location filename="../app/pages/options_page.py" line="2313"/>
        <source>End Macrocycle:</source>
        <translation>结束宏周期:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3559"/>
        <source>No Data</source>
        <translation>无数据</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3561"/>
        <source>No periodization data available. Please add microcycles to at least one mesocycle.</source>
        <translation>没有可用的周期化数据。请至少向一个中周期添加小周期。</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="444"/>
        <location filename="../app/pages/options_page.py" line="2319"/>
        <source>Periodization Chart</source>
        <translation>周期化图表</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="415"/>
        <location filename="../app/pages/options_page.py" line="2307"/>
        <source>Set the start date of the Macrocycle</source>
        <translation>设置宏周期的开始日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="422"/>
        <location filename="../app/pages/options_page.py" line="2309"/>
        <source>Set the end date of the Macrocycle</source>
        <translation>设置宏周期的结束日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="441"/>
        <location filename="../app/pages/options_page.py" line="2317"/>
        <source>Validate the macrocycle dates and save if valid</source>
        <translation>验证宏周期日期并在有效时保存</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="446"/>
        <location filename="../app/pages/options_page.py" line="2320"/>
        <source>Show a chart of intensity values across all periodization cycles</source>
        <translation>显示所有周期化周期的强度值图表</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="299"/>
        <location filename="../app/pages/options_page.py" line="2276"/>
        <source>Season Dates</source>
        <translation>赛季日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="316"/>
        <location filename="../app/pages/options_page.py" line="2282"/>
        <location filename="../app/pages/options_page.py" line="3233"/>
        <location filename="../app/pages/options_page.py" line="3420"/>
        <source>Start Date:</source>
        <translation>开始日期:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="317"/>
        <location filename="../app/pages/options_page.py" line="2284"/>
        <location filename="../app/pages/options_page.py" line="3234"/>
        <location filename="../app/pages/options_page.py" line="3421"/>
        <source>End Date:</source>
        <translation>结束日期:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="459"/>
        <location filename="../app/pages/options_page.py" line="2323"/>
        <source>Preparation Mesocycle</source>
        <translation>准备中周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="532"/>
        <location filename="../app/pages/options_page.py" line="2338"/>
        <source>Basic Mesocycle</source>
        <translation>基础中周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="599"/>
        <location filename="../app/pages/options_page.py" line="2353"/>
        <source>Competition Mesocycle</source>
        <translation>比赛中周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="666"/>
        <location filename="../app/pages/options_page.py" line="2368"/>
        <source>Transition Mesocycle</source>
        <translation>过渡中周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="340"/>
        <location filename="../app/pages/options_page.py" line="466"/>
        <location filename="../app/pages/options_page.py" line="539"/>
        <location filename="../app/pages/options_page.py" line="606"/>
        <location filename="../app/pages/options_page.py" line="673"/>
        <location filename="../app/pages/options_page.py" line="2290"/>
        <location filename="../app/pages/options_page.py" line="2326"/>
        <location filename="../app/pages/options_page.py" line="2341"/>
        <location filename="../app/pages/options_page.py" line="2356"/>
        <location filename="../app/pages/options_page.py" line="2371"/>
        <location filename="../app/pages/options_page.py" line="2466"/>
        <location filename="../app/pages/options_page.py" line="2585"/>
        <source>ID</source>
        <translation>ID</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="467"/>
        <location filename="../app/pages/options_page.py" line="540"/>
        <location filename="../app/pages/options_page.py" line="607"/>
        <location filename="../app/pages/options_page.py" line="674"/>
        <location filename="../app/pages/options_page.py" line="2326"/>
        <location filename="../app/pages/options_page.py" line="2341"/>
        <location filename="../app/pages/options_page.py" line="2356"/>
        <location filename="../app/pages/options_page.py" line="2371"/>
        <source>Start Microcycle</source>
        <translation>开始小周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="468"/>
        <location filename="../app/pages/options_page.py" line="541"/>
        <location filename="../app/pages/options_page.py" line="608"/>
        <location filename="../app/pages/options_page.py" line="675"/>
        <location filename="../app/pages/options_page.py" line="2326"/>
        <location filename="../app/pages/options_page.py" line="2341"/>
        <location filename="../app/pages/options_page.py" line="2356"/>
        <location filename="../app/pages/options_page.py" line="2371"/>
        <source>End Microcycle</source>
        <translation>结束小周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="469"/>
        <location filename="../app/pages/options_page.py" line="542"/>
        <location filename="../app/pages/options_page.py" line="609"/>
        <location filename="../app/pages/options_page.py" line="676"/>
        <location filename="../app/pages/options_page.py" line="2326"/>
        <location filename="../app/pages/options_page.py" line="2341"/>
        <location filename="../app/pages/options_page.py" line="2356"/>
        <location filename="../app/pages/options_page.py" line="2371"/>
        <location filename="../app/pages/options_page.py" line="2466"/>
        <location filename="../app/pages/options_page.py" line="2587"/>
        <source>Name</source>
        <translation>名称</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="470"/>
        <location filename="../app/pages/options_page.py" line="543"/>
        <location filename="../app/pages/options_page.py" line="610"/>
        <location filename="../app/pages/options_page.py" line="677"/>
        <location filename="../app/pages/options_page.py" line="2327"/>
        <location filename="../app/pages/options_page.py" line="2342"/>
        <location filename="../app/pages/options_page.py" line="2357"/>
        <location filename="../app/pages/options_page.py" line="2372"/>
        <source>Target 1</source>
        <translation>目标 1</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="471"/>
        <location filename="../app/pages/options_page.py" line="544"/>
        <location filename="../app/pages/options_page.py" line="611"/>
        <location filename="../app/pages/options_page.py" line="678"/>
        <location filename="../app/pages/options_page.py" line="2327"/>
        <location filename="../app/pages/options_page.py" line="2342"/>
        <location filename="../app/pages/options_page.py" line="2357"/>
        <location filename="../app/pages/options_page.py" line="2372"/>
        <source>Target 2</source>
        <translation>目标 2</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="472"/>
        <location filename="../app/pages/options_page.py" line="545"/>
        <location filename="../app/pages/options_page.py" line="612"/>
        <location filename="../app/pages/options_page.py" line="679"/>
        <location filename="../app/pages/options_page.py" line="2327"/>
        <location filename="../app/pages/options_page.py" line="2342"/>
        <location filename="../app/pages/options_page.py" line="2357"/>
        <location filename="../app/pages/options_page.py" line="2372"/>
        <source>Target 3</source>
        <translation>目标 3</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="473"/>
        <location filename="../app/pages/options_page.py" line="546"/>
        <location filename="../app/pages/options_page.py" line="613"/>
        <location filename="../app/pages/options_page.py" line="680"/>
        <location filename="../app/pages/options_page.py" line="2328"/>
        <location filename="../app/pages/options_page.py" line="2343"/>
        <location filename="../app/pages/options_page.py" line="2358"/>
        <location filename="../app/pages/options_page.py" line="2373"/>
        <source>Intensity</source>
        <translation>强度</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="342"/>
        <location filename="../app/pages/options_page.py" line="475"/>
        <location filename="../app/pages/options_page.py" line="548"/>
        <location filename="../app/pages/options_page.py" line="615"/>
        <location filename="../app/pages/options_page.py" line="682"/>
        <location filename="../app/pages/options_page.py" line="2292"/>
        <location filename="../app/pages/options_page.py" line="2329"/>
        <location filename="../app/pages/options_page.py" line="2344"/>
        <location filename="../app/pages/options_page.py" line="2359"/>
        <location filename="../app/pages/options_page.py" line="2374"/>
        <location filename="../app/pages/options_page.py" line="2468"/>
        <location filename="../app/pages/options_page.py" line="2592"/>
        <source>Notes</source>
        <translation>备注</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="506"/>
        <location filename="../app/pages/options_page.py" line="583"/>
        <location filename="../app/pages/options_page.py" line="650"/>
        <location filename="../app/pages/options_page.py" line="717"/>
        <location filename="../app/pages/options_page.py" line="2331"/>
        <location filename="../app/pages/options_page.py" line="2346"/>
        <location filename="../app/pages/options_page.py" line="2361"/>
        <location filename="../app/pages/options_page.py" line="2376"/>
        <location filename="../app/pages/options_page.py" line="3176"/>
        <source>Add Microcycle</source>
        <translation>添加小周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="507"/>
        <location filename="../app/pages/options_page.py" line="584"/>
        <location filename="../app/pages/options_page.py" line="651"/>
        <location filename="../app/pages/options_page.py" line="718"/>
        <location filename="../app/pages/options_page.py" line="2333"/>
        <location filename="../app/pages/options_page.py" line="2348"/>
        <location filename="../app/pages/options_page.py" line="2363"/>
        <location filename="../app/pages/options_page.py" line="2378"/>
        <location filename="../app/pages/options_page.py" line="3366"/>
        <source>Edit Microcycle</source>
        <translation>编辑小周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="508"/>
        <location filename="../app/pages/options_page.py" line="585"/>
        <location filename="../app/pages/options_page.py" line="652"/>
        <location filename="../app/pages/options_page.py" line="719"/>
        <location filename="../app/pages/options_page.py" line="2335"/>
        <location filename="../app/pages/options_page.py" line="2350"/>
        <location filename="../app/pages/options_page.py" line="2365"/>
        <location filename="../app/pages/options_page.py" line="2380"/>
        <source>Remove Microcycle</source>
        <translation>删除小周期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1040"/>
        <location filename="../app/pages/options_page.py" line="2260"/>
        <source>Dates</source>
        <translation>日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1038"/>
        <location filename="../app/pages/options_page.py" line="2256"/>
        <source>Football Rules</source>
        <translation>足球规则</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1039"/>
        <location filename="../app/pages/options_page.py" line="2258"/>
        <source>Nationality Zones</source>
        <translation>国籍区域</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="241"/>
        <location filename="../app/pages/options_page.py" line="2252"/>
        <source>Options</source>
        <translation>选项</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1337"/>
        <location filename="../app/pages/options_page.py" line="1649"/>
        <source>Invalid Date Range</source>
        <translation>无效的日期范围</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1339"/>
        <source>The selected date range is invalid. Reverting to the last valid dates.</source>
        <translation>所选日期范围无效。恢复到最后有效的日期。</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1273"/>
        <location filename="../app/pages/options_page.py" line="1680"/>
        <location filename="../app/pages/options_page.py" line="1696"/>
        <location filename="../app/pages/options_page.py" line="1718"/>
        <location filename="../app/pages/options_page.py" line="3268"/>
        <location filename="../app/pages/options_page.py" line="3455"/>
        <source>End date must be after start date</source>
        <translation>结束日期必须在开始日期之后</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1287"/>
        <source>Season cannot exceed {max_days} days</source>
        <translation>赛季不能超过 {max_days} 天</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3232"/>
        <location filename="../app/pages/options_page.py" line="3419"/>
        <source>Name:</source>
        <translation>名称:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3235"/>
        <location filename="../app/pages/options_page.py" line="3422"/>
        <source>Target 1:</source>
        <translation>目标 1:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3236"/>
        <location filename="../app/pages/options_page.py" line="3423"/>
        <source>Target 2:</source>
        <translation>目标 2:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3237"/>
        <location filename="../app/pages/options_page.py" line="3424"/>
        <source>Target 3:</source>
        <translation>目标 3:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3238"/>
        <location filename="../app/pages/options_page.py" line="3425"/>
        <source>Intensity:</source>
        <translation>强度:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3239"/>
        <location filename="../app/pages/options_page.py" line="3426"/>
        <source>Notes:</source>
        <translation>备注:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="439"/>
        <location filename="../app/pages/options_page.py" line="2316"/>
        <source>Validate &amp;&amp; Save Dates</source>
        <translation>验证 &amp;&amp; 保存日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3357"/>
        <location filename="../app/pages/options_page.py" line="3573"/>
        <source>No Selection</source>
        <translation>未选择</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3358"/>
        <source>Please select a microcycle to edit.</source>
        <translation>请选择要编辑的小周期。</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3584"/>
        <source>Are you sure you want to remove this microcycle?</source>
        <translation>您确定要删除此小周期吗？</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3832"/>
        <location filename="../app/pages/options_page.py" line="3847"/>
        <location filename="../app/pages/options_page.py" line="3895"/>
        <location filename="../app/pages/options_page.py" line="3915"/>
        <source>Zone name cannot be empty.</source>
        <translation>Zone name cannot be empty.</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3828"/>
        <source>Add Zone</source>
        <translation>Add Zone</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="889"/>
        <location filename="../app/pages/options_page.py" line="2428"/>
        <source>Select a zone to view/edit assigned nationalities.</source>
        <translation>Select a zone to view/edit assigned nationalities.</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="921"/>
        <location filename="../app/pages/options_page.py" line="2441"/>
        <source>Add</source>
        <translation>Add</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="923"/>
        <location filename="../app/pages/options_page.py" line="2442"/>
        <source>Assign selected available nationality to the current zone</source>
        <translation>Assign selected available nationality to the current zone</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="917"/>
        <location filename="../app/pages/options_page.py" line="2439"/>
        <source>Nationalities currently assigned to the selected zone.</source>
        <translation>Nationalities currently assigned to the selected zone.</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="893"/>
        <location filename="../app/pages/options_page.py" line="922"/>
        <location filename="../app/pages/options_page.py" line="2432"/>
        <location filename="../app/pages/options_page.py" line="2444"/>
        <source>Remove</source>
        <translation>Remove</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3832"/>
        <location filename="../app/pages/options_page.py" line="3847"/>
        <location filename="../app/pages/options_page.py" line="3895"/>
        <location filename="../app/pages/options_page.py" line="3915"/>
        <source>Invalid Name</source>
        <translation>Invalid Name</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="909"/>
        <location filename="../app/pages/options_page.py" line="2425"/>
        <source>Assigned Nationalities:</source>
        <translation>Assigned Nationalities:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="887"/>
        <location filename="../app/pages/options_page.py" line="2421"/>
        <source>Defined Zones:</source>
        <translation>Defined Zones:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="894"/>
        <location filename="../app/pages/options_page.py" line="2434"/>
        <source>Rename...</source>
        <translation>Rename...</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3828"/>
        <source>Enter name for the new zone:</source>
        <translation>Enter name for the new zone:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="908"/>
        <location filename="../app/pages/options_page.py" line="2423"/>
        <source>Available Nationalities:</source>
        <translation>Available Nationalities:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3835"/>
        <location filename="../app/pages/options_page.py" line="3900"/>
        <source>Duplicate Name</source>
        <translation>Duplicate Name</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="924"/>
        <location filename="../app/pages/options_page.py" line="2445"/>
        <source>Remove selected assigned nationality from the current zone</source>
        <translation>Remove selected assigned nationality from the current zone</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="892"/>
        <location filename="../app/pages/options_page.py" line="2430"/>
        <source>Add...</source>
        <translation>Add...</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3835"/>
        <location filename="../app/pages/options_page.py" line="3900"/>
        <source>A zone with this name already exists.</source>
        <translation>A zone with this name already exists.</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="913"/>
        <location filename="../app/pages/options_page.py" line="2437"/>
        <source>Nationalities not assigned to the selected zone.</source>
        <translation>Nationalities not assigned to the selected zone.</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="306"/>
        <location filename="../app/pages/options_page.py" line="2278"/>
        <source>Set the start date of the season</source>
        <translation>设置赛季开始日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="313"/>
        <location filename="../app/pages/options_page.py" line="2280"/>
        <source>Set the end date of the season</source>
        <translation>设置赛季结束日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="333"/>
        <location filename="../app/pages/options_page.py" line="2287"/>
        <source>Competitions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="340"/>
        <location filename="../app/pages/options_page.py" line="1837"/>
        <location filename="../app/pages/options_page.py" line="2290"/>
        <location filename="../app/pages/options_page.py" line="3616"/>
        <location filename="../app/pages/options_page.py" line="3675"/>
        <source>Competition</source>
        <translation>比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="340"/>
        <location filename="../app/pages/options_page.py" line="2290"/>
        <source>Start Date</source>
        <translation>开始日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="340"/>
        <location filename="../app/pages/options_page.py" line="2290"/>
        <source>End Date</source>
        <translation>结束日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="341"/>
        <location filename="../app/pages/options_page.py" line="2291"/>
        <location filename="../app/pages/options_page.py" line="2467"/>
        <location filename="../app/pages/options_page.py" line="2589"/>
        <source>Type</source>
        <translation>类型</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="341"/>
        <location filename="../app/pages/options_page.py" line="2291"/>
        <location filename="../app/pages/options_page.py" line="2467"/>
        <location filename="../app/pages/options_page.py" line="2590"/>
        <source>Structure</source>
        <translation>结构</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="341"/>
        <location filename="../app/pages/options_page.py" line="2291"/>
        <source>Priority</source>
        <translation>优先级</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="364"/>
        <location filename="../app/pages/options_page.py" line="2294"/>
        <location filename="../app/pages/options_page.py" line="2470"/>
        <location filename="../app/pages/options_page.py" line="2620"/>
        <source>Add Competition</source>
        <translation>添加比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="365"/>
        <location filename="../app/pages/options_page.py" line="2296"/>
        <location filename="../app/pages/options_page.py" line="2472"/>
        <location filename="../app/pages/options_page.py" line="2621"/>
        <source>Edit Competition</source>
        <translation>编辑比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="366"/>
        <location filename="../app/pages/options_page.py" line="2298"/>
        <location filename="../app/pages/options_page.py" line="2474"/>
        <location filename="../app/pages/options_page.py" line="2622"/>
        <source>Remove Competition</source>
        <translation>删除比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="387"/>
        <location filename="../app/pages/options_page.py" line="2301"/>
        <source>Season Timeline</source>
        <translation>赛季时间线</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="741"/>
        <location filename="../app/pages/options_page.py" line="2384"/>
        <source>Evaluation Dates</source>
        <translation>评估日期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="745"/>
        <location filename="../app/pages/options_page.py" line="1796"/>
        <location filename="../app/pages/options_page.py" line="2386"/>
        <source>1st Evaluation</source>
        <translation>第一次评估</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="752"/>
        <location filename="../app/pages/options_page.py" line="2388"/>
        <source>Set the start date of the 1st evaluation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="757"/>
        <location filename="../app/pages/options_page.py" line="2390"/>
        <source>Set the end date of the 1st evaluation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="760"/>
        <location filename="../app/pages/options_page.py" line="2392"/>
        <source>Start 1st Evaluation:</source>
        <translation>开始第一次评估:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="761"/>
        <location filename="../app/pages/options_page.py" line="2394"/>
        <source>End 1st Evaluation:</source>
        <translation>结束第一次评估:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="774"/>
        <location filename="../app/pages/options_page.py" line="1797"/>
        <location filename="../app/pages/options_page.py" line="2397"/>
        <source>2nd Evaluation</source>
        <translation>第二次评估</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="781"/>
        <location filename="../app/pages/options_page.py" line="2399"/>
        <source>Set the start date of the 2nd evaluation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="786"/>
        <location filename="../app/pages/options_page.py" line="2401"/>
        <source>Set the end date of the 2nd evaluation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="789"/>
        <location filename="../app/pages/options_page.py" line="2403"/>
        <source>Start 2nd Evaluation:</source>
        <translation>开始第二次评估:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="790"/>
        <location filename="../app/pages/options_page.py" line="2405"/>
        <source>End 2nd Evaluation:</source>
        <translation>结束第二次评估:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="803"/>
        <location filename="../app/pages/options_page.py" line="1798"/>
        <location filename="../app/pages/options_page.py" line="2408"/>
        <source>3rd Evaluation</source>
        <translation>第三次评估</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="810"/>
        <location filename="../app/pages/options_page.py" line="2410"/>
        <source>Set the start date of the 3rd evaluation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="815"/>
        <location filename="../app/pages/options_page.py" line="2412"/>
        <source>Set the end date of the 3rd evaluation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="818"/>
        <location filename="../app/pages/options_page.py" line="2414"/>
        <source>Start 3rd Evaluation:</source>
        <translation>开始第三次评估:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="819"/>
        <location filename="../app/pages/options_page.py" line="2416"/>
        <source>End 3rd Evaluation:</source>
        <translation>结束第三次评估:</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="843"/>
        <location filename="../app/pages/options_page.py" line="2272"/>
        <source>Evaluation</source>
        <translation>评估</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="953"/>
        <location filename="../app/pages/options_page.py" line="2449"/>
        <source>Football Pitch</source>
        <translation>足球场</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="957"/>
        <location filename="../app/pages/options_page.py" line="2451"/>
        <source>Positions Pitch</source>
        <translation>位置场</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="972"/>
        <location filename="../app/pages/options_page.py" line="982"/>
        <location filename="../app/pages/options_page.py" line="992"/>
        <location filename="../app/pages/options_page.py" line="2454"/>
        <location filename="../app/pages/options_page.py" line="2456"/>
        <location filename="../app/pages/options_page.py" line="2458"/>
        <location filename="../app/pages/options_page.py" line="2752"/>
        <location filename="../app/pages/options_page.py" line="2876"/>
        <location filename="../app/pages/options_page.py" line="3000"/>
        <source>Click to upload image</source>
        <translation>点击上传图片</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1009"/>
        <location filename="../app/pages/options_page.py" line="2461"/>
        <source>Requirements: PNG format, &lt; 200KB, max 400x600 pixels</source>
        <translation>要求：PNG格式，&lt; 200KB，最大400x600像素</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1041"/>
        <location filename="../app/pages/options_page.py" line="2262"/>
        <source>App Media</source>
        <translation>应用媒体</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1042"/>
        <location filename="../app/pages/options_page.py" line="2264"/>
        <source>Football Competitions</source>
        <translation>足球比赛</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1621"/>
        <source>Macrocycle end date must be after start date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1626"/>
        <source>Macrocycle start date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1630"/>
        <source>Macrocycle end date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1651"/>
        <source>The selected macrocycle date range is invalid. Reverting to the last valid dates.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1685"/>
        <location filename="../app/pages/options_page.py" line="1701"/>
        <location filename="../app/pages/options_page.py" line="1723"/>
        <source>Start date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1690"/>
        <location filename="../app/pages/options_page.py" line="1706"/>
        <location filename="../app/pages/options_page.py" line="1728"/>
        <source>End date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1712"/>
        <source>2nd evaluation must start after 1st evaluation ends</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1734"/>
        <source>3rd evaluation must start after 2nd evaluation ends</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1829"/>
        <location filename="../app/pages/options_page.py" line="3614"/>
        <location filename="../app/pages/options_page.py" line="3673"/>
        <source>Preparation</source>
        <translation>准备期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1833"/>
        <location filename="../app/pages/options_page.py" line="3615"/>
        <location filename="../app/pages/options_page.py" line="3674"/>
        <source>Basic</source>
        <translation>基础期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1841"/>
        <location filename="../app/pages/options_page.py" line="3617"/>
        <location filename="../app/pages/options_page.py" line="3676"/>
        <source>Transition</source>
        <translation>过渡期</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2074"/>
        <source>• Macrocycle end date must be after start date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2078"/>
        <source>• Macrocycle start date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2081"/>
        <source>• Macrocycle end date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2086"/>
        <source>• {}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2102"/>
        <source>The following issues were found with the macrocycle dates:

</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2104"/>
        <source>Date Range Issues:
</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2109"/>
        <source>Microcycles Outside Macrocycle Range:
</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2115"/>
        <source>Would you like to fix these issues automatically?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2117"/>
        <source>Validation Issues</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2131"/>
        <source>Issues Fixed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2133"/>
        <source>The issues have been fixed and dates have been saved.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2157"/>
        <source>Validation Successful</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2159"/>
        <source>All dates are valid and have been saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2466"/>
        <location filename="../app/pages/options_page.py" line="2586"/>
        <source>Logo</source>
        <translation>标志</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2466"/>
        <location filename="../app/pages/options_page.py" line="2588"/>
        <source>Organization</source>
        <translation>组织</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2514"/>
        <location filename="../app/pages/options_page.py" line="2536"/>
        <location filename="../app/pages/options_page.py" line="2558"/>
        <source>Upload Image</source>
        <translation type="unfinished">Upload Image</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2515"/>
        <location filename="../app/pages/options_page.py" line="2537"/>
        <location filename="../app/pages/options_page.py" line="2559"/>
        <location filename="../app/pages/options_page.py" line="2733"/>
        <location filename="../app/pages/options_page.py" line="2857"/>
        <location filename="../app/pages/options_page.py" line="2981"/>
        <source>Remove Image</source>
        <translation type="unfinished">Remove Image</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2516"/>
        <location filename="../app/pages/options_page.py" line="2538"/>
        <location filename="../app/pages/options_page.py" line="2560"/>
        <source>Add to Position</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2651"/>
        <location filename="../app/pages/options_page.py" line="2775"/>
        <location filename="../app/pages/options_page.py" line="2899"/>
        <source>Select Positions Pitch Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2654"/>
        <location filename="../app/pages/options_page.py" line="2778"/>
        <location filename="../app/pages/options_page.py" line="2902"/>
        <source>PNG Images (*.png)</source>
        <translation type="unfinished">PNG 图片 (*.png)</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2663"/>
        <location filename="../app/pages/options_page.py" line="2787"/>
        <location filename="../app/pages/options_page.py" line="2911"/>
        <source>Invalid File Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2665"/>
        <location filename="../app/pages/options_page.py" line="2789"/>
        <location filename="../app/pages/options_page.py" line="2913"/>
        <source>Only PNG files are allowed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2674"/>
        <location filename="../app/pages/options_page.py" line="2798"/>
        <location filename="../app/pages/options_page.py" line="2922"/>
        <source>File Too Large</source>
        <translation>文件太大</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2675"/>
        <location filename="../app/pages/options_page.py" line="2799"/>
        <location filename="../app/pages/options_page.py" line="2923"/>
        <source>The image file must be less than 200KB. Current size: {:.1f}KB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2684"/>
        <location filename="../app/pages/options_page.py" line="2808"/>
        <location filename="../app/pages/options_page.py" line="2932"/>
        <source>Image Too Large</source>
        <translation>图像太大</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2685"/>
        <location filename="../app/pages/options_page.py" line="2809"/>
        <location filename="../app/pages/options_page.py" line="2933"/>
        <source>The image dimensions must be at most 400x600 pixels. Current size: {}x{}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2714"/>
        <location filename="../app/pages/options_page.py" line="2838"/>
        <location filename="../app/pages/options_page.py" line="2962"/>
        <source>Image Uploaded</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2716"/>
        <location filename="../app/pages/options_page.py" line="2840"/>
        <location filename="../app/pages/options_page.py" line="2964"/>
        <source>The positions pitch image has been uploaded successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2720"/>
        <location filename="../app/pages/options_page.py" line="2844"/>
        <location filename="../app/pages/options_page.py" line="2968"/>
        <source>Upload Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2721"/>
        <location filename="../app/pages/options_page.py" line="2845"/>
        <location filename="../app/pages/options_page.py" line="2969"/>
        <source>An error occurred while uploading the image: {}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2734"/>
        <location filename="../app/pages/options_page.py" line="2858"/>
        <location filename="../app/pages/options_page.py" line="2982"/>
        <source>Are you sure you want to remove the positions pitch image?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2756"/>
        <location filename="../app/pages/options_page.py" line="2880"/>
        <location filename="../app/pages/options_page.py" line="3004"/>
        <source>Image Removed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2758"/>
        <location filename="../app/pages/options_page.py" line="2882"/>
        <location filename="../app/pages/options_page.py" line="3006"/>
        <source>The positions pitch image has been removed successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2762"/>
        <location filename="../app/pages/options_page.py" line="2886"/>
        <location filename="../app/pages/options_page.py" line="3010"/>
        <source>Remove Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2763"/>
        <location filename="../app/pages/options_page.py" line="2887"/>
        <location filename="../app/pages/options_page.py" line="3011"/>
        <source>An error occurred while removing the image: {}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3102"/>
        <source>Image Not Found</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3104"/>
        <source>The selected image could not be found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3119"/>
        <source>Image Added</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3121"/>
        <source>The image has been added to the Position tab in the Roster page.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3131"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3132"/>
        <source>An error occurred while adding the image to the Position tab: {}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3148"/>
        <location filename="../app/pages/options_page.py" line="3977"/>
        <source>Microcycle Overlap Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3149"/>
        <location filename="../app/pages/options_page.py" line="3978"/>
        <source>There are overlapping microcycles in your schedule:

{}

Do you want to close without saving the microcycle data?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3274"/>
        <location filename="../app/pages/options_page.py" line="3461"/>
        <source>Start date must be within macrocycle dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3279"/>
        <location filename="../app/pages/options_page.py" line="3466"/>
        <source>End date must be within macrocycle dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3292"/>
        <location filename="../app/pages/options_page.py" line="3480"/>
        <source>Microcycle dates cannot overlap with existing microcycles in this mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3299"/>
        <location filename="../app/pages/options_page.py" line="3487"/>
        <source>Microcycle dates cannot overlap with existing microcycles: {}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3574"/>
        <source>Please select a microcycle to remove.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3583"/>
        <location filename="../app/pages/options_page.py" line="3858"/>
        <source>Confirm Removal</source>
        <translation type="unfinished">Confirm Removal</translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3643"/>
        <source>Overlap detected between {} Mesocycle microcycle &apos;{}&apos; ({} to {}) and {} Mesocycle microcycle &apos;{}&apos; ({} to {})</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3696"/>
        <source>Dates overlap with {} Mesocycle microcycle &apos;{}&apos; ({} to {})</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3715"/>
        <source>Microcycle Overlap Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3716"/>
        <source>Error: Cannot save because there are overlapping microcycles in your schedule:

{}

Please fix the overlaps before saving.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3859"/>
        <source>Are you sure you want to remove the zone &apos;{}&apos;?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3887"/>
        <source>Rename Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="3888"/>
        <source>Enter new name for zone &apos;{}&apos;:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PeriodizationChartDialog</name>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="25"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="20"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="308"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="25"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="25"/>
        <source>Periodization Chart</source>
        <translation>周期化图表</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="74"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="66"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="66"/>
        <source>Periodization Intensity Chart</source>
        <translation type="unfinished">周期化强度图表</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="91"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="68"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="83"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="83"/>
        <source>Chart Type</source>
        <translation type="unfinished">图表类型</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="94"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="86"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="86"/>
        <source>Line Chart</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="95"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="87"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="87"/>
        <source>Area Chart</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="96"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="88"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="88"/>
        <source>Step Chart</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="97"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="89"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="89"/>
        <source>Bar Chart</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="115"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="82"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="107"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="107"/>
        <source>Color Scheme</source>
        <translation type="unfinished">配色方案</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="118"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="110"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="110"/>
        <source>Color by Mesocycle Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="119"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="111"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="111"/>
        <source>Single Color Gradient</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="120"/>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="136"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="96"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="112"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="112"/>
        <source>Custom Colors</source>
        <translation type="unfinished">自定义颜色</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="171"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="145"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="145"/>
        <source>Additional Display Options</source>
        <translation type="unfinished">附加显示选项</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="174"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="163"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="148"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="148"/>
        <source>Show Mesocycle Boundaries</source>
        <translation type="unfinished">显示中周期边界</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="175"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="149"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="149"/>
        <source>Show Microcycle Names</source>
        <translation type="unfinished">显示小周期名称</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="176"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="150"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="150"/>
        <source>Show Date Ranges</source>
        <translation type="unfinished">显示日期范围</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="177"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="151"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="151"/>
        <source>Show Grid Lines</source>
        <translation type="unfinished">显示网格线</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="191"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="126"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="165"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="165"/>
        <source>Line Width</source>
        <translation type="unfinished">线宽</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="220"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="189"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="194"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="194"/>
        <source>Updates</source>
        <translation type="unfinished">更新</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="223"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="197"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="197"/>
        <source>Enable Real-time Updates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="230"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="204"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="204"/>
        <source>Update Chart</source>
        <translation type="unfinished">更新图表</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="314"/>
        <source>Select Color for {mesocycle_type}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="375"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="313"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="335"/>
        <source>Date</source>
        <translation type="unfinished">日期</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="380"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="318"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="340"/>
        <source>Intensity (%)</source>
        <translation type="unfinished">强度 (%)</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="467"/>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="578"/>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="647"/>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="897"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="382"/>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="474"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="412"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="520"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="586"/>
        <source>Intensity</source>
        <translation type="unfinished">强度</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog.py" line="923"/>
        <source>Microcycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="143"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="128"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="128"/>
        <source>Data Display Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="146"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="131"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="131"/>
        <source>Show Intensity Values</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="149"/>
        <source>Show intensity percentage values on chart points</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="151"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="132"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="132"/>
        <source>Show Target 1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="155"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="133"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="133"/>
        <source>Show Target 2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="159"/>
        <location filename="../app/dialogs/periodization_chart_dialog_complete.py" line="134"/>
        <location filename="../app/dialogs/periodization_chart_dialog_updated.py" line="134"/>
        <source>Show Target 3</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="167"/>
        <source>Show Mesocycle Names</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="171"/>
        <source>Show Dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="175"/>
        <source>Show Grid</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="192"/>
        <source>Real-time Updates</source>
        <translation type="unfinished">实时更新</translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="194"/>
        <source>Update chart automatically when settings change</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="198"/>
        <source>Refresh Chart</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="199"/>
        <source>Click to update the chart with current settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/periodization_chart_dialog_fixed.py" line="259"/>
        <source>Select Color</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PeriodizationTimelineWidget</name>
    <message>
        <location filename="../app/widgets/periodization_timeline_widget.py" line="44"/>
        <source>Macrocycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/periodization_timeline_widget.py" line="48"/>
        <source>Mesocycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/periodization_timeline_widget.py" line="52"/>
        <source>Microcycles</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RemoveFontDialog</name>
    <message>
        <location filename="../app/windows/settings_window.py" line="42"/>
        <source>Remove Custom Fonts</source>
        <translation>Remove Custom Fonts</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="141"/>
        <source>Are you sure you want to remove the font family &apos;{}&apos;?
This will delete the following file(s):
 - {}</source>
        <translation>Are you sure you want to remove the font family &apos;{}&apos;?
This will delete the following file(s):
 - {}</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="186"/>
        <source>Could not delete file {}: {}</source>
        <translation>Could not delete file {}: {}</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="188"/>
        <source>Unexpected error removing {}: {}</source>
        <translation>Unexpected error removing {}: {}</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="211"/>
        <source>Successfully removed font family &apos;{}&apos; and deleted {} associated file(s).</source>
        <translation>Successfully removed font family &apos;{}&apos; and deleted {} associated file(s).</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="125"/>
        <location filename="../app/windows/settings_window.py" line="130"/>
        <source>Error</source>
        <translation>Error</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="125"/>
        <source>Font information not found for {}.</source>
        <translation>Font information not found for {}.</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="207"/>
        <source>Removal Partially Failed</source>
        <translation>Removal Partially Failed</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="55"/>
        <source>Remove Selected</source>
        <translation>Remove Selected</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="51"/>
        <source>Select custom font family to remove:</source>
        <translation>Select custom font family to remove:</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="140"/>
        <source>Confirm Removal</source>
        <translation>Confirm Removal</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="208"/>
        <source>Removed family &apos;{}&apos;.
Deleted {} file(s).
Encountered errors:
 - {}</source>
        <translation>Removed family &apos;{}&apos;.
Deleted {} file(s).
Encountered errors:
 - {}</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="130"/>
        <source>No font files associated with {}.</source>
        <translation>No font files associated with {}.</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="210"/>
        <source>Removal Successful</source>
        <translation>Removal Successful</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="184"/>
        <source>Internal error finding font ID for {}).</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RosterDelegate</name>
    <message>
        <location filename="../app/pages/roster_page.py" line="218"/>
        <location filename="../app/pages/roster_page.py" line="303"/>
        <source>(None)</source>
        <translation type="unfinished">(None)</translation>
    </message>
</context>
<context>
    <name>RosterPage</name>
    <message>
        <location filename="../app/pages/roster_page.py" line="3957"/>
        <source>Show Body Data Columns</source>
        <translation>Show Body Data Columns</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3633"/>
        <source>Religious Practices Notes:</source>
        <translation>Religious Practices Notes:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3641"/>
        <source>Retirement Plans:</source>
        <translation>Retirement Plans:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1394"/>
        <location filename="../app/pages/roster_page.py" line="3829"/>
        <source>Sex</source>
        <translation>Sex</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1159"/>
        <location filename="../app/pages/roster_page.py" line="3592"/>
        <source>Temperament:</source>
        <translation>Temperament:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="865"/>
        <location filename="../app/pages/roster_page.py" line="3529"/>
        <source>Forearm:</source>
        <translation>Forearm:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="860"/>
        <location filename="../app/pages/roster_page.py" line="3524"/>
        <source>Weight:</source>
        <translation>Weight:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="866"/>
        <location filename="../app/pages/roster_page.py" line="3530"/>
        <source>Thigh:</source>
        <translation>Thigh:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="669"/>
        <location filename="../app/pages/roster_page.py" line="3454"/>
        <source>Date of Birth:</source>
        <translation>Date of Birth:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1161"/>
        <location filename="../app/pages/roster_page.py" line="3594"/>
        <source>Leadership:</source>
        <translation>Leadership:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1180"/>
        <location filename="../app/pages/roster_page.py" line="3604"/>
        <source>Education Level:</source>
        <translation>Education Level:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1392"/>
        <location filename="../app/pages/roster_page.py" line="3827"/>
        <source>Nationality</source>
        <translation>Nationality</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1165"/>
        <location filename="../app/pages/roster_page.py" line="3626"/>
        <source>Life Motto / Quote:</source>
        <translation>Life Motto / Quote:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1379"/>
        <location filename="../app/pages/roster_page.py" line="3814"/>
        <source>No.</source>
        <translation>No.</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="990"/>
        <location filename="../app/pages/roster_page.py" line="3783"/>
        <source>Loan End Date:</source>
        <translation>Loan End Date:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1172"/>
        <location filename="../app/pages/roster_page.py" line="3600"/>
        <source>Father&apos;s Name:</source>
        <translation>Father&apos;s Name:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3877"/>
        <location filename="../app/pages/roster_page.py" line="3884"/>
        <source>Both</source>
        <translation>Both</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1175"/>
        <location filename="../app/pages/roster_page.py" line="3629"/>
        <source>Siblings Summary:</source>
        <translation>Siblings Summary:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1178"/>
        <location filename="../app/pages/roster_page.py" line="3640"/>
        <source>Home Address:</source>
        <translation>Home Address:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1376"/>
        <location filename="../app/pages/roster_page.py" line="3811"/>
        <source>ID</source>
        <translation>ID</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1164"/>
        <location filename="../app/pages/roster_page.py" line="3625"/>
        <source>Emotional Intelligence Notes:</source>
        <translation>Emotional Intelligence Notes:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1157"/>
        <location filename="../app/pages/roster_page.py" line="3590"/>
        <source>Team Spirit:</source>
        <translation>Team Spirit:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1395"/>
        <location filename="../app/pages/roster_page.py" line="3830"/>
        <source>Pref. Foot</source>
        <translation>Pref. Foot</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="985"/>
        <location filename="../app/pages/roster_page.py" line="3782"/>
        <source>Loan Start Date:</source>
        <translation>Loan Start Date:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2681"/>
        <source>New</source>
        <translation>New</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3850"/>
        <source>Extended Columns</source>
        <translation>Extended Columns</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1396"/>
        <location filename="../app/pages/roster_page.py" line="3831"/>
        <source>Strong Eye</source>
        <translation>Strong Eye</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1315"/>
        <location filename="../app/pages/roster_page.py" line="3846"/>
        <source>Remove Player</source>
        <translation>Remove Player</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="651"/>
        <location filename="../app/pages/roster_page.py" line="3865"/>
        <source>Female</source>
        <translation>Female</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1796"/>
        <location filename="../app/pages/roster_page.py" line="2461"/>
        <location filename="../app/pages/roster_page.py" line="2623"/>
        <location filename="../app/pages/roster_page.py" line="4104"/>
        <location filename="../app/pages/roster_page.py" line="4531"/>
        <location filename="../app/pages/roster_page.py" line="4551"/>
        <location filename="../app/pages/roster_page.py" line="5218"/>
        <source>(None)</source>
        <translation>(None)</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1176"/>
        <location filename="../app/pages/roster_page.py" line="3630"/>
        <source>Family Background Notes:</source>
        <translation>Family Background Notes:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="668"/>
        <location filename="../app/pages/roster_page.py" line="3453"/>
        <location filename="../app/pages/roster_page.py" line="3500"/>
        <source>Detailed Position:</source>
        <translation>Detailed Position:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="684"/>
        <location filename="../app/pages/roster_page.py" line="3458"/>
        <source>Preferred Foot:</source>
        <translation>Preferred Foot:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="971"/>
        <location filename="../app/pages/roster_page.py" line="3779"/>
        <source>Contract End Date:</source>
        <translation>Contract End Date:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="864"/>
        <location filename="../app/pages/roster_page.py" line="3528"/>
        <source>Wrist:</source>
        <translation>Wrist:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1334"/>
        <location filename="../app/pages/roster_page.py" line="3856"/>
        <source>Search all columns...</source>
        <translation>Search all columns...</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="859"/>
        <location filename="../app/pages/roster_page.py" line="3523"/>
        <source>Height:</source>
        <translation>Height:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="892"/>
        <location filename="../app/pages/roster_page.py" line="3534"/>
        <source>Body Fat % (Est. Navy):</source>
        <translation>Body Fat % (Est. Navy):</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="863"/>
        <location filename="../app/pages/roster_page.py" line="3527"/>
        <source>Neck:</source>
        <translation>Neck:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2721"/>
        <source>Are you sure you want to remove player: {}?</source>
        <translation>Are you sure you want to remove player: {}?</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3610"/>
        <source>Website/Blog:</source>
        <translation>Website/Blog:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2707"/>
        <source>Could not add new player to the database.</source>
        <translation>Could not add new player to the database.</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3639"/>
        <source>Media Notes:</source>
        <translation>Media Notes:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2682"/>
        <source>Player</source>
        <translation>Player</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3635"/>
        <source>Charity/Community:</source>
        <translation>Charity/Community:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2746"/>
        <source>Could not remove the selected player from the database.</source>
        <translation>Could not remove the selected player from the database.</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="679"/>
        <location filename="../app/pages/roster_page.py" line="3456"/>
        <source>Zone:</source>
        <translation>Zone:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1380"/>
        <location filename="../app/pages/roster_page.py" line="3815"/>
        <source>Pos.</source>
        <translation>Pos.</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1389"/>
        <location filename="../app/pages/roster_page.py" line="3824"/>
        <source>Wrist</source>
        <translation>Wrist</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3608"/>
        <source>Coaching Interest:</source>
        <translation>Coaching Interest:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1391"/>
        <location filename="../app/pages/roster_page.py" line="3826"/>
        <source>Thigh</source>
        <translation>Thigh</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3612"/>
        <source>Phone:</source>
        <translation>Phone:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="711"/>
        <location filename="../app/pages/roster_page.py" line="3407"/>
        <location filename="../app/pages/roster_page.py" line="3424"/>
        <source>Personal</source>
        <translation>Personal</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1158"/>
        <location filename="../app/pages/roster_page.py" line="3591"/>
        <source>Adaptability:</source>
        <translation>Adaptability:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3632"/>
        <source>Superstitions / Rituals:</source>
        <translation>Superstitions / Rituals:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3893"/>
        <location filename="../app/pages/roster_page.py" line="3900"/>
        <source>Not available</source>
        <translation>Not available</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1377"/>
        <location filename="../app/pages/roster_page.py" line="3812"/>
        <source>Last Name</source>
        <translation>Last Name</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="928"/>
        <location filename="../app/pages/roster_page.py" line="3727"/>
        <source>Player Status Tags:</source>
        <translation>Player Status Tags:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3631"/>
        <source>Pets Description:</source>
        <translation>Pets Description:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1150"/>
        <location filename="../app/pages/roster_page.py" line="3584"/>
        <source>Date Joined Club:</source>
        <translation>Date Joined Club:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1170"/>
        <location filename="../app/pages/roster_page.py" line="3598"/>
        <source>Marital Status:</source>
        <translation>Marital Status:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1162"/>
        <location filename="../app/pages/roster_page.py" line="3595"/>
        <source>Charisma:</source>
        <translation>Charisma:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3634"/>
        <source>Political Views Summary:</source>
        <translation>Political Views Summary:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1397"/>
        <location filename="../app/pages/roster_page.py" line="3832"/>
        <source>Zone</source>
        <translation>Zone</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="981"/>
        <location filename="../app/pages/roster_page.py" line="3781"/>
        <source>Loan Club:</source>
        <translation>Loan Club:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1156"/>
        <location filename="../app/pages/roster_page.py" line="3589"/>
        <source>Determination:</source>
        <translation>Determination:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1154"/>
        <location filename="../app/pages/roster_page.py" line="3587"/>
        <source>Work Ethic:</source>
        <translation>Work Ethic:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1160"/>
        <location filename="../app/pages/roster_page.py" line="3593"/>
        <source>Ambition:</source>
        <translation>Ambition:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1173"/>
        <location filename="../app/pages/roster_page.py" line="3601"/>
        <source>Mother&apos;s Name:</source>
        <translation>Mother&apos;s Name:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1400"/>
        <location filename="../app/pages/roster_page.py" line="3835"/>
        <source>Secondary Group</source>
        <translation>Secondary Group</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1151"/>
        <location filename="../app/pages/roster_page.py" line="3585"/>
        <source>Personality Type:</source>
        <translation>Personality Type:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1398"/>
        <location filename="../app/pages/roster_page.py" line="3833"/>
        <source>Fitness</source>
        <translation>Fitness</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1189"/>
        <location filename="../app/pages/roster_page.py" line="3613"/>
        <source>Agent Name:</source>
        <translation>Agent Name:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="894"/>
        <location filename="../app/pages/roster_page.py" line="3536"/>
        <source>Waist-to-Hip Ratio:</source>
        <translation>Waist-to-Hip Ratio:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3637"/>
        <source>Business Ventures:</source>
        <translation>Business Ventures:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3877"/>
        <location filename="../app/pages/roster_page.py" line="3880"/>
        <location filename="../app/pages/roster_page.py" line="3893"/>
        <location filename="../app/pages/roster_page.py" line="3896"/>
        <source>Left</source>
        <translation>Left</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="666"/>
        <location filename="../app/pages/roster_page.py" line="3451"/>
        <source>Shirt Number:</source>
        <translation>Shirt Number:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1390"/>
        <location filename="../app/pages/roster_page.py" line="3825"/>
        <source>Forearm</source>
        <translation>Forearm</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1168"/>
        <location filename="../app/pages/roster_page.py" line="3596"/>
        <source>Hometown:</source>
        <translation>Hometown:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="650"/>
        <location filename="../app/pages/roster_page.py" line="3864"/>
        <source>Male</source>
        <translation>Male</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1401"/>
        <location filename="../app/pages/roster_page.py" line="3803"/>
        <location filename="../app/pages/roster_page.py" line="3836"/>
        <source>Status Tags</source>
        <translation>Status Tags</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1174"/>
        <location filename="../app/pages/roster_page.py" line="3628"/>
        <source>Parents&apos; Occupations:</source>
        <translation>Parents&apos; Occupations:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1155"/>
        <location filename="../app/pages/roster_page.py" line="3588"/>
        <source>Professionalism:</source>
        <translation>Professionalism:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1185"/>
        <location filename="../app/pages/roster_page.py" line="3609"/>
        <source>Media Friendliness:</source>
        <translation>Media Friendliness:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="867"/>
        <location filename="../app/pages/roster_page.py" line="3531"/>
        <source>Fitness:</source>
        <translation>Fitness:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1387"/>
        <location filename="../app/pages/roster_page.py" line="3822"/>
        <source>Hip</source>
        <translation>Hip</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="688"/>
        <location filename="../app/pages/roster_page.py" line="3465"/>
        <source>Secondary Group:</source>
        <translation>Secondary Group:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1560"/>
        <location filename="../app/pages/roster_page.py" line="2707"/>
        <location filename="../app/pages/roster_page.py" line="2746"/>
        <location filename="../app/pages/roster_page.py" line="4448"/>
        <source>Error</source>
        <translation>Error</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3636"/>
        <source>Volunteer Work:</source>
        <translation>Volunteer Work:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1181"/>
        <location filename="../app/pages/roster_page.py" line="3605"/>
        <source>Field of Study:</source>
        <translation>Field of Study:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="683"/>
        <location filename="../app/pages/roster_page.py" line="3457"/>
        <source>Sex:</source>
        <translation>Sex:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1383"/>
        <location filename="../app/pages/roster_page.py" line="3818"/>
        <source>Age</source>
        <translation>Age</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1378"/>
        <location filename="../app/pages/roster_page.py" line="3813"/>
        <source>Name</source>
        <translation>Name</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="950"/>
        <location filename="../app/pages/roster_page.py" line="3760"/>
        <source>Transfer Status:</source>
        <translation>Transfer Status:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3607"/>
        <source>Other Job:</source>
        <translation>Other Job:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1177"/>
        <location filename="../app/pages/roster_page.py" line="3602"/>
        <source>Number of Children:</source>
        <translation>Number of Children:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1386"/>
        <location filename="../app/pages/roster_page.py" line="3821"/>
        <source>Waist</source>
        <translation>Waist</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="665"/>
        <location filename="../app/pages/roster_page.py" line="3450"/>
        <source>First Name:</source>
        <translation>First Name:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1216"/>
        <location filename="../app/pages/roster_page.py" line="3415"/>
        <location filename="../app/pages/roster_page.py" line="3430"/>
        <location filename="../app/pages/roster_page.py" line="3577"/>
        <source>Profile</source>
        <translation>Profile</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1388"/>
        <location filename="../app/pages/roster_page.py" line="3823"/>
        <source>Neck</source>
        <translation>Neck</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="664"/>
        <location filename="../app/pages/roster_page.py" line="3449"/>
        <source>Last Name:</source>
        <translation>Last Name:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3848"/>
        <source>Basic Columns</source>
        <translation>Basic Columns</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="667"/>
        <location filename="../app/pages/roster_page.py" line="3452"/>
        <location filename="../app/pages/roster_page.py" line="3499"/>
        <source>Position:</source>
        <translation>Position:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="862"/>
        <location filename="../app/pages/roster_page.py" line="3526"/>
        <source>Hip:</source>
        <translation>Hip:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="687"/>
        <location filename="../app/pages/roster_page.py" line="3460"/>
        <source>Primary Group:</source>
        <translation>Primary Group:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="891"/>
        <location filename="../app/pages/roster_page.py" line="3533"/>
        <source>BMI:</source>
        <translation>BMI:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1171"/>
        <location filename="../app/pages/roster_page.py" line="3599"/>
        <source>Partner&apos;s Name:</source>
        <translation>Partner&apos;s Name:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1399"/>
        <location filename="../app/pages/roster_page.py" line="3834"/>
        <source>Primary Group</source>
        <translation>Primary Group</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3618"/>
        <source>Personal Assistant:</source>
        <translation>Personal Assistant:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="685"/>
        <location filename="../app/pages/roster_page.py" line="3459"/>
        <source>Strong Eye:</source>
        <translation>Strong Eye:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="144"/>
        <location filename="../app/pages/roster_page.py" line="600"/>
        <source>N/A</source>
        <translation>N/A</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1314"/>
        <location filename="../app/pages/roster_page.py" line="3844"/>
        <source>Add Player</source>
        <translation>Add Player</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="899"/>
        <location filename="../app/pages/roster_page.py" line="3546"/>
        <source>View Physical Profile Chart</source>
        <translation>View Physical Profile Chart</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3877"/>
        <location filename="../app/pages/roster_page.py" line="3882"/>
        <location filename="../app/pages/roster_page.py" line="3893"/>
        <location filename="../app/pages/roster_page.py" line="3898"/>
        <source>Right</source>
        <translation>Right</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3638"/>
        <source>Public Image:</source>
        <translation>Public Image:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1402"/>
        <location filename="../app/pages/roster_page.py" line="3804"/>
        <location filename="../app/pages/roster_page.py" line="3837"/>
        <source>Transfer Status</source>
        <translation>Transfer Status</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="670"/>
        <location filename="../app/pages/roster_page.py" line="3455"/>
        <source>Age:</source>
        <translation>Age:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3606"/>
        <source>University/School:</source>
        <translation>University/School:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1393"/>
        <location filename="../app/pages/roster_page.py" line="3828"/>
        <source>Flag</source>
        <translation>Flag</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="895"/>
        <location filename="../app/pages/roster_page.py" line="3537"/>
        <source>Waist-to-Height Ratio:</source>
        <translation>Waist-to-Height Ratio:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1169"/>
        <location filename="../app/pages/roster_page.py" line="3597"/>
        <source>Current Residence:</source>
        <translation>Current Residence:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3611"/>
        <source>Email:</source>
        <translation>Email:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="906"/>
        <location filename="../app/pages/roster_page.py" line="3409"/>
        <location filename="../app/pages/roster_page.py" line="3426"/>
        <source>Physical</source>
        <translation>Physical</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="893"/>
        <location filename="../app/pages/roster_page.py" line="3535"/>
        <source>Lean Body Mass (Est.):</source>
        <translation>Lean Body Mass (Est.):</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1153"/>
        <location filename="../app/pages/roster_page.py" line="3586"/>
        <source>Mentality:</source>
        <translation>Mentality:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3852"/>
        <source>All Columns</source>
        <translation>All Columns</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="663"/>
        <location filename="../app/pages/roster_page.py" line="3448"/>
        <source>ID:</source>
        <translation>ID:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1384"/>
        <location filename="../app/pages/roster_page.py" line="3819"/>
        <source>Height</source>
        <translation>Height</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="676"/>
        <location filename="../app/pages/roster_page.py" line="3479"/>
        <source>Nationality:</source>
        <translation>Nationality:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="861"/>
        <location filename="../app/pages/roster_page.py" line="3525"/>
        <source>Waist:</source>
        <translation>Waist:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1382"/>
        <location filename="../app/pages/roster_page.py" line="3817"/>
        <source>Date of Birth</source>
        <translation>Date of Birth</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1166"/>
        <location filename="../app/pages/roster_page.py" line="3627"/>
        <source>Personal Goals (Outside Football):</source>
        <translation>Personal Goals (Outside Football):</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1381"/>
        <location filename="../app/pages/roster_page.py" line="3816"/>
        <source>Detailed Pos.</source>
        <translation>Detailed Pos.</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1385"/>
        <location filename="../app/pages/roster_page.py" line="3820"/>
        <source>Weight</source>
        <translation>Weight</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1179"/>
        <location filename="../app/pages/roster_page.py" line="3603"/>
        <source>Religion / Faith:</source>
        <translation>Religion / Faith:</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1022"/>
        <location filename="../app/pages/roster_page.py" line="3410"/>
        <location filename="../app/pages/roster_page.py" line="3428"/>
        <source>Status</source>
        <translation>Status</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="529"/>
        <location filename="../app/pages/roster_page.py" line="721"/>
        <location filename="../app/pages/roster_page.py" line="799"/>
        <location filename="../app/pages/roster_page.py" line="915"/>
        <location filename="../app/pages/roster_page.py" line="1046"/>
        <location filename="../app/pages/roster_page.py" line="2252"/>
        <location filename="../app/pages/roster_page.py" line="3550"/>
        <location filename="../app/pages/roster_page.py" line="3552"/>
        <location filename="../app/pages/roster_page.py" line="3554"/>
        <location filename="../app/pages/roster_page.py" line="3556"/>
        <location filename="../app/pages/roster_page.py" line="3703"/>
        <source>Select a player from the list</source>
        <translation>Select a player from the list</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2720"/>
        <location filename="../app/pages/roster_page.py" line="4779"/>
        <source>Confirm Removal</source>
        <translation>Confirm Removal</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="545"/>
        <source>Click to upload player image (PNG, 200x300, &lt;20KB)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="779"/>
        <location filename="../app/pages/roster_page.py" line="1540"/>
        <source>Clear All Ratings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="780"/>
        <source>Remove all position ratings for the selected player</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="789"/>
        <location filename="../app/pages/roster_page.py" line="3408"/>
        <source>Position</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="976"/>
        <location filename="../app/pages/roster_page.py" line="3780"/>
        <source>Date Added to List:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="995"/>
        <location filename="../app/pages/roster_page.py" line="3784"/>
        <source>Club Sold To:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="999"/>
        <location filename="../app/pages/roster_page.py" line="3785"/>
        <source>Date Joining New Club:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1004"/>
        <location filename="../app/pages/roster_page.py" line="3786"/>
        <source>Date Released:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1145"/>
        <source>Enter player biography notes here...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1182"/>
        <source>University / School:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1183"/>
        <source>Dual Career / Other Job:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1184"/>
        <source>Coach / Mentor Willingness:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1186"/>
        <source>Website / Blog:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1187"/>
        <source>Personal Email:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1188"/>
        <source>Personal Phone:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1190"/>
        <source>Personal Assistant / Manager:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1317"/>
        <source>Select Basic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1318"/>
        <source>Select Extended</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1319"/>
        <source>Select All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1541"/>
        <source>Are you sure you want to clear all position ratings for this player?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1562"/>
        <source>Failed to clear position ratings. Please try again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1769"/>
        <source>Summary of selected status tags.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="1788"/>
        <source>Determined by nationality based on configured zones.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2139"/>
        <source>Error loading profile data.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2350"/>
        <location filename="../app/pages/roster_page.py" line="2463"/>
        <location filename="../app/pages/roster_page.py" line="2551"/>
        <location filename="../app/pages/roster_page.py" line="5220"/>
        <location filename="../app/pages/roster_page.py" line="5308"/>
        <source>Update Failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2350"/>
        <source>Could not save changes to the database.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2463"/>
        <location filename="../app/pages/roster_page.py" line="5220"/>
        <source>Could not save group change to the database.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2484"/>
        <location filename="../app/pages/roster_page.py" line="5241"/>
        <source>Invalid Input</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2484"/>
        <location filename="../app/pages/roster_page.py" line="5241"/>
        <source>Shirt number {} is already assigned.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2551"/>
        <location filename="../app/pages/roster_page.py" line="5308"/>
        <source>Could not save change for {} to the database.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2789"/>
        <source>Select Player</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2790"/>
        <source>Please select a player before uploading an image.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2795"/>
        <source>Select Player Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2796"/>
        <source>PNG Images (*.png)</source>
        <translation type="unfinished">PNG 图片 (*.png)</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2812"/>
        <source>Invalid File Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2813"/>
        <source>Please select a PNG image file.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2822"/>
        <source>File Too Large</source>
        <translation>文件太大</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2824"/>
        <source>Image file size must be less than {}KB. Selected size: {:.1f}KB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2828"/>
        <source>Error Reading File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2829"/>
        <source>Could not read file properties: {}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2835"/>
        <source>Cannot Read Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2836"/>
        <source>Could not read image data. The file might be corrupted.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2844"/>
        <source>Incorrect Dimensions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2846"/>
        <source>Image dimensions must be no larger than {}x{} pixels. Selected dimensions: {}x{} pixels.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2861"/>
        <source>Upload Successful</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2862"/>
        <source>Player image updated successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2865"/>
        <source>Upload Failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="2866"/>
        <source>Could not save the image: {}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3501"/>
        <source>Primary Position:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3502"/>
        <source>Secondary Position:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3503"/>
        <source>Position Notes:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3942"/>
        <source>This column cannot be hidden.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="3946"/>
        <source>Group ID columns cannot be toggled here.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4422"/>
        <source>No Player Selected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4423"/>
        <source>Please select a player from the roster first.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4442"/>
        <source>Insufficient Data</source>
        <translation type="unfinished">数据不足</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4443"/>
        <source>Not enough physical data available for the selected player to generate a chart.</source>
        <translation type="unfinished">所选球员没有足够的身体数据来生成图表。</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4448"/>
        <source>Failed to retrieve data for the chart.</source>
        <translation type="unfinished">无法获取图表数据。</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4741"/>
        <source>Upload Photo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4749"/>
        <source>Remove Photo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4754"/>
        <source>No specific image to remove for this player.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4773"/>
        <source>No Image</source>
        <translation type="unfinished">无图片</translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4774"/>
        <source>There is no specific image file to remove for this player.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4780"/>
        <source>Are you sure you want to remove the image for player ID {}?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4789"/>
        <source>Image Removed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4790"/>
        <source>Player image removed successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4795"/>
        <source>Removal Failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/roster_page.py" line="4796"/>
        <source>Could not remove the image file: {}</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SeasonTimelineWidget</name>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="85"/>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="138"/>
        <location filename="../app/widgets/season_timeline_widget.py" line="86"/>
        <source>Refresh Timeline</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="87"/>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="139"/>
        <location filename="../app/widgets/season_timeline_widget.py" line="88"/>
        <source>Refresh the timeline visualization</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="91"/>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="142"/>
        <source>Show Months</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="95"/>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="143"/>
        <source>Show Weeks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="99"/>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="144"/>
        <source>Show Intensity Chart</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SettingsWindow</name>
    <message>
        <location filename="../app/windows/settings_window.py" line="946"/>
        <source>Language:</source>
        <translation>Language:</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="405"/>
        <location filename="../app/windows/settings_window.py" line="1006"/>
        <source>Dark</source>
        <translation>Dark</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1028"/>
        <source>Family:</source>
        <translation>Family:</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1027"/>
        <source>Font</source>
        <translation>Font</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="763"/>
        <source>Could not create custom fonts directory: {} {}</source>
        <translation>Could not create custom fonts directory: {} {}</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="769"/>
        <source>Select Custom Font Files</source>
        <translation>Select Custom Font Files</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="404"/>
        <location filename="../app/windows/settings_window.py" line="1005"/>
        <source>Light</source>
        <translation>Light</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="949"/>
        <source>General</source>
        <translation>General</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="694"/>
        <source>Could not apply language change immediately.</source>
        <translation>Could not apply language change immediately.</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1004"/>
        <source>Theme</source>
        <translation>Theme</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1024"/>
        <source>Zoom Level</source>
        <translation>Zoom Level</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1031"/>
        <source>Remove Font...</source>
        <translation>Remove Font...</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="894"/>
        <source>Restart Required</source>
        <translation>Restart Required</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1030"/>
        <source>Add Font...</source>
        <translation>Add Font...</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="323"/>
        <location filename="../app/windows/settings_window.py" line="943"/>
        <source>Settings</source>
        <translation>Settings</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="694"/>
        <location filename="../app/windows/settings_window.py" line="697"/>
        <location filename="../app/windows/settings_window.py" line="763"/>
        <source>Error</source>
        <translation>Error</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1029"/>
        <source>Size:</source>
        <translation>Size:</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1009"/>
        <source>Accent Color</source>
        <translation>Accent Color</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="950"/>
        <source>Appearance</source>
        <translation>Appearance</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="768"/>
        <source>Font Files (*.ttf *.otf)</source>
        <translation>Font Files (*.ttf *.otf)</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1019"/>
        <source>Layout Density</source>
        <translation>Layout Density</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="895"/>
        <source>The application must be restarted for the zoom level change to take effect.</source>
        <translation>The application must be restarted for the zoom level change to take effect.</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="406"/>
        <location filename="../app/windows/settings_window.py" line="1007"/>
        <source>System</source>
        <translation>System</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="835"/>
        <source>Add Custom Fonts Result</source>
        <translation>Add Custom Fonts Result</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="438"/>
        <location filename="../app/windows/settings_window.py" line="1014"/>
        <source>Button Shape</source>
        <translation>Button Shape</translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="341"/>
        <source>Select the application language</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="343"/>
        <source>Choose the language for the application interface</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="359"/>
        <source>Enable or disable tooltips throughout the application</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="365"/>
        <source>Set how long tooltips remain visible</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="367"/>
        <source>Choose the duration tooltips remain visible</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="697"/>
        <source>An unexpected error occurred while changing language: {}).</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/windows/settings_window.py" line="1034"/>
        <source>Search:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SponsorSectionWidget</name>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="109"/>
        <location filename="../app/widgets/sponsors_panel.py" line="677"/>
        <source>Season</source>
        <translation>Season</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="92"/>
        <location filename="../app/widgets/sponsors_panel.py" line="663"/>
        <source>Sponsor Name...</source>
        <translation>Sponsor Name...</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="125"/>
        <location filename="../app/widgets/sponsors_panel.py" line="635"/>
        <source>Deal Term:</source>
        <translation>Deal Term:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="136"/>
        <location filename="../app/widgets/sponsors_panel.py" line="666"/>
        <source>Enter any relevant notes here...</source>
        <translation>Enter any relevant notes here...</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="122"/>
        <location filename="../app/widgets/sponsors_panel.py" line="632"/>
        <source>Type:</source>
        <translation>Type:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="112"/>
        <location filename="../app/widgets/sponsors_panel.py" line="680"/>
        <source>Other</source>
        <translation>Other</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="208"/>
        <location filename="../app/widgets/sponsors_panel.py" line="708"/>
        <source>Email:</source>
        <translation>Email:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="201"/>
        <location filename="../app/widgets/sponsors_panel.py" line="665"/>
        <source>Contact Person...</source>
        <translation>Contact Person...</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="126"/>
        <location filename="../app/widgets/sponsors_panel.py" line="636"/>
        <source>Fee/Value:</source>
        <translation>Fee/Value:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="131"/>
        <location filename="../app/widgets/sponsors_panel.py" line="688"/>
        <source>Notes</source>
        <translation>Notes</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="161"/>
        <location filename="../app/widgets/sponsors_panel.py" line="486"/>
        <location filename="../app/widgets/sponsors_panel.py" line="693"/>
        <source>No Logo</source>
        <translation>No Logo</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="209"/>
        <location filename="../app/widgets/sponsors_panel.py" line="709"/>
        <source>Phone:</source>
        <translation>Phone:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="123"/>
        <location filename="../app/widgets/sponsors_panel.py" line="633"/>
        <source>Deal Start:</source>
        <translation>Deal Start:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="124"/>
        <location filename="../app/widgets/sponsors_panel.py" line="634"/>
        <source>Deal End:</source>
        <translation>Deal End:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="203"/>
        <location filename="../app/widgets/sponsors_panel.py" line="667"/>
        <source>Email...</source>
        <translation>Email...</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="120"/>
        <location filename="../app/widgets/sponsors_panel.py" line="630"/>
        <source>Sponsor Name:</source>
        <translation>Sponsor Name:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="195"/>
        <location filename="../app/widgets/sponsors_panel.py" line="690"/>
        <source>Contact Info</source>
        <translation>Contact Info</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="106"/>
        <location filename="../app/widgets/sponsors_panel.py" line="674"/>
        <source>Select deal</source>
        <translation>Select deal</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="108"/>
        <location filename="../app/widgets/sponsors_panel.py" line="676"/>
        <source>Monthly</source>
        <translation>Monthly</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="107"/>
        <location filename="../app/widgets/sponsors_panel.py" line="675"/>
        <source>Daily</source>
        <translation>Daily</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="97"/>
        <location filename="../app/widgets/sponsors_panel.py" line="664"/>
        <source>Type...</source>
        <translation>Type...</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="172"/>
        <location filename="../app/widgets/sponsors_panel.py" line="694"/>
        <source>Upload Logo</source>
        <translation>Upload Logo</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="207"/>
        <location filename="../app/widgets/sponsors_panel.py" line="707"/>
        <source>Contact Person:</source>
        <translation>Contact Person:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="205"/>
        <location filename="../app/widgets/sponsors_panel.py" line="668"/>
        <source>Phone...</source>
        <translation>Phone...</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="173"/>
        <location filename="../app/widgets/sponsors_panel.py" line="695"/>
        <source>Remove Logo</source>
        <translation>Remove Logo</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="121"/>
        <location filename="../app/widgets/sponsors_panel.py" line="631"/>
        <source>Category:</source>
        <translation>Category:</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="110"/>
        <location filename="../app/widgets/sponsors_panel.py" line="678"/>
        <source>Yearly</source>
        <translation>Yearly</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="182"/>
        <location filename="../app/widgets/sponsors_panel.py" line="491"/>
        <location filename="../app/widgets/sponsors_panel.py" line="697"/>
        <source>(PNG, &lt;{kb}KB, &lt;{w}x{h}px)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="258"/>
        <source>Sponsor name cannot be empty.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="263"/>
        <source>Please enter a valid email address.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="268"/>
        <source>Please enter a valid numeric fee/value.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="271"/>
        <source>End date cannot be before start date.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="275"/>
        <source>Type cannot be empty.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="303"/>
        <source>Upload Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="303"/>
        <source>Could not create directory for sponsor logos.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="308"/>
        <source>PNG Images (*.png)</source>
        <translation type="unfinished">PNG 图片 (*.png)</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="310"/>
        <source>Select Sponsor Logo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="324"/>
        <source>Error: File size exceeds {kb}KB.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="333"/>
        <source>Error: Cannot read image file.</source>
        <translation type="unfinished">错误：无法读取图像文件。</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="340"/>
        <source>Error: Dimensions exceed {w}x{h}px.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="376"/>
        <source>Error copying logo!</source>
        <translation type="unfinished">复制徽标时出错！</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="401"/>
        <source>Remove Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="401"/>
        <source>Could not remove the logo file. Check permissions.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SponsorsPanel</name>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="975"/>
        <location filename="../app/widgets/sponsors_panel.py" line="1178"/>
        <source>Main</source>
        <translation>Main</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="982"/>
        <location filename="../app/widgets/sponsors_panel.py" line="1185"/>
        <source>Sports Brand</source>
        <translation>Sports Brand</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="976"/>
        <location filename="../app/widgets/sponsors_panel.py" line="1179"/>
        <source>Supporters</source>
        <translation>Supporters</translation>
    </message>
    <message>
        <location filename="../app/widgets/sponsors_panel.py" line="977"/>
        <location filename="../app/widgets/sponsors_panel.py" line="1180"/>
        <source>Charity</source>
        <translation>Charity</translation>
    </message>
</context>
<context>
    <name>StaffAssignmentDialog</name>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="30"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="30"/>
        <source>Assign Staff to {team_name}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="38"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="38"/>
        <source>Assign staff members to {team_name}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="49"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="49"/>
        <source>Technical Director</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="50"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="50"/>
        <source>Head Coach</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="51"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="51"/>
        <source>Coach</source>
        <translation type="unfinished">教练</translation>
    </message>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="52"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="52"/>
        <source>Assistant Coach</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="53"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="53"/>
        <source>Staff 1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="54"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="54"/>
        <source>Staff 2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/staff_assignment_dialog.py" line="67"/>
        <location filename="../app/dialogs/staff_assignment_dialog.py" line="67"/>
        <source>(Unassigned)</source>
        <translation type="unfinished">（未分配）</translation>
    </message>
</context>
<context>
    <name>StaffFilterDialog</name>
    <message>
        <location filename="../app/dialogs/staff_filter_dialog.py" line="59"/>
        <location filename="../app/dialogs/staff_filter_dialog.py" line="248"/>
        <location filename="../app/dialogs/staff_filter_dialog.py" line="254"/>
        <source>-- Any --</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/dialogs/staff_filter_dialog.py" line="207"/>
        <source>Filter</source>
        <translation type="unfinished">筛选</translation>
    </message>
    <message>
        <location filename="../app/dialogs/staff_filter_dialog.py" line="215"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../app/dialogs/staff_filter_dialog.py" line="217"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../app/dialogs/staff_filter_dialog.py" line="219"/>
        <source>Clear Filters</source>
        <translation type="unfinished">Clear Filters</translation>
    </message>
</context>
<context>
    <name>TeamGroupsWidget</name>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="1030"/>
        <source>Group Id</source>
        <translation>ID</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="1032"/>
        <source>Team Name</source>
        <translation>队名</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="1034"/>
        <source>Group Category</source>
        <translation>组别</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="486"/>
        <location filename="../app/widgets/team_groups_widget.py" line="977"/>
        <source>Add Group</source>
        <translation>添加组别</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="487"/>
        <location filename="../app/widgets/team_groups_widget.py" line="978"/>
        <source>Remove Selected</source>
        <translation>删除所选</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="503"/>
        <location filename="../app/widgets/team_groups_widget.py" line="991"/>
        <location filename="../app/widgets/team_groups_widget.py" line="1000"/>
        <source>Search:</source>
        <translation>搜索：</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="491"/>
        <location filename="../app/widgets/team_groups_widget.py" line="1006"/>
        <source>Search...</source>
        <translation>搜索...</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="492"/>
        <location filename="../app/widgets/team_groups_widget.py" line="1008"/>
        <source>Clear</source>
        <translation>清除</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="1011"/>
        <source>Filter</source>
        <translation>筛选</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="1014"/>
        <source>Clear Filter</source>
        <translation>清除筛选</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="657"/>
        <source>Add New Team Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="658"/>
        <source>Enter the name for the new group:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="663"/>
        <location filename="../app/widgets/team_groups_widget.py" line="700"/>
        <source>Input Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="663"/>
        <location filename="../app/widgets/team_groups_widget.py" line="700"/>
        <location filename="../app/widgets/team_groups_widget.py" line="787"/>
        <source>Group name cannot be empty.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="669"/>
        <source>Duplicate Name</source>
        <translation type="unfinished">Duplicate Name</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="670"/>
        <location filename="../app/widgets/team_groups_widget.py" line="791"/>
        <source>A group with the name &apos;{name}&apos; already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="697"/>
        <location filename="../app/widgets/team_groups_widget.py" line="750"/>
        <location filename="../app/widgets/team_groups_widget.py" line="820"/>
        <source>Database Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="698"/>
        <source>Failed to add the new group to the database.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="717"/>
        <location filename="../app/widgets/team_groups_widget.py" line="724"/>
        <source>Error</source>
        <translation type="unfinished">Error</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="717"/>
        <source>Could not retrieve group information from the selected row.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="724"/>
        <source>Could not retrieve group ID.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="730"/>
        <source>Cannot Remove Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="732"/>
        <source>Cannot remove group &apos;{name}&apos; because {count} player(s) are still assigned to it. Please reassign players first.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="737"/>
        <source>Confirm Removal</source>
        <translation type="unfinished">Confirm Removal</translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="738"/>
        <source>Are you sure you want to remove the group: &apos;{name}&apos;?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="751"/>
        <source>Failed to remove the group from the database.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="798"/>
        <source>Validation Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="820"/>
        <source>Failed to update group in the database.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="1036"/>
        <source>M1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/team_groups_widget.py" line="1040"/>
        <source>C (1)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>self.parent_widget</name>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="269"/>
        <source>Season</source>
        <translation>赛季</translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="270"/>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="446"/>
        <source>Macrocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="271"/>
        <source>Mesocycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="272"/>
        <source>Microcycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="273"/>
        <source>Evaluations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="277"/>
        <source>Competitions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/widgets/season_timeline_widget_new.py" line="620"/>
        <source>Intensity</source>
        <translation type="unfinished">强度</translation>
    </message>
</context>
</TS>
