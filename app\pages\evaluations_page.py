import sys
import logging
import traceback
from PySide6.QtWidgets import (
    QA<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QSplitter, QMessageBox,
    QMenu, QAbstractItemView, QTabWidget, QToolBar, QInputDialog,
    QSpinBox, QStyledItemDelegate, QScrollArea, QAbstractItemDelegate,
    QGridLayout, QGroupBox, QStackedWidget, QLineEdit, QComboBox,
    QPushButton, QDialog, QDialogButtonBox
)
from PySide6.QtCore import Qt, QSettings, QEvent, QTimer, QDateTime, QDate, QCoreApplication
from app.utils.color_constants import get_color
from PySide6.QtGui import QBrush, QColor, QIcon, QAction

from app.data.roster_manager import RosterManager
from app.data.evaluation_manager import EvaluationManager

class SpinBoxDelegate(QStyledItemDelegate):
    """Custom delegate for editing values with a spin box (0-10)."""

    def createEditor(self, parent, option, index):
        """Create a spin box editor for the cell."""
        if index.column() == 1:  # Only for value column
            editor = QSpinBox(parent)
            editor.setMinimum(0)
            editor.setMaximum(10)
            editor.setSingleStep(1)

            # Install event filter to handle Enter key directly
            editor.installEventFilter(self)

            return editor
        return super().createEditor(parent, option, index)

    def setEditorData(self, editor, index):
        """Set the editor data."""
        if isinstance(editor, QSpinBox):
            value = index.model().data(index, Qt.ItemDataRole.EditRole)
            if value and value.strip():
                try:
                    editor.setValue(int(float(value)))
                except (ValueError, TypeError):
                    editor.setValue(0)
            else:
                editor.setValue(0)
        else:
            super().setEditorData(editor, index)

    def setModelData(self, editor, model, index):
        """Set the model data from the editor."""
        if isinstance(editor, QSpinBox):
            model.setData(index, str(editor.value()), Qt.ItemDataRole.EditRole)
        else:
            super().setModelData(editor, model, index)

    def eventFilter(self, obj, event):
        """Handle events for the editor."""
        if event.type() == QEvent.Type.KeyPress:
            if event.key() == Qt.Key.Key_Enter or event.key() == Qt.Key.Key_Return:
                # Commit the current edit and move to the next item
                self.commitData.emit(obj)
                self.closeEditor.emit(obj, QAbstractItemDelegate.EndEditHint.EditNextItem)
                return True
            elif event.key() == Qt.Key.Key_Tab:
                # Also handle Tab key to move to the next item
                self.commitData.emit(obj)
                self.closeEditor.emit(obj, QAbstractItemDelegate.EndEditHint.EditNextItem)
                return True
        return super().eventFilter(obj, event)

class EvaluationsPage(QWidget):
    """Page for managing player evaluations."""

    def __init__(self, roster_manager=None, evaluation_manager=None, selected_player_ids=None, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.evaluations")

        # Set log level to INFO for better performance (was DEBUG)
        self.logger.setLevel(logging.INFO)

        # Use provided managers or create defaults
        self.roster_manager = roster_manager if roster_manager else RosterManager()
        self.evaluation_manager = evaluation_manager if evaluation_manager else EvaluationManager()

        # Store selected player IDs
        self.selected_player_ids = selected_player_ids if selected_player_ids else []

        # Add batch saving support
        self._save_timer = QTimer(self)
        self._save_timer.setSingleShot(True)
        self._save_timer.timeout.connect(self._save_modified_data)
        self._modified_categories = set()  # Track which categories need saving

        self.setObjectName("EvaluationsPage")
        self.setWindowTitle(self.tr("Evaluations"))

        # Set window flags to create a standalone window
        self.setWindowFlags(Qt.WindowType.Window)
        # Ensure it behaves as a normal window that doesn't block other windows
        self.setWindowModality(Qt.WindowModality.NonModal)

        # Initialize sort order for player names
        self.sort_order = Qt.SortOrder.AscendingOrder

        # Initialize current player ID
        self.current_player_id = None

        # Initialize field groups
        self.football_skills_groups = {}
        self.physical_condition_groups = {}
        self.mental_ability_groups = {}
        self.goalkeeper_skills_groups = {}

        # Load field groups
        self._load_field_groups()

        # Initialize UI
        self._init_ui()

        # Load data
        self._load_players()

        # If we have players, select the first one to populate the tables
        if self.filtered_players:
            # Select the first player to show the evaluation fields
            self.player_table.selectRow(0)
            first_player_id = self.filtered_players[0]['player_id']
            self.current_player_id = first_player_id
            # Populate the first tab with fields
            self._populate_evaluation_tables("1st", first_player_id)



    def _load_field_groups(self):
        """Load all field groups from settings."""
        self.logger.debug("Loading field groups from settings")
        self.football_skills_groups = self._get_football_skills_groups()
        self.physical_condition_groups = self._get_physical_condition_groups()
        self.mental_ability_groups = self._get_mental_ability_groups()
        self.goalkeeper_skills_groups = self._get_goalkeeper_skills_groups()

    def _init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout(self)

        # Create a splitter for left and right panels
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.splitter)

        # Left panel - Player list
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)

        # Player list label
        player_list_label = QLabel(self.tr("Players Selected for Evaluation"))
        player_list_label.setStyleSheet("font-weight: bold;")
        left_layout.addWidget(player_list_label)

        # Player table
        self.player_table = QTableWidget()
        self.player_table.setColumnCount(1)
        self.player_table.setHorizontalHeaderLabels([self.tr("Name")])
        self.player_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.player_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.player_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.player_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.player_table.verticalHeader().setVisible(False)
        self.player_table.setAlternatingRowColors(False)  # Remove alternating row colors
        self.player_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.player_table.customContextMenuRequested.connect(self._show_player_context_menu)
        self.player_table.itemSelectionChanged.connect(self._on_player_selection_changed)
        # Enable sorting when clicking on header
        self.player_table.horizontalHeader().setSectionsClickable(True)
        self.player_table.horizontalHeader().sectionClicked.connect(self._on_header_clicked)
        left_layout.addWidget(self.player_table)

        # Right panel - Evaluation tabs
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)

        # Create tab widget for evaluations
        self.evaluation_tabs = QTabWidget()

        # Create the evaluation tabs
        self.first_evaluation_tab = QWidget()
        self.second_evaluation_tab = QWidget()
        self.third_evaluation_tab = QWidget()
        self.progress_tab = QWidget()

        # Initialize the evaluation tabs
        self._init_evaluation_tab(self.first_evaluation_tab, "1st")
        self._init_evaluation_tab(self.second_evaluation_tab, "2nd")
        self._init_evaluation_tab(self.third_evaluation_tab, "3rd")
        self._init_progress_tab(self.progress_tab)

        # Add tabs to the tab widget
        self.evaluation_tabs.addTab(self.first_evaluation_tab, self.tr("1st Evaluation"))
        self.evaluation_tabs.addTab(self.second_evaluation_tab, self.tr("2nd Evaluation"))
        self.evaluation_tabs.addTab(self.third_evaluation_tab, self.tr("3rd Evaluation"))
        self.evaluation_tabs.addTab(self.progress_tab, self.tr("Progress"))

        # Connect tab change signal
        self.evaluation_tabs.currentChanged.connect(self._on_tab_changed)

        right_layout.addWidget(self.evaluation_tabs)

        # Add panels to splitter
        self.splitter.addWidget(left_panel)
        self.splitter.addWidget(right_panel)

        # Set initial sizes (30% left, 70% right)
        self.splitter.setSizes([300, 700])

        # Set a reasonable default size for the window
        self.resize(1200, 700)

    def _load_players(self):
        """Load players from the roster manager, filtered by selected player IDs if available."""
        # Get all players first
        all_players = self.roster_manager.get_all_players()

        # Debug: Print selected player IDs
        self.logger.info(f"Selected player IDs: {self.selected_player_ids}")

        # ALWAYS try to get selected player IDs from the database (not just when empty)
        try:
            if self.roster_manager.conn:
                self.roster_manager.cursor.execute("SELECT player_id FROM player_selections WHERE selection_type = 'evaluation' AND selected = 1")
                rows = self.roster_manager.cursor.fetchall()
                if rows:
                    db_selected_ids = [row[0] for row in rows]
                    self.logger.info(f"Found {len(db_selected_ids)} players selected for evaluation in database: {db_selected_ids}")

                    # Use database IDs if we don't have any, or if they're different
                    if not self.selected_player_ids or set(self.selected_player_ids) != set(db_selected_ids):
                        self.selected_player_ids = db_selected_ids
                        self.logger.info(f"Updated selected player IDs from database: {self.selected_player_ids}")
        except Exception as e:
            self.logger.error(f"Error loading selected player IDs from database: {e}")

        # Filter players based on selected IDs if any are provided
        if self.selected_player_ids and len(self.selected_player_ids) > 0:
            # Convert all IDs to integers for comparison
            selected_ids = [int(id) if isinstance(id, str) else id for id in self.selected_player_ids]
            self.logger.info(f"Using selected IDs (converted): {selected_ids}")

            # Debug: Print all player IDs for comparison
            all_ids = [p['player_id'] for p in all_players]
            self.logger.info(f"All player IDs: {all_ids}")

            # Filter players
            self.players = [p for p in all_players if p['player_id'] in selected_ids]
            self.logger.info(f"Filtered to {len(self.players)} selected players out of {len(all_players)} total players")

            # Debug: Print filtered player IDs
            filtered_ids = [p['player_id'] for p in self.players]
            self.logger.info(f"Filtered player IDs: {filtered_ids}")
        else:
            # If no selected IDs, use an empty list (not all players)
            self.players = []
            self.logger.info("No players selected for evaluation, showing empty list")

        # Store the filtered players for use in other methods
        self.filtered_players = self.players

        # Update the player table
        self._update_player_table()

    def _update_player_table(self):
        """Update the player table with the filtered players."""
        self.player_table.setRowCount(0)  # Clear the table

        if not self.filtered_players:
            # Add a message row if no players are selected
            self.player_table.setRowCount(1)
            message_item = QTableWidgetItem(self.tr("No players selected for evaluation"))
            message_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            message_item.setForeground(QBrush(QColor(150, 150, 150)))
            self.player_table.setItem(0, 0, message_item)
            return

        # Sort players by last name, first name
        sorted_players = sorted(
            self.filtered_players,
            key=lambda p: (p.get('last_name', ''), p.get('first_name', '')),
            reverse=(self.sort_order == Qt.SortOrder.DescendingOrder)
        )

        # Add players to the table
        for row, player in enumerate(sorted_players):
            self.player_table.insertRow(row)

            # Create player name cell
            name_item = QTableWidgetItem(f"{player['last_name']}, {player['first_name']}")
            name_item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            name_item.setData(Qt.ItemDataRole.UserRole, player['player_id'])  # Store player_id for selection

            # Set goalkeeper names to blue
            if player.get('position') == 'GK':
                name_item.setForeground(QBrush(get_color("goalkeeper_highlight")))

            self.player_table.setItem(row, 0, name_item)

    def _on_header_clicked(self, _):
        """Handle header click to toggle sorting order."""
        # Toggle sort order
        if self.sort_order == Qt.SortOrder.AscendingOrder:
            self.sort_order = Qt.SortOrder.DescendingOrder
        else:
            self.sort_order = Qt.SortOrder.AscendingOrder

        # Update the table with the new sort order
        self._update_player_table()

    def _show_player_context_menu(self, position):
        """Show context menu for the player table."""
        if not self.filtered_players:
            return

        menu = QMenu()
        sort_asc_action = menu.addAction(self.tr("Sort Ascending"))
        sort_desc_action = menu.addAction(self.tr("Sort Descending"))

        action = menu.exec(self.player_table.mapToGlobal(position))

        if action == sort_asc_action:
            self.sort_order = Qt.SortOrder.AscendingOrder
            self._update_player_table()
        elif action == sort_desc_action:
            self.sort_order = Qt.SortOrder.DescendingOrder
            self._update_player_table()

    def _init_evaluation_tab(self, tab, period):
        """Initialize an evaluation tab with three category tables side by side."""
        # Main layout for the tab
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Add toolbar with editing tools
        toolbar = QToolBar()

        # Add "Edit Selected Cells" action
        edit_selected_action = QAction(QIcon.fromTheme("edit"), self.tr("Edit Selected"), self)
        edit_selected_action.triggered.connect(lambda: self._edit_multiple_cells(period))
        toolbar.addAction(edit_selected_action)

        # Add separator
        toolbar.addSeparator()

        # Add "Clear All" action
        clear_all_action = QAction(QIcon.fromTheme("edit-clear"), self.tr("Clear All"), self)
        clear_all_action.triggered.connect(lambda: self._clear_all_values(period))
        toolbar.addAction(clear_all_action)

        # Add separator
        toolbar.addSeparator()

        # Add "Charts" action
        charts_action = QAction(QIcon.fromTheme("chart"), self.tr("Charts"), self)
        charts_action.triggered.connect(lambda: self._show_evaluation_charts(period))
        charts_action.setToolTip(self.tr("View evaluation data visualizations"))
        toolbar.addAction(charts_action)

        main_layout.addWidget(toolbar)

        # Create horizontal layout for the three category tables
        tables_layout = QHBoxLayout()
        tables_layout.setSpacing(10)

        # Create scroll areas for each table
        football_scroll = QScrollArea()
        football_scroll.setWidgetResizable(True)
        football_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        football_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        physical_scroll = QScrollArea()
        physical_scroll.setWidgetResizable(True)
        physical_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        physical_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        mental_scroll = QScrollArea()
        mental_scroll.setWidgetResizable(True)
        mental_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        mental_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Create the goalkeeper skills scroll area (initially hidden)
        goalkeeper_scroll = QScrollArea()
        goalkeeper_scroll.setWidgetResizable(True)
        goalkeeper_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        goalkeeper_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        goalkeeper_scroll.setVisible(False)  # Initially hidden, will be shown for goalkeepers

        # Create the category tables
        football_table = self._create_category_table(self.tr("FOOTBALL SKILLS"))
        physical_table = self._create_category_table(self.tr("PHYSICAL CONDITION"))
        mental_table = self._create_category_table(self.tr("MENTAL ABILITY"))
        goalkeeper_table = self._create_category_table(self.tr("GOALKEEPER SKILLS"))

        # Set custom delegate for editing
        football_table.setItemDelegate(SpinBoxDelegate())
        physical_table.setItemDelegate(SpinBoxDelegate())
        mental_table.setItemDelegate(SpinBoxDelegate())
        goalkeeper_table.setItemDelegate(SpinBoxDelegate())

        # Connect item changed signals
        football_table.itemChanged.connect(lambda item: self._on_item_changed(football_table, item, period))
        physical_table.itemChanged.connect(lambda item: self._on_item_changed(physical_table, item, period))
        mental_table.itemChanged.connect(lambda item: self._on_item_changed(mental_table, item, period))
        goalkeeper_table.itemChanged.connect(lambda item: self._on_item_changed(goalkeeper_table, item, period))

        # Add tables to scroll areas
        football_scroll.setWidget(football_table)
        physical_scroll.setWidget(physical_table)
        mental_scroll.setWidget(mental_table)
        goalkeeper_scroll.setWidget(goalkeeper_table)

        # Store references to the tables based on period
        if period == "1st":
            self.first_football_skills_table = football_table
            self.first_physical_condition_table = physical_table
            self.first_mental_ability_table = mental_table
            self.first_goalkeeper_skills_table = goalkeeper_table
            self.first_goalkeeper_scroll = goalkeeper_scroll
        elif period == "2nd":
            self.second_football_skills_table = football_table
            self.second_physical_condition_table = physical_table
            self.second_mental_ability_table = mental_table
            self.second_goalkeeper_skills_table = goalkeeper_table
            self.second_goalkeeper_scroll = goalkeeper_scroll
        else:
            self.third_football_skills_table = football_table
            self.third_physical_condition_table = physical_table
            self.third_mental_ability_table = mental_table
            self.third_goalkeeper_skills_table = goalkeeper_table
            self.third_goalkeeper_scroll = goalkeeper_scroll

        # Add scroll areas to the layout with equal width (1:1:1 ratio)
        tables_layout.addWidget(football_scroll, 1)  # 25% width
        tables_layout.addWidget(physical_scroll, 1)  # 25% width
        tables_layout.addWidget(mental_scroll, 1)  # 25% width
        tables_layout.addWidget(goalkeeper_scroll, 1)  # 25% width

        main_layout.addLayout(tables_layout)

    def _create_category_table(self, category_name):
        """Create a table for a specific evaluation category."""
        # Create table widget
        table = QTableWidget()
        table.setColumnCount(2)  # Field name and Value

        # Set header for the category
        header_text = self.tr("Field")  # Default text
        if category_name == "FOOTBALL SKILLS":
            header_text = self.tr("Football Skills")
        elif category_name == "PHYSICAL CONDITION":
            header_text = self.tr("Physical Condition")
        elif category_name == "MENTAL ABILITY":
            header_text = self.tr("Mental Ability")
        elif category_name == "GOALKEEPER SKILLS":
            header_text = self.tr("Goalkeeper Skills")

        table.setHorizontalHeaderLabels([header_text, self.tr("Value")])

        # Set table properties
        table.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        table.horizontalHeader().setDefaultSectionSize(80)

        # Create a custom table with enhanced keyboard navigation
        class EnhancedTable(QTableWidget):
            def keyPressEvent(self, event):
                if event.key() == Qt.Key.Key_Enter or event.key() == Qt.Key.Key_Return:
                    current_row = self.currentRow()
                    current_col = self.currentColumn()

                    # If we're in the value column, try to move to the next row
                    if current_col == 1 and current_row < self.rowCount() - 1:
                        # Find the next editable cell in column 1
                        for row in range(current_row + 1, self.rowCount()):
                            item = self.item(row, 1)
                            if item and item.flags() & Qt.ItemFlag.ItemIsEditable:
                                # Move to the next cell
                                self.setCurrentCell(row, 1)
                                # If the item is editable, start editing it
                                self.editItem(item)
                                return

                # For all other keys or if no next cell found, use the default handler
                super().keyPressEvent(event)

        # Replace the table with our enhanced version
        new_table = EnhancedTable()
        new_table.setColumnCount(2)  # Field name and Value

        # Set header for the category
        header_text = self.tr("Field")  # Default text
        if category_name == "FOOTBALL SKILLS":
            header_text = self.tr("Football Skills")
        elif category_name == "PHYSICAL CONDITION":
            header_text = self.tr("Physical Condition")
        elif category_name == "MENTAL ABILITY":
            header_text = self.tr("Mental Ability")
        elif category_name == "GOALKEEPER SKILLS":
            header_text = self.tr("Goalkeeper Skills")

        # Set the header labels on the new table
        new_table.setHorizontalHeaderLabels([header_text, self.tr("Value")])

        new_table.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        new_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        new_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        new_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
        new_table.horizontalHeader().setDefaultSectionSize(80)

        # Enable keyboard navigation with Enter key
        new_table.setTabKeyNavigation(True)

        # Install event filter to handle key presses for multiple cell editing
        new_table.installEventFilter(self)

        # Set the property for the new table
        new_table.setProperty("categoryName", category_name)

        # Return the enhanced table
        table = new_table

        # Add category header as a title
        title_label = QLabel(category_name)
        title_label.setStyleSheet("""
            background-color: #3c3c64;
            color: white;
            font-weight: bold;
            padding: 5px;
            border-radius: 3px;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Set the table's title
        table.setProperty("categoryName", category_name)

        return table

    def _on_player_selection_changed(self):
        """Handle player selection change in the table."""
        # Save current player data before switching
        if self.current_player_id:
            self.logger.info(f"Saving data for current player {self.current_player_id} before switching")
            self._save_all_evaluation_data()

        selected_items = self.player_table.selectedItems()
        if not selected_items:
            return

        # Get the player ID from the selected row
        player_id = selected_items[0].data(Qt.ItemDataRole.UserRole)
        if player_id:
            self.logger.info(f"Selected player ID: {player_id}")
            self.current_player_id = player_id

            # Only load data for the current tab
            current_tab_index = self.evaluation_tabs.currentIndex()

            # Use QTimer to allow the UI to update before loading data
            QTimer.singleShot(50, lambda: self._load_current_tab_data(player_id, current_tab_index))

    def _load_current_tab_data(self, player_id, tab_index):
        """Load data only for the current tab."""
        if tab_index <= 2:  # Evaluation tabs
            period = ["1st", "2nd", "3rd"][tab_index]
            # Load data for the current period
            self._populate_evaluation_tables(period, player_id)
        elif tab_index == 3:  # Progress tab
            # For Progress tab, load it on demand
            self._populate_progress_tab(player_id)

    def _init_progress_tab(self, tab):
        """Initialize the Progress tab with visualization widgets."""
        # Main layout for the tab
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)  # Minimal spacing between widgets

        # Add a title label
        title_label = QLabel(self.tr("Player Progress Dashboard"))
        title_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: white;
            background-color: #3c3c64;
            padding: 6px;
            border-radius: 4px;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # Create a stacked widget to manage different states (placeholder, loading, content)
        self.progress_stacked_widget = QStackedWidget()
        main_layout.addWidget(self.progress_stacked_widget)

        # Create placeholder page
        placeholder_page = QWidget()
        placeholder_layout = QVBoxLayout(placeholder_page)
        self.progress_placeholder = QLabel(self.tr("Progress visualization will appear here when a player is selected."))
        self.progress_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.progress_placeholder.setStyleSheet("font-style: italic; color: gray; margin: 50px;")
        placeholder_layout.addWidget(self.progress_placeholder)
        self.progress_stacked_widget.addWidget(placeholder_page)

        # Create loading page
        loading_page = QWidget()
        loading_layout = QVBoxLayout(loading_page)
        loading_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Create a container widget for the loading animation and text
        loading_container = QWidget()
        loading_container_layout = QVBoxLayout(loading_container)
        loading_container_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        loading_container.setStyleSheet("""
            background-color: #3c3c64;
            border-radius: 8px;
            padding: 20px;
            margin: 50px;
        """)

        # Create loading spinner animation
        self.loading_spinner = QLabel()
        self.loading_spinner.setFixedSize(48, 48)
        self.loading_spinner.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Create a timer to update the spinner animation
        self.spinner_timer = QTimer(self)
        self.spinner_timer.timeout.connect(self._update_spinner)
        self.spinner_frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        self.spinner_index = 0

        # Set initial spinner frame
        self.loading_spinner.setStyleSheet("""
            font-size: 36px;
            color: white;
            font-weight: bold;
        """)
        self.loading_spinner.setText(self.spinner_frames[0])

        # Create loading text
        self.loading_label = QLabel(self.tr("Loading progress data..."))
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet("""
            font-size: 14px;
            color: white;
            font-weight: bold;
            margin-top: 10px;
        """)

        # Add spinner and text to container
        loading_container_layout.addWidget(self.loading_spinner)
        loading_container_layout.addWidget(self.loading_label)

        # Add container to loading page
        loading_layout.addWidget(loading_container)

        self.progress_stacked_widget.addWidget(loading_page)

        # Create content page
        content_page = QWidget()
        content_layout = QVBoxLayout(content_page)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(10)

        # Create a grid layout for the dashboard widgets
        self.progress_grid_layout = QGridLayout()
        self.progress_grid_layout.setSpacing(5)  # Minimal spacing between grid items
        self.progress_grid_layout.setVerticalSpacing(3)  # Even smaller vertical spacing
        content_layout.addLayout(self.progress_grid_layout)

        self.progress_stacked_widget.addWidget(content_page)

        # Set initial state to placeholder
        self.progress_stacked_widget.setCurrentIndex(0)  # Show placeholder initially

    def _on_tab_changed(self, index):
        """Handle tab change in the evaluation tabs."""
        if not self.current_player_id:
            return

        # Save data from the previous tab
        self.logger.info(f"Saving data before switching tabs for player {self.current_player_id}")
        self._save_all_evaluation_data()

        # Load data for the new tab
        self._load_current_tab_data(self.current_player_id, index)

    def _update_spinner(self):
        """Update the spinner animation frame."""
        self.spinner_index = (self.spinner_index + 1) % len(self.spinner_frames)
        self.loading_spinner.setText(self.spinner_frames[self.spinner_index])

    def _finish_loading(self, target_index):
        """Finish the loading process and switch to the target page."""
        # Stop the spinner animation
        self.spinner_timer.stop()

        # Cancel any timeout timer
        if hasattr(self, 'timeout_timer') and self.timeout_timer.isActive():
            self.timeout_timer.stop()

        # Switch to the target page
        self.progress_stacked_widget.setCurrentIndex(target_index)

        # Process events to ensure UI is updated
        QApplication.processEvents()

    def _handle_progress_timeout(self, player_id):
        """Handle timeout when loading progress data takes too long."""
        self.logger.error(f"Timeout occurred while loading progress data for player {player_id}")

        # Stop the spinner animation
        self.spinner_timer.stop()

        # Clear the grid layout
        for i in reversed(range(self.progress_grid_layout.count())):
            item = self.progress_grid_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)

        # Create timeout message container
        timeout_container = QWidget()
        timeout_layout = QVBoxLayout(timeout_container)

        # Show timeout message
        timeout_label = QLabel(self.tr("Loading progress data is taking too long."))
        timeout_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        timeout_label.setStyleSheet("""
            color: #664400;
            font-weight: bold;
            font-size: 14px;
            background-color: #fff8e1;
            border: 1px solid #ffe082;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        """)
        timeout_layout.addWidget(timeout_label)

        # Add retry button
        retry_button = QPushButton(self.tr("Try Again"))
        retry_button.setMinimumWidth(150)
        retry_button.setStyleSheet("""
            QPushButton {
                background-color: #3c3c64;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #4c4c74;
            }
            QPushButton:pressed {
                background-color: #2c2c54;
            }
        """)

        # Connect retry button to reload the progress tab
        retry_button.clicked.connect(lambda: self._populate_progress_tab(player_id))

        # Add button to layout with center alignment
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.addStretch(1)
        button_layout.addWidget(retry_button)
        button_layout.addStretch(1)
        timeout_layout.addWidget(button_container)

        # Add simplified view option
        simplified_label = QLabel(self.tr("Or try a simplified view:"))
        simplified_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        timeout_layout.addWidget(simplified_label)

        simplified_button = QPushButton(self.tr("Load Summary Only"))
        simplified_button.setMinimumWidth(150)
        simplified_button.setStyleSheet("""
            QPushButton {
                background-color: #607d8b;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #78909c;
            }
            QPushButton:pressed {
                background-color: #455a64;
            }
        """)

        # Connect simplified button to load only summary data
        simplified_button.clicked.connect(lambda: self._load_simplified_progress(player_id))

        # Add button to layout with center alignment
        simplified_container = QWidget()
        simplified_layout = QHBoxLayout(simplified_container)
        simplified_layout.addStretch(1)
        simplified_layout.addWidget(simplified_button)
        simplified_layout.addStretch(1)
        timeout_layout.addWidget(simplified_container)

        # Add timeout container to grid layout
        self.progress_grid_layout.addWidget(timeout_container, 0, 0, 1, 2)

        # Switch to content page (which now shows the timeout message)
        self.progress_stacked_widget.setCurrentIndex(2)

    def _load_simplified_progress(self, player_id):
        """Load a simplified version of the progress tab with only summary data."""
        self.logger.info(f"Loading simplified progress view for player {player_id}")

        try:
            # Show loading state
            self.progress_stacked_widget.setCurrentIndex(1)  # Switch to loading page

            # Start the spinner animation
            self.spinner_timer.start(100)  # Update every 100ms

            # Record the start time
            self.loading_start_time = QDateTime.currentMSecsSinceEpoch()

            # Clear any cached values from previous players
            if hasattr(self, '_field_value_cache'):
                self._field_value_cache.clear()

            if hasattr(self, '_category_avg_cache'):
                self._category_avg_cache.clear()

            if hasattr(self, '_trend_item_cache'):
                self._trend_item_cache.clear()

            # Process events to ensure the loading indicator is displayed
            QApplication.processEvents()

            # Clear existing widgets from the grid layout
            for i in reversed(range(self.progress_grid_layout.count())):
                item = self.progress_grid_layout.itemAt(i)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.setParent(None)

            # Get player name
            player_name = ""
            for player in self.filtered_players:
                if player['player_id'] == player_id:
                    player_name = f"{player['first_name']} {player['last_name']}"
                    break

            # Create a header with player name
            header_text = f"Progress Dashboard for: {player_name} (Simplified View)"
            header_label = QLabel(header_text)
            header_label.setStyleSheet("""
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
            """)
            header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.progress_grid_layout.addWidget(header_label, 0, 0, 1, 2)

            # Load evaluation data for all periods (with a shorter timeout)
            player_data = {}

            # Use a separate thread or process to load data with a timeout
            for period in ["1st", "2nd", "3rd"]:
                try:
                    # Load data with a short timeout
                    data = self._load_player_evaluation_data(player_id, period)
                    player_data[period] = data
                except Exception as e:
                    self.logger.error(f"Error loading data for period {period}: {e}")
                    player_data[period] = {}  # Use empty dict if loading fails

                # Process events to keep UI responsive
                QApplication.processEvents()

            # Check if we have data to display
            has_data = any(data for data in player_data.values())

            if not has_data:
                # Show a message if no data is available
                no_data_label = QLabel(self.tr("No evaluation data available for this player."))
                no_data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                no_data_label.setStyleSheet("font-style: italic; color: gray; margin: 50px;")
                self.progress_grid_layout.addWidget(no_data_label, 1, 0, 1, 2)

                # Switch to content page
                self._finish_loading(2)
                return

            # Create a simple summary table
            summary_table = QTableWidget()
            summary_table.setColumnCount(4)
            summary_table.setHorizontalHeaderLabels([
                self.tr("Category"),
                self.tr("1st Eval"),
                self.tr("2nd Eval"),
                self.tr("3rd Eval")
            ])

            # Add rows for each category
            categories = [
                ("FOOTBALL SKILLS", self.football_skills_groups),
                ("PHYSICAL CONDITION", self.physical_condition_groups),
                ("MENTAL ABILITY", self.mental_ability_groups)
            ]

            # Check if player is a goalkeeper
            for player in self.filtered_players:
                if player['player_id'] == player_id and player.get('position') == 'GK':
                    categories.append(("GOALKEEPER SKILLS", self.goalkeeper_skills_groups))
                    break

            summary_table.setRowCount(len(categories))

            # Pre-calculate all averages at once to avoid repeated calculations
            category_averages = {}
            for category_name, _ in categories:
                normalized_category = category_name.lower().replace(" ", "_")
                category_averages[category_name] = {}

                for period in ["1st", "2nd", "3rd"]:
                    avg_value = self._calculate_category_average(player_data[period], normalized_category)
                    category_averages[category_name][period] = avg_value

            # Fill the table with average values
            for row, (category_name, _) in enumerate(categories):
                # Set category name
                category_item = QTableWidgetItem(self.tr(category_name))
                category_item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
                summary_table.setItem(row, 0, category_item)

                # Set average values for each period
                for col, period in enumerate(["1st", "2nd", "3rd"], 1):
                    avg_value = category_averages[category_name][period]

                    if avg_value is not None:
                        value_item = QTableWidgetItem(f"{avg_value:.1f}")

                        # Set background color based on value
                        if avg_value >= 9.0:
                            value_item.setBackground(QColor(0, 150, 0))  # Dark green
                            value_item.setForeground(QColor(255, 255, 255))
                        elif avg_value >= 7.0:
                            value_item.setBackground(QColor(100, 200, 100))  # Light green
                            value_item.setForeground(QColor(0, 0, 0))
                        elif avg_value >= 4.0:
                            value_item.setBackground(QColor(255, 255, 0))  # Yellow
                            value_item.setForeground(QColor(0, 0, 0))
                        else:
                            value_item.setBackground(QColor(255, 100, 100))  # Red
                            value_item.setForeground(QColor(0, 0, 0))
                    else:
                        value_item = QTableWidgetItem("-")

                    value_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    summary_table.setItem(row, col, value_item)

            # Set table properties
            summary_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
            for i in range(1, 4):
                summary_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Fixed)
                summary_table.horizontalHeader().resizeSection(i, 80)

            summary_table.verticalHeader().setVisible(False)
            summary_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

            # Add the table to the grid
            self.progress_grid_layout.addWidget(summary_table, 1, 0, 1, 2)

            # Add a note about simplified view
            note_label = QLabel(self.tr("Note: This is a simplified view showing only category averages. For detailed progress comparison, try the full view."))
            note_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            note_label.setStyleSheet("font-style: italic; color: gray; margin-top: 20px;")
            self.progress_grid_layout.addWidget(note_label, 2, 0, 1, 2)

            # Add a button to try loading the full view
            full_view_button = QPushButton(self.tr("Try Loading Full View"))
            full_view_button.setMinimumWidth(200)
            full_view_button.setStyleSheet("""
                QPushButton {
                    background-color: #3c3c64;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 4px;
                    margin-top: 10px;
                }
                QPushButton:hover {
                    background-color: #4c4c74;
                }
                QPushButton:pressed {
                    background-color: #2c2c54;
                }
            """)

            # Connect button to reload the full progress tab
            full_view_button.clicked.connect(lambda: self._populate_progress_tab(player_id))

            # Add button to layout with center alignment
            button_container = QWidget()
            button_layout = QHBoxLayout(button_container)
            button_layout.addStretch(1)
            button_layout.addWidget(full_view_button)
            button_layout.addStretch(1)
            self.progress_grid_layout.addWidget(button_container, 3, 0, 1, 2)

            # Calculate how long we've been loading
            current_time = QDateTime.currentMSecsSinceEpoch()
            elapsed_time = current_time - self.loading_start_time

            # Ensure loading is shown for at least 500ms for better UX
            min_loading_time = 500  # milliseconds

            if elapsed_time < min_loading_time:
                # Wait for the remaining time before showing content
                remaining_time = min_loading_time - elapsed_time
                QTimer.singleShot(remaining_time, lambda: self._finish_loading(2))
            else:
                # Show content immediately
                self._finish_loading(2)

        except Exception as e:
            # Handle any errors
            self.logger.error(f"Error loading simplified progress view: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

            # Clear the grid layout
            for i in reversed(range(self.progress_grid_layout.count())):
                item = self.progress_grid_layout.itemAt(i)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.setParent(None)

            # Show error message
            error_label = QLabel(self.tr("Error loading simplified progress data."))
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("""
                color: red;
                font-weight: bold;
                font-size: 14px;
                background-color: #ffeeee;
                border: 1px solid #ffcccc;
                border-radius: 4px;
                padding: 10px;
            """)
            self.progress_grid_layout.addWidget(error_label, 0, 0, 1, 2)

            # Add retry button
            retry_button = QPushButton(self.tr("Try Again"))
            retry_button.setMinimumWidth(150)
            retry_button.clicked.connect(lambda: self._load_simplified_progress(player_id))

            # Add button to layout with center alignment
            button_container = QWidget()
            button_layout = QHBoxLayout(button_container)
            button_layout.addStretch(1)
            button_layout.addWidget(retry_button)
            button_layout.addStretch(1)
            self.progress_grid_layout.addWidget(button_container, 1, 0, 1, 2)

            # Show error immediately
            self._finish_loading(2)

    def _populate_progress_tab(self, player_id):
        """Populate the Progress tab with visualization widgets for the selected player."""
        self.logger.info(f"Populating Progress tab for player {player_id}")

        # Cancel any existing timeout timer
        if hasattr(self, 'timeout_timer') and self.timeout_timer.isActive():
            self.timeout_timer.stop()

        try:
            # Show loading state
            self.progress_stacked_widget.setCurrentIndex(1)  # Switch to loading page

            # Start the spinner animation
            self.spinner_timer.start(100)  # Update every 100ms

            # Record the start time to ensure minimum loading display time
            self.loading_start_time = QDateTime.currentMSecsSinceEpoch()

            # Clear any cached values from previous players
            if hasattr(self, '_field_value_cache'):
                self._field_value_cache.clear()

            if hasattr(self, '_category_avg_cache'):
                self._category_avg_cache.clear()

            if hasattr(self, '_trend_item_cache'):
                self._trend_item_cache.clear()

            # Set a timeout timer to prevent hanging indefinitely
            self.timeout_timer = QTimer(self)
            self.timeout_timer.setSingleShot(True)
            self.timeout_timer.timeout.connect(lambda: self._handle_progress_timeout(player_id))
            self.timeout_timer.start(15000)  # 15 seconds timeout

            # Process events to ensure the loading indicator is displayed
            QApplication.processEvents()

            # Clear existing widgets from the grid layout
            for i in reversed(range(self.progress_grid_layout.count())):
                item = self.progress_grid_layout.itemAt(i)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.setParent(None)

            # Get player name
            player_name = ""
            for player in self.filtered_players:
                if player['player_id'] == player_id:
                    player_name = f"{player['first_name']} {player['last_name']}"
                    break

            # Create a header with player name
            header_text = f"Progress Dashboard for: {player_name}"
            header_label = QLabel(header_text)
            header_label.setStyleSheet("""
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
            """)
            header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.progress_grid_layout.addWidget(header_label, 0, 0, 1, 2)

            # Load evaluation data for all periods
            player_data = {
                "1st": self._load_player_evaluation_data(player_id, "1st"),
                "2nd": self._load_player_evaluation_data(player_id, "2nd"),
                "3rd": self._load_player_evaluation_data(player_id, "3rd")
            }

            # Check if we have data to display
            has_data = any(data for data in player_data.values())

            if not has_data:
                # Calculate how long we've been loading
                current_time = QDateTime.currentMSecsSinceEpoch()
                elapsed_time = current_time - self.loading_start_time

                # Ensure loading is shown for at least 600ms for better UX
                min_loading_time = 600  # milliseconds

                # Clear the grid layout
                for i in reversed(range(self.progress_grid_layout.count())):
                    item = self.progress_grid_layout.itemAt(i)
                    if item:
                        widget = item.widget()
                        if widget:
                            widget.setParent(None)

                # Show a message if no data is available
                no_data_label = QLabel(self.tr("No evaluation data available for this player."))
                no_data_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                no_data_label.setStyleSheet("font-style: italic; color: gray; margin: 50px;")
                self.progress_grid_layout.addWidget(no_data_label, 0, 0, 1, 2)

                if elapsed_time < min_loading_time:
                    # Wait for the remaining time before showing message
                    remaining_time = min_loading_time - elapsed_time
                    QTimer.singleShot(remaining_time, lambda: self._finish_loading(2))
                else:
                    # Show message immediately
                    self._finish_loading(2)
                return

            # Create a simple summary table for now
            summary_table = QTableWidget()
            summary_table.setColumnCount(4)
            summary_table.setHorizontalHeaderLabels([
                self.tr("Category"),
                self.tr("1st Eval"),
                self.tr("2nd Eval"),
                self.tr("3rd Eval")
            ])

            # Add rows for each category
            categories = [
                ("FOOTBALL SKILLS", self.football_skills_groups),
                ("PHYSICAL CONDITION", self.physical_condition_groups),
                ("MENTAL ABILITY", self.mental_ability_groups)
            ]

            # Check if player is a goalkeeper
            for player in self.filtered_players:
                if player['player_id'] == player_id and player.get('position') == 'GK':
                    categories.append(("GOALKEEPER SKILLS", self.goalkeeper_skills_groups))
                    break

            summary_table.setRowCount(len(categories))

            # Pre-calculate all averages at once to avoid repeated calculations
            category_averages = {}
            for category_name, _ in categories:
                normalized_category = category_name.lower().replace(" ", "_")
                category_averages[category_name] = {}

                for period in ["1st", "2nd", "3rd"]:
                    avg_value = self._calculate_category_average(player_data[period], normalized_category)
                    category_averages[category_name][period] = avg_value

            # Fill the table with average values
            for row, (category_name, _) in enumerate(categories):
                # Set category name
                category_item = QTableWidgetItem(self.tr(category_name))
                category_item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
                summary_table.setItem(row, 0, category_item)

                # Set average values for each period
                for col, period in enumerate(["1st", "2nd", "3rd"], 1):
                    avg_value = category_averages[category_name][period]

                    if avg_value is not None:
                        value_item = QTableWidgetItem(f"{avg_value:.1f}")

                        # Set background color based on value
                        if avg_value >= 9.0:
                            value_item.setBackground(QColor(0, 150, 0))  # Dark green
                            value_item.setForeground(QColor(255, 255, 255))
                        elif avg_value >= 7.0:
                            value_item.setBackground(QColor(100, 200, 100))  # Light green
                            value_item.setForeground(QColor(0, 0, 0))
                        elif avg_value >= 4.0:
                            value_item.setBackground(QColor(255, 255, 0))  # Yellow
                            value_item.setForeground(QColor(0, 0, 0))
                        else:
                            value_item.setBackground(QColor(255, 100, 100))  # Red
                            value_item.setForeground(QColor(0, 0, 0))
                    else:
                        value_item = QTableWidgetItem("-")

                    value_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    summary_table.setItem(row, col, value_item)

            # Set table properties
            summary_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
            for i in range(1, 4):
                summary_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Fixed)
                summary_table.horizontalHeader().resizeSection(i, 80)

            summary_table.verticalHeader().setVisible(False)
            summary_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

            # Add the table to the grid
            self.progress_grid_layout.addWidget(summary_table, 1, 0, 1, 2)

            # Create a detailed progress comparison table
            self._create_progress_comparison_table(player_id, player_data, 2, 0)

            # Calculate how long we've been loading
            current_time = QDateTime.currentMSecsSinceEpoch()
            elapsed_time = current_time - self.loading_start_time

            # Ensure loading is shown for at least 800ms for better UX
            min_loading_time = 800  # milliseconds

            if elapsed_time < min_loading_time:
                # Wait for the remaining time before showing content
                remaining_time = min_loading_time - elapsed_time

                # Use a single-shot timer to show content after the delay
                QTimer.singleShot(remaining_time, lambda: self._finish_loading(2))
            else:
                # Stop the spinner and show content immediately
                self._finish_loading(2)

        except Exception as e:
            # Handle any errors that occur during progress tab population
            self.logger.error(f"Error populating progress tab: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

            # Calculate how long we've been loading
            current_time = QDateTime.currentMSecsSinceEpoch()
            elapsed_time = current_time - self.loading_start_time

            # Ensure loading is shown for at least 500ms for better UX (shorter for errors)
            min_loading_time = 500  # milliseconds

            # Clear the grid layout
            for i in reversed(range(self.progress_grid_layout.count())):
                item = self.progress_grid_layout.itemAt(i)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.setParent(None)

            # Create error message container
            error_container = QWidget()
            error_layout = QVBoxLayout(error_container)

            # Show error message in the progress tab
            error_label = QLabel(self.tr("Error loading progress data."))
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("""
                color: red;
                font-weight: bold;
                font-size: 14px;
                background-color: #ffeeee;
                border: 1px solid #ffcccc;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 10px;
            """)
            error_layout.addWidget(error_label)

            # Add retry button
            retry_button = QPushButton(self.tr("Retry Loading"))
            retry_button.setMinimumWidth(150)
            retry_button.setStyleSheet("""
                QPushButton {
                    background-color: #3c3c64;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #4c4c74;
                }
                QPushButton:pressed {
                    background-color: #2c2c54;
                }
            """)

            # Connect retry button to reload the progress tab
            retry_button.clicked.connect(lambda: self._populate_progress_tab(player_id))

            # Add button to layout with center alignment
            button_container = QWidget()
            button_layout = QHBoxLayout(button_container)
            button_layout.addStretch(1)
            button_layout.addWidget(retry_button)
            button_layout.addStretch(1)
            error_layout.addWidget(button_container)

            # Add error container to grid layout
            self.progress_grid_layout.addWidget(error_container, 0, 0, 1, 2)

            if elapsed_time < min_loading_time:
                # Wait for the remaining time before showing error
                remaining_time = min_loading_time - elapsed_time
                QTimer.singleShot(remaining_time, lambda: self._finish_loading(2))
            else:
                # Show error immediately
                self._finish_loading(2)

    def _create_progress_comparison_table(self, player_id, player_data, row, col):
        """Create a detailed progress comparison table with trend indicators."""
        # Create a group box for the comparison table
        group_box = QGroupBox(self.tr("Detailed Progress Comparison"))
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cccccc;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px;
            }
        """)

        # Create layout for the group box
        group_layout = QVBoxLayout(group_box)
        group_layout.setContentsMargins(3, 10, 3, 3)  # Reduced margins
        group_layout.setSpacing(2)  # Minimal spacing

        # Create a widget to hold the table and controls
        container_widget = QWidget()
        container_layout = QVBoxLayout(container_widget)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(5)

        # Add category filter dropdown
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 0, 0, 5)

        filter_label = QLabel(self.tr("Filter by Category:"))
        filter_layout.addWidget(filter_label)

        self.category_filter = QComboBox()
        self.category_filter.addItem(self.tr("All Categories"), "all")
        self.category_filter.addItem(self.tr("Football Skills"), "football_skills")
        self.category_filter.addItem(self.tr("Physical Condition"), "physical_condition")
        self.category_filter.addItem(self.tr("Mental Ability"), "mental_ability")

        # Check if player is a goalkeeper
        is_goalkeeper = False
        for player in self.filtered_players:
            if player['player_id'] == player_id and player.get('position') == 'GK':
                is_goalkeeper = True
                self.category_filter.addItem(self.tr("Goalkeeper Skills"), "goalkeeper_skills")
                break

        self.category_filter.currentIndexChanged.connect(self._on_category_filter_changed)
        filter_layout.addWidget(self.category_filter)

        # Add search box
        search_label = QLabel(self.tr("Search:"))
        filter_layout.addWidget(search_label)

        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText(self.tr("Search fields..."))
        self.search_box.textChanged.connect(self._on_search_text_changed)
        filter_layout.addWidget(self.search_box)

        filter_layout.addStretch(1)

        container_layout.addLayout(filter_layout)

        # Create the comparison table
        self.comparison_table = QTableWidget()
        self.comparison_table.setColumnCount(7)
        self.comparison_table.setHorizontalHeaderLabels([
            self.tr("Field"),
            self.tr("1st"),
            self.tr("2nd"),
            self.tr("1→2"),
            self.tr("3rd"),
            self.tr("2→3"),
            self.tr("Overall")
        ])

        # Store player data for filtering
        self.progress_player_data = player_data

        # Get all unique fields across all periods
        self.all_progress_fields = []
        self.field_categories = {}  # Map fields to their categories

        # Process each category separately
        categories = ["football_skills", "physical_condition", "mental_ability"]
        if is_goalkeeper:
            categories.append("goalkeeper_skills")

        for category in categories:
            category_fields = set()

            # Get fields from all periods
            for period_data in player_data.values():
                for key in period_data.keys():
                    if key.startswith(f"{category}/"):
                        # Extract the field name from the key
                        parts = key.split('/')
                        if len(parts) >= 2:
                            field_name = parts[-1]  # Get the last part as the field name
                            category_fields.add(field_name)
                            self.field_categories[field_name] = category

            # Add sorted fields from this category
            self.all_progress_fields.extend(sorted(category_fields))

        # Populate the table with initial data (all fields)
        self._populate_comparison_table(self.all_progress_fields)

        # Set table properties
        self.comparison_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        for i in range(1, 7):
            self.comparison_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Fixed)
            self.comparison_table.horizontalHeader().resizeSection(i, 60)

        self.comparison_table.verticalHeader().setVisible(False)
        self.comparison_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.comparison_table.setAlternatingRowColors(True)

        # Add the table to the container
        container_layout.addWidget(self.comparison_table)

        # Add pagination controls if there are many fields
        if len(self.all_progress_fields) > 50:
            pagination_layout = QHBoxLayout()
            pagination_layout.setContentsMargins(0, 5, 0, 0)

            self.page_info_label = QLabel()
            pagination_layout.addWidget(self.page_info_label)

            pagination_layout.addStretch(1)

            self.prev_page_button = QPushButton(self.tr("Previous"))
            self.prev_page_button.clicked.connect(self._on_prev_page)
            pagination_layout.addWidget(self.prev_page_button)

            self.next_page_button = QPushButton(self.tr("Next"))
            self.next_page_button.clicked.connect(self._on_next_page)
            pagination_layout.addWidget(self.next_page_button)

            container_layout.addLayout(pagination_layout)

            # Initialize pagination
            self.current_page = 1
            self.items_per_page = 50
            self._update_pagination()

        # Add the container to the group box
        group_layout.addWidget(container_widget)

        # Add the group box to the grid
        self.progress_grid_layout.addWidget(group_box, row, col, 1, 2)

    def _populate_comparison_table(self, fields, page=1, items_per_page=None):
        """Populate the comparison table with the given fields."""
        # Apply pagination if specified
        if items_per_page:
            start_idx = (page - 1) * items_per_page
            end_idx = start_idx + items_per_page
            display_fields = fields[start_idx:end_idx]
        else:
            display_fields = fields

        # Set row count
        self.comparison_table.setRowCount(len(display_fields))

        # Fill the table with values and trend indicators
        for row_idx, field in enumerate(display_fields):
            # Set field name
            field_item = QTableWidgetItem(field)
            field_item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            self.comparison_table.setItem(row_idx, 0, field_item)

            # Get values for each period
            values = {}
            for period in ["1st", "2nd", "3rd"]:
                value = self._get_field_value(self.progress_player_data[period], field)
                values[period] = value

                # Set value in table
                if value is not None:
                    value_item = QTableWidgetItem(str(value))
                else:
                    value_item = QTableWidgetItem("-")

                # Center-align the value
                value_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                # Set column based on period
                col_idx = {"1st": 1, "2nd": 2, "3rd": 4}[period]
                self.comparison_table.setItem(row_idx, col_idx, value_item)

            # Calculate and set trend indicators
            # 1st to 2nd comparison
            if values["1st"] is not None and values["2nd"] is not None:
                diff_1_2 = values["2nd"] - values["1st"]
                trend_1_2 = self._create_trend_item(diff_1_2)
                self.comparison_table.setItem(row_idx, 3, trend_1_2)
            else:
                item = QTableWidgetItem("-")
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.comparison_table.setItem(row_idx, 3, item)

            # 2nd to 3rd comparison
            if values["2nd"] is not None and values["3rd"] is not None:
                diff_2_3 = values["3rd"] - values["2nd"]
                trend_2_3 = self._create_trend_item(diff_2_3)
                self.comparison_table.setItem(row_idx, 5, trend_2_3)
            else:
                item = QTableWidgetItem("-")
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.comparison_table.setItem(row_idx, 5, item)

            # Overall comparison (1st to 3rd)
            if values["1st"] is not None and values["3rd"] is not None:
                diff_overall = values["3rd"] - values["1st"]
                trend_overall = self._create_trend_item(diff_overall)
                self.comparison_table.setItem(row_idx, 6, trend_overall)
            else:
                item = QTableWidgetItem("-")
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.comparison_table.setItem(row_idx, 6, item)

    def _on_category_filter_changed(self, _):
        """Handle category filter change."""
        category = self.category_filter.currentData()

        # Apply filter
        if category == "all":
            filtered_fields = self.all_progress_fields
        else:
            filtered_fields = [field for field in self.all_progress_fields
                              if self.field_categories.get(field) == category]

        # Apply search filter if there's text in the search box
        search_text = self.search_box.text().strip().lower()
        if search_text:
            filtered_fields = [field for field in filtered_fields
                              if search_text in field.lower()]

        # Update the table with filtered fields
        if hasattr(self, 'items_per_page'):
            self.current_page = 1  # Reset to first page
            self._populate_comparison_table(filtered_fields, self.current_page, self.items_per_page)
            self._update_pagination(filtered_fields)
        else:
            self._populate_comparison_table(filtered_fields)

    def _on_search_text_changed(self, text):
        """Handle search text change."""
        search_text = text.strip().lower()
        category = self.category_filter.currentData()

        # Apply category filter
        if category == "all":
            filtered_fields = self.all_progress_fields
        else:
            filtered_fields = [field for field in self.all_progress_fields
                              if self.field_categories.get(field) == category]

        # Apply search filter
        if search_text:
            filtered_fields = [field for field in filtered_fields
                              if search_text in field.lower()]

        # Update the table with filtered fields
        if hasattr(self, 'items_per_page'):
            self.current_page = 1  # Reset to first page
            self._populate_comparison_table(filtered_fields, self.current_page, self.items_per_page)
            self._update_pagination(filtered_fields)
        else:
            self._populate_comparison_table(filtered_fields)

    def _on_prev_page(self):
        """Go to the previous page."""
        if self.current_page > 1:
            self.current_page -= 1

            # Get current filtered fields
            category = self.category_filter.currentData()
            search_text = self.search_box.text().strip().lower()

            # Apply filters
            if category == "all":
                filtered_fields = self.all_progress_fields
            else:
                filtered_fields = [field for field in self.all_progress_fields
                                  if self.field_categories.get(field) == category]

            if search_text:
                filtered_fields = [field for field in filtered_fields
                                  if search_text in field.lower()]

            # Update table and pagination
            self._populate_comparison_table(filtered_fields, self.current_page, self.items_per_page)
            self._update_pagination(filtered_fields)

    def _on_next_page(self):
        """Go to the next page."""
        # Get current filtered fields
        category = self.category_filter.currentData()
        search_text = self.search_box.text().strip().lower()

        # Apply filters
        if category == "all":
            filtered_fields = self.all_progress_fields
        else:
            filtered_fields = [field for field in self.all_progress_fields
                              if self.field_categories.get(field) == category]

        if search_text:
            filtered_fields = [field for field in filtered_fields
                              if search_text in field.lower()]

        # Calculate total pages
        total_pages = (len(filtered_fields) + self.items_per_page - 1) // self.items_per_page

        if self.current_page < total_pages:
            self.current_page += 1
            self._populate_comparison_table(filtered_fields, self.current_page, self.items_per_page)
            self._update_pagination(filtered_fields)

    def _update_pagination(self, fields=None):
        """Update pagination controls and info."""
        if fields is None:
            fields = self.all_progress_fields

        total_items = len(fields)
        total_pages = (total_items + self.items_per_page - 1) // self.items_per_page

        start_item = (self.current_page - 1) * self.items_per_page + 1
        end_item = min(start_item + self.items_per_page - 1, total_items)

        self.page_info_label.setText(
            self.tr(f"Showing {start_item}-{end_item} of {total_items} items (Page {self.current_page} of {total_pages})")
        )

        # Enable/disable pagination buttons
        self.prev_page_button.setEnabled(self.current_page > 1)
        self.next_page_button.setEnabled(self.current_page < total_pages)

    def _get_field_value(self, period_data, field_name):
        """Get the value for a specific field from period data."""
        # Check if we have a cached field value map
        if not hasattr(self, '_field_value_cache'):
            self._field_value_cache = {}

        # Create a cache key
        cache_key = f"{id(period_data)}:{field_name}"

        # Check if we have a cached result
        if cache_key in self._field_value_cache:
            return self._field_value_cache[cache_key]

        # If not in cache, search for the field
        result = None

        # Try different key formats
        for key in period_data.keys():
            parts = key.split('/')
            if parts[-1] == field_name:
                try:
                    result = float(period_data[key])
                    break
                except (ValueError, TypeError):
                    result = None
                    break

        # Cache the result
        self._field_value_cache[cache_key] = result

        return result

    def _create_trend_item(self, diff):
        """Create a table item with trend indicator based on the difference."""
        # Cache trend items to avoid creating new ones for the same difference values
        if not hasattr(self, '_trend_item_cache'):
            self._trend_item_cache = {}

        # Check if we have a cached item for this difference
        cache_key = round(diff, 1)  # Round to 1 decimal place for caching
        if cache_key in self._trend_item_cache:
            # Clone the cached item to avoid reference issues
            cached_item = self._trend_item_cache[cache_key]
            item = QTableWidgetItem(cached_item.text())
            item.setForeground(cached_item.foreground())
            item.setTextAlignment(cached_item.textAlignment())
            return item

        # Create a new item if not in cache
        if diff > 0:
            # Improvement
            if diff >= 3:
                text = f"+{diff:.1f} ↑↑"  # Significant improvement
                color = get_color("trend_significant_improvement")
            else:
                text = f"+{diff:.1f} ↑"  # Moderate improvement
                color = get_color("trend_moderate_improvement")
        elif diff < 0:
            # Regression
            if diff <= -3:
                text = f"{diff:.1f} ↓↓"  # Significant regression
                color = get_color("trend_significant_regression")
            else:
                text = f"{diff:.1f} ↓"  # Moderate regression
                color = get_color("trend_moderate_regression")
        else:
            # No change
            text = "0 →"
            color = get_color("trend_no_change")

        item = QTableWidgetItem(text)
        item.setForeground(color)
        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

        # Cache the item for future use
        self._trend_item_cache[cache_key] = item

        return item

    def _load_player_evaluation_data(self, player_id, period):
        """Load all evaluation data for a player and period."""
        settings = QSettings()
        player_data = {}

        # Load data for each category
        categories = ["football_skills", "physical_condition", "mental_ability", "goalkeeper_skills"]

        for category in categories:
            # Use a single beginGroup call to avoid redundant operations
            settings.beginGroup(f"evaluations/{player_id}/{period}/{category}")

            # First get all direct keys at this level
            for key in settings.childKeys():
                player_data[f"{category}/{key}"] = settings.value(key)

            # Then process all groups
            for group in settings.childGroups():
                settings.beginGroup(group)
                for key in settings.childKeys():
                    player_data[f"{category}/{group}/{key}"] = settings.value(key)
                settings.endGroup()

            # End the category group
            settings.endGroup()

        return player_data

    def _calculate_category_average(self, player_data, category):
        """Calculate the average value for a category from player data."""
        # Check if we have a cached category average
        if not hasattr(self, '_category_avg_cache'):
            self._category_avg_cache = {}

        # Create a cache key
        cache_key = f"{id(player_data)}:{category}"

        # Check if we have a cached result
        if cache_key in self._category_avg_cache:
            return self._category_avg_cache[cache_key]

        # If not in cache, calculate the average
        if not player_data:
            self._category_avg_cache[cache_key] = None
            return None

        total = 0
        count = 0

        # Extract values for this category
        for key, value in player_data.items():
            if key.startswith(f"{category}/"):
                try:
                    val = float(value)
                    total += val
                    count += 1
                except (ValueError, TypeError):
                    pass

        # Calculate average
        if count > 0:
            result = total / count
        else:
            result = None

        # Cache the result
        self._category_avg_cache[cache_key] = result

        return result

    def _load_evaluation_data(self, player_id):
        """Load evaluation data for the selected player."""
        # This method is kept for backward compatibility
        # Get the current tab index
        current_tab_index = self.evaluation_tabs.currentIndex()

        # Use the new method that only loads data for the current tab
        self._load_current_tab_data(player_id, current_tab_index)

    def refresh_data(self):
        """Refresh the player list and evaluation data."""
        self.logger.info("Refreshing evaluation data")
        try:
            # Check if we have any selected player IDs
            self.logger.info(f"Current selected_player_ids: {self.selected_player_ids}")

            # Try to get selected player IDs directly from the database
            try:
                if self.roster_manager.conn:
                    self.roster_manager.cursor.execute("SELECT player_id FROM player_selections WHERE selection_type = 'evaluation' AND selected = 1")
                    rows = self.roster_manager.cursor.fetchall()
                    if rows:
                        direct_ids = [row[0] for row in rows]
                        self.logger.info(f"Direct DB query found {len(direct_ids)} players selected for evaluation: {direct_ids}")
                        self.selected_player_ids = direct_ids
            except Exception as e:
                self.logger.error(f"Error refreshing selected player IDs from database: {e}")

            # Reload players and update UI
            self._load_players()

        except Exception as e:
            self.logger.error(f"Error refreshing data: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _on_item_changed(self, table, item, period):
        """Handle item changed in a table."""
        # Only process value cells (column 1)
        if item.column() != 1 or not self.current_player_id:
            return

        # Find the row of this item
        row = item.row()

        # Skip if this is a header row
        if (table.item(row, 0) and
            table.item(row, 0).font().bold()):
            return

        # Log the change
        field_item = table.item(row, 0)
        if field_item:
            field_name = field_item.data(Qt.ItemDataRole.UserRole) or field_item.text().strip()
            self.logger.info(f"Item changed: {field_name} = {item.text()}")

            # Get the category from the table
            category = None
            if table == self.football_table:
                category = "football_skills"
            elif table == self.physical_table:
                category = "physical_condition"
            elif table == self.mental_table:
                category = "mental_ability"
            elif table == self.goalkeeper_table:
                category = "goalkeeper_skills"

            # Save this specific value directly
            self._save_specific_value(field_name, item.text(), period, table)

            # Mark this category as modified for batch saving
            if category:
                self._modified_categories.add(category)
                # Schedule a full save after 3 seconds of inactivity
                self._save_timer.start(3000)

        # Update the group average
        self._update_group_for_row(table, row)

        # Save the changes immediately
        self._save_table_data(table, period)

        # Force settings to sync
        QSettings().sync()

    def _normalize_field_name(self, field_name):
        """Normalize field name for consistent storage and retrieval."""
        if not field_name:
            return ""
        # First strip any whitespace
        clean_field = field_name.strip()
        # Replace any spaces with underscores for consistent key format
        normalized_field = clean_field.replace(" ", "_")
        return normalized_field

    def _get_group_for_field(self, table, field_name):
        """Find the group name for a given field in the table."""
        # Iterate through all rows to find the field and its group
        row = 0
        current_group = None

        while row < table.rowCount():
            item = table.item(row, 0)
            if not item:
                row += 1
                continue

            # Check if this is a group header
            if item.font().bold():
                current_group = item.text()
            # Check if this is our field
            elif item.text().strip() == field_name.strip() or item.data(Qt.ItemDataRole.UserRole) == field_name:
                return current_group

            row += 1

        # If we couldn't find the field in any group, return None
        return None

    def _save_specific_value(self, field_name, value, period, table):
        """Save a specific field value directly to settings."""
        if not field_name:
            return

        # Check if we're in multi-edit mode (multiple players selected)
        selected_items = self.player_table.selectedItems()
        selected_player_ids = []

        if selected_items:
            # Get all selected player IDs
            for i in range(0, len(selected_items), self.player_table.columnCount()):
                if i < len(selected_items):
                    player_id = selected_items[i].data(Qt.ItemDataRole.UserRole)
                    if player_id and player_id not in selected_player_ids:
                        selected_player_ids.append(player_id)

        # If no players are selected or we couldn't get IDs, just use the current player
        if not selected_player_ids and self.current_player_id:
            selected_player_ids = [self.current_player_id]

        if not selected_player_ids:
            self.logger.warning("No players selected for saving value")
            return

        settings = QSettings()
        category = table.property("categoryName").lower().replace(" ", "_")

        # Clean the field name
        clean_field = field_name.strip()

        # Get the group for this field to avoid conflicts with duplicate field names
        group_name = self._get_group_for_field(table, field_name)

        # Save the value for each selected player
        for player_id in selected_player_ids:
            if group_name:
                normalized_group = self._normalize_field_name(group_name)
                # Include group in the key to avoid conflicts with duplicate field names
                value_key = f"evaluations/{player_id}/{period}/{category}/{normalized_group}/{clean_field}"
            else:
                # Fallback to old format if group not found
                value_key = f"evaluations/{player_id}/{period}/{category}/{clean_field}"

            # Save the value
            if value:
                self.logger.info(f"Saving value: {value} for key: {value_key} (player: {player_id})")
                settings.setValue(value_key, value)
            else:
                self.logger.info(f"Removing empty value for key: {value_key} (player: {player_id})")
                settings.remove(value_key)

        # We don't force sync here anymore - it will be done in batch
        # Only sync if this is a multi-player edit (for immediate feedback)
        if len(selected_player_ids) > 1:
            settings.sync()

    def _update_group_for_row(self, table, row):
        """Update the group average for the group containing the given row."""
        # Find the group header row for this field
        header_row = row
        while header_row > 0:
            if (table.item(header_row, 0) and
                table.item(header_row, 0).font().bold()):
                break
            header_row -= 1

        # If we found a header, update its average
        if header_row >= 0 and table.item(header_row, 0) and table.item(header_row, 0).font().bold():
            # Find all field rows in this group
            field_values = []
            next_row = header_row + 1

            # Collect values until we hit another header or end of table
            while (next_row < table.rowCount() and
                   table.item(next_row, 0) and
                   not table.item(next_row, 0).font().bold()):

                value_item = table.item(next_row, 1)
                if value_item and value_item.text():
                    try:
                        field_values.append(float(value_item.text()))
                    except (ValueError, TypeError):
                        pass
                next_row += 1

            # Update average value
            if field_values:
                avg_value = sum(field_values) / len(field_values)
                self._update_group_average(table, header_row, avg_value)
            else:
                # Clear the average if no values
                self._update_group_average(table, header_row, None)

    def _populate_evaluation_tables(self, period, player_id):
        """Populate all three category tables for the given evaluation period."""
        # Get the tables for this period
        if period == "1st":
            football_table = self.first_football_skills_table
            physical_table = self.first_physical_condition_table
            mental_table = self.first_mental_ability_table
            goalkeeper_table = self.first_goalkeeper_skills_table
            goalkeeper_scroll = self.first_goalkeeper_scroll
        elif period == "2nd":
            football_table = self.second_football_skills_table
            physical_table = self.second_physical_condition_table
            mental_table = self.second_mental_ability_table
            goalkeeper_table = self.second_goalkeeper_skills_table
            goalkeeper_scroll = self.second_goalkeeper_scroll
        else:
            football_table = self.third_football_skills_table
            physical_table = self.third_physical_condition_table
            mental_table = self.third_mental_ability_table
            goalkeeper_table = self.third_goalkeeper_skills_table
            goalkeeper_scroll = self.third_goalkeeper_scroll

        # Clear all tables
        football_table.setRowCount(0)
        physical_table.setRowCount(0)
        mental_table.setRowCount(0)
        goalkeeper_table.setRowCount(0)

        # Use the already loaded field groups
        self.logger.debug(f"Populating tables for player {player_id}, period {period}")

        # Pre-load all evaluation data for this player and period to avoid multiple disk accesses
        settings = QSettings()
        player_data = {}

        # Load football skills data
        settings.beginGroup(f"evaluations/{player_id}/{period}/football_skills")
        for key in settings.childKeys():
            player_data[f"football_skills/{key}"] = settings.value(key)
        settings.endGroup()

        # Load group-based football skills data
        settings.beginGroup(f"evaluations/{player_id}/{period}/football_skills")
        for group in settings.childGroups():
            settings.beginGroup(group)
            for key in settings.childKeys():
                player_data[f"football_skills/{group}/{key}"] = settings.value(key)
            settings.endGroup()
        settings.endGroup()

        # Load physical condition data
        settings.beginGroup(f"evaluations/{player_id}/{period}/physical_condition")
        for key in settings.childKeys():
            player_data[f"physical_condition/{key}"] = settings.value(key)
        settings.endGroup()

        # Load group-based physical condition data
        settings.beginGroup(f"evaluations/{player_id}/{period}/physical_condition")
        for group in settings.childGroups():
            settings.beginGroup(group)
            for key in settings.childKeys():
                player_data[f"physical_condition/{group}/{key}"] = settings.value(key)
            settings.endGroup()
        settings.endGroup()

        # Load mental ability data
        settings.beginGroup(f"evaluations/{player_id}/{period}/mental_ability")
        for key in settings.childKeys():
            player_data[f"mental_ability/{key}"] = settings.value(key)
        settings.endGroup()

        # Load group-based mental ability data
        settings.beginGroup(f"evaluations/{player_id}/{period}/mental_ability")
        for group in settings.childGroups():
            settings.beginGroup(group)
            for key in settings.childKeys():
                player_data[f"mental_ability/{group}/{key}"] = settings.value(key)
            settings.endGroup()
        settings.endGroup()

        # Load goalkeeper skills data
        settings.beginGroup(f"evaluations/{player_id}/{period}/goalkeeper_skills")
        for key in settings.childKeys():
            player_data[f"goalkeeper_skills/{key}"] = settings.value(key)
        settings.endGroup()

        # Load group-based goalkeeper skills data
        settings.beginGroup(f"evaluations/{player_id}/{period}/goalkeeper_skills")
        for group in settings.childGroups():
            settings.beginGroup(group)
            for key in settings.childKeys():
                player_data[f"goalkeeper_skills/{group}/{key}"] = settings.value(key)
            settings.endGroup()
        settings.endGroup()

        # Populate Football Skills table
        self._populate_category_table(
            football_table,
            self.football_skills_groups,
            player_id,
            period,
            "football_skills",
            player_data
        )

        # Populate Physical Condition table
        self._populate_category_table(
            physical_table,
            self.physical_condition_groups,
            player_id,
            period,
            "physical_condition",
            player_data
        )

        # Populate Mental Ability table
        self._populate_category_table(
            mental_table,
            self.mental_ability_groups,
            player_id,
            period,
            "mental_ability",
            player_data
        )

        # Check if the player is a goalkeeper
        is_goalkeeper = False
        for player in self.filtered_players:
            if player['player_id'] == player_id and player.get('position') == 'GK':
                is_goalkeeper = True
                break

        # Get the goalkeeper scroll area
        if period == "1st":
            goalkeeper_scroll = self.first_goalkeeper_scroll
        elif period == "2nd":
            goalkeeper_scroll = self.second_goalkeeper_scroll
        else:
            goalkeeper_scroll = self.third_goalkeeper_scroll

        # Show/hide goalkeeper skills table based on player position
        goalkeeper_scroll.setVisible(is_goalkeeper)

        # Populate Goalkeeper Skills table if player is a goalkeeper
        if is_goalkeeper:
            self._populate_category_table(
                goalkeeper_table,
                self.goalkeeper_skills_groups,
                player_id,
                period,
                "goalkeeper_skills",
                player_data
            )

    def _populate_category_table(self, table, groups, player_id, period, category, player_data=None):
        """Populate a category table with groups and fields."""
        # Calculate total number of rows needed
        total_rows = 0

        # Add rows for each group and its fields
        for group_name, fields in groups.items():
            # Add 1 row for group header
            total_rows += 1
            # Add rows for fields
            total_rows += len(fields)

        table.setRowCount(total_rows)

        # Current row index
        row = 0

        # Add each group with its fields
        for group_name, fields in sorted(groups.items()):
            # Add group header with average value
            self._add_group_header(table, row, group_name)
            row += 1

            # Normalize group name for key lookup
            normalized_group = self._normalize_field_name(group_name)

            # Add each field with its value
            field_values = []
            for field in fields:
                # Clean the field name to ensure consistency
                clean_field = field.strip()

                # Add field row with cached data if available
                if player_data:
                    value = self._add_field_row_with_cache(table, row, clean_field, normalized_group, category, player_data)
                else:
                    value = self._add_field_row(table, row, clean_field, player_id, period, category)

                if value is not None:
                    field_values.append(value)
                row += 1

            # Update average value in the group header
            if field_values:
                avg_value = sum(field_values) / len(field_values)
                self._update_group_average(table, row - len(fields) - 1, avg_value)
            else:
                # Clear the average if no values
                self._update_group_average(table, row - len(fields) - 1, None)

    def _add_group_header(self, table, row, group_name):
        """Add a group header row to the table."""
        # Create group header item
        group_item = QTableWidgetItem(group_name)
        group_item.setFlags(Qt.ItemFlag.ItemIsEnabled)  # Make it non-editable
        group_item.setBackground(QColor(230, 230, 240))
        font = group_item.font()
        font.setBold(True)
        group_item.setFont(font)
        table.setItem(row, 0, group_item)

        # Create average value item (will be updated later)
        avg_item = QTableWidgetItem("")
        avg_item.setFlags(Qt.ItemFlag.ItemIsEnabled)  # Make it non-editable
        avg_item.setBackground(QColor(230, 230, 240))
        avg_item.setFont(font)
        avg_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        table.setItem(row, 1, avg_item)

    def _add_field_row_with_cache(self, table, row, field, group_name, category, player_data):
        """Add a field row to the table using cached player data and return its value if available."""
        # Create field item
        field_item = QTableWidgetItem("  " + field)  # Indent field names
        field_item.setFlags(Qt.ItemFlag.ItemIsEnabled)  # Make it non-editable

        # Store the original field name as data for saving
        field_item.setData(Qt.ItemDataRole.UserRole, field)
        table.setItem(row, 0, field_item)

        # Create value item
        value_item = QTableWidgetItem()
        value_item.setFlags(Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable | Qt.ItemFlag.ItemIsSelectable)
        value_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

        # Set the item in the table
        table.setItem(row, 1, value_item)

        # Try to get value from cached data
        value = None

        # Try with group-based key format first
        if group_name:
            cache_key = f"{category}/{group_name}/{field}"
            value = player_data.get(cache_key)

        # If not found, try legacy formats
        if not value:
            cache_key = f"{category}/{field}"
            value = player_data.get(cache_key)

        # If still not found, try with normalized field name
        if not value:
            normalized_field = self._normalize_field_name(field)
            cache_key = f"{category}/{normalized_field}"
            value = player_data.get(cache_key)

        # Set the value if found
        if value:
            value_item.setText(str(value))
            try:
                return float(value)
            except (ValueError, TypeError):
                return None

        return None

    def _add_field_row(self, table, row, field, player_id, period, category):
        """Add a field row to the table and return its value if available."""
        # Create field item
        field_item = QTableWidgetItem("  " + field)  # Indent field names
        field_item.setFlags(Qt.ItemFlag.ItemIsEnabled)  # Make it non-editable

        # Store the original field name as data for saving
        field_item.setData(Qt.ItemDataRole.UserRole, field)
        table.setItem(row, 0, field_item)

        # Create value item
        value_item = QTableWidgetItem()
        value_item.setFlags(Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable | Qt.ItemFlag.ItemIsSelectable)
        value_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

        # Get saved value if exists - try multiple key formats to be safe
        settings = QSettings()

        # Clean the field name
        clean_field = field.strip()

        # Find the group for this field
        # We need to look at the rows above this one to find the most recent group header
        current_row = row
        group_name = None

        while current_row >= 0:
            header_item = table.item(current_row, 0)
            if header_item and header_item.font().bold():
                group_name = header_item.text()
                break
            current_row -= 1

        # Try with the group-based key format first (new format)
        if group_name:
            normalized_group = self._normalize_field_name(group_name)
            group_key = f"evaluations/{player_id}/{period}/{category}/{normalized_group}/{clean_field}"
            self.logger.debug(f"Loading value with group key: {group_key}")  # Reduced to debug level
            value = settings.value(group_key, "")

            if value:
                self.logger.debug(f"Found value using group key: {value}")  # Reduced to debug level
                table.setItem(row, 1, value_item)
                value_item.setText(str(value))
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return None

        # If not found with group key, try legacy formats

        # Try the standard key format (legacy)
        value_key = f"evaluations/{player_id}/{period}/{category}/{field}"
        self.logger.debug(f"Loading value for legacy key: {value_key}")  # Reduced to debug level
        value = settings.value(value_key, "")

        # If not found, try with stripped field name (legacy)
        if not value and field.startswith("  "):
            stripped_field = field.strip()
            value_key_alt = f"evaluations/{player_id}/{period}/{category}/{stripped_field}"
            self.logger.debug(f"Trying alternative legacy key: {value_key_alt}")  # Reduced to debug level
            value = settings.value(value_key_alt, "")

        # If still not found, try with normalized field name (legacy)
        if not value:
            normalized_field = self._normalize_field_name(field)
            normalized_key = f"evaluations/{player_id}/{period}/{category}/{normalized_field}"
            self.logger.debug(f"Trying normalized legacy key: {normalized_key}")  # Reduced to debug level
            value = settings.value(normalized_key, "")

        # Always set the item in the table
        table.setItem(row, 1, value_item)

        if value:
            self.logger.debug(f"Found value: {value} for field: {field}")  # Reduced to debug level
            value_item.setText(str(value))
            value_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            try:
                return float(value)
            except (ValueError, TypeError):
                return None
        else:
            self.logger.debug(f"No value found for field: {field}")  # Reduced to debug level

        return None

    def _update_group_average(self, table, row, avg_value):
        """Update the average value for a group header."""
        avg_item = table.item(row, 1)
        if avg_item:
            if avg_value is None:
                # Clear the average if no values
                avg_item.setText("")
                avg_item.setBackground(QColor(230, 230, 240))  # Default header background
                avg_item.setForeground(QColor(0, 0, 0))  # Default text color
            else:
                # Format and display the average
                avg_item.setText(f"{avg_value:.1f}")
                avg_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                # Set background color based on average value
                if avg_value >= 9.0:
                    avg_item.setBackground(get_color("rating_excellent"))
                    avg_item.setForeground(get_color("rating_excellent_text"))
                elif avg_value >= 7.0:
                    avg_item.setBackground(get_color("rating_good"))
                    avg_item.setForeground(get_color("rating_good_text"))
                elif avg_value >= 4.0:
                    avg_item.setBackground(get_color("rating_average"))
                    avg_item.setForeground(get_color("rating_average_text"))
                else:
                    avg_item.setBackground(get_color("rating_poor"))
                    avg_item.setForeground(get_color("rating_poor_text"))

    def _edit_multiple_cells(self, period):
        """Edit multiple selected cells at once."""
        # Get the current tables
        if period == "1st":
            tables = [self.first_football_skills_table, self.first_physical_condition_table, self.first_mental_ability_table, self.first_goalkeeper_skills_table]
        elif period == "2nd":
            tables = [self.second_football_skills_table, self.second_physical_condition_table, self.second_mental_ability_table, self.second_goalkeeper_skills_table]
        else:
            tables = [self.third_football_skills_table, self.third_physical_condition_table, self.third_mental_ability_table, self.third_goalkeeper_skills_table]

        # Collect all selected items from all tables
        editable_items = []
        for table in tables:
            selected_items = table.selectedItems()
            # Only include value cells (column 1) that are editable
            for item in selected_items:
                if item.column() == 1 and item.flags() & Qt.ItemFlag.ItemIsEditable:
                    editable_items.append((table, item))

        if not editable_items:
            return

        # Show dialog to get the value
        value, ok = QInputDialog.getInt(
            self,
            self.tr("Edit Multiple Cells"),
            self.tr("Enter value (0-10):"),
            0, 0, 10, 1
        )

        if ok:
            # Get all unique field names and tables
            field_table_pairs = []
            for table, item in editable_items:
                row = item.row()
                field_item = table.item(row, 0)
                if field_item:
                    field_name = field_item.data(Qt.ItemDataRole.UserRole) or field_item.text().strip()
                    field_table_pairs.append((field_name, table))

            # Update all selected cells
            for table, item in editable_items:
                item.setText(str(value))
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # Save each unique field-table pair once
            unique_pairs = set(field_table_pairs)
            for field_name, table in unique_pairs:
                # Save this specific value directly for all selected players
                self._save_specific_value(field_name, str(value), period, table)

            # Update affected averages and save changes
            for table in tables:
                self._update_all_averages(table)
                self._save_table_data(table, period)

            # Force settings to sync
            QSettings().sync()

    def _clear_all_values(self, period):
        """Clear all values in the current evaluation period."""
        # Confirm with the user
        reply = QMessageBox.question(
            self,
            self.tr("Clear All Values"),
            self.tr("Are you sure you want to clear all evaluation values for this period?"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Get the current tables
        if period == "1st":
            tables = [self.first_football_skills_table, self.first_physical_condition_table, self.first_mental_ability_table, self.first_goalkeeper_skills_table]
        elif period == "2nd":
            tables = [self.second_football_skills_table, self.second_physical_condition_table, self.second_mental_ability_table, self.second_goalkeeper_skills_table]
        else:
            tables = [self.third_football_skills_table, self.third_physical_condition_table, self.third_mental_ability_table, self.third_goalkeeper_skills_table]

        # Clear all value cells
        for table in tables:
            for row in range(table.rowCount()):
                item = table.item(row, 1)
                if item and item.flags() & Qt.ItemFlag.ItemIsEditable:
                    item.setText("")

            # Update averages
            self._update_all_averages(table)

            # Save changes
            self._save_table_data(table, period)

    def _update_all_averages(self, table):
        """Update all average values in the table."""
        row = 0
        while row < table.rowCount():
            # Check if this is a group header row (has bold font)
            if (table.item(row, 0) and
                table.item(row, 0).font().bold() and
                table.item(row, 1)):

                # This is a group header, calculate average of the next rows (up to the next header)
                field_values = []
                next_row = row + 1

                # Collect values until we hit another header or end of table
                while (next_row < table.rowCount() and
                       table.item(next_row, 0) and
                       not table.item(next_row, 0).font().bold()):

                    value_item = table.item(next_row, 1)
                    if value_item and value_item.text():
                        try:
                            field_values.append(float(value_item.text()))
                        except (ValueError, TypeError):
                            pass
                    next_row += 1

                # Update average value
                if field_values:
                    avg_value = sum(field_values) / len(field_values)
                    self._update_group_average(table, row, avg_value)
                else:
                    # Clear the average if no values
                    self._update_group_average(table, row, None)

            row += 1

    def _save_table_data(self, table, period):
        """Save the data from a table to settings."""
        if not self.current_player_id:
            self.logger.warning("Cannot save data: No player selected")
            return

        settings = QSettings()

        # Get the category from the table
        category = table.property("categoryName").lower().replace(" ", "_")
        self.logger.debug(f"Saving data for category: {category}, period: {period}, player: {self.current_player_id}")

        # Create a dictionary to batch all settings changes
        changes = {}
        removals = []

        # Track current group to avoid repeated lookups
        current_group = None

        # Iterate through all rows
        row = 0
        saved_count = 0
        while row < table.rowCount():
            # Check if this is a header row
            if (table.item(row, 0) and
                table.item(row, 0).font().bold()):
                # Update current group
                current_group = table.item(row, 0).text()
                row += 1
                continue

            # Get field name and value
            field_item = table.item(row, 0)
            value_item = table.item(row, 1)

            if field_item and value_item:
                # Get the original field name from user data (without indentation)
                field_name = field_item.data(Qt.ItemDataRole.UserRole)
                if not field_name:  # Fallback to text if data not set
                    field_name = field_item.text().strip()
                    # Remove any leading spaces (indentation)
                    if field_name.startswith("  "):
                        field_name = field_name[2:]

                value = value_item.text()

                # Save to settings if we have a field name
                if field_name:
                    # Use the tracked current group
                    group_name = current_group

                    # Create the key with group context to avoid conflicts with duplicate field names
                    if group_name:
                        normalized_group = self._normalize_field_name(group_name)
                        value_key = f"evaluations/{self.current_player_id}/{period}/{category}/{normalized_group}/{field_name}"
                    else:
                        # Fallback to old format if group not found
                        value_key = f"evaluations/{self.current_player_id}/{period}/{category}/{field_name}"

                    if value:
                        # Add to changes dictionary instead of immediately saving
                        changes[value_key] = value
                        saved_count += 1
                    else:
                        # Add to removals list
                        removals.append(value_key)
                else:
                    self.logger.debug(f"No field name found for row {row}")
            else:
                self.logger.debug(f"Missing field_item or value_item at row {row}")

            row += 1

        # Apply all changes at once
        for key, value in changes.items():
            settings.setValue(key, value)

        # Apply all removals
        for key in removals:
            settings.remove(key)

        # Sync settings to disk once at the end
        settings.sync()

        self.logger.debug(f"Saved {saved_count} values for {category}, period: {period}")

        # No need for a second sync

    def _get_football_skills_groups(self):
        """Get the football skills groups from settings."""
        settings = QSettings()
        settings.beginGroup("evaluation_fields")
        titles_data = settings.value("titles", {})
        settings.endGroup()

        # Convert to proper format
        groups = {}
        if titles_data:
            for title, fields in titles_data.items():
                if isinstance(title, str) and isinstance(fields, list):
                    groups[title] = fields

        # If no groups found in settings, use default groups
        if not groups:
            groups = {
                "Ball_Carrying": ["Carrying the Ball Forward", "Breaking Lines with Ball", "Transition Control with Ball"],
                "Ball_Control": ["First Touch", "Ball Reception", "Ball Protection"],
                "Passing": ["Short Passing", "Long Passing", "Through Balls", "Crossing"],
                "Shooting": ["Finishing", "Long Shots", "Heading", "Volleys"],
                "Defending": ["Tackling", "Interceptions", "Aerial Duels", "Positioning"]
            }

        return groups

    def _get_physical_condition_groups(self):
        """Get the physical condition groups from settings."""
        settings = QSettings()
        settings.beginGroup("physical_condition_fields")
        titles_data = settings.value("titles", {})
        settings.endGroup()

        # Convert to proper format
        groups = {}
        if titles_data:
            for title, fields in titles_data.items():
                if isinstance(title, str) and isinstance(fields, list):
                    groups[title] = fields

        # If no groups found in settings, use default groups
        if not groups:
            groups = {
                "Speed_&_Agility": ["Acceleration", "Top Speed", "Agility", "Balance", "Coordination"],
                "Strength_&_Power": ["Upper Body Strength", "Lower Body Strength", "Jumping", "Power", "Endurance"],
                "Fitness": ["Stamina", "Recovery Rate", "Injury Resistance", "Work Rate", "Physical Presence"]
            }

        return groups

    def _get_mental_ability_groups(self):
        """Get the mental ability groups from settings."""
        settings = QSettings()
        settings.beginGroup("mental_ability_fields")
        titles_data = settings.value("titles", {})
        settings.endGroup()

        # Convert to proper format
        groups = {}
        if titles_data:
            for title, fields in titles_data.items():
                if isinstance(title, str) and isinstance(fields, list):
                    groups[title] = fields

        # If no groups found in settings, use default groups
        if not groups:
            groups = {
                "Game_Intelligence": ["Football Intelligence", "Game Awareness", "Tactical Understanding", "Positional Sense"],
                "Leadership_&_Communication": ["Leadership", "Communication", "Influence on Teammates", "Team-first Mentality"],
                "Mental_Processing": ["Decision Making", "Speed of Thought", "Anticipation", "Vision"],
                "Mental_Toughness": ["Composure Under Pressure", "Mental Toughness", "Concentration", "Focus in High-pressure Moments"]
            }

        return groups

    def _get_goalkeeper_skills_groups(self):
        """Get the goalkeeper skills groups from settings."""
        settings = QSettings()
        settings.beginGroup("goalkeeper_skills_fields")
        titles_data = settings.value("titles", {})
        settings.endGroup()

        # Convert to proper format
        groups = {}
        if titles_data:
            for title, fields in titles_data.items():
                if isinstance(title, str) and isinstance(fields, list):
                    groups[title] = fields

        # If no groups found in settings, use default groups
        if not groups:
            groups = {
                "Shot_Stopping": ["Reflexes", "Positioning", "Handling", "One-on-Ones", "Diving"],
                "Distribution": ["Kicking", "Throwing", "Passing", "Ball Control", "Vision"],
                "Command_of_Area": ["Communication", "Aerial Ability", "Bravery", "Decision Making", "Leadership"]
            }

        return groups

    def _show_evaluation_charts(self, period):
        """Show the evaluation charts dialog for the current player and period.

        Args:
            period (str): The evaluation period ('1st', '2nd', '3rd').
        """
        self.logger.info(f"Opening evaluation charts for period {period}")

        # Check if we have a player selected
        if not self.current_player_id:
            QMessageBox.warning(
                self,
                self.tr("No Player Selected"),
                self.tr("Please select a player to view evaluation charts.")
            )
            return

        try:
            # Import here to avoid circular imports
            from app.dialogs.evaluation_chart_selection_dialog import EvaluationChartSelectionDialog

            # Create and show the chart selection dialog
            dialog = EvaluationChartSelectionDialog(
                parent=self,
                evaluation_manager=self.evaluation_manager,
                roster_manager=self.roster_manager,
                current_player_id=self.current_player_id,
                current_period=period
            )

            # Show the dialog
            dialog.show()
            dialog.activateWindow()  # Bring it to the front

        except Exception as e:
            self.logger.error(f"Error showing evaluation charts: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

            QMessageBox.critical(
                self,
                self.tr("Error"),
                self.tr("An error occurred while opening the evaluation charts: {}").format(str(e))
            )



    def eventFilter(self, obj, event):
        """Handle events for the tables."""
        # Check if this is a key press event on one of our tables
        if (event.type() == QEvent.Type.KeyPress and
            isinstance(obj, QTableWidget) and
            obj.property("categoryName") is not None):

            # Check if we have a digit key press (0-9)
            if event.key() >= Qt.Key.Key_0 and event.key() <= Qt.Key.Key_9:
                # Get the digit value
                digit = event.key() - Qt.Key.Key_0

                # Get all selected items
                selected_items = obj.selectedItems()

                # Filter to only include value cells (column 1) that are editable
                editable_items = [item for item in selected_items
                                 if item.column() == 1 and
                                 item.flags() & Qt.ItemFlag.ItemIsEditable]

                if editable_items:
                    # Get the current period
                    current_tab_index = self.evaluation_tabs.currentIndex()
                    period = ["1st", "2nd", "3rd"][current_tab_index]

                    # Get all unique field names
                    field_table_pairs = []
                    for item in editable_items:
                        row = item.row()
                        field_item = obj.item(row, 0)
                        if field_item:
                            field_name = field_item.data(Qt.ItemDataRole.UserRole) or field_item.text().strip()
                            field_table_pairs.append((field_name, obj))

                    # Update all selected cells
                    for item in editable_items:
                        item.setText(str(digit))
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                    # Save each unique field-table pair once
                    unique_pairs = set(field_table_pairs)
                    for field_name, table in unique_pairs:
                        # Save this specific value directly for all selected players
                        self._save_specific_value(field_name, str(digit), period, table)

                    # Update averages and save changes
                    self._update_all_averages(obj)
                    self._save_table_data(obj, period)

                    # Force settings to sync
                    QSettings().sync()

                    return True  # Event handled

            # Check for Enter key when not in edit mode
            elif event.key() == Qt.Key.Key_Enter or event.key() == Qt.Key.Key_Return:
                current_row = obj.currentRow()
                current_col = obj.currentColumn()

                # If we're in the value column, try to move to the next row
                if current_col == 1 and current_row < obj.rowCount() - 1:
                    # Find the next editable cell in column 1
                    for row in range(current_row + 1, obj.rowCount()):
                        item = obj.item(row, 1)
                        if item and item.flags() & Qt.ItemFlag.ItemIsEditable:
                            obj.setCurrentCell(row, 1)
                            # If the item is editable, start editing it
                            obj.editItem(item)
                            return True

                # If we can't find a next cell, just let the default handler take over
                return False

        # Pass the event to the parent class
        return super().eventFilter(obj, event)

    def _save_modified_data(self):
        """Save all modified categories for the current player and period."""
        if not self.current_player_id:
            return

        # Get the current period
        current_tab_index = self.evaluation_tabs.currentIndex()
        if current_tab_index > 2:  # Not an evaluation tab
            return

        period = ["1st", "2nd", "3rd"][current_tab_index]

        # Save only the modified categories
        for category in self._modified_categories:
            self.logger.info(f"Batch saving category: {category}, period: {period}, player: {self.current_player_id}")

            # Get the appropriate table for this category
            table = None
            if category == "football_skills":
                if period == "1st":
                    table = self.first_football_skills_table
                elif period == "2nd":
                    table = self.second_football_skills_table
                elif period == "3rd":
                    table = self.third_football_skills_table
            elif category == "physical_condition":
                if period == "1st":
                    table = self.first_physical_condition_table
                elif period == "2nd":
                    table = self.second_physical_condition_table
                elif period == "3rd":
                    table = self.third_physical_condition_table
            elif category == "mental_ability":
                if period == "1st":
                    table = self.first_mental_ability_table
                elif period == "2nd":
                    table = self.second_mental_ability_table
                elif period == "3rd":
                    table = self.third_mental_ability_table
            elif category == "goalkeeper_skills":
                if period == "1st":
                    table = self.first_goalkeeper_skills_table
                elif period == "2nd":
                    table = self.second_goalkeeper_skills_table
                elif period == "3rd":
                    table = self.third_goalkeeper_skills_table

            # Save the table data if we found the table
            if table:
                self._save_table_data(table, period)

        # Clear the modified categories
        self._modified_categories.clear()

    def closeEvent(self, event):
        """Handle window close event to ensure all data is saved."""
        self.logger.info("Evaluation window closing - saving all data")

        # Save data for all tabs if a player is selected
        if self.current_player_id:
            self._save_all_evaluation_data()

        # Accept the close event
        event.accept()

    def changeEvent(self, event):
        """Handle change events, including language changes."""
        if event.type() == QEvent.Type.LanguageChange:
            self.logger.info("Language change event received, retranslating UI")
            self.retranslateUi()

        # Call base class implementation
        super().changeEvent(event)

    def _save_all_evaluation_data(self):
        """Save all evaluation data for the current player."""
        self.logger.debug(f"Saving all evaluation data for player {self.current_player_id}")

        # Save data for all periods
        for period, tables in [
            ("1st", [self.first_football_skills_table, self.first_physical_condition_table, self.first_mental_ability_table, self.first_goalkeeper_skills_table]),
            ("2nd", [self.second_football_skills_table, self.second_physical_condition_table, self.second_mental_ability_table, self.second_goalkeeper_skills_table]),
            ("3rd", [self.third_football_skills_table, self.third_physical_condition_table, self.third_mental_ability_table, self.third_goalkeeper_skills_table])
        ]:
            for table in tables:
                self._save_table_data(table, period)

        # No need for an additional sync here as _save_table_data already syncs

        self.logger.debug("All evaluation data saved")

    def retranslateUi(self):
        """Update the UI text when the language changes."""
        self.setWindowTitle(self.tr("Evaluations"))

        # Update player table header
        self.player_table.setHorizontalHeaderLabels([self.tr("Name")])

        # Update tab titles
        self.evaluation_tabs.setTabText(0, self.tr("1st Evaluation"))
        self.evaluation_tabs.setTabText(1, self.tr("2nd Evaluation"))
        self.evaluation_tabs.setTabText(2, self.tr("3rd Evaluation"))

        # Update table headers
        # Football skills tables
        for table in [self.first_football_skills_table, self.second_football_skills_table, self.third_football_skills_table]:
            table.setHorizontalHeaderLabels([self.tr("Football Skills"), self.tr("Value")])

        # Physical condition tables
        for table in [self.first_physical_condition_table, self.second_physical_condition_table, self.third_physical_condition_table]:
            table.setHorizontalHeaderLabels([self.tr("Physical Condition"), self.tr("Value")])

        # Mental ability tables
        for table in [self.first_mental_ability_table, self.second_mental_ability_table, self.third_mental_ability_table]:
            table.setHorizontalHeaderLabels([self.tr("Mental Ability"), self.tr("Value")])

        # Goalkeeper skills tables
        for table in [self.first_goalkeeper_skills_table, self.second_goalkeeper_skills_table, self.third_goalkeeper_skills_table]:
            table.setHorizontalHeaderLabels([self.tr("Goalkeeper Skills"), self.tr("Value")])

        # Update the player table to refresh any translated messages
        self._update_player_table()

# Example usage (for testing)
if __name__ == '__main__':
    app = QApplication(sys.argv)

    # Create test managers
    roster_manager = RosterManager()
    evaluation_manager = EvaluationManager()

    # Add some test data if needed
    if len(roster_manager.get_all_players()) == 0:
        roster_manager.add_player({'last_name': 'Smith', 'first_name': 'John'})
        roster_manager.add_player({'last_name': 'Johnson', 'first_name': 'Mike'})
        roster_manager.add_player({'last_name': 'Williams', 'first_name': 'David'})

    # Create and show the evaluations page
    window = EvaluationsPage(roster_manager, evaluation_manager)
    window.resize(1000, 600)
    window.show()

    # Clean up on exit
    app.aboutToQuit.connect(roster_manager.close_db)
    app.aboutToQuit.connect(evaluation_manager.close_db)

    sys.exit(app.exec())
