"""
Simple Timeline Widget with event list and real-time timeline visualization.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QLineEdit, QCheckBox, QGroupBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QFrame, QButtonGroup, QRadioButton
)
from PySide6.QtCore import Qt, Signal, QSettings
from PySide6.QtGui import QFont, QPainter, QPen, QBrush, QColor, QFontMetrics

from app.widgets.minute_selector_widget import MinuteSelectorPopup


@dataclass
class TimelineEvent:
    """Represents a single timeline event."""
    player_name: str
    event_type: str  # "goal", "assist", "yellow", "red", "sub_in", "sub_out", "injury"
    minute: int  # 0 = not set yet
    playing_time: Tuple[int, int]  # (start, end) when player was on pitch
    is_editable: bool = True  # False for subs/injuries (from stats)
    event_id: str = ""  # Unique identifier

    def __post_init__(self):
        if not self.event_id:
            self.event_id = f"{self.player_name}_{self.event_type}_{id(self)}"

    @property
    def is_complete(self) -> bool:
        return self.minute > 0

    @property
    def icon(self) -> str:
        icons = {
            "goal": "⚽",
            "assist": "🅰️",
            "yellow": "🟨",
            "red": "🟥",
            "sub_in": "🔄",
            "sub_out": "🔄",
            "injury": "🏥"
        }
        return icons.get(self.event_type, "📌")


class SimpleTimelineCanvas(QWidget):
    """Simple timeline canvas showing events as they are completed."""

    event_clicked = Signal(str)  # event_id

    def __init__(self, parent=None):
        super().__init__(parent)
        self.events = []
        self.setMinimumHeight(100)
        self.setMaximumHeight(150)

        # Timeline configuration
        self.margin_left = 50
        self.margin_right = 30
        self.margin_top = 20
        self.margin_bottom = 20

    def set_events(self, events: List[TimelineEvent]):
        """Set events to display on timeline."""
        self.events = [e for e in events if e.is_complete]
        self.update()

    def paintEvent(self, event):
        """Paint the timeline."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Calculate dimensions
        width = self.width() - self.margin_left - self.margin_right
        timeline_y = self.height() // 2

        # Draw main timeline
        painter.setPen(QPen(QColor("#4CAF50"), 3))
        painter.drawLine(self.margin_left, timeline_y, self.margin_left + width, timeline_y)

        # Draw minute markers
        painter.setPen(QPen(QColor("#999"), 1))
        painter.setFont(QFont("Arial", 8))
        for minute in [0, 15, 30, 45, 60, 75, 90]:
            x = self._minute_to_x(minute, width)
            painter.drawLine(x, timeline_y - 5, x, timeline_y + 5)
            painter.drawText(x - 10, timeline_y + 20, f"{minute}'")

        # Draw events
        self._draw_events(painter, width, timeline_y)

    def _draw_events(self, painter, width, timeline_y):
        """Draw events on timeline."""
        # Group events by minute to handle overlaps
        minute_events = {}
        for event in self.events:
            if event.minute not in minute_events:
                minute_events[event.minute] = []
            minute_events[event.minute].append(event)

        # Draw each minute's events
        for minute, events in minute_events.items():
            x = self._minute_to_x(minute, width)

            if len(events) == 1:
                # Single event
                self._draw_single_event(painter, events[0], x, timeline_y)
            else:
                # Multiple events - stack vertically
                for i, event in enumerate(events):
                    y_offset = (i - len(events)/2 + 0.5) * 25
                    self._draw_single_event(painter, event, x, timeline_y + y_offset)

    def _draw_single_event(self, painter, event, x, y):
        """Draw a single event icon."""
        # Event color
        color = self._get_event_color(event)

        # Draw icon background
        icon_size = 18
        painter.setPen(QPen(color, 2))
        painter.setBrush(QBrush(color.lighter(150)))
        painter.drawEllipse(x - icon_size//2, y - icon_size//2, icon_size, icon_size)

        # Draw icon text
        painter.setPen(QPen(QColor("white")))
        painter.setFont(QFont("Arial", 8, QFont.Weight.Bold))

        # Center the icon text
        text = event.icon
        text_rect = painter.fontMetrics().boundingRect(text)
        text_x = x - text_rect.width() // 2
        text_y = y + text_rect.height() // 4
        painter.drawText(text_x, text_y, text)

    def _get_event_color(self, event):
        """Get color for event type."""
        colors = {
            "goal": QColor("#4CAF50"),      # Green
            "assist": QColor("#2196F3"),    # Blue
            "yellow": QColor("#FFC107"),    # Yellow
            "red": QColor("#F44336"),       # Red
            "sub_in": QColor("#9C27B0"),    # Purple
            "sub_out": QColor("#9C27B0"),   # Purple
            "injury": QColor("#FF5722")     # Orange
        }
        return colors.get(event.event_type, QColor("#666"))

    def _minute_to_x(self, minute, width):
        """Convert minute to x coordinate."""
        return self.margin_left + int((minute / 90) * width)

    def mousePressEvent(self, event):
        """Handle mouse clicks on timeline events."""
        if event.button() == Qt.MouseButton.LeftButton:
            clicked_event = self._get_event_at_position(event.pos())
            if clicked_event and clicked_event.is_editable:
                self.event_clicked.emit(clicked_event.event_id)

    def _get_event_at_position(self, pos):
        """Get event at mouse position."""
        width = self.width() - self.margin_left - self.margin_right
        timeline_y = self.height() // 2

        for event in self.events:
            x = self._minute_to_x(event.minute, width)
            if abs(pos.x() - x) <= 15 and abs(pos.y() - timeline_y) <= 30:
                return event
        return None


class SimpleTimelineWidget(QWidget):
    """Simple timeline widget with event list and real-time visualization."""

    # Signals
    events_updated = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.simple_timeline")

        # Data
        self.events: List[TimelineEvent] = []
        self.goal_minutes: List[int] = []  # Available goal minutes for assists

        self._init_ui()

    def _init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Controls
        controls = self._create_controls()
        main_layout.addWidget(controls)

        # Timeline canvas
        timeline_section = self._create_timeline_section()
        main_layout.addWidget(timeline_section)

        # Events table
        events_section = self._create_events_section()
        main_layout.addWidget(events_section)

    def _create_controls(self) -> QWidget:
        """Create control buttons and options."""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # Sort options
        sort_group = QGroupBox("Sort Events By")
        sort_layout = QHBoxLayout(sort_group)

        self.sort_by_player = QRadioButton("Player")
        self.sort_by_event = QRadioButton("Event Type")
        self.sort_by_player.setChecked(True)

        self.sort_by_player.toggled.connect(self._refresh_events_table)
        self.sort_by_event.toggled.connect(self._refresh_events_table)

        sort_layout.addWidget(self.sort_by_player)
        sort_layout.addWidget(self.sort_by_event)

        # Action buttons
        self.refresh_btn = QPushButton("Refresh from Player Stats")
        self.refresh_btn.clicked.connect(self._refresh_from_stats)

        layout.addWidget(sort_group)
        layout.addStretch()
        layout.addWidget(self.refresh_btn)

        return widget

    def _create_timeline_section(self) -> QWidget:
        """Create the timeline visualization section."""
        group = QGroupBox("Match Timeline")
        layout = QVBoxLayout(group)

        # Timeline canvas
        self.timeline_canvas = SimpleTimelineCanvas()
        self.timeline_canvas.event_clicked.connect(self._on_timeline_event_clicked)

        layout.addWidget(self.timeline_canvas)

        # Timeline info
        info_label = QLabel("Click on timeline events to edit them")
        info_label.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
        layout.addWidget(info_label)

        return group

    def _create_events_section(self) -> QWidget:
        """Create the events table section."""
        group = QGroupBox("Complete Event Minutes")
        layout = QVBoxLayout(group)

        # Events table
        self.events_table = QTableWidget()
        self.events_table.setColumnCount(4)
        self.events_table.setHorizontalHeaderLabels([
            "Player", "Event", "Minute", "Status"
        ])

        # Table settings
        self.events_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.events_table.setAlternatingRowColors(True)
        self.events_table.verticalHeader().setVisible(False)
        self.events_table.setMinimumHeight(200)  # Ensure minimum height

        # Column widths - balanced layout
        header = self.events_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)      # Player
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Event
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)        # Minute - fixed width
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)        # Status - fixed width

        # Set reasonable fixed widths - make Minute column wider for button
        self.events_table.setColumnWidth(2, 140)  # Minute column - wider for "Select..." button
        self.events_table.setColumnWidth(3, 120)  # Status column

        layout.addWidget(self.events_table)
        return group

    def _refresh_events_table(self):
        """Refresh the events table with current data."""
        self.logger.info(f"Refreshing events table with {len(self.events)} events")

        # Sort events
        if self.sort_by_player.isChecked():
            # Sort by player with lineup priority and position order
            sorted_events = sorted(self.events, key=lambda e: self._get_player_sort_key(e))
        else:
            # Sort by event type with specific order
            sorted_events = sorted(self.events, key=lambda e: self._get_event_sort_key(e))

        self.logger.info(f"Sorted events: {len(sorted_events)} events")

        # Clear table
        self.events_table.setRowCount(0)
        self.events_table.setRowCount(len(sorted_events))

        # Populate table
        for row, event in enumerate(sorted_events):
            self.logger.info(f"Adding row {row}: {event.player_name} - {event.event_type}")

            # Player name
            player_item = QTableWidgetItem(event.player_name)
            player_item.setFlags(player_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.events_table.setItem(row, 0, player_item)

            # Event type with icon
            event_text = f"{event.icon} {event.event_type.replace('_', ' ').title()}"
            event_item = QTableWidgetItem(event_text)
            event_item.setFlags(event_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.events_table.setItem(row, 1, event_item)

            # Minute selector
            if event.is_editable:
                minute_button = self._create_minute_button(event)
                self.events_table.setCellWidget(row, 2, minute_button)
            else:
                # Non-editable (subs, injuries)
                minute_item = QTableWidgetItem(f"{event.minute}'")
                minute_item.setFlags(minute_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                minute_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.events_table.setItem(row, 2, minute_item)

            # Status
            status_text = "✓ Complete" if event.is_complete else "⏳ Pending"
            status_item = QTableWidgetItem(status_text)
            status_item.setFlags(status_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            if event.is_complete:
                status_item.setForeground(QColor("#4CAF50"))
            else:
                status_item.setForeground(QColor("#F44336"))
            self.events_table.setItem(row, 3, status_item)

    def _get_event_sort_key(self, event: TimelineEvent):
        """Get sort key for event type sorting with specific order."""
        # Event type order: Sub in, Sub out, Goal, Assists, Yellows, Red, Injuries
        event_order = {
            "sub_in": 0,
            "sub_out": 1,
            "goal": 2,
            "assist": 3,
            "yellow": 4,
            "red": 5,
            "injury": 6
        }
        return (event_order.get(event.event_type, 99), event.player_name)

    def _get_player_sort_key(self, event: TimelineEvent):
        """Get sort key for player sorting with lineup priority and position order."""
        # Try to get player info from parent if available
        lineup_priority = 1  # Default: not in lineup
        position_priority = 99  # Default: unknown position

        # Check if we have a parent with player stats
        if hasattr(self, 'parent_matches_page'):
            player_info = self.parent_matches_page._get_player_info_for_sorting(event.player_name)
            if player_info:
                lineup_priority = 0 if player_info.get('is_lineup', False) else 1
                position = player_info.get('position', '')
                # Position order: GK, DF, MF, AT
                position_order = {'GK': 0, 'DF': 1, 'MF': 2, 'AT': 3}
                position_priority = position_order.get(position, 99)

        return (lineup_priority, position_priority, event.player_name, event.event_type)

    def set_parent_matches_page(self, parent):
        """Set reference to parent matches page for player info."""
        self.parent_matches_page = parent

    def _create_minute_button(self, event: TimelineEvent) -> QPushButton:
        """Create minute selection button for an event."""
        # Determine button text
        if event.minute > 0:
            button_text = f"{event.minute}'"
            button_style = """
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """
        else:
            button_text = "Select..."
            button_style = """
                QPushButton {
                    background-color: #f0f0f0;
                    color: #666;
                    border: 1px solid #ccc;
                    padding: 4px 8px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                    border-color: #999;
                }
            """

        button = QPushButton(button_text)
        button.setStyleSheet(button_style)
        button.setFixedHeight(25)
        # Remove width restriction - let button use full column width

        # Connect click to show minute selector
        button.clicked.connect(lambda: self._show_minute_selector(event, button))

        return button

    def _show_minute_selector(self, event: TimelineEvent, button: QPushButton):
        """Show the compact minute selector popup."""
        # Get available minutes
        if event.event_type in ["goal", "assist"]:
            # Goals and assists: only during playing time
            start_minute = max(1, event.playing_time[0])
            end_minute = min(90, event.playing_time[1])
            available_minutes = list(range(start_minute, end_minute + 1))

            # For assists, prioritize goal minutes
            if event.event_type == "assist" and self.goal_minutes:
                # Put goal minutes at the beginning
                goal_minutes_in_range = [m for m in self.goal_minutes
                                       if start_minute <= m <= end_minute]
                other_minutes = [m for m in available_minutes
                               if m not in goal_minutes_in_range]
                available_minutes = goal_minutes_in_range + other_minutes
        else:
            # Cards: any time during match
            available_minutes = list(range(1, 91))

        # Create and show popup
        popup = MinuteSelectorPopup(available_minutes, event.minute, self)
        popup.minute_selected.connect(lambda minute: self._on_minute_selected_from_popup(event, button, minute))

        # Position popup to the left of the button for more space
        button_pos = button.mapToGlobal(button.rect().bottomLeft())
        # Move popup significantly to the left to avoid table edge
        popup_pos = button_pos
        popup_pos.setX(popup_pos.x() - 80)  # Move further left
        popup.show_at_position(popup_pos)

    def _on_minute_selected_from_popup(self, event: TimelineEvent, button: QPushButton, minute: int):
        """Handle minute selection from popup."""
        self._on_minute_changed(event, minute)

        # Update button appearance
        if minute > 0:
            button.setText(f"{minute}'")
            button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
        else:
            button.setText("Select...")
            button.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    color: #666;
                    border: 1px solid #ccc;
                    padding: 4px 8px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                    border-color: #999;
                }
            """)

    def _on_minute_changed(self, event: TimelineEvent, minute: int):
        """Handle minute selection change."""
        if minute > 0:
            event.minute = minute

            # If this is a goal, add to goal minutes for assists
            if event.event_type == "goal":
                if minute not in self.goal_minutes:
                    self.goal_minutes.append(minute)
                    self.goal_minutes.sort()

        else:
            event.minute = 0

        # Update timeline and table
        self._update_timeline()
        self._refresh_events_table()
        self.events_updated.emit()

    def _on_timeline_event_clicked(self, event_id: str):
        """Handle clicking on timeline event."""
        # Find the event
        event = next((e for e in self.events if e.event_id == event_id), None)
        if not event or not event.is_editable:
            return

        # Find the row in table and focus on minute combo
        for row in range(self.events_table.rowCount()):
            player_item = self.events_table.item(row, 0)
            event_item = self.events_table.item(row, 1)

            if (player_item and player_item.text() == event.player_name and
                event_item and event.event_type in event_item.text().lower()):

                # Focus on the minute combo
                minute_combo = self.events_table.cellWidget(row, 2)
                if minute_combo:
                    minute_combo.showPopup()
                break

    def _update_timeline(self):
        """Update the timeline canvas."""
        self.timeline_canvas.set_events(self.events)

    def _refresh_from_stats(self):
        """Refresh events from player stats - to be connected by parent."""
        self.logger.info("Refresh from stats requested")

    def set_events(self, events: List[TimelineEvent]):
        """Set the events list."""
        self.logger.info(f"Setting {len(events)} events in timeline widget")
        self.events = events

        # Extract goal minutes
        self.goal_minutes = [e.minute for e in events
                           if e.event_type == "goal" and e.minute > 0]
        self.goal_minutes.sort()

        self._refresh_events_table()
        self._update_timeline()

    def get_events(self) -> List[TimelineEvent]:
        """Get current events list."""
        return self.events

    def add_test_data(self):
        """Add test data for development."""
        self.logger.info("Adding test data to timeline widget")
        test_events = [
            TimelineEvent("Smith", "goal", 0, (0, 90)),
            TimelineEvent("Smith", "goal", 0, (0, 90)),
            TimelineEvent("Smith", "assist", 0, (0, 90)),
            TimelineEvent("Jones", "goal", 0, (0, 67)),
            TimelineEvent("Jones", "assist", 0, (0, 67)),
            TimelineEvent("Brown", "yellow", 0, (0, 90)),
            TimelineEvent("Wilson", "sub_out", 67, (0, 67), False),  # Not editable
            TimelineEvent("Davis", "sub_in", 67, (67, 90), False),   # Not editable
        ]
        self.logger.info(f"Created {len(test_events)} test events")
        self.set_events(test_events)
