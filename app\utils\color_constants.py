"""
Color constants for the FOOTDATA8 application.
This module defines all custom colors used throughout the application.
Colors can be customized through the App Colors tab in the Settings page.
The module also provides functions for working with color themes.
"""

from PySide6.QtGui import QColor
from PySide6.QtCore import QSettings

# Default color values
DEFAULT_COLORS = {
    # UI Colors
    "chart_header_bg": "#3c3c64",       # Dark purple background for chart headers

    # Player Status Colors
    "goalkeeper_highlight": "#0064FF",  # Blue color for goalkeeper names
    "selected_player": "#00AA00",       # Green for selected players
    "unselected_player": "#FF0000",     # Red for unselected players
    "player_list_bg": "#F0F0F0",        # Light gray background for player lists

    # Evaluation Rating Colors
    "rating_excellent": "#009600",      # Dark green for ratings ≥ 9.0
    "rating_excellent_text": "#FFFFFF", # White text for excellent ratings
    "rating_good": "#64C864",           # Light green for ratings ≥ 7.0
    "rating_good_text": "#000000",      # Black text for good ratings
    "rating_average": "#FFFF00",        # Yellow for ratings ≥ 4.0
    "rating_average_text": "#000000",   # Black text for average ratings
    "rating_poor": "#FF6464",           # Red for ratings < 4.0
    "rating_poor_text": "#000000",      # Black text for poor ratings
    "group_header_bg": "#E6E6F0",       # Light blue-gray for group headers

    # Evaluation Chart Colors
    "evaluation_low": "#FF6464",        # Red for low ratings (0-3)
    "evaluation_medium": "#FFFF00",     # Yellow for medium ratings (4-6)
    "evaluation_high": "#64C864",       # Green for high ratings (7-10)

    # Attendance Status Colors
    "attendance_present": "#90EE90",    # Green for present status
    "attendance_absent": "#FFA0A0",     # Red for absent status
    "attendance_injured": "#FFFFA0",    # Yellow for injured status
    "attendance_injured_present": "#FFC878", # Orange for injured but present
    "attendance_special": "#C8C8C8",    # Gray for special leave

    # Attendance Calendar Colors
    "attendance_weekend": "#F5F5F5",    # Light gray for weekend days
    "attendance_today": "#E6E6FF",      # Light blue for today
    "attendance_out_of_season_bg": "#C8C8C8", # Darker gray for out-of-season dates
    "attendance_out_of_season_text": "#787878", # Darker gray text for out-of-season dates
    "attendance_name_column_bg": "#F0F0F0", # Light gray background for name column
    "attendance_week_divider": "#F0F0F0", # Light gray for week dividers

    # Position Rating Colors
    "position_best_fit": "#F44336",     # Red for position rating 3 (Best Fit)
    "position_can_play": "#FF9800",     # Orange for position rating 2 (Can Play)
    "position_alternative": "#FFEB3B",  # Yellow for position rating 1 (Alternative)

    # Season Timeline Colors
    "season_bg": "#C8E6FF64",           # Light blue with transparency for season
    "macrocycle_bg": "#B4DCF096",       # Slightly darker blue for macrocycle
    "mesocycle_preparation": "#FFC8C8", # Light red for preparation mesocycle
    "mesocycle_basic": "#C8FFC8",       # Light green for basic mesocycle
    "mesocycle_competition": "#FFFFC8", # Light yellow for competition mesocycle
    "mesocycle_transition": "#DCDCFF",  # Light purple for transition mesocycle
    "evaluation_1st": "#FF9696",        # Reddish for 1st evaluation
    "evaluation_2nd": "#FF6464",        # Darker red for 2nd evaluation
    "evaluation_3rd": "#C86464",        # Even darker red for 3rd evaluation

    # Trend Indicator Colors
    "trend_significant_improvement": "#009600", # Dark green for significant improvement
    "trend_moderate_improvement": "#64C864",    # Light green for moderate improvement
    "trend_no_change": "#969696",               # Gray for no change
    "trend_moderate_regression": "#FF6464",     # Light red for moderate regression
    "trend_significant_regression": "#C80000",  # Dark red for significant regression

    # Match Validation Colors
    "match_validation_serious": "#FF0000",      # Red for serious data missing/errors
    "match_validation_warning": "#FFA500",      # Orange for warnings
    "match_validation_ok": "#00AA00",           # Green for checked ok
    "match_missing_minutes_highlight": "#FFF5F5", # Very light red for missing minutes cell highlighting

    # Roster Outlier Colors
    "roster_outlier_high": "#FF6464",           # Light red for values significantly above average
    "roster_outlier_low": "#FFB464",            # Light orange for values significantly below average
}

def get_color(color_key):
    """
    Get a QColor object for the specified color key.
    If the color has been customized in settings, returns the custom color.
    Otherwise, returns the default color.

    Args:
        color_key: The key for the color in the DEFAULT_COLORS dictionary

    Returns:
        A QColor object
    """
    if color_key not in DEFAULT_COLORS:
        raise ValueError(f"Unknown color key: {color_key}")

    # Check if there's a custom color in settings
    settings = QSettings()
    settings.beginGroup("app_colors")
    color_hex = settings.value(color_key, DEFAULT_COLORS[color_key])
    settings.endGroup()

    return QColor(color_hex)

def set_color(color_key, color_value):
    """
    Set a custom color in settings.

    Args:
        color_key: The key for the color in the DEFAULT_COLORS dictionary
        color_value: The color value as a hex string or QColor

    Returns:
        None
    """
    if color_key not in DEFAULT_COLORS:
        raise ValueError(f"Unknown color key: {color_key}")

    # Convert QColor to hex string if needed
    if isinstance(color_value, QColor):
        color_hex = color_value.name()
    else:
        color_hex = color_value

    # Save to settings
    settings = QSettings()
    settings.beginGroup("app_colors")
    settings.setValue(color_key, color_hex)
    settings.endGroup()
    settings.sync()

def reset_color(color_key):
    """
    Reset a color to its default value.

    Args:
        color_key: The key for the color in the DEFAULT_COLORS dictionary

    Returns:
        None
    """
    if color_key not in DEFAULT_COLORS:
        raise ValueError(f"Unknown color key: {color_key}")

    # Remove custom color from settings
    settings = QSettings()
    settings.beginGroup("app_colors")
    settings.remove(color_key)
    settings.endGroup()
    settings.sync()

def reset_all_colors():
    """
    Reset all colors to their default values.

    Returns:
        None
    """
    settings = QSettings()
    settings.beginGroup("app_colors")
    settings.remove("")  # Remove all keys in this group
    settings.endGroup()

    # Also reset the current theme
    settings.setValue("app_colors/current_theme", "default")
    settings.sync()

def get_current_theme():
    """
    Get the name of the currently selected theme.

    Returns:
        The name of the current theme (string)
    """
    settings = QSettings()
    return settings.value("app_colors/current_theme", "default")

def set_current_theme(theme_name):
    """
    Set the current theme.

    Args:
        theme_name: The name of the theme to set

    Returns:
        None
    """
    settings = QSettings()
    settings.setValue("app_colors/current_theme", theme_name)
    settings.sync()

def apply_theme(theme_name, override_customized=False):
    """
    Apply a theme by setting all colors to the theme's values.

    Args:
        theme_name: The name of the theme to apply
        override_customized: Whether to override customized colors (default: False)

    Returns:
        None
    """
    # Import here to avoid circular imports
    from app.utils.color_themes import get_theme

    # Get the theme colors
    theme_colors = get_theme(theme_name)

    # Get the list of customized colors
    customized_colors = get_customized_colors()

    # Apply the theme colors
    settings = QSettings()
    settings.beginGroup("app_colors")

    for color_key, color_value in theme_colors.items():
        # Skip customized colors unless override_customized is True
        if not override_customized and color_key in customized_colors:
            continue

        settings.setValue(color_key, color_value)

    settings.endGroup()

    # Set the current theme
    set_current_theme(theme_name)
    settings.sync()

def get_customized_colors():
    """
    Get a list of color keys that have been customized by the user.

    Returns:
        A list of color keys
    """
    settings = QSettings()
    settings.beginGroup("app_colors")
    customized_keys = [key for key in settings.childKeys() if key != "current_theme"]
    settings.endGroup()

    return customized_keys

def is_color_customized(color_key):
    """
    Check if a color has been customized by the user.

    Args:
        color_key: The key for the color

    Returns:
        True if the color has been customized, False otherwise
    """
    settings = QSettings()
    settings.beginGroup("app_colors")
    is_customized = color_key in settings.childKeys()
    settings.endGroup()

    return is_customized

def get_color_categories():
    """
    Get a dictionary of color categories and their color keys.

    Returns:
        A dictionary with category names as keys and lists of color keys as values
    """
    return {
        "UI Elements": [
            "chart_header_bg"
        ],
        "Player Status": [
            "goalkeeper_highlight",
            "selected_player",
            "unselected_player",
            "player_list_bg"
        ],
        "Evaluation Ratings": [
            "rating_excellent",
            "rating_excellent_text",
            "rating_good",
            "rating_good_text",
            "rating_average",
            "rating_average_text",
            "rating_poor",
            "rating_poor_text",
            "group_header_bg",
            "evaluation_low",
            "evaluation_medium",
            "evaluation_high"
        ],
        "Attendance Status": [
            "attendance_present",
            "attendance_absent",
            "attendance_injured",
            "attendance_injured_present",
            "attendance_special"
        ],
        "Attendance Calendar": [
            "attendance_weekend",
            "attendance_today",
            "attendance_out_of_season_bg",
            "attendance_out_of_season_text",
            "attendance_name_column_bg",
            "attendance_week_divider"
        ],
        "Position Ratings": [
            "position_best_fit",
            "position_can_play",
            "position_alternative"
        ],
        "Season Timeline": [
            "season_bg",
            "macrocycle_bg",
            "mesocycle_preparation",
            "mesocycle_basic",
            "mesocycle_competition",
            "mesocycle_transition",
            "evaluation_1st",
            "evaluation_2nd",
            "evaluation_3rd"
        ],
        "Trend Indicators": [
            "trend_significant_improvement",
            "trend_moderate_improvement",
            "trend_no_change",
            "trend_moderate_regression",
            "trend_significant_regression"
        ],
        "Matches": [
            "match_validation_serious",
            "match_validation_warning",
            "match_validation_ok",
            "match_missing_minutes_highlight"
        ],
        "Roster": [
            "roster_outlier_high",
            "roster_outlier_low"
        ]
    }

def get_color_name(color_key):
    """
    Get a human-readable name for a color key.

    Args:
        color_key: The key for the color in the DEFAULT_COLORS dictionary

    Returns:
        A human-readable name for the color key
    """
    # Map of color keys to human-readable names
    color_names = {
        # UI Elements
        "chart_header_bg": "Chart Header Background",

        # Player Status
        "goalkeeper_highlight": "Goalkeeper Name Color",
        "selected_player": "Selected Player Color",
        "unselected_player": "Unselected Player Color",
        "player_list_bg": "Player List Background",

        # Evaluation Ratings
        "rating_excellent": "Excellent Rating Background",
        "rating_excellent_text": "Excellent Rating Text",
        "rating_good": "Good Rating Background",
        "rating_good_text": "Good Rating Text",
        "rating_average": "Average Rating Background",
        "rating_average_text": "Average Rating Text",
        "rating_poor": "Poor Rating Background",
        "rating_poor_text": "Poor Rating Text",
        "group_header_bg": "Group Header Background",
        "evaluation_low": "Chart Low Rating Color",
        "evaluation_medium": "Chart Medium Rating Color",
        "evaluation_high": "Chart High Rating Color",

        # Trend Colors
        "trend_significant_improvement": "Significant Improvement",
        "trend_moderate_improvement": "Moderate Improvement",
        "trend_no_change": "No Change",
        "trend_moderate_regression": "Moderate Regression",
        "trend_significant_regression": "Significant Regression",

        # Attendance Status
        "attendance_present": "Present",
        "attendance_absent": "Absent",
        "attendance_injured": "Injured",
        "attendance_injured_present": "Injured but Present",
        "attendance_special": "Special Status",

        # Attendance Calendar
        "attendance_weekend": "Weekend Days",
        "attendance_today": "Today's Date",
        "attendance_out_of_season_bg": "Out of Season Background",
        "attendance_out_of_season_text": "Out of Season Text",
        "attendance_name_column_bg": "Name Column Background",
        "attendance_week_divider": "Week Divider",

        # Position Ratings
        "position_best_fit": "Best Fit Position",
        "position_can_play": "Can Play Position",
        "position_alternative": "Alternative Position",

        # Season Timeline
        "season_bg": "Season Background",
        "macrocycle_bg": "Macrocycle Background",
        "mesocycle_preparation": "Preparation Mesocycle",
        "mesocycle_basic": "Basic Mesocycle",
        "mesocycle_competition": "Competition Mesocycle",
        "mesocycle_transition": "Transition Mesocycle",
        "evaluation_1st": "1st Evaluation",
        "evaluation_2nd": "2nd Evaluation",
        "evaluation_3rd": "3rd Evaluation",

        # Match Validation
        "match_validation_serious": "Serious data missing/errors",
        "match_validation_warning": "Warnings",
        "match_validation_ok": "Checked ok",
        "match_missing_minutes_highlight": "Missing minutes cell highlight",

        # Roster Outliers
        "roster_outlier_high": "High Outlier Values (Above Average)",
        "roster_outlier_low": "Low Outlier Values (Below Average)"
    }

    # Return the mapped name if it exists, otherwise fallback to a formatted version of the key
    return color_names.get(color_key, color_key.replace('_', ' ').title())
