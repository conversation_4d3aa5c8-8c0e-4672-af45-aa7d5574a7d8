<?xml version='1.0' encoding='utf-8'?>
<TS version="2.1" language="el_GR">
<context>
    <name>PhysicalChartDialog</name>
    <message>
        <source>Physical Profile Chart</source>
        <translation>Γράφημα Φυσικού Προφίλ</translation>
    </message>
    <message>
        <source>BMI</source>
        <translation>ΔΜΣ</translation>
    </message>
    <message>
        <source>Body Fat %</source>
        <translation>Λίπος Σώματος %</translation>
    </message>
    <message>
        <source>Lean Mass (kg)</source>
        <translation>Μυϊκή Μάζα (kg)</translation>
    </message>
    <message>
        <source>W/H Ratio</source>
        <translation>Αναλογία Μ/Γ</translation>
    </message>
    <message>
        <source>W/Ht Ratio</source>
        <translation>Αναλογία Μ/Υ</translation>
    </message>
    <message>
        <source>Player</source>
        <translation>Παίκτης</translation>
    </message>
    <message>
        <source>Club Average</source>
        <translation>Μέσος Όρος Συλλόγου</translation>
    </message>
    <message>
        <source>Club Avg (Male)</source>
        <translation>Μ.Ο. Συλλόγου (Άνδρες)</translation>
    </message>
    <message>
        <source>Club Avg (Female)</source>
        <translation>Μ.Ο. Συλλόγου (Γυναίκες)</translation>
    </message>
    <message>
        <source>View Physical Profile Chart</source>
        <translation>Προβολή Γραφήματος Φυσικού Προφίλ</translation>
    </message>
    <message>
        <source>Insufficient Data</source>
        <translation>Ανεπαρκή Δεδομένα</translation>
    </message>
    <message>
        <source>Not enough physical data available for the selected player to generate a chart.</source>
        <translation>Δεν υπάρχουν επαρκή φυσικά δεδομένα για τον επιλεγμένο παίκτη για τη δημιουργία γραφήματος.</translation>
    </message>
    <message>
        <source>Error</source>
        <translation>Σφάλμα</translation>
    </message>
    <message>
        <source>Failed to retrieve data for the chart.</source>
        <translation>Αποτυχία ανάκτησης δεδομένων για το γράφημα.</translation>
    </message>
</context>
<context>
    <name>RosterPage</name>
    <message>
        <source>Show Body Data Columns</source>
        <translation>Εμφάνιση Στηλών Δεδομένων Σώματος</translation>
    </message>
</context>
<context><name>TeamGroupsWidget</name><message><source>Group Id</source><translation>ID</translation></message><message><source>Team Name</source><translation type="unfinished">Team Name</translation></message><message><source>Group Category</source><translation type="unfinished">Group Category</translation></message><message><source>Add Group</source><translation type="unfinished">Add Group</translation></message><message><source>Remove Selected</source><translation type="unfinished">Remove Selected</translation></message><message><source>Search:</source><translation type="unfinished">Search:</translation></message><message><source>Search...</source><translation type="unfinished">Search...</translation></message><message><source>Clear</source><translation type="unfinished">Clear</translation></message><message><source>Filter</source><translation type="unfinished">Filter</translation></message><message><source>Clear Filter</source><translation type="unfinished">Clear Filter</translation></message><message><source>(Unassigned)</source><translation type="unfinished">(Unassigned)</translation></message><message><source>-- Any Coach --</source><translation type="unfinished">-- Any Coach --</translation></message><message><source>Male</source><translation type="unfinished">Male</translation></message><message><source>Female</source><translation type="unfinished">Female</translation></message><message><source>All</source><translation type="unfinished">All</translation></message><message><source>Any</source><translation type="unfinished">Any</translation></message></context><context><name>TeamGroupDelegate</name><message><source>(Unassigned)</source><translation type="unfinished">(Unassigned)</translation></message></context></TS>