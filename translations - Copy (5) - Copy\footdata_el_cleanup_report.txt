=== Cleanup Report for translations/footdata_el.ts ===
Messages to remove: 482
Messages to retain: 97

=== MESSAGES TO REMOVE ===

Context: OptionsPage
Text: Add Competition
Locations: ../app/pages/options_page.py:364, ../app/pages/options_page.py:2294, ../app/pages/options_page.py:2470, ../app/pages/options_page.py:2620

Context: OptionsPage
Text: Start Date:
Locations: ../app/pages/options_page.py:316, ../app/pages/options_page.py:2282, ../app/pages/options_page.py:3233, ../app/pages/options_page.py:3420

Context: OptionsPage
Text: End Date:
Locations: ../app/pages/options_page.py:317, ../app/pages/options_page.py:2284, ../app/pages/options_page.py:3234, ../app/pages/options_page.py:3421

Context: OptionsPage
Text: National

Context: OptionsPage
Text: International

Context: OptionsPage
Text: Local

Context: OptionsPage
Text: Other

Context: SponsorSectionWidget
Text: Other
Locations: ../app/widgets/sponsors_panel.py:112, ../app/widgets/sponsors_panel.py:680

Context: OptionsPage
Text: Type:

Context: SponsorSectionWidget
Text: Type:
Locations: ../app/widgets/sponsors_panel.py:122, ../app/widgets/sponsors_panel.py:632

Context: OptionsPage
Text: League

Context: OptionsPage
Text: League+playoffs/playouts

Context: OptionsPage
Text: Group+Knockouts

Context: OptionsPage
Text: Knockouts

Context: OptionsPage
Text: Tournament

Context: OptionsPage
Text: Preparation matches

Context: OptionsPage
Text: Charity

Context: SponsorsPanel
Text: Charity
Locations: ../app/widgets/sponsors_panel.py:977, ../app/widgets/sponsors_panel.py:1180

Context: OptionsPage
Text: Structure:

Context: OptionsPage
Text: Notes:
Locations: ../app/pages/options_page.py:3239, ../app/pages/options_page.py:3426

Context: OptionsPage
Text: No Selection
Locations: ../app/pages/options_page.py:3357, ../app/pages/options_page.py:3573

Context: OptionsPage
Text: Edit Competition
Locations: ../app/pages/options_page.py:365, ../app/pages/options_page.py:2296, ../app/pages/options_page.py:2472, ../app/pages/options_page.py:2621

Context: ClubWindow
Text: No Logo
Locations: ../app/windows/club_window.py:390, ../app/windows/club_window.py:2015, ../app/windows/club_window.py:3428

Context: SponsorSectionWidget
Text: No Logo
Locations: ../app/widgets/sponsors_panel.py:161, ../app/widgets/sponsors_panel.py:486, ../app/widgets/sponsors_panel.py:693

Context: OptionsPage
Text: Name:
Locations: ../app/pages/options_page.py:3232, ../app/pages/options_page.py:3419

Context: OptionsPage
Text: Organization:

Context: ClubWindow
Text: Select Logo Image
Locations: ../app/windows/club_window.py:2097

Context: OptionsPage
Text: File Too Large
Locations: ../app/pages/options_page.py:2674, ../app/pages/options_page.py:2798, ../app/pages/options_page.py:2922

Context: RosterPage
Text: File Too Large
Locations: ../app/pages/roster_page.py:2822

Context: OptionsPage
Text: Image Too Large
Locations: ../app/pages/options_page.py:2684, ../app/pages/options_page.py:2808, ../app/pages/options_page.py:2932

Context: SponsorSectionWidget
Text: Upload Logo
Locations: ../app/widgets/sponsors_panel.py:172, ../app/widgets/sponsors_panel.py:694

Context: RosterPage
Text: ID:
Locations: ../app/pages/roster_page.py:663, ../app/pages/roster_page.py:3448

Context: OptionsPage
Text: Dates
Locations: ../app/pages/options_page.py:1040, ../app/pages/options_page.py:2260

Context: OptionsPage
Text: Please select a microcycle to edit.
Locations: ../app/pages/options_page.py:3358

Context: OptionsPage
Text: End date must be after start date
Locations: ../app/pages/options_page.py:1273, ../app/pages/options_page.py:1680, ../app/pages/options_page.py:1696, ../app/pages/options_page.py:1718, ../app/pages/options_page.py:3268, ../app/pages/options_page.py:3455

Context: OptionsPage
Text: Start date must be within macrocycle dates
Locations: ../app/pages/options_page.py:3274, ../app/pages/options_page.py:3461

Context: OptionsPage
Text: End date must be within macrocycle dates
Locations: ../app/pages/options_page.py:3279, ../app/pages/options_page.py:3466

Context: OptionsPage
Text: Microcycle dates cannot overlap with existing microcycles in this mesocycle
Locations: ../app/pages/options_page.py:3292, ../app/pages/options_page.py:3480

Context: OptionsPage
Text: Target 1:
Locations: ../app/pages/options_page.py:3235, ../app/pages/options_page.py:3422

Context: OptionsPage
Text: Target 2:
Locations: ../app/pages/options_page.py:3236, ../app/pages/options_page.py:3423

Context: OptionsPage
Text: Target 3:
Locations: ../app/pages/options_page.py:3237, ../app/pages/options_page.py:3424

Context: OptionsPage
Text: Intensity:
Locations: ../app/pages/options_page.py:3238, ../app/pages/options_page.py:3425

Context: OptionsPage
Text: Please select a microcycle to remove.
Locations: ../app/pages/options_page.py:3574

Context: OptionsPage
Text: Are you sure you want to remove this microcycle?
Locations: ../app/pages/options_page.py:3584

Context: OptionsPage
Text: Microcycle Overlap Error
Locations: ../app/pages/options_page.py:3715

Context: OptionsPage
Text: Error: Cannot save because there are overlapping microcycles in your schedule:

{}

Please fix the overlaps before saving.
Locations: ../app/pages/options_page.py:3716

Context: OptionsPage
Text: Intensity
Locations: ../app/pages/options_page.py:473, ../app/pages/options_page.py:546, ../app/pages/options_page.py:613, ../app/pages/options_page.py:680, ../app/pages/options_page.py:2328, ../app/pages/options_page.py:2343, ../app/pages/options_page.py:2358, ../app/pages/options_page.py:2373

Context: PeriodizationChartDialog
Text: Intensity
Locations: ../app/dialogs/periodization_chart_dialog.py:467, ../app/dialogs/periodization_chart_dialog.py:578, ../app/dialogs/periodization_chart_dialog.py:647, ../app/dialogs/periodization_chart_dialog.py:897, ../app/dialogs/periodization_chart_dialog_fixed.py:382, ../app/dialogs/periodization_chart_dialog_fixed.py:474, ../app/dialogs/periodization_chart_dialog_complete.py:412, ../app/dialogs/periodization_chart_dialog_complete.py:520, ../app/dialogs/periodization_chart_dialog_complete.py:586

Context: self.parent_widget
Text: Intensity
Locations: ../app/widgets/season_timeline_widget_new.py:636

Context: MainWindow
Text: Microcycles

Context: PeriodizationChartDialog
Text: Microcycles
Locations: ../app/dialogs/periodization_chart_dialog.py:923

Context: PeriodizationTimelineWidget
Text: Microcycles
Locations: ../app/widgets/periodization_timeline_widget.py:52

Context: SeasonTimelineWidget
Text: Microcycles

Context: self.parent_widget
Text: Microcycles
Locations: ../app/widgets/season_timeline_widget_new.py:277

Context: TimelineWindow
Text: Microcycles

Context: Timeline
Text: Microcycles

Context: SeasonTimeline
Text: Microcycles

Context: ChronogramWindow
Text: Microcycles

Context: Chronogram
Text: Microcycles

Context: Χρονοδιάγραμμα
Text: Microcycles

Context: χρονοδιάγραμμα
Text: Microcycles

Context: Χρονοδιάγραμμα Σεζόν
Text: Microcycles

Context: ΧρονοδιάγραμμαΣεζόν
Text: Microcycles

Context: SeasonChronogram
Text: Microcycles

Context: XronodiagraamaSezon
Text: Microcycles

Context: ΧρονοδιάγραμμαΑγώνων
Text: Microcycles

Context: TimelineForm
Text: Microcycles

Context: TimelineControl
Text: Microcycles

Context: SeasonTimelineForm
Text: Microcycles

Context: SeasonView
Text: Microcycles

Context: ChronogramForm
Text: Microcycles

Context: PeriodizationChartDialog
Text: Date
Locations: ../app/dialogs/periodization_chart_dialog.py:375, ../app/dialogs/periodization_chart_dialog_fixed.py:313, ../app/dialogs/periodization_chart_dialog_complete.py:335

Context: PeriodizationChartDialog
Text: Intensity (%)
Locations: ../app/dialogs/periodization_chart_dialog.py:380, ../app/dialogs/periodization_chart_dialog_fixed.py:318, ../app/dialogs/periodization_chart_dialog_complete.py:340

Context: RosterPage
Text: Phone:
Locations: ../app/pages/roster_page.py:3612

Context: SponsorSectionWidget
Text: Phone:
Locations: ../app/widgets/sponsors_panel.py:209, ../app/widgets/sponsors_panel.py:709

Context: RosterPage
Text: Email:
Locations: ../app/pages/roster_page.py:3611

Context: SponsorSectionWidget
Text: Email:
Locations: ../app/widgets/sponsors_panel.py:208, ../app/widgets/sponsors_panel.py:708

Context: OptionsPage
Text: Remove
Locations: ../app/pages/options_page.py:893, ../app/pages/options_page.py:922, ../app/pages/options_page.py:2432, ../app/pages/options_page.py:2444

Context: MainWindow
Text: Settings

Context: SettingsWindow
Text: Settings
Locations: ../app/windows/settings_window.py:323, ../app/windows/settings_window.py:943

Context: OptionsPage
Text: Name
Locations: ../app/pages/options_page.py:469, ../app/pages/options_page.py:542, ../app/pages/options_page.py:609, ../app/pages/options_page.py:676, ../app/pages/options_page.py:2326, ../app/pages/options_page.py:2341, ../app/pages/options_page.py:2356, ../app/pages/options_page.py:2371, ../app/pages/options_page.py:2466, ../app/pages/options_page.py:2587

Context: RosterPage
Text: Name
Locations: ../app/pages/roster_page.py:1378, ../app/pages/roster_page.py:3813

Context: OptionsPage
Text: PNG Images (*.png)
Locations: ../app/pages/options_page.py:2654, ../app/pages/options_page.py:2778, ../app/pages/options_page.py:2902

Context: RosterPage
Text: PNG Images (*.png)
Locations: ../app/pages/roster_page.py:2796

Context: SponsorSectionWidget
Text: PNG Images (*.png)
Locations: ../app/widgets/sponsors_panel.py:308

Context: SponsorSectionWidget
Text: Error copying logo!
Locations: ../app/widgets/sponsors_panel.py:376

Context: SponsorSectionWidget
Text: Remove Logo
Locations: ../app/widgets/sponsors_panel.py:173, ../app/widgets/sponsors_panel.py:695

Context: OptionsPage
Text: Add
Locations: ../app/pages/options_page.py:921, ../app/pages/options_page.py:2441

Context: TeamGroupsWidget
Text: Search...
Locations: ../app/widgets/team_groups_widget.py:491, ../app/widgets/team_groups_widget.py:1006

Context: SettingsWindow
Text: Search:
Locations: ../app/windows/settings_window.py:1034

Context: TeamGroupsWidget
Text: Search:
Locations: ../app/widgets/team_groups_widget.py:503, ../app/widgets/team_groups_widget.py:991, ../app/widgets/team_groups_widget.py:1000

Context: StaffFilterDialog
Text: Filter
Locations: ../app/dialogs/staff_filter_dialog.py:207

Context: TeamGroupsWidget
Text: Filter
Locations: ../app/widgets/team_groups_widget.py:1011

Context: TeamGroupsWidget
Text: Clear
Locations: ../app/widgets/team_groups_widget.py:492, ../app/widgets/team_groups_widget.py:1008

Context: TeamGroupsWidget
Text: Clear Filter
Locations: ../app/widgets/team_groups_widget.py:1014

Context: StaffAssignmentDialog
Text: (Unassigned)
Locations: ../app/widgets/staff_assignment_dialog.py:67, ../app/dialogs/staff_assignment_dialog.py:67

Context: RosterPage
Text: No Image
Locations: ../app/pages/roster_page.py:4773

Context: SponsorSectionWidget
Text: Error: Cannot read image file.
Locations: ../app/widgets/sponsors_panel.py:333

Context: OptionsPage
Text: Error
Locations: ../app/pages/options_page.py:3131

Context: RemoveFontDialog
Text: Error
Locations: ../app/windows/settings_window.py:125, ../app/windows/settings_window.py:130

Context: RosterPage
Text: Error
Locations: ../app/pages/roster_page.py:1560, ../app/pages/roster_page.py:2707, ../app/pages/roster_page.py:2746, ../app/pages/roster_page.py:4448

Context: SettingsWindow
Text: Error
Locations: ../app/windows/settings_window.py:694, ../app/windows/settings_window.py:697, ../app/windows/settings_window.py:763

Context: TeamGroupsWidget
Text: Error
Locations: ../app/widgets/team_groups_widget.py:717, ../app/widgets/team_groups_widget.py:724

Context: MainWindow
Text: Options

Context: OptionsPage
Text: Options
Locations: ../app/pages/options_page.py:241, ../app/pages/options_page.py:2252

Context: SeasonTimelineWidget
Text: Show Months
Locations: ../app/widgets/season_timeline_widget_new.py:92, ../app/widgets/season_timeline_widget_new.py:143

Context: TimelineWindow
Text: Show Months

Context: Timeline
Text: Show Months

Context: SeasonTimeline
Text: Show Months

Context: ChronogramWindow
Text: Show Months

Context: Chronogram
Text: Show Months

Context: Χρονοδιάγραμμα
Text: Show Months

Context: χρονοδιάγραμμα
Text: Show Months

Context: Χρονοδιάγραμμα Σεζόν
Text: Show Months

Context: ΧρονοδιάγραμμαΣεζόν
Text: Show Months

Context: SeasonChronogram
Text: Show Months

Context: XronodiagraamaSezon
Text: Show Months

Context: ΧρονοδιάγραμμαΑγώνων
Text: Show Months

Context: TimelineForm
Text: Show Months

Context: TimelineControl
Text: Show Months

Context: SeasonTimelineForm
Text: Show Months

Context: SeasonView
Text: Show Months

Context: ChronogramForm
Text: Show Months

Context: SeasonTimelineWidget
Text: Show Weeks
Locations: ../app/widgets/season_timeline_widget_new.py:96, ../app/widgets/season_timeline_widget_new.py:144

Context: TimelineWindow
Text: Show Weeks

Context: Timeline
Text: Show Weeks

Context: SeasonTimeline
Text: Show Weeks

Context: ChronogramWindow
Text: Show Weeks

Context: Chronogram
Text: Show Weeks

Context: Χρονοδιάγραμμα
Text: Show Weeks

Context: χρονοδιάγραμμα
Text: Show Weeks

Context: Χρονοδιάγραμμα Σεζόν
Text: Show Weeks

Context: ΧρονοδιάγραμμαΣεζόν
Text: Show Weeks

Context: SeasonChronogram
Text: Show Weeks

Context: XronodiagraamaSezon
Text: Show Weeks

Context: ΧρονοδιάγραμμαΑγώνων
Text: Show Weeks

Context: TimelineForm
Text: Show Weeks

Context: TimelineControl
Text: Show Weeks

Context: SeasonTimelineForm
Text: Show Weeks

Context: SeasonView
Text: Show Weeks

Context: ChronogramForm
Text: Show Weeks

Context: SeasonTimelineWidget
Text: Show Intensity Chart
Locations: ../app/widgets/season_timeline_widget_new.py:100, ../app/widgets/season_timeline_widget_new.py:145

Context: TimelineWindow
Text: Show Intensity Chart

Context: Timeline
Text: Show Intensity Chart

Context: SeasonTimeline
Text: Show Intensity Chart

Context: ChronogramWindow
Text: Show Intensity Chart

Context: Chronogram
Text: Show Intensity Chart

Context: Χρονοδιάγραμμα
Text: Show Intensity Chart

Context: χρονοδιάγραμμα
Text: Show Intensity Chart

Context: Χρονοδιάγραμμα Σεζόν
Text: Show Intensity Chart

Context: ΧρονοδιάγραμμαΣεζόν
Text: Show Intensity Chart

Context: SeasonChronogram
Text: Show Intensity Chart

Context: XronodiagraamaSezon
Text: Show Intensity Chart

Context: ΧρονοδιάγραμμαΑγώνων
Text: Show Intensity Chart

Context: TimelineForm
Text: Show Intensity Chart

Context: TimelineControl
Text: Show Intensity Chart

Context: SeasonTimelineForm
Text: Show Intensity Chart

Context: SeasonView
Text: Show Intensity Chart

Context: ChronogramForm
Text: Show Intensity Chart

Context: SeasonTimelineWidget
Text: Jan

Context: TimelineWindow
Text: Jan

Context: Timeline
Text: Jan

Context: SeasonTimeline
Text: Jan

Context: ChronogramWindow
Text: Jan

Context: Chronogram
Text: Jan

Context: Χρονοδιάγραμμα
Text: Jan

Context: χρονοδιάγραμμα
Text: Jan

Context: Χρονοδιάγραμμα Σεζόν
Text: Jan

Context: ΧρονοδιάγραμμαΣεζόν
Text: Jan

Context: SeasonChronogram
Text: Jan

Context: XronodiagraamaSezon
Text: Jan

Context: ΧρονοδιάγραμμαΑγώνων
Text: Jan

Context: TimelineForm
Text: Jan

Context: TimelineControl
Text: Jan

Context: SeasonTimelineForm
Text: Jan

Context: SeasonView
Text: Jan

Context: ChronogramForm
Text: Jan

Context: QPlatformTheme
Text: Jan

Context: SeasonTimelineWidget
Text: Feb

Context: TimelineWindow
Text: Feb

Context: Timeline
Text: Feb

Context: SeasonTimeline
Text: Feb

Context: ChronogramWindow
Text: Feb

Context: Chronogram
Text: Feb

Context: Χρονοδιάγραμμα
Text: Feb

Context: χρονοδιάγραμμα
Text: Feb

Context: Χρονοδιάγραμμα Σεζόν
Text: Feb

Context: ΧρονοδιάγραμμαΣεζόν
Text: Feb

Context: SeasonChronogram
Text: Feb

Context: XronodiagraamaSezon
Text: Feb

Context: ΧρονοδιάγραμμαΑγώνων
Text: Feb

Context: TimelineForm
Text: Feb

Context: TimelineControl
Text: Feb

Context: SeasonTimelineForm
Text: Feb

Context: SeasonView
Text: Feb

Context: ChronogramForm
Text: Feb

Context: QPlatformTheme
Text: Feb

Context: SeasonTimelineWidget
Text: Mar

Context: TimelineWindow
Text: Mar

Context: Timeline
Text: Mar

Context: SeasonTimeline
Text: Mar

Context: ChronogramWindow
Text: Mar

Context: Chronogram
Text: Mar

Context: Χρονοδιάγραμμα
Text: Mar

Context: χρονοδιάγραμμα
Text: Mar

Context: Χρονοδιάγραμμα Σεζόν
Text: Mar

Context: ΧρονοδιάγραμμαΣεζόν
Text: Mar

Context: SeasonChronogram
Text: Mar

Context: XronodiagraamaSezon
Text: Mar

Context: ΧρονοδιάγραμμαΑγώνων
Text: Mar

Context: TimelineForm
Text: Mar

Context: TimelineControl
Text: Mar

Context: SeasonTimelineForm
Text: Mar

Context: SeasonView
Text: Mar

Context: ChronogramForm
Text: Mar

Context: QPlatformTheme
Text: Mar

Context: SeasonTimelineWidget
Text: Apr

Context: TimelineWindow
Text: Apr

Context: Timeline
Text: Apr

Context: SeasonTimeline
Text: Apr

Context: ChronogramWindow
Text: Apr

Context: Chronogram
Text: Apr

Context: Χρονοδιάγραμμα
Text: Apr

Context: χρονοδιάγραμμα
Text: Apr

Context: Χρονοδιάγραμμα Σεζόν
Text: Apr

Context: ΧρονοδιάγραμμαΣεζόν
Text: Apr

Context: SeasonChronogram
Text: Apr

Context: XronodiagraamaSezon
Text: Apr

Context: ΧρονοδιάγραμμαΑγώνων
Text: Apr

Context: TimelineForm
Text: Apr

Context: TimelineControl
Text: Apr

Context: SeasonTimelineForm
Text: Apr

Context: SeasonView
Text: Apr

Context: ChronogramForm
Text: Apr

Context: QPlatformTheme
Text: Apr

Context: SeasonTimelineWidget
Text: May

Context: TimelineWindow
Text: May

Context: Timeline
Text: May

Context: SeasonTimeline
Text: May

Context: ChronogramWindow
Text: May

Context: Chronogram
Text: May

Context: Χρονοδιάγραμμα
Text: May

Context: χρονοδιάγραμμα
Text: May

Context: Χρονοδιάγραμμα Σεζόν
Text: May

Context: ΧρονοδιάγραμμαΣεζόν
Text: May

Context: SeasonChronogram
Text: May

Context: XronodiagraamaSezon
Text: May

Context: ΧρονοδιάγραμμαΑγώνων
Text: May

Context: TimelineForm
Text: May

Context: TimelineControl
Text: May

Context: SeasonTimelineForm
Text: May

Context: SeasonView
Text: May

Context: ChronogramForm
Text: May

Context: QPlatformTheme
Text: May

Context: SeasonTimelineWidget
Text: Jun

Context: TimelineWindow
Text: Jun

Context: Timeline
Text: Jun

Context: SeasonTimeline
Text: Jun

Context: ChronogramWindow
Text: Jun

Context: Chronogram
Text: Jun

Context: Χρονοδιάγραμμα
Text: Jun

Context: χρονοδιάγραμμα
Text: Jun

Context: Χρονοδιάγραμμα Σεζόν
Text: Jun

Context: ΧρονοδιάγραμμαΣεζόν
Text: Jun

Context: SeasonChronogram
Text: Jun

Context: XronodiagraamaSezon
Text: Jun

Context: ΧρονοδιάγραμμαΑγώνων
Text: Jun

Context: TimelineForm
Text: Jun

Context: TimelineControl
Text: Jun

Context: SeasonTimelineForm
Text: Jun

Context: SeasonView
Text: Jun

Context: ChronogramForm
Text: Jun

Context: QPlatformTheme
Text: Jun

Context: SeasonTimelineWidget
Text: Jul

Context: TimelineWindow
Text: Jul

Context: Timeline
Text: Jul

Context: SeasonTimeline
Text: Jul

Context: ChronogramWindow
Text: Jul

Context: Chronogram
Text: Jul

Context: Χρονοδιάγραμμα
Text: Jul

Context: χρονοδιάγραμμα
Text: Jul

Context: Χρονοδιάγραμμα Σεζόν
Text: Jul

Context: ΧρονοδιάγραμμαΣεζόν
Text: Jul

Context: SeasonChronogram
Text: Jul

Context: XronodiagraamaSezon
Text: Jul

Context: ΧρονοδιάγραμμαΑγώνων
Text: Jul

Context: TimelineForm
Text: Jul

Context: TimelineControl
Text: Jul

Context: SeasonTimelineForm
Text: Jul

Context: SeasonView
Text: Jul

Context: ChronogramForm
Text: Jul

Context: QPlatformTheme
Text: Jul

Context: SeasonTimelineWidget
Text: Aug

Context: TimelineWindow
Text: Aug

Context: Timeline
Text: Aug

Context: SeasonTimeline
Text: Aug

Context: ChronogramWindow
Text: Aug

Context: Chronogram
Text: Aug

Context: Χρονοδιάγραμμα
Text: Aug

Context: χρονοδιάγραμμα
Text: Aug

Context: Χρονοδιάγραμμα Σεζόν
Text: Aug

Context: ΧρονοδιάγραμμαΣεζόν
Text: Aug

Context: SeasonChronogram
Text: Aug

Context: XronodiagraamaSezon
Text: Aug

Context: ΧρονοδιάγραμμαΑγώνων
Text: Aug

Context: TimelineForm
Text: Aug

Context: TimelineControl
Text: Aug

Context: SeasonTimelineForm
Text: Aug

Context: SeasonView
Text: Aug

Context: ChronogramForm
Text: Aug

Context: QPlatformTheme
Text: Aug

Context: SeasonTimelineWidget
Text: Sep

Context: TimelineWindow
Text: Sep

Context: Timeline
Text: Sep

Context: SeasonTimeline
Text: Sep

Context: ChronogramWindow
Text: Sep

Context: Chronogram
Text: Sep

Context: Χρονοδιάγραμμα
Text: Sep

Context: χρονοδιάγραμμα
Text: Sep

Context: Χρονοδιάγραμμα Σεζόν
Text: Sep

Context: ΧρονοδιάγραμμαΣεζόν
Text: Sep

Context: SeasonChronogram
Text: Sep

Context: XronodiagraamaSezon
Text: Sep

Context: ΧρονοδιάγραμμαΑγώνων
Text: Sep

Context: TimelineForm
Text: Sep

Context: TimelineControl
Text: Sep

Context: SeasonTimelineForm
Text: Sep

Context: SeasonView
Text: Sep

Context: ChronogramForm
Text: Sep

Context: QPlatformTheme
Text: Sep

Context: SeasonTimelineWidget
Text: Oct

Context: TimelineWindow
Text: Oct

Context: Timeline
Text: Oct

Context: SeasonTimeline
Text: Oct

Context: ChronogramWindow
Text: Oct

Context: Chronogram
Text: Oct

Context: Χρονοδιάγραμμα
Text: Oct

Context: χρονοδιάγραμμα
Text: Oct

Context: Χρονοδιάγραμμα Σεζόν
Text: Oct

Context: ΧρονοδιάγραμμαΣεζόν
Text: Oct

Context: SeasonChronogram
Text: Oct

Context: XronodiagraamaSezon
Text: Oct

Context: ΧρονοδιάγραμμαΑγώνων
Text: Oct

Context: TimelineForm
Text: Oct

Context: TimelineControl
Text: Oct

Context: SeasonTimelineForm
Text: Oct

Context: SeasonView
Text: Oct

Context: ChronogramForm
Text: Oct

Context: QPlatformTheme
Text: Oct

Context: SeasonTimelineWidget
Text: Nov

Context: TimelineWindow
Text: Nov

Context: Timeline
Text: Nov

Context: SeasonTimeline
Text: Nov

Context: ChronogramWindow
Text: Nov

Context: Chronogram
Text: Nov

Context: Χρονοδιάγραμμα
Text: Nov

Context: χρονοδιάγραμμα
Text: Nov

Context: Χρονοδιάγραμμα Σεζόν
Text: Nov

Context: ΧρονοδιάγραμμαΣεζόν
Text: Nov

Context: SeasonChronogram
Text: Nov

Context: XronodiagraamaSezon
Text: Nov

Context: ΧρονοδιάγραμμαΑγώνων
Text: Nov

Context: TimelineForm
Text: Nov

Context: TimelineControl
Text: Nov

Context: SeasonTimelineForm
Text: Nov

Context: SeasonView
Text: Nov

Context: ChronogramForm
Text: Nov

Context: QPlatformTheme
Text: Nov

Context: SeasonTimelineWidget
Text: Dec

Context: TimelineWindow
Text: Dec

Context: Timeline
Text: Dec

Context: SeasonTimeline
Text: Dec

Context: ChronogramWindow
Text: Dec

Context: Chronogram
Text: Dec

Context: Χρονοδιάγραμμα
Text: Dec

Context: χρονοδιάγραμμα
Text: Dec

Context: Χρονοδιάγραμμα Σεζόν
Text: Dec

Context: ΧρονοδιάγραμμαΣεζόν
Text: Dec

Context: SeasonChronogram
Text: Dec

Context: XronodiagraamaSezon
Text: Dec

Context: ΧρονοδιάγραμμαΑγώνων
Text: Dec

Context: TimelineForm
Text: Dec

Context: TimelineControl
Text: Dec

Context: SeasonTimelineForm
Text: Dec

Context: SeasonView
Text: Dec

Context: ChronogramForm
Text: Dec

Context: QPlatformTheme
Text: Dec

Context: OptionsPage
Text: Season
Locations: ../app/pages/options_page.py:841, ../app/pages/options_page.py:2268

Context: SeasonTimelineWidget
Text: Season

Context: SponsorSectionWidget
Text: Season
Locations: ../app/widgets/sponsors_panel.py:109, ../app/widgets/sponsors_panel.py:677

Context: self.parent_widget
Text: Season
Locations: ../app/widgets/season_timeline_widget_new.py:274

Context: TimelineWindow
Text: Season

Context: Timeline
Text: Season

Context: SeasonTimeline
Text: Season

Context: ChronogramWindow
Text: Season

Context: Chronogram
Text: Season

Context: Χρονοδιάγραμμα
Text: Season

Context: χρονοδιάγραμμα
Text: Season

Context: Χρονοδιάγραμμα Σεζόν
Text: Season

Context: ΧρονοδιάγραμμαΣεζόν
Text: Season

Context: SeasonChronogram
Text: Season

Context: XronodiagraamaSezon
Text: Season

Context: ΧρονοδιάγραμμαΑγώνων
Text: Season

Context: TimelineForm
Text: Season

Context: TimelineControl
Text: Season

Context: SeasonTimelineForm
Text: Season

Context: SeasonView
Text: Season

Context: ChronogramForm
Text: Season

Context: PeriodizationTimelineWidget
Text: Mesocycles
Locations: ../app/widgets/periodization_timeline_widget.py:48

Context: SeasonTimelineWidget
Text: Mesocycles

Context: self.parent_widget
Text: Mesocycles
Locations: ../app/widgets/season_timeline_widget_new.py:276

Context: TimelineWindow
Text: Mesocycles

Context: Timeline
Text: Mesocycles

Context: SeasonTimeline
Text: Mesocycles

Context: ChronogramWindow
Text: Mesocycles

Context: Chronogram
Text: Mesocycles

Context: Χρονοδιάγραμμα
Text: Mesocycles

Context: χρονοδιάγραμμα
Text: Mesocycles

Context: Χρονοδιάγραμμα Σεζόν
Text: Mesocycles

Context: ΧρονοδιάγραμμαΣεζόν
Text: Mesocycles

Context: SeasonChronogram
Text: Mesocycles

Context: XronodiagraamaSezon
Text: Mesocycles

Context: ΧρονοδιάγραμμαΑγώνων
Text: Mesocycles

Context: TimelineForm
Text: Mesocycles

Context: TimelineControl
Text: Mesocycles

Context: SeasonTimelineForm
Text: Mesocycles

Context: SeasonView
Text: Mesocycles

Context: ChronogramForm
Text: Mesocycles

Context: SeasonTimelineWidget
Text: Evaluations

Context: self.parent_widget
Text: Evaluations
Locations: ../app/widgets/season_timeline_widget_new.py:278

Context: TimelineWindow
Text: Evaluations

Context: Timeline
Text: Evaluations

Context: SeasonTimeline
Text: Evaluations

Context: ChronogramWindow
Text: Evaluations

Context: Chronogram
Text: Evaluations

Context: Χρονοδιάγραμμα
Text: Evaluations

Context: χρονοδιάγραμμα
Text: Evaluations

Context: Χρονοδιάγραμμα Σεζόν
Text: Evaluations

Context: ΧρονοδιάγραμμαΣεζόν
Text: Evaluations

Context: SeasonChronogram
Text: Evaluations

Context: XronodiagraamaSezon
Text: Evaluations

Context: ΧρονοδιάγραμμαΑγώνων
Text: Evaluations

Context: TimelineForm
Text: Evaluations

Context: TimelineControl
Text: Evaluations

Context: SeasonTimelineForm
Text: Evaluations

Context: SeasonView
Text: Evaluations

Context: ChronogramForm
Text: Evaluations

Context: OptionsPage
Text: Competition
Locations: ../app/pages/options_page.py:340, ../app/pages/options_page.py:1837, ../app/pages/options_page.py:2290, ../app/pages/options_page.py:3616, ../app/pages/options_page.py:3675

Context: SeasonTimelineWidget
Text: Competition

Context: TimelineWindow
Text: Competition

Context: Timeline
Text: Competition

Context: SeasonTimeline
Text: Competition

Context: ChronogramWindow
Text: Competition

Context: Chronogram
Text: Competition

Context: Χρονοδιάγραμμα
Text: Competition

Context: χρονοδιάγραμμα
Text: Competition

Context: Χρονοδιάγραμμα Σεζόν
Text: Competition

Context: ΧρονοδιάγραμμαΣεζόν
Text: Competition

Context: SeasonChronogram
Text: Competition

Context: XronodiagraamaSezon
Text: Competition

Context: ΧρονοδιάγραμμαΑγώνων
Text: Competition

Context: TimelineForm
Text: Competition

Context: TimelineControl
Text: Competition

Context: SeasonTimelineForm
Text: Competition

Context: SeasonView
Text: Competition

Context: ChronogramForm
Text: Competition

Context: RosterPage
Text: ID
Locations: ../app/pages/roster_page.py:1376, ../app/pages/roster_page.py:3811

Context: SponsorSectionWidget
Text: Notes
Locations: ../app/widgets/sponsors_panel.py:131, ../app/widgets/sponsors_panel.py:688

Context: PeriodizationChartDialog
Text: Periodization Chart
Locations: ../app/dialogs/periodization_chart_dialog.py:25, ../app/dialogs/periodization_chart_dialog_fixed.py:20, ../app/dialogs/periodization_chart_dialog_fixed.py:308, ../app/dialogs/periodization_chart_dialog_complete.py:25, ../app/dialogs/periodization_chart_dialog_updated.py:25

Context: TeamGroupsWidget
Text: Duplicate Name
Locations: ../app/widgets/team_groups_widget.py:669

Context: self.parent_widget
Text: Competitions
Locations: ../app/widgets/season_timeline_widget_new.py:282

Context: RosterPage
Text: Invalid File Type
Locations: ../app/pages/roster_page.py:2812

Context: SponsorSectionWidget
Text: Upload Error
Locations: ../app/widgets/sponsors_panel.py:303

Context: RosterPage
Text: Image Removed
Locations: ../app/pages/roster_page.py:4789

Context: SponsorSectionWidget
Text: Remove Error
Locations: ../app/widgets/sponsors_panel.py:401

Context: RemoveFontDialog
Text: Confirm Removal
Locations: ../app/windows/settings_window.py:140

Context: RosterPage
Text: Confirm Removal
Locations: ../app/pages/roster_page.py:2720, ../app/pages/roster_page.py:4779

Context: TeamGroupsWidget
Text: Confirm Removal
Locations: ../app/widgets/team_groups_widget.py:737

Context: TeamGroupsWidget
Text: Remove Selected
Locations: ../app/widgets/team_groups_widget.py:487, ../app/widgets/team_groups_widget.py:978

Context: RosterPage
Text: (None)
Locations: ../app/pages/roster_page.py:1796, ../app/pages/roster_page.py:2461, ../app/pages/roster_page.py:2623, ../app/pages/roster_page.py:4104, ../app/pages/roster_page.py:4531, ../app/pages/roster_page.py:4551, ../app/pages/roster_page.py:5218

Context: self.parent_widget
Text: Macrocycle
Locations: ../app/widgets/season_timeline_widget_new.py:275, ../app/widgets/season_timeline_widget_new.py:462

Context: self.parent_widget
Text: W
Locations: ../app/widgets/season_timeline_widget_new.py:385
