"""
Debug utilities for the FootData application.

This module provides centralized debug functionality that can be controlled
via the Developer Settings window.
"""

import logging
from PySide6.QtCore import QSettings

# Create logger for debug utilities
logger = logging.getLogger("app.debug_utils")

class DebugManager:
    """Manages debug settings and provides debug output functionality."""
    
    def __init__(self):
        self.settings = QSettings()
    
    def is_debug_enabled(self, debug_type):
        """
        Check if a specific debug type is enabled.
        
        Args:
            debug_type (str): The debug type to check (e.g., 'window_initialization', 'match_page', 'progress_validation')
            
        Returns:
            bool: True if debug is enabled for this type, False otherwise
        """
        return self.settings.value(f"debug/{debug_type}", False, type=bool)
    
    def debug_print(self, debug_type, message):
        """
        Print a debug message if the debug type is enabled.
        
        Args:
            debug_type (str): The debug type (e.g., 'window_initialization', 'match_page', 'progress_validation')
            message (str): The debug message to print
        """
        if self.is_debug_enabled(debug_type):
            logger.debug(f"[{debug_type.upper()}] {message}")
    
    def window_init_debug(self, message):
        """Print window initialization debug message if enabled."""
        self.debug_print("window_initialization", message)
    
    def match_page_debug(self, message):
        """Print match page debug message if enabled."""
        self.debug_print("match_page", message)
    
    def progress_validation_debug(self, message):
        """Print progress validation debug message if enabled."""
        self.debug_print("progress_validation", message)

# Create singleton instance
_debug_manager = None

def get_debug_manager():
    """Get the singleton debug manager instance."""
    global _debug_manager
    if _debug_manager is None:
        _debug_manager = DebugManager()
    return _debug_manager

# Convenience functions for common debug types
def window_init_debug(message):
    """Print window initialization debug message if enabled."""
    get_debug_manager().window_init_debug(message)

def match_page_debug(message):
    """Print match page debug message if enabled."""
    get_debug_manager().match_page_debug(message)

def progress_validation_debug(message):
    """Print progress validation debug message if enabled."""
    get_debug_manager().progress_validation_debug(message)

def is_debug_enabled(debug_type):
    """Check if a specific debug type is enabled."""
    return get_debug_manager().is_debug_enabled(debug_type)
