from PySide6.QtWidgets import (
    Q<PERSON><PERSON><PERSON>, QVBoxLayout, QTabWidget, QListWidget, QPushButton,
    QHBoxLayout, QLabel, QInputDialog, QMessageBox, QAbstractItemView,
    QGridLayout, QListWidgetItem, QLineEdit, QFormLayout, QDateEdit,
    QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDialog, QDialogButtonBox, QTextEdit, QRadioButton, QScrollArea,
    QFrame, QMenu, QFileDialog, QSpinBox
)
from PySide6.QtCore import QCoreApplication, Qt, QSettings, QDate, QSize, QFileInfo, QEvent # Added QEvent
from PySide6.QtCore import Signal # Added Signal
from PySide6.QtGui import QPixmap, QImage
import os
import sys
import pathlib
import shutil
import logging

# Import the master list of countries
from app.utils.constants import countries

# Import Basic Mesocycle methods
from app.pages.basic_mesocycle_methods import _add_basic_microcycle
from app.pages.basic_mesocycle_methods2 import _edit_basic_microcycle, _remove_basic_microcycle
from app.pages.basic_mesocycle_methods3 import _save_basic_microcycle_data, _load_basic_microcycle_data

# Import Competition Mesocycle methods
from app.pages.competition_mesocycle_methods import _add_competition_microcycle
from app.pages.competition_mesocycle_methods2 import _edit_competition_microcycle, _remove_competition_microcycle
from app.pages.competition_mesocycle_methods3 import _save_competition_microcycle_data, _load_competition_microcycle_data

# Import Transition Mesocycle methods
from app.pages.transition_mesocycle_methods import _add_transition_microcycle
from app.pages.transition_mesocycle_methods2 import _edit_transition_microcycle, _remove_transition_microcycle
from app.pages.transition_mesocycle_methods3 import _save_transition_microcycle_data, _load_transition_microcycle_data

# Import Periodization Chart dialog
from app.dialogs.periodization_chart_dialog import PeriodizationChartDialog
from app.dialogs.periodization_chart_data import collect_microcycle_data

# Import Season Timeline Widget
from app.widgets.season_timeline_widget import SeasonTimelineWidget

class DateChangeConfirmationDialog(QDialog):
    """Dialog to confirm date changes and show affected dates."""

    ADJUST_ALL = 0
    KEEP_WHERE_POSSIBLE = 1
    CANCEL = 2

    def __init__(self, parent=None, season_changes=None, macrocycle_changes=None, microcycle_changes=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("Confirm Date Changes"))
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Store the changes
        self.season_changes = season_changes or {}
        self.macrocycle_changes = macrocycle_changes or {}
        self.microcycle_changes = microcycle_changes or []

        # Create layout
        layout = QVBoxLayout(self)

        # Add explanation label
        explanation = QLabel(self.tr(
            "Changing the season dates will affect other date ranges in the application. "
            "Please review the changes below and select how you want to proceed."
        ))
        explanation.setWordWrap(True)
        layout.addWidget(explanation)

        # Create a scroll area for changes
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # Add season changes section
        if self.season_changes:
            season_frame = QFrame()
            season_frame.setFrameShape(QFrame.Shape.StyledPanel)
            season_layout = QVBoxLayout(season_frame)

            season_title = QLabel(self.tr("Season Date Changes:"))
            season_title.setStyleSheet("font-weight: bold;")
            season_layout.addWidget(season_title)

            old_start = self.season_changes.get('old_start', QDate())
            old_end = self.season_changes.get('old_end', QDate())
            new_start = self.season_changes.get('new_start', QDate())
            new_end = self.season_changes.get('new_end', QDate())

            season_info = QLabel(
                self.tr("Old range: {0} to {1}\nNew range: {2} to {3}").format(
                    old_start.toString("yyyy-MM-dd"),
                    old_end.toString("yyyy-MM-dd"),
                    new_start.toString("yyyy-MM-dd"),
                    new_end.toString("yyyy-MM-dd")
                )
            )
            season_layout.addWidget(season_info)
            scroll_layout.addWidget(season_frame)

        # Add macrocycle changes section
        if self.macrocycle_changes:
            macro_frame = QFrame()
            macro_frame.setFrameShape(QFrame.Shape.StyledPanel)
            macro_layout = QVBoxLayout(macro_frame)

            macro_title = QLabel(self.tr("Macrocycle Date Changes:"))
            macro_title.setStyleSheet("font-weight: bold;")
            macro_layout.addWidget(macro_title)

            old_start = self.macrocycle_changes.get('old_start', QDate())
            old_end = self.macrocycle_changes.get('old_end', QDate())
            new_start = self.macrocycle_changes.get('new_start', QDate())
            new_end = self.macrocycle_changes.get('new_end', QDate())

            macro_info = QLabel(
                self.tr("Old range: {0} to {1}\nNew range: {2} to {3}").format(
                    old_start.toString("yyyy-MM-dd"),
                    old_end.toString("yyyy-MM-dd"),
                    new_start.toString("yyyy-MM-dd"),
                    new_end.toString("yyyy-MM-dd")
                )
            )
            macro_layout.addWidget(macro_info)
            scroll_layout.addWidget(macro_frame)

        # Add microcycle changes section
        if self.microcycle_changes:
            micro_frame = QFrame()
            micro_frame.setFrameShape(QFrame.Shape.StyledPanel)
            micro_layout = QVBoxLayout(micro_frame)

            micro_title = QLabel(self.tr("Microcycle Date Changes:"))
            micro_title.setStyleSheet("font-weight: bold;")
            micro_layout.addWidget(micro_title)

            for i, change in enumerate(self.microcycle_changes):
                old_start = change.get('old_start', QDate())
                old_end = change.get('old_end', QDate())
                new_start = change.get('new_start', QDate())
                new_end = change.get('new_end', QDate())
                name = change.get('name', f"Microcycle {i+1}")

                micro_info = QLabel(
                    self.tr("{0}: {1} to {2} → {3} to {4}").format(
                        name,
                        old_start.toString("yyyy-MM-dd"),
                        old_end.toString("yyyy-MM-dd"),
                        new_start.toString("yyyy-MM-dd"),
                        new_end.toString("yyyy-MM-dd")
                    )
                )
                micro_layout.addWidget(micro_info)

            scroll_layout.addWidget(micro_frame)

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        # Add options
        options_group = QGroupBox(self.tr("Options"))
        options_layout = QVBoxLayout(options_group)

        self.adjust_all_radio = QRadioButton(self.tr("Adjust all dates to maintain relationships"))
        self.adjust_all_radio.setChecked(True)
        self.adjust_all_radio.setToolTip(self.tr(
            "Automatically adjust all dependent dates to maintain their relative positions"
        ))

        self.keep_where_possible_radio = QRadioButton(self.tr("Keep dates where possible"))
        self.keep_where_possible_radio.setToolTip(self.tr(
            "Only adjust dates that would be invalid with the new season dates"
        ))

        options_layout.addWidget(self.adjust_all_radio)
        options_layout.addWidget(self.keep_where_possible_radio)

        layout.addWidget(options_group)

        # Add buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def get_selected_option(self):
        """Returns the selected option."""
        if self.adjust_all_radio.isChecked():
            return self.ADJUST_ALL
        elif self.keep_where_possible_radio.isChecked():
            return self.KEEP_WHERE_POSSIBLE
        else:
            return self.CANCEL

# --- Helper Function (can be moved later) --- #
def get_nationality_zone(nationality):
    """Loads zone definitions and returns the zone for a given nationality.

    Args:
        nationality (str or None): The nationality to look up.

    Returns:
        str: The name of the zone the nationality belongs to, or 'Unknown'
             if not found or nationality is None/empty.
    """
    if not nationality:
        return "Unknown" # Or return None if preferred

    settings = QSettings()
    settings.beginGroup("nationality_zones")
    zone_data = settings.value("groups", defaultValue={})
    settings.endGroup()

    # Check type manually (this check is now crucial)
    if not isinstance(zone_data, dict):
        print("Warning: Invalid zone data in settings during lookup.")
        return "Unknown" # Or None

    # Iterate through zones to find the nationality
    for zone_name, assigned_nationalities in zone_data.items():
        if isinstance(assigned_nationalities, list) and nationality in assigned_nationalities:
            return zone_name # Found it!

    # If not found in any zone
    return "Unknown" # Default if not assigned to any zone
# ------------------------------------------ #

class OptionsPage(QWidget):
    """A page for configuring application options, including rules and zones."""
    language_changed = Signal() # Signal to notify MainWindow

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("OptionsPage")
        self.setWindowTitle(self.tr("Options"))
        # Set window flags to create a standalone window
        self.setWindowFlags(Qt.WindowType.Window)
        # Ensure it behaves as a normal window that doesn't block other windows
        self.setWindowModality(Qt.WindowModality.NonModal)

        self.zone_data = {}
        # Load the master list of countries
        self.all_countries = sorted(countries) # Keep a sorted copy

        # Track last valid dates - will be properly initialized when loading settings
        self.last_valid_season_start = QDate.currentDate()
        self.last_valid_season_end = QDate.currentDate().addDays(365)
        self.last_valid_macrocycle_start = QDate.currentDate()
        self.last_valid_macrocycle_end = QDate.currentDate().addDays(365)

        # Create UI
        self._init_ui()

        # Add connections after UI is initialized
        self._setup_connections()

        # Load settings after UI and connections
        self._load_settings()

        # Initial state for zone list
        if hasattr(self, 'zone_list'):
            self._on_group_selection_changed(self.zone_list.currentItem(), None)

        # Initialize the timeline with current data
        self._update_timeline()



    def _init_ui(self):
        main_layout = QVBoxLayout(self)
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)

        # Load settings after UI is initialized
        self._load_settings_called = False  # Flag to prevent double loading

        # Create Football Rules tab
        self.football_rules_tab = QWidget()
        self._init_football_rules_tab()

        # --- Build Dates Tab --- #
        self.dates_tab = QWidget()
        dates_layout = QVBoxLayout(self.dates_tab)

        # Create a tab widget inside the Dates tab
        self.dates_subtabs = QTabWidget()
        dates_layout.addWidget(self.dates_subtabs)

        # Create Season sub-tab
        self.season_tab = QWidget()
        season_tab_layout = QVBoxLayout(self.season_tab)

        # Create a group box for season dates
        self.season_group = QGroupBox(self.tr("Season Dates"))
        season_layout = QFormLayout(self.season_group)

        # Create date edit widgets
        self.season_start_date = QDateEdit(calendarPopup=True)
        self.season_start_date.setDisplayFormat("yyyy-MM-dd")
        self.season_start_date.setDate(QDate.currentDate())
        self.season_start_date.setToolTip(self.tr("Set the start date of the season"))

        self.season_end_date = QDateEdit(calendarPopup=True)
        self.season_end_date.setDisplayFormat("yyyy-MM-dd")
        # Set default end date to 1 year from today
        default_end_date = QDate.currentDate().addDays(365)
        self.season_end_date.setDate(default_end_date)
        self.season_end_date.setToolTip(self.tr("Set the end date of the season"))

        # Create and store label references (IMPORTANT CHANGE)
        self.start_date_label = QLabel(self.tr("Start Date:"))
        self.end_date_label = QLabel(self.tr("End Date:"))

        # Add date fields to the form layout using our stored labels
        season_layout.addRow(self.start_date_label, self.season_start_date)
        season_layout.addRow(self.end_date_label, self.season_end_date)

        # Add validation label (initially hidden)
        self.date_validation_label = QLabel()
        self.date_validation_label.setStyleSheet("color: red;")
        self.date_validation_label.setVisible(False)
        season_layout.addRow("", self.date_validation_label)

        # Add the group box to the season tab layout
        season_tab_layout.addWidget(self.season_group)

        # Create a group box for Competitions
        self.competitions_group = QGroupBox(self.tr("Competitions"))
        competitions_layout = QVBoxLayout(self.competitions_group)

        # Create competitions table
        self.competitions_table = QTableWidget()
        self.competitions_table.setColumnCount(8)
        self.competitions_table.setHorizontalHeaderLabels([
            self.tr("ID"), self.tr("Competition"), self.tr("Start Date"), self.tr("End Date"),
            self.tr("Type"), self.tr("Structure"), self.tr("Priority"), self.tr("Notes")
        ])

        # Set reasonable default widths
        self.competitions_table.setColumnWidth(0, 40)   # ID
        self.competitions_table.setColumnWidth(1, 150)  # Competition
        self.competitions_table.setColumnWidth(2, 100)  # Start Date
        self.competitions_table.setColumnWidth(3, 100)  # End Date
        self.competitions_table.setColumnWidth(4, 100)  # Type
        self.competitions_table.setColumnWidth(5, 150)  # Structure
        self.competitions_table.setColumnWidth(6, 80)   # Priority
        self.competitions_table.setColumnWidth(7, 200)  # Notes

        # Set table properties
        self.competitions_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.competitions_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.competitions_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # Set header resize mode to Interactive to allow column width adjustment
        self.competitions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)

        # Create buttons for competitions
        competitions_buttons_layout = QHBoxLayout()
        self.add_competition_button = QPushButton(self.tr("Add Competition"))
        self.edit_competition_button = QPushButton(self.tr("Edit Competition"))
        self.remove_competition_button = QPushButton(self.tr("Remove Competition"))

        # Add buttons to layout
        competitions_buttons_layout.addWidget(self.add_competition_button)
        competitions_buttons_layout.addWidget(self.edit_competition_button)
        competitions_buttons_layout.addWidget(self.remove_competition_button)
        competitions_buttons_layout.addStretch()

        # Connect button signals
        self.add_competition_button.clicked.connect(self._add_competition)
        self.edit_competition_button.clicked.connect(self._edit_competition)
        self.remove_competition_button.clicked.connect(self._remove_competition)

        # Add widgets to competitions layout
        competitions_layout.addWidget(self.competitions_table)
        competitions_layout.addLayout(competitions_buttons_layout)

        # Add the competitions group to the season tab layout
        season_tab_layout.addWidget(self.competitions_group)

        # Create a group box for the season timeline
        self.timeline_group = QGroupBox(self.tr("Season Timeline"))
        timeline_layout = QVBoxLayout(self.timeline_group)

        # Create the timeline widget
        self.season_timeline = SeasonTimelineWidget()
        # Connect the refresh_requested signal to our _update_timeline method
        self.season_timeline.refresh_requested.connect(self._update_timeline)
        timeline_layout.addWidget(self.season_timeline)

        # Add the timeline group to the season tab layout
        season_tab_layout.addWidget(self.timeline_group)
        season_tab_layout.addStretch()

        # Create Periodization sub-tab
        self.periodization_tab = QWidget()
        periodization_tab_layout = QVBoxLayout(self.periodization_tab)

        # Create a group box for periodization
        self.periodization_group = QGroupBox(self.tr("Periodization"))
        periodization_layout = QVBoxLayout(self.periodization_group)

        # Create a form layout for Macrocycle dates
        self.macrocycle_form = QFormLayout()

        # Create date edit widgets for Macrocycle
        self.macrocycle_start_date = QDateEdit(calendarPopup=True)
        self.macrocycle_start_date.setDisplayFormat("yyyy-MM-dd")
        self.macrocycle_start_date.setDate(QDate.currentDate())
        self.macrocycle_start_date.setToolTip(self.tr("Set the start date of the Macrocycle"))

        self.macrocycle_end_date = QDateEdit(calendarPopup=True)
        self.macrocycle_end_date.setDisplayFormat("yyyy-MM-dd")
        # Set default end date to 1 year from today
        default_macrocycle_end = QDate.currentDate().addDays(365)
        self.macrocycle_end_date.setDate(default_macrocycle_end)
        self.macrocycle_end_date.setToolTip(self.tr("Set the end date of the Macrocycle"))

        # Create and store label references for Macrocycle
        self.start_macrocycle_label = QLabel(self.tr("Start Macrocycle:"))
        self.end_macrocycle_label = QLabel(self.tr("End Macrocycle:"))

        # Add date fields to the form layout using our stored labels
        self.macrocycle_form.addRow(self.start_macrocycle_label, self.macrocycle_start_date)
        self.macrocycle_form.addRow(self.end_macrocycle_label, self.macrocycle_end_date)

        # Add validation label for Macrocycle (initially hidden)
        self.macrocycle_validation_label = QLabel()
        self.macrocycle_validation_label.setStyleSheet("color: red;")
        self.macrocycle_validation_label.setVisible(False)
        self.macrocycle_form.addRow("", self.macrocycle_validation_label)

        # Create a validate button for macrocycle dates
        self.validate_macrocycle_button = QPushButton(self.tr("Validate && Save Dates"))
        self.validate_macrocycle_button.clicked.connect(self._validate_and_save_macrocycle_dates)
        self.validate_macrocycle_button.setToolTip(self.tr("Validate the macrocycle dates and save if valid"))

        # Create a button for the periodization chart
        self.periodization_chart_button = QPushButton(self.tr("Periodization Chart"))
        self.periodization_chart_button.clicked.connect(self._show_periodization_chart)
        self.periodization_chart_button.setToolTip(self.tr("Show a chart of intensity values across all periodization cycles"))

        # Create a horizontal layout for the buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(self.validate_macrocycle_button)
        buttons_layout.addWidget(self.periodization_chart_button)
        buttons_layout.addStretch()

        # Add the form layout and buttons to the main periodization layout
        periodization_layout.addLayout(self.macrocycle_form)
        periodization_layout.addLayout(buttons_layout)

        # Create a sub-section for Preparation Mesocycle
        self.preparation_mesocycle_group = QGroupBox(self.tr("Preparation Mesocycle"))
        mesocycle_layout = QVBoxLayout(self.preparation_mesocycle_group)

        # Create table for Microcycles
        self.microcycle_table = QTableWidget()
        self.microcycle_table.setColumnCount(9)  # Increased from 8 to 9 for the intensity column
        self.microcycle_table.setHorizontalHeaderLabels([
            self.tr("ID"),
            self.tr("Start Microcycle"),
            self.tr("End Microcycle"),
            self.tr("Name"),
            self.tr("Target 1"),  # Renamed from "Target" to "Target 1"
            self.tr("Target 2"),  # New column
            self.tr("Target 3"),  # New column
            self.tr("Intensity"),  # New intensity column
            self.tr("Notes")
        ])

        # Configure table appearance - make all columns resizable by user
        for i in range(self.microcycle_table.columnCount()):
            self.microcycle_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

        # Set reasonable default widths
        self.microcycle_table.setColumnWidth(0, 40)  # ID
        self.microcycle_table.setColumnWidth(1, 120)  # Start Date
        self.microcycle_table.setColumnWidth(2, 120)  # End Date
        self.microcycle_table.setColumnWidth(3, 150)  # Name
        self.microcycle_table.setColumnWidth(4, 80)  # Target 1
        self.microcycle_table.setColumnWidth(5, 80)  # Target 2
        self.microcycle_table.setColumnWidth(6, 80)  # Target 3
        self.microcycle_table.setColumnWidth(7, 80)  # Intensity
        self.microcycle_table.setColumnWidth(8, 200)  # Notes

        # Disable sorting
        self.microcycle_table.horizontalHeader().setSortIndicatorShown(False)
        self.microcycle_table.setSortingEnabled(False)

        # Make table non-editable
        self.microcycle_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        self.microcycle_table.verticalHeader().setVisible(False)
        self.microcycle_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.microcycle_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.microcycle_table.setAlternatingRowColors(True)

        # Add buttons for managing microcycles
        button_layout = QHBoxLayout()
        self.add_microcycle_button = QPushButton(self.tr("Add Microcycle"))
        self.edit_microcycle_button = QPushButton(self.tr("Edit Microcycle"))
        self.remove_microcycle_button = QPushButton(self.tr("Remove Microcycle"))

        # Connect button signals for Preparation Mesocycle
        self.add_microcycle_button.clicked.connect(self._add_microcycle)
        self.edit_microcycle_button.clicked.connect(self._edit_microcycle)
        self.remove_microcycle_button.clicked.connect(self._remove_microcycle)

        # Connect double-click on table to edit microcycle
        self.microcycle_table.doubleClicked.connect(self._handle_table_double_click)

        # Add buttons to layout
        button_layout.addWidget(self.add_microcycle_button)
        button_layout.addWidget(self.edit_microcycle_button)
        button_layout.addWidget(self.remove_microcycle_button)
        button_layout.addStretch()

        # Add table and buttons to mesocycle layout
        mesocycle_layout.addWidget(self.microcycle_table)
        mesocycle_layout.addLayout(button_layout)

        # Add the preparation mesocycle group to the periodization layout
        periodization_layout.addWidget(self.preparation_mesocycle_group)

        # Create a sub-section for Basic Mesocycle
        self.basic_mesocycle_group = QGroupBox(self.tr("Basic Mesocycle"))
        basic_mesocycle_layout = QVBoxLayout(self.basic_mesocycle_group)

        # Create table for Basic Microcycles
        self.basic_microcycle_table = QTableWidget()
        self.basic_microcycle_table.setColumnCount(9)  # Same columns as Preparation Mesocycle
        self.basic_microcycle_table.setHorizontalHeaderLabels([
            self.tr("ID"),
            self.tr("Start Microcycle"),
            self.tr("End Microcycle"),
            self.tr("Name"),
            self.tr("Target 1"),
            self.tr("Target 2"),
            self.tr("Target 3"),
            self.tr("Intensity"),
            self.tr("Notes")
        ])

        # Configure table appearance - make all columns resizable by user
        for i in range(self.basic_microcycle_table.columnCount()):
            self.basic_microcycle_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

        # Set reasonable default widths
        self.basic_microcycle_table.setColumnWidth(0, 40)  # ID
        self.basic_microcycle_table.setColumnWidth(1, 120)  # Start Date
        self.basic_microcycle_table.setColumnWidth(2, 120)  # End Date
        self.basic_microcycle_table.setColumnWidth(3, 150)  # Name
        self.basic_microcycle_table.setColumnWidth(4, 80)  # Target 1
        self.basic_microcycle_table.setColumnWidth(5, 80)  # Target 2
        self.basic_microcycle_table.setColumnWidth(6, 80)  # Target 3
        self.basic_microcycle_table.setColumnWidth(7, 80)  # Intensity
        self.basic_microcycle_table.setColumnWidth(8, 200)  # Notes

        # Disable sorting
        self.basic_microcycle_table.horizontalHeader().setSortIndicatorShown(False)
        self.basic_microcycle_table.setSortingEnabled(False)

        # Make table non-editable
        self.basic_microcycle_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        self.basic_microcycle_table.verticalHeader().setVisible(False)
        self.basic_microcycle_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.basic_microcycle_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.basic_microcycle_table.setAlternatingRowColors(True)

        # Add table to layout
        basic_mesocycle_layout.addWidget(self.basic_microcycle_table)

        # Create buttons for Basic Microcycle management
        button_layout = QHBoxLayout()

        self.add_basic_microcycle_button = QPushButton(self.tr("Add Microcycle"))
        self.edit_basic_microcycle_button = QPushButton(self.tr("Edit Microcycle"))
        self.remove_basic_microcycle_button = QPushButton(self.tr("Remove Microcycle"))

        button_layout.addWidget(self.add_basic_microcycle_button)
        button_layout.addWidget(self.edit_basic_microcycle_button)
        button_layout.addWidget(self.remove_basic_microcycle_button)
        button_layout.addStretch()

        # Add buttons to layout
        basic_mesocycle_layout.addLayout(button_layout)

        # Add the basic mesocycle group to the periodization layout
        periodization_layout.addWidget(self.basic_mesocycle_group)

        # Create a sub-section for Competition Mesocycle
        self.competition_mesocycle_group = QGroupBox(self.tr("Competition Mesocycle"))
        competition_mesocycle_layout = QVBoxLayout(self.competition_mesocycle_group)

        # Create table for Competition Microcycles
        self.competition_microcycle_table = QTableWidget()
        self.competition_microcycle_table.setColumnCount(9)  # Same columns as Basic Mesocycle
        self.competition_microcycle_table.setHorizontalHeaderLabels([
            self.tr("ID"),
            self.tr("Start Microcycle"),
            self.tr("End Microcycle"),
            self.tr("Name"),
            self.tr("Target 1"),
            self.tr("Target 2"),
            self.tr("Target 3"),
            self.tr("Intensity"),
            self.tr("Notes")
        ])

        # Configure table appearance - make all columns resizable by user
        for i in range(self.competition_microcycle_table.columnCount()):
            self.competition_microcycle_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

        # Set reasonable default widths
        self.competition_microcycle_table.setColumnWidth(0, 40)  # ID
        self.competition_microcycle_table.setColumnWidth(1, 120)  # Start Date
        self.competition_microcycle_table.setColumnWidth(2, 120)  # End Date
        self.competition_microcycle_table.setColumnWidth(3, 150)  # Name
        self.competition_microcycle_table.setColumnWidth(4, 80)  # Target 1
        self.competition_microcycle_table.setColumnWidth(5, 80)  # Target 2
        self.competition_microcycle_table.setColumnWidth(6, 80)  # Target 3
        self.competition_microcycle_table.setColumnWidth(7, 80)  # Intensity
        self.competition_microcycle_table.setColumnWidth(8, 200)  # Notes

        # Disable sorting
        self.competition_microcycle_table.horizontalHeader().setSortIndicatorShown(False)
        self.competition_microcycle_table.setSortingEnabled(False)

        # Make table non-editable
        self.competition_microcycle_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        self.competition_microcycle_table.verticalHeader().setVisible(False)
        self.competition_microcycle_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.competition_microcycle_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.competition_microcycle_table.setAlternatingRowColors(True)

        # Add table to layout
        competition_mesocycle_layout.addWidget(self.competition_microcycle_table)

        # Create buttons for Competition Microcycle management
        button_layout = QHBoxLayout()

        self.add_competition_microcycle_button = QPushButton(self.tr("Add Microcycle"))
        self.edit_competition_microcycle_button = QPushButton(self.tr("Edit Microcycle"))
        self.remove_competition_microcycle_button = QPushButton(self.tr("Remove Microcycle"))

        button_layout.addWidget(self.add_competition_microcycle_button)
        button_layout.addWidget(self.edit_competition_microcycle_button)
        button_layout.addWidget(self.remove_competition_microcycle_button)
        button_layout.addStretch()

        # Add buttons to layout
        competition_mesocycle_layout.addLayout(button_layout)

        # Add the competition mesocycle group to the periodization layout
        periodization_layout.addWidget(self.competition_mesocycle_group)

        # Create a sub-section for Transition Mesocycle
        self.transition_mesocycle_group = QGroupBox(self.tr("Transition Mesocycle"))
        transition_mesocycle_layout = QVBoxLayout(self.transition_mesocycle_group)

        # Create table for Transition Microcycles
        self.transition_microcycle_table = QTableWidget()
        self.transition_microcycle_table.setColumnCount(9)  # Same columns as Competition Mesocycle
        self.transition_microcycle_table.setHorizontalHeaderLabels([
            self.tr("ID"),
            self.tr("Start Microcycle"),
            self.tr("End Microcycle"),
            self.tr("Name"),
            self.tr("Target 1"),
            self.tr("Target 2"),
            self.tr("Target 3"),
            self.tr("Intensity"),
            self.tr("Notes")
        ])

        # Configure table appearance - make all columns resizable by user
        for i in range(self.transition_microcycle_table.columnCount()):
            self.transition_microcycle_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

        # Set reasonable default widths
        self.transition_microcycle_table.setColumnWidth(0, 40)  # ID
        self.transition_microcycle_table.setColumnWidth(1, 120)  # Start Date
        self.transition_microcycle_table.setColumnWidth(2, 120)  # End Date
        self.transition_microcycle_table.setColumnWidth(3, 150)  # Name
        self.transition_microcycle_table.setColumnWidth(4, 80)  # Target 1
        self.transition_microcycle_table.setColumnWidth(5, 80)  # Target 2
        self.transition_microcycle_table.setColumnWidth(6, 80)  # Target 3
        self.transition_microcycle_table.setColumnWidth(7, 80)  # Intensity
        self.transition_microcycle_table.setColumnWidth(8, 200)  # Notes

        # Disable sorting
        self.transition_microcycle_table.horizontalHeader().setSortIndicatorShown(False)
        self.transition_microcycle_table.setSortingEnabled(False)

        # Make table non-editable
        self.transition_microcycle_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        self.transition_microcycle_table.verticalHeader().setVisible(False)
        self.transition_microcycle_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.transition_microcycle_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.transition_microcycle_table.setAlternatingRowColors(True)

        # Add table to layout
        transition_mesocycle_layout.addWidget(self.transition_microcycle_table)

        # Create buttons for Transition Microcycle management
        button_layout = QHBoxLayout()

        self.add_transition_microcycle_button = QPushButton(self.tr("Add Microcycle"))
        self.edit_transition_microcycle_button = QPushButton(self.tr("Edit Microcycle"))
        self.remove_transition_microcycle_button = QPushButton(self.tr("Remove Microcycle"))

        button_layout.addWidget(self.add_transition_microcycle_button)
        button_layout.addWidget(self.edit_transition_microcycle_button)
        button_layout.addWidget(self.remove_transition_microcycle_button)
        button_layout.addStretch()

        # Add buttons to layout
        transition_mesocycle_layout.addLayout(button_layout)

        # Add the transition mesocycle group to the periodization layout
        periodization_layout.addWidget(self.transition_mesocycle_group)

        # Add the periodization group box to the periodization tab layout
        periodization_tab_layout.addWidget(self.periodization_group)
        periodization_tab_layout.addStretch()

        # Create Evaluation sub-tab
        self.evaluation_tab = QWidget()
        evaluation_tab_layout = QVBoxLayout(self.evaluation_tab)

        # Create a group box for evaluation dates
        self.evaluation_group = QGroupBox(self.tr("Evaluation Dates"))
        evaluation_layout = QVBoxLayout(self.evaluation_group)

        # Create 1st evaluation sub-section
        self.first_eval_group = QGroupBox(self.tr("1st Evaluation"))
        first_eval_layout = QFormLayout(self.first_eval_group)

        # Create date fields for 1st evaluation
        self.first_eval_start_date = QDateEdit(calendarPopup=True)
        self.first_eval_start_date.setDisplayFormat("yyyy-MM-dd")
        self.first_eval_start_date.setDate(QDate.currentDate().addDays(30))  # Default to 30 days from today
        self.first_eval_start_date.setToolTip(self.tr("Set the start date of the 1st evaluation"))

        self.first_eval_end_date = QDateEdit(calendarPopup=True)
        self.first_eval_end_date.setDisplayFormat("yyyy-MM-dd")
        self.first_eval_end_date.setDate(QDate.currentDate().addDays(37))  # Default to 7 days after start
        self.first_eval_end_date.setToolTip(self.tr("Set the end date of the 1st evaluation"))

        # Create and store label references
        self.first_eval_start_label = QLabel(self.tr("Start 1st Evaluation:"))
        self.first_eval_end_label = QLabel(self.tr("End 1st Evaluation:"))

        # Add date fields to the form layout
        first_eval_layout.addRow(self.first_eval_start_label, self.first_eval_start_date)
        first_eval_layout.addRow(self.first_eval_end_label, self.first_eval_end_date)

        # Add validation label for 1st evaluation
        self.first_eval_validation_label = QLabel()
        self.first_eval_validation_label.setStyleSheet("color: red;")
        self.first_eval_validation_label.setVisible(False)
        first_eval_layout.addRow("", self.first_eval_validation_label)

        # Create 2nd evaluation sub-section
        self.second_eval_group = QGroupBox(self.tr("2nd Evaluation"))
        second_eval_layout = QFormLayout(self.second_eval_group)

        # Create date fields for 2nd evaluation
        self.second_eval_start_date = QDateEdit(calendarPopup=True)
        self.second_eval_start_date.setDisplayFormat("yyyy-MM-dd")
        self.second_eval_start_date.setDate(QDate.currentDate().addDays(120))  # Default to 120 days from today
        self.second_eval_start_date.setToolTip(self.tr("Set the start date of the 2nd evaluation"))

        self.second_eval_end_date = QDateEdit(calendarPopup=True)
        self.second_eval_end_date.setDisplayFormat("yyyy-MM-dd")
        self.second_eval_end_date.setDate(QDate.currentDate().addDays(127))  # Default to 7 days after start
        self.second_eval_end_date.setToolTip(self.tr("Set the end date of the 2nd evaluation"))

        # Create and store label references
        self.second_eval_start_label = QLabel(self.tr("Start 2nd Evaluation:"))
        self.second_eval_end_label = QLabel(self.tr("End 2nd Evaluation:"))

        # Add date fields to the form layout
        second_eval_layout.addRow(self.second_eval_start_label, self.second_eval_start_date)
        second_eval_layout.addRow(self.second_eval_end_label, self.second_eval_end_date)

        # Add validation label for 2nd evaluation
        self.second_eval_validation_label = QLabel()
        self.second_eval_validation_label.setStyleSheet("color: red;")
        self.second_eval_validation_label.setVisible(False)
        second_eval_layout.addRow("", self.second_eval_validation_label)

        # Create 3rd evaluation sub-section
        self.third_eval_group = QGroupBox(self.tr("3rd Evaluation"))
        third_eval_layout = QFormLayout(self.third_eval_group)

        # Create date fields for 3rd evaluation
        self.third_eval_start_date = QDateEdit(calendarPopup=True)
        self.third_eval_start_date.setDisplayFormat("yyyy-MM-dd")
        self.third_eval_start_date.setDate(QDate.currentDate().addDays(240))  # Default to 240 days from today
        self.third_eval_start_date.setToolTip(self.tr("Set the start date of the 3rd evaluation"))

        self.third_eval_end_date = QDateEdit(calendarPopup=True)
        self.third_eval_end_date.setDisplayFormat("yyyy-MM-dd")
        self.third_eval_end_date.setDate(QDate.currentDate().addDays(247))  # Default to 7 days after start
        self.third_eval_end_date.setToolTip(self.tr("Set the end date of the 3rd evaluation"))

        # Create and store label references
        self.third_eval_start_label = QLabel(self.tr("Start 3rd Evaluation:"))
        self.third_eval_end_label = QLabel(self.tr("End 3rd Evaluation:"))

        # Add date fields to the form layout
        third_eval_layout.addRow(self.third_eval_start_label, self.third_eval_start_date)
        third_eval_layout.addRow(self.third_eval_end_label, self.third_eval_end_date)

        # Add validation label for 3rd evaluation
        self.third_eval_validation_label = QLabel()
        self.third_eval_validation_label.setStyleSheet("color: red;")
        self.third_eval_validation_label.setVisible(False)
        third_eval_layout.addRow("", self.third_eval_validation_label)

        # Add all evaluation sub-sections to the main evaluation layout
        evaluation_layout.addWidget(self.first_eval_group)
        evaluation_layout.addWidget(self.second_eval_group)
        evaluation_layout.addWidget(self.third_eval_group)

        # Add the evaluation group box to the evaluation tab layout
        evaluation_tab_layout.addWidget(self.evaluation_group)
        evaluation_tab_layout.addStretch()

        # Add the sub-tabs to the dates tab widget
        self.dates_subtabs.addTab(self.season_tab, self.tr("Season"))
        self.dates_subtabs.addTab(self.periodization_tab, self.tr("Periodization"))
        self.dates_subtabs.addTab(self.evaluation_tab, self.tr("Evaluation"))

        # Connect signals for season dates
        self.season_start_date.dateChanged.connect(self._validate_season_dates)
        self.season_end_date.dateChanged.connect(self._validate_season_dates)

        # Connect signals for Macrocycle dates
        self.macrocycle_start_date.dateChanged.connect(self._validate_macrocycle_dates)
        self.macrocycle_end_date.dateChanged.connect(self._validate_macrocycle_dates)

        # Connect season date changes to also validate Macrocycle dates
        self.season_start_date.dateChanged.connect(self._validate_macrocycle_dates)
        self.season_end_date.dateChanged.connect(self._validate_macrocycle_dates)

        # Connect signals for Evaluation dates
        self.first_eval_start_date.dateChanged.connect(self._validate_evaluation_dates)
        self.first_eval_end_date.dateChanged.connect(self._validate_evaluation_dates)
        self.second_eval_start_date.dateChanged.connect(self._validate_evaluation_dates)
        self.second_eval_end_date.dateChanged.connect(self._validate_evaluation_dates)
        self.third_eval_start_date.dateChanged.connect(self._validate_evaluation_dates)
        self.third_eval_end_date.dateChanged.connect(self._validate_evaluation_dates)

        # Connect season date changes to also validate Evaluation dates
        self.season_start_date.dateChanged.connect(self._validate_evaluation_dates)
        self.season_end_date.dateChanged.connect(self._validate_evaluation_dates)

        # Connect date changes to update the timeline
        self.season_start_date.dateChanged.connect(self._update_timeline)
        self.season_end_date.dateChanged.connect(self._update_timeline)
        self.macrocycle_start_date.dateChanged.connect(self._update_timeline)
        self.macrocycle_end_date.dateChanged.connect(self._update_timeline)
        self.first_eval_start_date.dateChanged.connect(self._update_timeline)
        self.first_eval_end_date.dateChanged.connect(self._update_timeline)
        self.second_eval_start_date.dateChanged.connect(self._update_timeline)
        self.second_eval_end_date.dateChanged.connect(self._update_timeline)
        self.third_eval_start_date.dateChanged.connect(self._update_timeline)
        self.third_eval_end_date.dateChanged.connect(self._update_timeline)

        # --- Build Nationality Zones Tab --- #
        self.nationality_zones_tab = QWidget()
        zones_main_layout = QHBoxLayout(self.nationality_zones_tab)

        # -- Left Panel (Group Management) -- #
        group_panel_layout = QVBoxLayout()
        self.group_label = QLabel(self.tr("Defined Zones:"))
        self.group_list = QListWidget()
        self.group_list.setToolTip(self.tr("Select a zone to view/edit assigned nationalities."))

        group_button_layout = QHBoxLayout()
        self.add_group_button = QPushButton(self.tr("Add..."))
        self.remove_group_button = QPushButton(self.tr("Remove"))
        self.rename_group_button = QPushButton(self.tr("Rename..."))
        self.remove_group_button.setEnabled(False) # Disable initially
        self.rename_group_button.setEnabled(False) # Disable initially
        group_button_layout.addWidget(self.add_group_button)
        group_button_layout.addWidget(self.remove_group_button)
        group_button_layout.addWidget(self.rename_group_button)
        group_button_layout.addStretch()

        group_panel_layout.addWidget(self.group_label)
        group_panel_layout.addWidget(self.group_list)
        group_panel_layout.addLayout(group_button_layout)

        # -- Right Panel (Nationality Assignment) -- #
        assign_panel_layout = QGridLayout() # Use grid for flexibility
        self.available_label = QLabel(self.tr("Available Nationalities:"))
        self.assigned_label = QLabel(self.tr("Assigned Nationalities:"))

        self.available_list = QListWidget()
        self.available_list.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection) # Allow multi-select
        self.available_list.setToolTip(self.tr("Nationalities not assigned to the selected zone."))

        self.assigned_list = QListWidget()
        self.assigned_list.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection) # Allow multi-select
        self.assigned_list.setToolTip(self.tr("Nationalities currently assigned to the selected zone."))

        # Buttons in the middle column
        assign_button_layout = QVBoxLayout()
        self.add_nationality_button = QPushButton(self.tr("Add") + " >>")
        self.remove_nationality_button = QPushButton("<< " + self.tr("Remove"))
        self.add_nationality_button.setToolTip(self.tr("Assign selected available nationality to the current zone"))
        self.remove_nationality_button.setToolTip(self.tr("Remove selected assigned nationality from the current zone"))
        self.add_nationality_button.setEnabled(False) # Disable initially
        self.remove_nationality_button.setEnabled(False) # Disable initially
        assign_button_layout.addStretch()
        assign_button_layout.addWidget(self.add_nationality_button)
        assign_button_layout.addWidget(self.remove_nationality_button)
        assign_button_layout.addStretch()

        # Add widgets to grid layout
        assign_panel_layout.addWidget(self.available_label, 0, 0)
        assign_panel_layout.addWidget(self.assigned_label, 0, 2)
        assign_panel_layout.addWidget(self.available_list, 1, 0)
        assign_panel_layout.addLayout(assign_button_layout, 1, 1, alignment=Qt.AlignmentFlag.AlignCenter) # Add button layout to middle
        assign_panel_layout.addWidget(self.assigned_list, 1, 2)

        # Set column stretch factors (make lists wider than button column)
        assign_panel_layout.setColumnStretch(0, 3) # Available list wider
        assign_panel_layout.setColumnStretch(1, 1) # Buttons narrow
        assign_panel_layout.setColumnStretch(2, 3) # Assigned list wider

        # --- Add Panels to Zones Tab Main Layout --- #
        zones_main_layout.addLayout(group_panel_layout, 1) # Stretch factor 1
        zones_main_layout.addLayout(assign_panel_layout, 3) # Stretch factor 3 (wider)

        # --- Build App Media Tab --- #
        self.app_media_tab = QWidget()
        app_media_layout = QVBoxLayout(self.app_media_tab)

        # Create Football Pitch section
        football_pitch_group = QGroupBox(self.tr("Football Pitch"))
        football_pitch_layout = QVBoxLayout(football_pitch_group)

        # Create Positions Pitch frame
        positions_pitch_group = QGroupBox(self.tr("Positions Pitch"))
        positions_pitch_layout = QVBoxLayout(positions_pitch_group)

        # Create a horizontal layout for the two frames
        frames_layout = QHBoxLayout()

        # Define default style for frames
        default_style = "background-color: #f0f0f0; border: 1px solid #cccccc;"

        # Create the first frame for the pitch image
        self.positions_pitch_frame = QLabel()
        self.positions_pitch_frame.setObjectName("positions_pitch_frame")
        self.positions_pitch_frame.setFixedSize(70, 100)
        self.positions_pitch_frame.setFrameShape(QFrame.Shape.Box)
        self.positions_pitch_frame.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.positions_pitch_frame.setText(self.tr("Click to upload image"))
        self.positions_pitch_frame.setStyleSheet(default_style)
        self.positions_pitch_frame.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # Create the second frame for the pitch image
        self.positions_pitch_frame2 = QLabel()
        self.positions_pitch_frame2.setObjectName("positions_pitch_frame2")
        self.positions_pitch_frame2.setFixedSize(70, 100)
        self.positions_pitch_frame2.setFrameShape(QFrame.Shape.Box)
        self.positions_pitch_frame2.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.positions_pitch_frame2.setText(self.tr("Click to upload image"))
        self.positions_pitch_frame2.setStyleSheet(default_style)
        self.positions_pitch_frame2.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # Create the third frame for the pitch image
        self.positions_pitch_frame3 = QLabel()
        self.positions_pitch_frame3.setObjectName("positions_pitch_frame3")
        self.positions_pitch_frame3.setFixedSize(70, 100)
        self.positions_pitch_frame3.setFrameShape(QFrame.Shape.Box)
        self.positions_pitch_frame3.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.positions_pitch_frame3.setText(self.tr("Click to upload image"))
        self.positions_pitch_frame3.setStyleSheet(default_style)
        self.positions_pitch_frame3.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # Add all three frames to the horizontal layout with some spacing
        frames_layout.addStretch()
        frames_layout.addWidget(self.positions_pitch_frame)
        frames_layout.addSpacing(20)  # Add some space between the frames
        frames_layout.addWidget(self.positions_pitch_frame2)
        frames_layout.addSpacing(20)  # Add some space between the frames
        frames_layout.addWidget(self.positions_pitch_frame3)
        frames_layout.addStretch()

        # Add the frames layout to the positions pitch layout
        positions_pitch_layout.addLayout(frames_layout)

        # Add a note about image requirements
        self.requirements_label = QLabel(self.tr("Requirements: PNG format, < 200KB, max 400x600 pixels"))
        self.requirements_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.requirements_label.setStyleSheet("font-size: 9pt; color: #666666;")
        positions_pitch_layout.addWidget(self.requirements_label)

        # Add the positions pitch group to the football pitch layout
        football_pitch_layout.addWidget(positions_pitch_group)

        # Add the football pitch group to the main layout
        app_media_layout.addWidget(football_pitch_group)
        app_media_layout.addStretch()

        # Connect signals for the first pitch frame
        self.positions_pitch_frame.mousePressEvent = self._on_positions_pitch_clicked
        self.positions_pitch_frame.customContextMenuRequested.connect(self._show_positions_pitch_context_menu)

        # Connect signals for the second pitch frame
        self.positions_pitch_frame2.mousePressEvent = self._on_positions_pitch2_clicked
        self.positions_pitch_frame2.customContextMenuRequested.connect(self._show_positions_pitch2_context_menu)

        # Connect signals for the third pitch frame
        self.positions_pitch_frame3.mousePressEvent = self._on_positions_pitch3_clicked
        self.positions_pitch_frame3.customContextMenuRequested.connect(self._show_positions_pitch3_context_menu)

        # Create Football Competitions tab
        self.football_competitions_tab = QWidget()
        _init_football_competitions_tab()

        # Add tabs to the main tab widget
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QTabWidget, QListWidget, QPushButton,
    QHBoxLayout, QLabel, QInputDialog, QMessageBox, QAbstractItemView,
    QGridLayout, QListWidgetItem, QLineEdit, QFormLayout, QDateEdit,
    QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDialog, QDialogButtonBox, QTextEdit, QRadioButton, QScrollArea,
    QFrame, QMenu, QFileDialog
)
from PySide6.QtCore import QCoreApplication, Qt, QSettings, QDate, QSize, QFileInfo, QEvent # Added QEvent
from PySide6.QtCore import Signal # Added Signal
from PySide6.QtGui import QPixmap, QImage
import os
import sys
import pathlib
import shutil
import logging

# Import the master list of countries
from app.utils.constants import countries

# Import Basic Mesocycle methods
from app.pages.basic_mesocycle_methods import _add_basic_microcycle
from app.pages.basic_mesocycle_methods2 import _edit_basic_microcycle, _remove_basic_microcycle
from app.pages.basic_mesocycle_methods3 import _save_basic_microcycle_data, _load_basic_microcycle_data

# Import Competition Mesocycle methods
from app.pages.competition_mesocycle_methods import _add_competition_microcycle
from app.pages.competition_mesocycle_methods2 import _edit_competition_microcycle, _remove_competition_microcycle
from app.pages.competition_mesocycle_methods3 import _save_competition_microcycle_data, _load_competition_microcycle_data

# Import Transition Mesocycle methods
from app.pages.transition_mesocycle_methods import _add_transition_microcycle
from app.pages.transition_mesocycle_methods2 import _edit_transition_microcycle, _remove_transition_microcycle
from app.pages.transition_mesocycle_methods3 import _save_transition_microcycle_data, _load_transition_microcycle_data

# Import Periodization Chart dialog
from app.dialogs.periodization_chart_dialog import PeriodizationChartDialog
from app.dialogs.periodization_chart_data import collect_microcycle_data

# Import Season Timeline Widget
from app.widgets.season_timeline_widget import SeasonTimelineWidget

class DateChangeConfirmationDialog(QDialog):
    """Dialog to confirm date changes and show affected dates."""

    ADJUST_ALL = 0
    KEEP_WHERE_POSSIBLE = 1
    CANCEL = 2

    def __init__(self, parent=None, season_changes=None, macrocycle_changes=None, microcycle_changes=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("Confirm Date Changes"))
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Store the changes
        self.season_changes = season_changes or {}
        self.macrocycle_changes = macrocycle_changes or {}
        self.microcycle_changes = microcycle_changes or []

        # Create layout
        layout = QVBoxLayout(self)

        # Add explanation label
        explanation = QLabel(self.tr(
            "Changing the season dates will affect other date ranges in the application. "
            "Please review the changes below and select how you want to proceed."
        ))
        explanation.setWordWrap(True)
        layout.addWidget(explanation)

        # Create a scroll area for changes
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # Add season changes section
        if self.season_changes:
            season_frame = QFrame()
            season_frame.setFrameShape(QFrame.Shape.StyledPanel)
            season_layout = QVBoxLayout(season_frame)

            season_title = QLabel(self.tr("Season Date Changes:"))
            season_title.setStyleSheet("font-weight: bold;")
            season_layout.addWidget(season_title)

            old_start = self.season_changes.get('old_start', QDate())
            old_end = self.season_changes.get('old_end', QDate())
            new_start = self.season_changes.get('new_start', QDate())
            new_end = self.season_changes.get('new_end', QDate())

            season_info = QLabel(
                self.tr("Old range: {0} to {1}\nNew range: {2} to {3}").format(
                    old_start.toString("yyyy-MM-dd"),
                    old_end.toString("yyyy-MM-dd"),
                    new_start.toString("yyyy-MM-dd"),
                    new_end.toString("yyyy-MM-dd")
                )
            )
            season_layout.addWidget(season_info)
            scroll_layout.addWidget(season_frame)

        # Add macrocycle changes section
        if self.macrocycle_changes:
            macro_frame = QFrame()
            macro_frame.setFrameShape(QFrame.Shape.StyledPanel)
            macro_layout = QVBoxLayout(macro_frame)

            macro_title = QLabel(self.tr("Macrocycle Date Changes:"))
            macro_title.setStyleSheet("font-weight: bold;")
            macro_layout.addWidget(macro_title)

            old_start = self.macrocycle_changes.get('old_start', QDate())
            old_end = self.macrocycle_changes.get('old_end', QDate())
            new_start = self.macrocycle_changes.get('new_start', QDate())
            new_end = self.macrocycle_changes.get('new_end', QDate())

            macro_info = QLabel(
                self.tr("Old range: {0} to {1}\nNew range: {2} to {3}").format(
                    old_start.toString("yyyy-MM-dd"),
                    old_end.toString("yyyy-MM-dd"),
                    new_start.toString("yyyy-MM-dd"),
                    new_end.toString("yyyy-MM-dd")
                )
            )
            macro_layout.addWidget(macro_info)
            scroll_layout.addWidget(macro_frame)

        # Add microcycle changes section
        if self.microcycle_changes:
            micro_frame = QFrame()
            micro_frame.setFrameShape(QFrame.Shape.StyledPanel)
            micro_layout = QVBoxLayout(micro_frame)

            micro_title = QLabel(self.tr("Microcycle Date Changes:"))
            micro_title.setStyleSheet("font-weight: bold;")
            micro_layout.addWidget(micro_title)

            for i, change in enumerate(self.microcycle_changes):
                old_start = change.get('old_start', QDate())
                old_end = change.get('old_end', QDate())
                new_start = change.get('new_start', QDate())
                new_end = change.get('new_end', QDate())
                name = change.get('name', f"Microcycle {i+1}")

                micro_info = QLabel(
                    self.tr("{0}: {1} to {2} → {3} to {4}").format(
                        name,
                        old_start.toString("yyyy-MM-dd"),
                        old_end.toString("yyyy-MM-dd"),
                        new_start.toString("yyyy-MM-dd"),
                        new_end.toString("yyyy-MM-dd")
                    )
                )
                micro_layout.addWidget(micro_info)

            scroll_layout.addWidget(micro_frame)

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        # Add options
        options_group = QGroupBox(self.tr("Options"))
        options_layout = QVBoxLayout(options_group)

        self.adjust_all_radio = QRadioButton(self.tr("Adjust all dates to maintain relationships"))
        self.adjust_all_radio.setChecked(True)
        self.adjust_all_radio.setToolTip(self.tr(
            "Automatically adjust all dependent dates to maintain their relative positions"
        ))

        self.keep_where_possible_radio = QRadioButton(self.tr("Keep dates where possible"))
        self.keep_where_possible_radio.setToolTip(self.tr(
            "Only adjust dates that would be invalid with the new season dates"
        ))

        options_layout.addWidget(self.adjust_all_radio)
        options_layout.addWidget(self.keep_where_possible_radio)

        layout.addWidget(options_group)

        # Add buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def get_selected_option(self):
        """Returns the selected option."""
        if self.adjust_all_radio.isChecked():
            return self.ADJUST_ALL
        elif self.keep_where_possible_radio.isChecked():
            return self.KEEP_WHERE_POSSIBLE
        else:
            return self.CANCEL

# --- Helper Function (can be moved later) --- #
def get_nationality_zone(nationality):
    """Loads zone definitions and returns the zone for a given nationality.

    Args:
        nationality (str or None): The nationality to look up.

    Returns:
        str: The name of the zone the nationality belongs to, or 'Unknown'
             if not found or nationality is None/empty.
    """
    if not nationality:
        return "Unknown" # Or return None if preferred

    settings = QSettings()
    settings.beginGroup("nationality_zones")
    zone_data = settings.value("groups", defaultValue={})
    settings.endGroup()

    # Check type manually (this check is now crucial)
    if not isinstance(zone_data, dict):
        print("Warning: Invalid zone data in settings during lookup.")
        return "Unknown" # Or None

    # Iterate through zones to find the nationality
    for zone_name, assigned_nationalities in zone_data.items():
        if isinstance(assigned_nationalities, list) and nationality in assigned_nationalities:
            return zone_name # Found it!

    # If not found in any zone
    return "Unknown" # Default if not assigned to any zone
# ------------------------------------------ #

class OptionsPage(QWidget):
    """A page for configuring application options, including rules and zones."""
    language_changed = Signal() # Signal to notify MainWindow

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("OptionsPage")
        self.setWindowTitle(self.tr("Options"))
        # Set window flags to create a standalone window
        self.setWindowFlags(Qt.WindowType.Window)
        # Ensure it behaves as a normal window that doesn't block other windows
        self.setWindowModality(Qt.WindowModality.NonModal)

        self.zone_data = {}
        # Load the master list of countries
        self.all_countries = sorted(countries) # Keep a sorted copy

        # Track last valid dates - will be properly initialized when loading settings
        self.last_valid_season_start = QDate.currentDate()
        self.last_valid_season_end = QDate.currentDate().addDays(365)
        self.last_valid_macrocycle_start = QDate.currentDate()
        self.last_valid_macrocycle_end = QDate.currentDate().addDays(365)

        # Create UI
        self._init_ui()

        # Add connections after UI is initialized
        self._setup_connections()

        # Load settings after UI and connections
        self._load_settings()

        # Initial state for zone list
        if hasattr(self, 'zone_list'):
            self._on_group_selection_changed(self.zone_list.currentItem(), None)

        # Initialize the timeline with current data
        self._update_timeline()



    def _init_ui(self):
        main_layout = QVBoxLayout(self)
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)

        # Load settings after UI is initialized
        self._load_settings_called = False  # Flag to prevent double loading

        # Create placeholder for the first tab
        self.football_rules_tab = QWidget()

        # --- Build Dates Tab --- #
        self.dates_tab = QWidget()
        dates_layout = QVBoxLayout(self.dates_tab)

        # Create a tab widget inside the Dates tab
        self.dates_subtabs = QTabWidget()
        dates_layout.addWidget(self.dates_subtabs)

        # Create Season sub-tab
        self.season_tab = QWidget()
        season_tab_layout = QVBoxLayout(self.season_tab)

        # Create a group box for season dates
        self.season_group = QGroupBox(self.tr("Season Dates"))
        season_layout = QFormLayout(self.season_group)

        # Create date edit widgets
        self.season_start_date = QDateEdit(calendarPopup=True)
        self.season_start_date.setDisplayFormat("yyyy-MM-dd")
        self.season_start_date.setDate(QDate.currentDate())
        self.season_start_date.setToolTip(self.tr("Set the start date of the season"))

        self.season_end_date = QDateEdit(calendarPopup=True)
        self.season_end_date.setDisplayFormat("yyyy-MM-dd")
        # Set default end date to 1 year from today
        default_end_date = QDate.currentDate().addDays(365)
        self.season_end_date.setDate(default_end_date)
        self.season_end_date.setToolTip(self.tr("Set the end date of the season"))

        # Create and store label references (IMPORTANT CHANGE)
        self.start_date_label = QLabel(self.tr("Start Date:"))
        self.end_date_label = QLabel(self.tr("End Date:"))

        # Add date fields to the form layout using our stored labels
        season_layout.addRow(self.start_date_label, self.season_start_date)
        season_layout.addRow(self.end_date_label, self.season_end_date)

        # Add validation label (initially hidden)
        self.date_validation_label = QLabel()
        self.date_validation_label.setStyleSheet("color: red;")
        self.date_validation_label.setVisible(False)
        season_layout.addRow("", self.date_validation_label)

        # Add the group box to the season tab layout
        season_tab_layout.addWidget(self.season_group)

        # Create a group box for Competitions
        self.competitions_group = QGroupBox(self.tr("Competitions"))
        competitions_layout = QVBoxLayout(self.competitions_group)

        # Create competitions table
        self.competitions_table = QTableWidget()
        self.competitions_table.setColumnCount(8)
        self.competitions_table.setHorizontalHeaderLabels([
            self.tr("ID"), self.tr("Competition"), self.tr("Start Date"), self.tr("End Date"),
            self.tr("Type"), self.tr("Structure"), self.tr("Priority"), self.tr("Notes")
        ])

        # Set reasonable default widths
        self.competitions_table.setColumnWidth(0, 40)   # ID
        self.competitions_table.setColumnWidth(1, 150)  # Competition
        self.competitions_table.setColumnWidth(2, 100)  # Start Date
        self.competitions_table.setColumnWidth(3, 100)  # End Date
        self.competitions_table.setColumnWidth(4, 100)  # Type
        self.competitions_table.setColumnWidth(5, 150)  # Structure
        self.competitions_table.setColumnWidth(6, 80)   # Priority
        self.competitions_table.setColumnWidth(7, 200)  # Notes

        # Set table properties
        self.competitions_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.competitions_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.competitions_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # Set header resize mode to Interactive to allow column width adjustment
        self.competitions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)

        # Create buttons for competitions
        competitions_buttons_layout = QHBoxLayout()
        self.add_competition_button = QPushButton(self.tr("Add Competition"))
        self.edit_competition_button = QPushButton(self.tr("Edit Competition"))
        self.remove_competition_button = QPushButton(self.tr("Remove Competition"))

        # Add buttons to layout
        competitions_buttons_layout.addWidget(self.add_competition_button)
        competitions_buttons_layout.addWidget(self.edit_competition_button)
        competitions_buttons_layout.addWidget(self.remove_competition_button)
        competitions_buttons_layout.addStretch()

        # Connect button signals
        self.add_competition_button.clicked.connect(self._add_competition)
        self.edit_competition_button.clicked.connect(self._edit_competition)
        self.remove_competition_button.clicked.connect(self._remove_competition)

        # Add widgets to competitions layout
        competitions_layout.addWidget(self.competitions_table)
        competitions_layout.addLayout(competitions_buttons_layout)

        # Add the competitions group to the season tab layout
        season_tab_layout.addWidget(self.competitions_group)

        # Create a group box for the season timeline
        self.timeline_group = QGroupBox(self.tr("Season Timeline"))
        timeline_layout = QVBoxLayout(self.timeline_group)

        # Create the timeline widget
        self.season_timeline = SeasonTimelineWidget()
        # Connect the refresh_requested signal to our _update_timeline method
        self.season_timeline.refresh_requested.connect(self._update_timeline)
        timeline_layout.addWidget(self.season_timeline)

        # Add the timeline group to the season tab layout
        season_tab_layout.addWidget(self.timeline_group)
        season_tab_layout.addStretch()

        # Create Periodization sub-tab
        self.periodization_tab = QWidget()
        periodization_tab_layout = QVBoxLayout(self.periodization_tab)

        # Create a group box for periodization
        self.periodization_group = QGroupBox(self.tr("Periodization"))
        periodization_layout = QVBoxLayout(self.periodization_group)

        # Create a form layout for Macrocycle dates
        self.macrocycle_form = QFormLayout()

        # Create date edit widgets for Macrocycle
        self.macrocycle_start_date = QDateEdit(calendarPopup=True)
        self.macrocycle_start_date.setDisplayFormat("yyyy-MM-dd")
        self.macrocycle_start_date.setDate(QDate.currentDate())
        self.macrocycle_start_date.setToolTip(self.tr("Set the start date of the Macrocycle"))

        self.macrocycle_end_date = QDateEdit(calendarPopup=True)
        self.macrocycle_end_date.setDisplayFormat("yyyy-MM-dd")
        # Set default end date to 1 year from today
        default_macrocycle_end = QDate.currentDate().addDays(365)
        self.macrocycle_end_date.setDate(default_macrocycle_end)
        self.macrocycle_end_date.setToolTip(self.tr("Set the end date of the Macrocycle"))

        # Create and store label references for Macrocycle
        self.start_macrocycle_label = QLabel(self.tr("Start Macrocycle:"))
        self.end_macrocycle_label = QLabel(self.tr("End Macrocycle:"))

        # Add date fields to the form layout using our stored labels
        self.macrocycle_form.addRow(self.start_macrocycle_label, self.macrocycle_start_date)
        self.macrocycle_form.addRow(self.end_macrocycle_label, self.macrocycle_end_date)

        # Add validation label for Macrocycle (initially hidden)
        self.macrocycle_validation_label = QLabel()
        self.macrocycle_validation_label.setStyleSheet("color: red;")
        self.macrocycle_validation_label.setVisible(False)
        self.macrocycle_form.addRow("", self.macrocycle_validation_label)

        # Create a validate button for macrocycle dates
        self.validate_macrocycle_button = QPushButton(self.tr("Validate && Save Dates"))
        self.validate_macrocycle_button.clicked.connect(self._validate_and_save_macrocycle_dates)
        self.validate_macrocycle_button.setToolTip(self.tr("Validate the macrocycle dates and save if valid"))

        # Create a button for the periodization chart
        self.periodization_chart_button = QPushButton(self.tr("Periodization Chart"))
        self.periodization_chart_button.clicked.connect(self._show_periodization_chart)
        self.periodization_chart_button.setToolTip(self.tr("Show a chart of intensity values across all periodization cycles"))

        # Create a horizontal layout for the buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(self.validate_macrocycle_button)
        buttons_layout.addWidget(self.periodization_chart_button)
        buttons_layout.addStretch()

        # Add the form layout and buttons to the main periodization layout
        periodization_layout.addLayout(self.macrocycle_form)
        periodization_layout.addLayout(buttons_layout)

        # Create a sub-section for Preparation Mesocycle
        self.preparation_mesocycle_group = QGroupBox(self.tr("Preparation Mesocycle"))
        mesocycle_layout = QVBoxLayout(self.preparation_mesocycle_group)

        # Create table for Microcycles
        self.microcycle_table = QTableWidget()
        self.microcycle_table.setColumnCount(9)  # Increased from 8 to 9 for the intensity column
        self.microcycle_table.setHorizontalHeaderLabels([
            self.tr("ID"),
            self.tr("Start Microcycle"),
            self.tr("End Microcycle"),
            self.tr("Name"),
            self.tr("Target 1"),  # Renamed from "Target" to "Target 1"
            self.tr("Target 2"),  # New column
            self.tr("Target 3"),  # New column
            self.tr("Intensity"),  # New intensity column
            self.tr("Notes")
        ])

        # Configure table appearance - make all columns resizable by user
        for i in range(self.microcycle_table.columnCount()):
            self.microcycle_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

        # Set reasonable default widths
        self.microcycle_table.setColumnWidth(0, 40)  # ID
        self.microcycle_table.setColumnWidth(1, 120)  # Start Date
        self.microcycle_table.setColumnWidth(2, 120)  # End Date
        self.microcycle_table.setColumnWidth(3, 150)  # Name
        self.microcycle_table.setColumnWidth(4, 80)  # Target 1
        self.microcycle_table.setColumnWidth(5, 80)  # Target 2
        self.microcycle_table.setColumnWidth(6, 80)  # Target 3
        self.microcycle_table.setColumnWidth(7, 80)  # Intensity
        self.microcycle_table.setColumnWidth(8, 200)  # Notes

        # Disable sorting
        self.microcycle_table.horizontalHeader().setSortIndicatorShown(False)
        self.microcycle_table.setSortingEnabled(False)

        # Make table non-editable
        self.microcycle_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        self.microcycle_table.verticalHeader().setVisible(False)
        self.microcycle_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.microcycle_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.microcycle_table.setAlternatingRowColors(True)

        # Add buttons for managing microcycles
        button_layout = QHBoxLayout()
        self.add_microcycle_button = QPushButton(self.tr("Add Microcycle"))
        self.edit_microcycle_button = QPushButton(self.tr("Edit Microcycle"))
        self.remove_microcycle_button = QPushButton(self.tr("Remove Microcycle"))

        # Connect button signals for Preparation Mesocycle
        self.add_microcycle_button.clicked.connect(self._add_microcycle)
        self.edit_microcycle_button.clicked.connect(self._edit_microcycle)
        self.remove_microcycle_button.clicked.connect(self._remove_microcycle)

        # Connect double-click on table to edit microcycle
        self.microcycle_table.doubleClicked.connect(self._handle_table_double_click)

        # Add buttons to layout
        button_layout.addWidget(self.add_microcycle_button)
        button_layout.addWidget(self.edit_microcycle_button)
        button_layout.addWidget(self.remove_microcycle_button)
        button_layout.addStretch()

        # Add table and buttons to mesocycle layout
        mesocycle_layout.addWidget(self.microcycle_table)
        mesocycle_layout.addLayout(button_layout)

        # Add the preparation mesocycle group to the periodization layout
        periodization_layout.addWidget(self.preparation_mesocycle_group)

        # Create a sub-section for Basic Mesocycle
        self.basic_mesocycle_group = QGroupBox(self.tr("Basic Mesocycle"))
        basic_mesocycle_layout = QVBoxLayout(self.basic_mesocycle_group)

        # Create table for Basic Microcycles
        self.basic_microcycle_table = QTableWidget()
        self.basic_microcycle_table.setColumnCount(9)  # Same columns as Preparation Mesocycle
        self.basic_microcycle_table.setHorizontalHeaderLabels([
            self.tr("ID"),
            self.tr("Start Microcycle"),
            self.tr("End Microcycle"),
            self.tr("Name"),
            self.tr("Target 1"),
            self.tr("Target 2"),
            self.tr("Target 3"),
            self.tr("Intensity"),
            self.tr("Notes")
        ])

        # Configure table appearance - make all columns resizable by user
        for i in range(self.basic_microcycle_table.columnCount()):
            self.basic_microcycle_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

        # Set reasonable default widths
        self.basic_microcycle_table.setColumnWidth(0, 40)  # ID
        self.basic_microcycle_table.setColumnWidth(1, 120)  # Start Date
        self.basic_microcycle_table.setColumnWidth(2, 120)  # End Date
        self.basic_microcycle_table.setColumnWidth(3, 150)  # Name
        self.basic_microcycle_table.setColumnWidth(4, 80)  # Target 1
        self.basic_microcycle_table.setColumnWidth(5, 80)  # Target 2
        self.basic_microcycle_table.setColumnWidth(6, 80)  # Target 3
        self.basic_microcycle_table.setColumnWidth(7, 80)  # Intensity
        self.basic_microcycle_table.setColumnWidth(8, 200)  # Notes

        # Disable sorting
        self.basic_microcycle_table.horizontalHeader().setSortIndicatorShown(False)
        self.basic_microcycle_table.setSortingEnabled(False)

        # Make table non-editable
        self.basic_microcycle_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        self.basic_microcycle_table.verticalHeader().setVisible(False)
        self.basic_microcycle_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.basic_microcycle_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.basic_microcycle_table.setAlternatingRowColors(True)

        # Add table to layout
        basic_mesocycle_layout.addWidget(self.basic_microcycle_table)

        # Create buttons for Basic Microcycle management
        button_layout = QHBoxLayout()

        self.add_basic_microcycle_button = QPushButton(self.tr("Add Microcycle"))
        self.edit_basic_microcycle_button = QPushButton(self.tr("Edit Microcycle"))
        self.remove_basic_microcycle_button = QPushButton(self.tr("Remove Microcycle"))

        button_layout.addWidget(self.add_basic_microcycle_button)
        button_layout.addWidget(self.edit_basic_microcycle_button)
        button_layout.addWidget(self.remove_basic_microcycle_button)
        button_layout.addStretch()

        # Add buttons to layout
        basic_mesocycle_layout.addLayout(button_layout)

        # Add the basic mesocycle group to the periodization layout
        periodization_layout.addWidget(self.basic_mesocycle_group)

        # Create a sub-section for Competition Mesocycle
        self.competition_mesocycle_group = QGroupBox(self.tr("Competition Mesocycle"))
        competition_mesocycle_layout = QVBoxLayout(self.competition_mesocycle_group)

        # Create table for Competition Microcycles
        self.competition_microcycle_table = QTableWidget()
        self.competition_microcycle_table.setColumnCount(9)  # Same columns as Basic Mesocycle
        self.competition_microcycle_table.setHorizontalHeaderLabels([
            self.tr("ID"),
            self.tr("Start Microcycle"),
            self.tr("End Microcycle"),
            self.tr("Name"),
            self.tr("Target 1"),
            self.tr("Target 2"),
            self.tr("Target 3"),
            self.tr("Intensity"),
            self.tr("Notes")
        ])

        # Configure table appearance - make all columns resizable by user
        for i in range(self.competition_microcycle_table.columnCount()):
            self.competition_microcycle_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

        # Set reasonable default widths
        self.competition_microcycle_table.setColumnWidth(0, 40)  # ID
        self.competition_microcycle_table.setColumnWidth(1, 120)  # Start Date
        self.competition_microcycle_table.setColumnWidth(2, 120)  # End Date
        self.competition_microcycle_table.setColumnWidth(3, 150)  # Name
        self.competition_microcycle_table.setColumnWidth(4, 80)  # Target 1
        self.competition_microcycle_table.setColumnWidth(5, 80)  # Target 2
        self.competition_microcycle_table.setColumnWidth(6, 80)  # Target 3
        self.competition_microcycle_table.setColumnWidth(7, 80)  # Intensity
        self.competition_microcycle_table.setColumnWidth(8, 200)  # Notes

        # Disable sorting
        self.competition_microcycle_table.horizontalHeader().setSortIndicatorShown(False)
        self.competition_microcycle_table.setSortingEnabled(False)

        # Make table non-editable
        self.competition_microcycle_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        self.competition_microcycle_table.verticalHeader().setVisible(False)
        self.competition_microcycle_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.competition_microcycle_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.competition_microcycle_table.setAlternatingRowColors(True)

        # Add table to layout
        competition_mesocycle_layout.addWidget(self.competition_microcycle_table)

        # Create buttons for Competition Microcycle management
        button_layout = QHBoxLayout()

        self.add_competition_microcycle_button = QPushButton(self.tr("Add Microcycle"))
        self.edit_competition_microcycle_button = QPushButton(self.tr("Edit Microcycle"))
        self.remove_competition_microcycle_button = QPushButton(self.tr("Remove Microcycle"))

        button_layout.addWidget(self.add_competition_microcycle_button)
        button_layout.addWidget(self.edit_competition_microcycle_button)
        button_layout.addWidget(self.remove_competition_microcycle_button)
        button_layout.addStretch()

        # Add buttons to layout
        competition_mesocycle_layout.addLayout(button_layout)

        # Add the competition mesocycle group to the periodization layout
        periodization_layout.addWidget(self.competition_mesocycle_group)

        # Create a sub-section for Transition Mesocycle
        self.transition_mesocycle_group = QGroupBox(self.tr("Transition Mesocycle"))
        transition_mesocycle_layout = QVBoxLayout(self.transition_mesocycle_group)

        # Create table for Transition Microcycles
        self.transition_microcycle_table = QTableWidget()
        self.transition_microcycle_table.setColumnCount(9)  # Same columns as Competition Mesocycle
        self.transition_microcycle_table.setHorizontalHeaderLabels([
            self.tr("ID"),
            self.tr("Start Microcycle"),
            self.tr("End Microcycle"),
            self.tr("Name"),
            self.tr("Target 1"),
            self.tr("Target 2"),
            self.tr("Target 3"),
            self.tr("Intensity"),
            self.tr("Notes")
        ])

        # Configure table appearance - make all columns resizable by user
        for i in range(self.transition_microcycle_table.columnCount()):
            self.transition_microcycle_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)

        # Set reasonable default widths
        self.transition_microcycle_table.setColumnWidth(0, 40)  # ID
        self.transition_microcycle_table.setColumnWidth(1, 120)  # Start Date
        self.transition_microcycle_table.setColumnWidth(2, 120)  # End Date
        self.transition_microcycle_table.setColumnWidth(3, 150)  # Name
        self.transition_microcycle_table.setColumnWidth(4, 80)  # Target 1
        self.transition_microcycle_table.setColumnWidth(5, 80)  # Target 2
        self.transition_microcycle_table.setColumnWidth(6, 80)  # Target 3
        self.transition_microcycle_table.setColumnWidth(7, 80)  # Intensity
        self.transition_microcycle_table.setColumnWidth(8, 200)  # Notes

        # Disable sorting
        self.transition_microcycle_table.horizontalHeader().setSortIndicatorShown(False)
        self.transition_microcycle_table.setSortingEnabled(False)

        # Make table non-editable
        self.transition_microcycle_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)

        self.transition_microcycle_table.verticalHeader().setVisible(False)
        self.transition_microcycle_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.transition_microcycle_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.transition_microcycle_table.setAlternatingRowColors(True)

        # Add table to layout
        transition_mesocycle_layout.addWidget(self.transition_microcycle_table)

        # Create buttons for Transition Microcycle management
        button_layout = QHBoxLayout()

        self.add_transition_microcycle_button = QPushButton(self.tr("Add Microcycle"))
        self.edit_transition_microcycle_button = QPushButton(self.tr("Edit Microcycle"))
        self.remove_transition_microcycle_button = QPushButton(self.tr("Remove Microcycle"))

        button_layout.addWidget(self.add_transition_microcycle_button)
        button_layout.addWidget(self.edit_transition_microcycle_button)
        button_layout.addWidget(self.remove_transition_microcycle_button)
        button_layout.addStretch()

        # Add buttons to layout
        transition_mesocycle_layout.addLayout(button_layout)

        # Add the transition mesocycle group to the periodization layout
        periodization_layout.addWidget(self.transition_mesocycle_group)

        # Add the periodization group box to the periodization tab layout
        periodization_tab_layout.addWidget(self.periodization_group)
        periodization_tab_layout.addStretch()

        # Create Evaluation sub-tab
        self.evaluation_tab = QWidget()
        evaluation_tab_layout = QVBoxLayout(self.evaluation_tab)

        # Create a group box for evaluation dates
        self.evaluation_group = QGroupBox(self.tr("Evaluation Dates"))
        evaluation_layout = QVBoxLayout(self.evaluation_group)

        # Create 1st evaluation sub-section
        self.first_eval_group = QGroupBox(self.tr("1st Evaluation"))
        first_eval_layout = QFormLayout(self.first_eval_group)

        # Create date fields for 1st evaluation
        self.first_eval_start_date = QDateEdit(calendarPopup=True)
        self.first_eval_start_date.setDisplayFormat("yyyy-MM-dd")
        self.first_eval_start_date.setDate(QDate.currentDate().addDays(30))  # Default to 30 days from today
        self.first_eval_start_date.setToolTip(self.tr("Set the start date of the 1st evaluation"))

        self.first_eval_end_date = QDateEdit(calendarPopup=True)
        self.first_eval_end_date.setDisplayFormat("yyyy-MM-dd")
        self.first_eval_end_date.setDate(QDate.currentDate().addDays(37))  # Default to 7 days after start
        self.first_eval_end_date.setToolTip(self.tr("Set the end date of the 1st evaluation"))

        # Create and store label references
        self.first_eval_start_label = QLabel(self.tr("Start 1st Evaluation:"))
        self.first_eval_end_label = QLabel(self.tr("End 1st Evaluation:"))

        # Add date fields to the form layout
        first_eval_layout.addRow(self.first_eval_start_label, self.first_eval_start_date)
        first_eval_layout.addRow(self.first_eval_end_label, self.first_eval_end_date)

        # Add validation label for 1st evaluation
        self.first_eval_validation_label = QLabel()
        self.first_eval_validation_label.setStyleSheet("color: red;")
        self.first_eval_validation_label.setVisible(False)
        first_eval_layout.addRow("", self.first_eval_validation_label)

        # Create 2nd evaluation sub-section
        self.second_eval_group = QGroupBox(self.tr("2nd Evaluation"))
        second_eval_layout = QFormLayout(self.second_eval_group)

        # Create date fields for 2nd evaluation
        self.second_eval_start_date = QDateEdit(calendarPopup=True)
        self.second_eval_start_date.setDisplayFormat("yyyy-MM-dd")
        self.second_eval_start_date.setDate(QDate.currentDate().addDays(120))  # Default to 120 days from today
        self.second_eval_start_date.setToolTip(self.tr("Set the start date of the 2nd evaluation"))

        self.second_eval_end_date = QDateEdit(calendarPopup=True)
        self.second_eval_end_date.setDisplayFormat("yyyy-MM-dd")
        self.second_eval_end_date.setDate(QDate.currentDate().addDays(127))  # Default to 7 days after start
        self.second_eval_end_date.setToolTip(self.tr("Set the end date of the 2nd evaluation"))

        # Create and store label references
        self.second_eval_start_label = QLabel(self.tr("Start 2nd Evaluation:"))
        self.second_eval_end_label = QLabel(self.tr("End 2nd Evaluation:"))

        # Add date fields to the form layout
        second_eval_layout.addRow(self.second_eval_start_label, self.second_eval_start_date)
        second_eval_layout.addRow(self.second_eval_end_label, self.second_eval_end_date)

        # Add validation label for 2nd evaluation
        self.second_eval_validation_label = QLabel()
        self.second_eval_validation_label.setStyleSheet("color: red;")
        self.second_eval_validation_label.setVisible(False)
        second_eval_layout.addRow("", self.second_eval_validation_label)

        # Create 3rd evaluation sub-section
        self.third_eval_group = QGroupBox(self.tr("3rd Evaluation"))
        third_eval_layout = QFormLayout(self.third_eval_group)

        # Create date fields for 3rd evaluation
        self.third_eval_start_date = QDateEdit(calendarPopup=True)
        self.third_eval_start_date.setDisplayFormat("yyyy-MM-dd")
        self.third_eval_start_date.setDate(QDate.currentDate().addDays(240))  # Default to 240 days from today
        self.third_eval_start_date.setToolTip(self.tr("Set the start date of the 3rd evaluation"))

        self.third_eval_end_date = QDateEdit(calendarPopup=True)
        self.third_eval_end_date.setDisplayFormat("yyyy-MM-dd")
        self.third_eval_end_date.setDate(QDate.currentDate().addDays(247))  # Default to 7 days after start
        self.third_eval_end_date.setToolTip(self.tr("Set the end date of the 3rd evaluation"))

        # Create and store label references
        self.third_eval_start_label = QLabel(self.tr("Start 3rd Evaluation:"))
        self.third_eval_end_label = QLabel(self.tr("End 3rd Evaluation:"))

        # Add date fields to the form layout
        third_eval_layout.addRow(self.third_eval_start_label, self.third_eval_start_date)
        third_eval_layout.addRow(self.third_eval_end_label, self.third_eval_end_date)

        # Add validation label for 3rd evaluation
        self.third_eval_validation_label = QLabel()
        self.third_eval_validation_label.setStyleSheet("color: red;")
        self.third_eval_validation_label.setVisible(False)
        third_eval_layout.addRow("", self.third_eval_validation_label)

        # Add all evaluation sub-sections to the main evaluation layout
        evaluation_layout.addWidget(self.first_eval_group)
        evaluation_layout.addWidget(self.second_eval_group)
        evaluation_layout.addWidget(self.third_eval_group)

        # Add the evaluation group box to the evaluation tab layout
        evaluation_tab_layout.addWidget(self.evaluation_group)
        evaluation_tab_layout.addStretch()

        # Add the sub-tabs to the dates tab widget
        self.dates_subtabs.addTab(self.season_tab, self.tr("Season"))
        self.dates_subtabs.addTab(self.periodization_tab, self.tr("Periodization"))
        self.dates_subtabs.addTab(self.evaluation_tab, self.tr("Evaluation"))

        # Connect signals for season dates
        self.season_start_date.dateChanged.connect(self._validate_season_dates)
        self.season_end_date.dateChanged.connect(self._validate_season_dates)

        # Connect signals for Macrocycle dates
        self.macrocycle_start_date.dateChanged.connect(self._validate_macrocycle_dates)
        self.macrocycle_end_date.dateChanged.connect(self._validate_macrocycle_dates)

        # Connect season date changes to also validate Macrocycle dates
        self.season_start_date.dateChanged.connect(self._validate_macrocycle_dates)
        self.season_end_date.dateChanged.connect(self._validate_macrocycle_dates)

        # Connect signals for Evaluation dates
        self.first_eval_start_date.dateChanged.connect(self._validate_evaluation_dates)
        self.first_eval_end_date.dateChanged.connect(self._validate_evaluation_dates)
        self.second_eval_start_date.dateChanged.connect(self._validate_evaluation_dates)
        self.second_eval_end_date.dateChanged.connect(self._validate_evaluation_dates)
        self.third_eval_start_date.dateChanged.connect(self._validate_evaluation_dates)
        self.third_eval_end_date.dateChanged.connect(self._validate_evaluation_dates)

        # Connect season date changes to also validate Evaluation dates
        self.season_start_date.dateChanged.connect(self._validate_evaluation_dates)
        self.season_end_date.dateChanged.connect(self._validate_evaluation_dates)

        # Connect date changes to update the timeline
        self.season_start_date.dateChanged.connect(self._update_timeline)
        self.season_end_date.dateChanged.connect(self._update_timeline)
        self.macrocycle_start_date.dateChanged.connect(self._update_timeline)
        self.macrocycle_end_date.dateChanged.connect(self._update_timeline)
        self.first_eval_start_date.dateChanged.connect(self._update_timeline)
        self.first_eval_end_date.dateChanged.connect(self._update_timeline)
        self.second_eval_start_date.dateChanged.connect(self._update_timeline)
        self.second_eval_end_date.dateChanged.connect(self._update_timeline)
        self.third_eval_start_date.dateChanged.connect(self._update_timeline)
        self.third_eval_end_date.dateChanged.connect(self._update_timeline)

        # --- Build Nationality Zones Tab --- #
        self.nationality_zones_tab = QWidget()
        zones_main_layout = QHBoxLayout(self.nationality_zones_tab)

        # -- Left Panel (Group Management) -- #
        group_panel_layout = QVBoxLayout()
        self.group_label = QLabel(self.tr("Defined Zones:"))
        self.group_list = QListWidget()
        self.group_list.setToolTip(self.tr("Select a zone to view/edit assigned nationalities."))

        group_button_layout = QHBoxLayout()
        self.add_group_button = QPushButton(self.tr("Add..."))
        self.remove_group_button = QPushButton(self.tr("Remove"))
        self.rename_group_button = QPushButton(self.tr("Rename..."))
        self.remove_group_button.setEnabled(False) # Disable initially
        self.rename_group_button.setEnabled(False) # Disable initially
        group_button_layout.addWidget(self.add_group_button)
        group_button_layout.addWidget(self.remove_group_button)
        group_button_layout.addWidget(self.rename_group_button)
        group_button_layout.addStretch()

        group_panel_layout.addWidget(self.group_label)
        group_panel_layout.addWidget(self.group_list)
        group_panel_layout.addLayout(group_button_layout)

        # -- Right Panel (Nationality Assignment) -- #
        assign_panel_layout = QGridLayout() # Use grid for flexibility
        self.available_label = QLabel(self.tr("Available Nationalities:"))
        self.assigned_label = QLabel(self.tr("Assigned Nationalities:"))

        self.available_list = QListWidget()
        self.available_list.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection) # Allow multi-select
        self.available_list.setToolTip(self.tr("Nationalities not assigned to the selected zone."))

        self.assigned_list = QListWidget()
        self.assigned_list.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection) # Allow multi-select
        self.assigned_list.setToolTip(self.tr("Nationalities currently assigned to the selected zone."))

        # Buttons in the middle column
        assign_button_layout = QVBoxLayout()
        self.add_nationality_button = QPushButton(self.tr("Add") + " >>")
        self.remove_nationality_button = QPushButton("<< " + self.tr("Remove"))
        self.add_nationality_button.setToolTip(self.tr("Assign selected available nationality to the current zone"))
        self.remove_nationality_button.setToolTip(self.tr("Remove selected assigned nationality from the current zone"))
        self.add_nationality_button.setEnabled(False) # Disable initially
        self.remove_nationality_button.setEnabled(False) # Disable initially
        assign_button_layout.addStretch()
        assign_button_layout.addWidget(self.add_nationality_button)
        assign_button_layout.addWidget(self.remove_nationality_button)
        assign_button_layout.addStretch()

        # Add widgets to grid layout
        assign_panel_layout.addWidget(self.available_label, 0, 0)
        assign_panel_layout.addWidget(self.assigned_label, 0, 2)
        assign_panel_layout.addWidget(self.available_list, 1, 0)
        assign_panel_layout.addLayout(assign_button_layout, 1, 1, alignment=Qt.AlignmentFlag.AlignCenter) # Add button layout to middle
        assign_panel_layout.addWidget(self.assigned_list, 1, 2)

        # Set column stretch factors (make lists wider than button column)
        assign_panel_layout.setColumnStretch(0, 3) # Available list wider
        assign_panel_layout.setColumnStretch(1, 1) # Buttons narrow
        assign_panel_layout.setColumnStretch(2, 3) # Assigned list wider

        # --- Add Panels to Zones Tab Main Layout --- #
        zones_main_layout.addLayout(group_panel_layout, 1) # Stretch factor 1
        zones_main_layout.addLayout(assign_panel_layout, 3) # Stretch factor 3 (wider)

        # --- Build App Media Tab --- #
        self.app_media_tab = QWidget()
        app_media_layout = QVBoxLayout(self.app_media_tab)

        # Create Football Pitch section
        football_pitch_group = QGroupBox(self.tr("Football Pitch"))
        football_pitch_layout = QVBoxLayout(football_pitch_group)

        # Create Positions Pitch frame
        positions_pitch_group = QGroupBox(self.tr("Positions Pitch"))
        positions_pitch_layout = QVBoxLayout(positions_pitch_group)

        # Create a horizontal layout for the two frames
        frames_layout = QHBoxLayout()

        # Define default style for frames
        default_style = "background-color: #f0f0f0; border: 1px solid #cccccc;"

        # Create the first frame for the pitch image
        self.positions_pitch_frame = QLabel()
        self.positions_pitch_frame.setObjectName("positions_pitch_frame")
        self.positions_pitch_frame.setFixedSize(70, 100)
        self.positions_pitch_frame.setFrameShape(QFrame.Shape.Box)
        self.positions_pitch_frame.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.positions_pitch_frame.setText(self.tr("Click to upload image"))
        self.positions_pitch_frame.setStyleSheet(default_style)
        self.positions_pitch_frame.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # Create the second frame for the pitch image
        self.positions_pitch_frame2 = QLabel()
        self.positions_pitch_frame2.setObjectName("positions_pitch_frame2")
        self.positions_pitch_frame2.setFixedSize(70, 100)
        self.positions_pitch_frame2.setFrameShape(QFrame.Shape.Box)
        self.positions_pitch_frame2.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.positions_pitch_frame2.setText(self.tr("Click to upload image"))
        self.positions_pitch_frame2.setStyleSheet(default_style)
        self.positions_pitch_frame2.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # Create the third frame for the pitch image
        self.positions_pitch_frame3 = QLabel()
        self.positions_pitch_frame3.setObjectName("positions_pitch_frame3")
        self.positions_pitch_frame3.setFixedSize(70, 100)
        self.positions_pitch_frame3.setFrameShape(QFrame.Shape.Box)
        self.positions_pitch_frame3.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.positions_pitch_frame3.setText(self.tr("Click to upload image"))
        self.positions_pitch_frame3.setStyleSheet(default_style)
        self.positions_pitch_frame3.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # Add all three frames to the horizontal layout with some spacing
        frames_layout.addStretch()
        frames_layout.addWidget(self.positions_pitch_frame)
        frames_layout.addSpacing(20)  # Add some space between the frames
        frames_layout.addWidget(self.positions_pitch_frame2)
        frames_layout.addSpacing(20)  # Add some space between the frames
        frames_layout.addWidget(self.positions_pitch_frame3)
        frames_layout.addStretch()

        # Add the frames layout to the positions pitch layout
        positions_pitch_layout.addLayout(frames_layout)

        # Add a note about image requirements
        self.requirements_label = QLabel(self.tr("Requirements: PNG format, < 200KB, max 400x600 pixels"))
        self.requirements_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.requirements_label.setStyleSheet("font-size: 9pt; color: #666666;")
        positions_pitch_layout.addWidget(self.requirements_label)

        # Add the positions pitch group to the football pitch layout
        football_pitch_layout.addWidget(positions_pitch_group)

        # Add the football pitch group to the main layout
        app_media_layout.addWidget(football_pitch_group)
        app_media_layout.addStretch()

        # Connect signals for the first pitch frame
        self.positions_pitch_frame.mousePressEvent = self._on_positions_pitch_clicked
        self.positions_pitch_frame.customContextMenuRequested.connect(self._show_positions_pitch_context_menu)

        # Connect signals for the second pitch frame
        self.positions_pitch_frame2.mousePressEvent = self._on_positions_pitch2_clicked
        self.positions_pitch_frame2.customContextMenuRequested.connect(self._show_positions_pitch2_context_menu)

        # Connect signals for the third pitch frame
        self.positions_pitch_frame3.mousePressEvent = self._on_positions_pitch3_clicked
        self.positions_pitch_frame3.customContextMenuRequested.connect(self._show_positions_pitch3_context_menu)

        # Create Football Competitions tab
        self.football_competitions_tab = QWidget()
        self._init_football_competitions_tab()

        # Add tabs to the main tab widget
        self.tabs.addTab(self.football_rules_tab, self.tr("Football Rules"))
        self.tabs.addTab(self.nationality_zones_tab, self.tr("Nationality Zones"))
        self.tabs.addTab(self.dates_tab, self.tr("Dates"))
        self.tabs.addTab(self.app_media_tab, self.tr("App Media"))
        self.tabs.addTab(self.football_competitions_tab, self.tr("Football Competitions"))

        # Set a reasonable default size
        self.resize(750, 500) # Increased size a bit

        # Connect button signals for Basic Mesocycle
        self.add_basic_microcycle_button.clicked.connect(self._add_basic_microcycle)
        self.edit_basic_microcycle_button.clicked.connect(self._edit_basic_microcycle)
        self.remove_basic_microcycle_button.clicked.connect(self._remove_basic_microcycle)

        # Connect double-click on basic table to edit basic microcycle
        self.basic_microcycle_table.doubleClicked.connect(self._handle_basic_table_double_click)

        # Connect button signals for Competition Mesocycle
        self.add_competition_microcycle_button.clicked.connect(self._add_competition_microcycle)
        self.edit_competition_microcycle_button.clicked.connect(self._edit_competition_microcycle)
        self.remove_competition_microcycle_button.clicked.connect(self._remove_competition_microcycle)

        # Connect double-click on competition table to edit competition microcycle
        self.competition_microcycle_table.doubleClicked.connect(self._handle_competition_table_double_click)

        # Connect button signals for Transition Mesocycle
        self.add_transition_microcycle_button.clicked.connect(self._add_transition_microcycle)
        self.edit_transition_microcycle_button.clicked.connect(self._edit_transition_microcycle)
        self.remove_transition_microcycle_button.clicked.connect(self._remove_transition_microcycle)

        # Connect double-click on transition table to edit transition microcycle
        self.transition_microcycle_table.doubleClicked.connect(self._handle_transition_table_double_click)

    def _init_football_rules_tab(self):
        """Initialize the Football Rules tab with game settings and data limits."""
        football_rules_layout = QVBoxLayout(self.football_rules_tab)

        # Create scroll area for the content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Create content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # --- Game Rules Section ---
        game_rules_group = QGroupBox(self.tr("Game Rules"))
        game_rules_layout = QFormLayout(game_rules_group)

        # X-a-side players
        self.players_per_side_spinbox = QSpinBox()
        self.players_per_side_spinbox.setRange(1, 15)
        self.players_per_side_spinbox.setValue(11)  # Default 11-a-side
        self.players_per_side_spinbox.setToolTip(self.tr("Number of players per team on the field"))
        game_rules_layout.addRow(self.tr("Players per side:"), self.players_per_side_spinbox)

        # Maximum substitutions
        self.max_subs_spinbox = QSpinBox()
        self.max_subs_spinbox.setRange(0, 10)
        self.max_subs_spinbox.setValue(5)  # Default 5 subs
        self.max_subs_spinbox.setToolTip(self.tr("Maximum number of substitutions allowed per match"))
        game_rules_layout.addRow(self.tr("Maximum substitutions:"), self.max_subs_spinbox)

        # Minimum required to start
        self.min_to_start_spinbox = QSpinBox()
        self.min_to_start_spinbox.setRange(1, 15)
        self.min_to_start_spinbox.setValue(9)  # Default minimum 9
        self.min_to_start_spinbox.setToolTip(self.tr("Minimum number of players required to start a match"))
        game_rules_layout.addRow(self.tr("Minimum required to start:"), self.min_to_start_spinbox)

        # Minimum required during match
        self.min_during_match_spinbox = QSpinBox()
        self.min_during_match_spinbox.setRange(1, 15)
        self.min_during_match_spinbox.setValue(7)  # Default minimum 7
        self.min_during_match_spinbox.setToolTip(self.tr("Minimum number of players required to continue a match"))
        game_rules_layout.addRow(self.tr("Minimum required during match:"), self.min_during_match_spinbox)

        content_layout.addWidget(game_rules_group)

        # --- Data Limits Section ---
        data_limits_group = QGroupBox(self.tr("Data Limits"))
        data_limits_layout = QFormLayout(data_limits_group)

        # Body data outlier threshold
        self.body_data_threshold_spinbox = QSpinBox()
        self.body_data_threshold_spinbox.setRange(10, 100)
        self.body_data_threshold_spinbox.setValue(50)  # Default 50%
        self.body_data_threshold_spinbox.setSuffix("%")
        self.body_data_threshold_spinbox.setToolTip(self.tr(
            "Percentage threshold for highlighting outlier values in physical measurements. "
            "Values that differ from the team average by more than this percentage will be highlighted as potential data entry errors."
        ))
        data_limits_layout.addRow(self.tr("Body data outlier threshold:"), self.body_data_threshold_spinbox)

        # Add explanation text
        explanation_label = QLabel(self.tr(
            "The outlier threshold helps identify potential data entry errors in physical measurements "
            "(height, weight, waist, hip, neck, wrist, forearm, thigh). Values that are significantly "
            "different from the team average will be highlighted in yellow as a warning, but will still be accepted."
        ))
        explanation_label.setWordWrap(True)
        explanation_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 11px;
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                margin: 5px 0px;
            }
        """)
        data_limits_layout.addRow("", explanation_label)

        content_layout.addWidget(data_limits_group)

        # Add stretch to push content to top
        content_layout.addStretch()

        # Set content widget to scroll area
        scroll_area.setWidget(content_widget)
        football_rules_layout.addWidget(scroll_area)

        # Connect signals for saving settings
        self.players_per_side_spinbox.valueChanged.connect(self._save_football_rules_settings)
        self.max_subs_spinbox.valueChanged.connect(self._save_football_rules_settings)
        self.min_to_start_spinbox.valueChanged.connect(self._save_football_rules_settings)
        self.min_during_match_spinbox.valueChanged.connect(self._save_football_rules_settings)
        self.body_data_threshold_spinbox.valueChanged.connect(self._save_football_rules_settings)

    def _save_football_rules_settings(self):
        """Save football rules settings to QSettings."""
        settings = QSettings()
        settings.beginGroup("football_rules")

        settings.setValue("players_per_side", self.players_per_side_spinbox.value())
        settings.setValue("max_substitutions", self.max_subs_spinbox.value())
        settings.setValue("min_to_start", self.min_to_start_spinbox.value())
        settings.setValue("min_during_match", self.min_during_match_spinbox.value())
        settings.setValue("body_data_threshold", self.body_data_threshold_spinbox.value())

        settings.endGroup()

    def _load_football_rules_settings(self):
        """Load football rules settings from QSettings."""
        settings = QSettings()
        settings.beginGroup("football_rules")

        # Load values with defaults
        players_per_side = settings.value("players_per_side", 11, int)
        max_subs = settings.value("max_substitutions", 5, int)
        min_to_start = settings.value("min_to_start", 9, int)
        min_during_match = settings.value("min_during_match", 7, int)
        body_data_threshold = settings.value("body_data_threshold", 50, int)

        # Set values to UI controls
        self.players_per_side_spinbox.setValue(players_per_side)
        self.max_subs_spinbox.setValue(max_subs)
        self.min_to_start_spinbox.setValue(min_to_start)
        self.min_during_match_spinbox.setValue(min_during_match)
        self.body_data_threshold_spinbox.setValue(body_data_threshold)

        settings.endGroup()

    def _load_settings(self):
        """Loads all settings from QSettings."""
        if self._load_settings_called:
            return

        self._load_zone_settings()
        self._load_date_settings()
        self._load_microcycle_data()
        self._load_basic_microcycle_data()
        self._load_competition_microcycle_data()
        self._load_transition_microcycle_data()
        self._load_competition_data()
        self._load_football_rules_settings()

        # Initialize the nationality zones UI
        self._update_nationality_zones_ui()

        self._load_settings_called = True

    def _update_nationality_zones_ui(self):
        """Updates the nationality zones UI elements."""
        # Populate the group list widget
        self.group_list.clear()
        group_names = sorted(self.zone_data.keys()) # Sort alphabetically
        self.group_list.addItems(group_names)
        print(f"Populated group list with: {group_names}")

        # Clear nationality lists as no group is selected initially
        self.available_list.clear()
        self.assigned_list.clear()
        # Disable buttons that require selection
        self._update_button_states()

    def _load_zone_settings(self):
        """Loads the zone configuration from QSettings and populates the group list."""
        print("Loading zone settings...")
        settings = QSettings()
        settings.beginGroup("nationality_zones")
        # Remove type=dict, check type manually after loading
        loaded_data = settings.value("groups", defaultValue={})
        settings.endGroup()

        # Ensure loaded data is actually a dictionary (this check is now crucial)
        if not isinstance(loaded_data, dict):
            print("Warning: Nationality zone data in settings is not a dictionary. Resetting.")
            self.zone_data = {}
        else:
            # Basic validation: Ensure values are lists of strings (optional)
            self.zone_data = {}
            for group_name, nat_list in loaded_data.items():
                if isinstance(group_name, str) and isinstance(nat_list, list):
                    # Further check if all items in list are strings
                    if all(isinstance(nat, str) for nat in nat_list):
                        self.zone_data[group_name] = nat_list
                    else:
                        print(f"Warning: Skipping zone '{group_name}' - contains non-string nationalities.")

    def _load_date_settings(self):
        """Loads the date settings from QSettings."""
        settings = QSettings()

        # Load season dates
        settings.beginGroup("season_dates")

        # Load start date with current date as default
        start_date_str = settings.value("start_date", QDate.currentDate().toString(Qt.DateFormat.ISODate))
        start_date = QDate.fromString(start_date_str, Qt.DateFormat.ISODate)
        if start_date.isValid():
            self.season_start_date.setDate(start_date)
        else:
            self.season_start_date.setDate(QDate.currentDate())

        # Load end date with current date + 365 days as default
        default_end = QDate.currentDate().addDays(365)
        end_date_str = settings.value("end_date", default_end.toString(Qt.DateFormat.ISODate))
        end_date = QDate.fromString(end_date_str, Qt.DateFormat.ISODate)
        if end_date.isValid():
            self.season_end_date.setDate(end_date)
        else:
            self.season_end_date.setDate(default_end)

        settings.endGroup()

        # Load macrocycle dates
        settings.beginGroup("periodization")

        # Load macrocycle start date with season start date as default
        macrocycle_start_str = settings.value("macrocycle_start", start_date.toString(Qt.DateFormat.ISODate))
        macrocycle_start = QDate.fromString(macrocycle_start_str, Qt.DateFormat.ISODate)
        if macrocycle_start.isValid():
            self.macrocycle_start_date.setDate(macrocycle_start)
        else:
            self.macrocycle_start_date.setDate(start_date)

        # Load macrocycle end date with season end date as default
        macrocycle_end_str = settings.value("macrocycle_end", end_date.toString(Qt.DateFormat.ISODate))
        macrocycle_end = QDate.fromString(macrocycle_end_str, Qt.DateFormat.ISODate)
        if macrocycle_end.isValid():
            self.macrocycle_end_date.setDate(macrocycle_end)
        else:
            self.macrocycle_end_date.setDate(end_date)

        settings.endGroup()

        # Load evaluation dates
        self._load_evaluation_dates(start_date, end_date)

        # Initialize last valid dates
        self.last_valid_season_start = self.season_start_date.date()
        self.last_valid_season_end = self.season_end_date.date()
        self.last_valid_macrocycle_start = self.macrocycle_start_date.date()
        self.last_valid_macrocycle_end = self.macrocycle_end_date.date()

        print(f"Loaded macrocycle dates: {self.macrocycle_start_date.date().toString('yyyy-MM-dd')} to {self.macrocycle_end_date.date().toString('yyyy-MM-dd')}")

        # Validate the loaded dates
        self._validate_season_dates()
        self._validate_macrocycle_dates()
        self._validate_evaluation_dates()

    def _load_evaluation_dates(self, season_start, season_end):
        """Loads the evaluation dates from QSettings."""
        settings = QSettings()

        # Calculate default dates based on season
        season_days = season_start.daysTo(season_end)

        # Default 1st evaluation: 1/6 of the season
        default_first_start = season_start.addDays(int(season_days * 1/6))
        default_first_end = default_first_start.addDays(7)  # 1 week duration

        # Default 2nd evaluation: 3/6 of the season
        default_second_start = season_start.addDays(int(season_days * 3/6))
        default_second_end = default_second_start.addDays(7)  # 1 week duration

        # Default 3rd evaluation: 5/6 of the season
        default_third_start = season_start.addDays(int(season_days * 5/6))
        default_third_end = default_third_start.addDays(7)  # 1 week duration

        # Load evaluation dates
        settings.beginGroup("evaluation_dates")

        # Load 1st evaluation dates
        first_start_str = settings.value("first_eval_start", default_first_start.toString(Qt.DateFormat.ISODate))
        first_start = QDate.fromString(first_start_str, Qt.DateFormat.ISODate)
        if first_start.isValid():
            self.first_eval_start_date.setDate(first_start)
        else:
            self.first_eval_start_date.setDate(default_first_start)

        first_end_str = settings.value("first_eval_end", default_first_end.toString(Qt.DateFormat.ISODate))
        first_end = QDate.fromString(first_end_str, Qt.DateFormat.ISODate)
        if first_end.isValid():
            self.first_eval_end_date.setDate(first_end)
        else:
            self.first_eval_end_date.setDate(default_first_end)

        # Load 2nd evaluation dates
        second_start_str = settings.value("second_eval_start", default_second_start.toString(Qt.DateFormat.ISODate))
        second_start = QDate.fromString(second_start_str, Qt.DateFormat.ISODate)
        if second_start.isValid():
            self.second_eval_start_date.setDate(second_start)
        else:
            self.second_eval_start_date.setDate(default_second_start)

        second_end_str = settings.value("second_eval_end", default_second_end.toString(Qt.DateFormat.ISODate))
        second_end = QDate.fromString(second_end_str, Qt.DateFormat.ISODate)
        if second_end.isValid():
            self.second_eval_end_date.setDate(second_end)
        else:
            self.second_eval_end_date.setDate(default_second_end)

        # Load 3rd evaluation dates
        third_start_str = settings.value("third_eval_start", default_third_start.toString(Qt.DateFormat.ISODate))
        third_start = QDate.fromString(third_start_str, Qt.DateFormat.ISODate)
        if third_start.isValid():
            self.third_eval_start_date.setDate(third_start)
        else:
            self.third_eval_start_date.setDate(default_third_start)

        third_end_str = settings.value("third_eval_end", default_third_end.toString(Qt.DateFormat.ISODate))
        third_end = QDate.fromString(third_end_str, Qt.DateFormat.ISODate)
        if third_end.isValid():
            self.third_eval_end_date.setDate(third_end)
        else:
            self.third_eval_end_date.setDate(default_third_end)

        settings.endGroup()

    def _validate_season_dates(self):
        """Validates that the season end date is after the start date and the period is ≤ 365/366 days."""
        start_date = self.season_start_date.date()
        end_date = self.season_end_date.date()

        # Temporarily disconnect signals to prevent recursive calls
        self.season_start_date.dateChanged.disconnect(self._validate_season_dates)
        self.season_end_date.dateChanged.disconnect(self._validate_season_dates)

        # Flag to track if we need to revert
        need_to_revert = False

        # Check if end date is after start date
        if end_date < start_date:
            self.date_validation_label.setText(self.tr("End date must be after start date"))
            self.date_validation_label.setVisible(True)
            need_to_revert = True
        else:
            # Calculate days between dates
            days_between = start_date.daysTo(end_date)

            # Check if the year is a leap year
            start_year = start_date.year()
            is_leap_year = (start_year % 4 == 0 and start_year % 100 != 0) or (start_year % 400 == 0)
            max_days = 366 if is_leap_year else 365

            # Check if period is within allowed range
            if days_between > max_days:
                self.date_validation_label.setText(self.tr(f"Season cannot exceed {max_days} days"))
                self.date_validation_label.setVisible(True)
                need_to_revert = True
            else:
                # If all validations pass
                self.date_validation_label.setVisible(False)

                # Check if dates have changed from the last valid dates
                if (start_date != self.last_valid_season_start or
                    end_date != self.last_valid_season_end):

                    # Check if we need to update dependent dates
                    if self._check_if_dependent_dates_need_update(start_date, end_date):
                        # Show confirmation dialog
                        result = self._show_date_change_confirmation(start_date, end_date)

                        if result == DateChangeConfirmationDialog.ADJUST_ALL:
                            # Update all dependent dates
                            self._adjust_all_dependent_dates(start_date, end_date)
                        elif result == DateChangeConfirmationDialog.KEEP_WHERE_POSSIBLE:
                            # Update only invalid dependent dates
                            self._adjust_invalid_dependent_dates(start_date, end_date)
                        else:  # CANCEL
                            # Revert to last valid dates
                            self._revert_to_last_valid_season_dates()

                            # Reconnect signals
                            self.season_start_date.dateChanged.connect(self._validate_season_dates)
                            self.season_end_date.dateChanged.connect(self._validate_season_dates)

                            return False

                # Store these as the last valid dates
                self.last_valid_season_start = start_date
                self.last_valid_season_end = end_date

                # Save the valid dates
                self._save_date_settings()

                # Also validate macrocycle dates since season dates affect them
                self._validate_macrocycle_dates()

        # If validation failed, revert to last valid dates
        if need_to_revert:
            self.season_start_date.setDate(self.last_valid_season_start)
            self.season_end_date.setDate(self.last_valid_season_end)

            # Show a message box to inform the user
            QMessageBox.warning(
                self,
                self.tr("Invalid Date Range"),
                self.tr("The selected date range is invalid. Reverting to the last valid dates.")
            )

        # Reconnect signals
        self.season_start_date.dateChanged.connect(self._validate_season_dates)
        self.season_end_date.dateChanged.connect(self._validate_season_dates)

        return not need_to_revert

    def _check_if_dependent_dates_need_update(self, new_start, new_end):
        """Check if macrocycle or microcycle dates need to be updated based on new season dates."""
        # Check macrocycle dates
        macro_start = self.macrocycle_start_date.date()
        macro_end = self.macrocycle_end_date.date()

        if (macro_start < new_start or macro_end > new_end):
            return True

        # Check microcycle dates
        for row in range(self.microcycle_table.rowCount()):
            micro_start = QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
            micro_end = QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd")

            if (micro_start < new_start or micro_end > new_end):
                return True

        return False

    def _show_date_change_confirmation(self, new_start, new_end):
        """Show confirmation dialog for date changes and return the selected option."""
        # Prepare season changes
        season_changes = {
            'old_start': self.last_valid_season_start,
            'old_end': self.last_valid_season_end,
            'new_start': new_start,
            'new_end': new_end
        }

        # Prepare macrocycle changes if needed
        macrocycle_changes = None
        macro_start = self.macrocycle_start_date.date()
        macro_end = self.macrocycle_end_date.date()

        if (macro_start < new_start or macro_end > new_end):
            # Calculate new macrocycle dates
            new_macro_start = max(macro_start, new_start)
            new_macro_end = min(macro_end, new_end)

            macrocycle_changes = {
                'old_start': macro_start,
                'old_end': macro_end,
                'new_start': new_macro_start,
                'new_end': new_macro_end
            }

        # Prepare microcycle changes if needed
        microcycle_changes = []

        for row in range(self.microcycle_table.rowCount()):
            micro_start = QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
            micro_end = QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd")
            name = self.microcycle_table.item(row, 3).text()

            if (micro_start < new_start or micro_end > new_end):
                # Calculate new microcycle dates
                new_micro_start = max(micro_start, new_start)
                new_micro_end = min(micro_end, new_end)

                microcycle_changes.append({
                    'row': row,
                    'name': name,
                    'old_start': micro_start,
                    'old_end': micro_end,
                    'new_start': new_micro_start,
                    'new_end': new_micro_end
                })

        # Show dialog if there are changes
        if macrocycle_changes or microcycle_changes:
            dialog = DateChangeConfirmationDialog(
                self,
                season_changes=season_changes,
                macrocycle_changes=macrocycle_changes,
                microcycle_changes=microcycle_changes
            )

            if dialog.exec() == QDialog.DialogCode.Accepted:
                return dialog.get_selected_option()
            else:
                return DateChangeConfirmationDialog.CANCEL

        return DateChangeConfirmationDialog.ADJUST_ALL  # Default if no changes needed

    def _adjust_all_dependent_dates(self, new_start, new_end):
        """Adjust all dependent dates proportionally based on new season dates."""
        old_start = self.last_valid_season_start
        old_end = self.last_valid_season_end

        # Calculate the scaling factors
        old_duration = old_start.daysTo(old_end)
        new_duration = new_start.daysTo(new_end)

        if old_duration <= 0 or new_duration <= 0:
            return  # Avoid division by zero

        scale_factor = new_duration / old_duration

        # Temporarily disconnect signals to prevent recursive calls
        self.macrocycle_start_date.dateChanged.disconnect(self._validate_macrocycle_dates)
        self.macrocycle_end_date.dateChanged.disconnect(self._validate_macrocycle_dates)

        # Adjust macrocycle dates
        macro_start = self.macrocycle_start_date.date()
        macro_end = self.macrocycle_end_date.date()

        # Calculate days from season start
        days_from_start = old_start.daysTo(macro_start)
        days_to_end = macro_end.daysTo(old_end)

        # Scale the days
        new_days_from_start = int(days_from_start * scale_factor)
        new_days_to_end = int(days_to_end * scale_factor)

        # Calculate new dates
        new_macro_start = new_start.addDays(new_days_from_start)
        new_macro_end = new_end.addDays(-new_days_to_end)

        # Ensure dates are within season
        new_macro_start = max(new_macro_start, new_start)
        new_macro_end = min(new_macro_end, new_end)

        # Update macrocycle dates
        self.macrocycle_start_date.setDate(new_macro_start)
        self.macrocycle_end_date.setDate(new_macro_end)

        # Update last valid macrocycle dates
        self.last_valid_macrocycle_start = new_macro_start
        self.last_valid_macrocycle_end = new_macro_end

        # Reconnect signals
        self.macrocycle_start_date.dateChanged.connect(self._validate_macrocycle_dates)
        self.macrocycle_end_date.dateChanged.connect(self._validate_macrocycle_dates)

        # Now adjust microcycle dates
        self._adjust_microcycle_dates(new_macro_start, new_macro_end, scale_factor)

    def _adjust_invalid_dependent_dates(self, new_start, new_end):
        """Adjust only dependent dates that would be invalid with the new season dates."""
        # Temporarily disconnect signals to prevent recursive calls
        self.macrocycle_start_date.dateChanged.disconnect(self._validate_macrocycle_dates)
        self.macrocycle_end_date.dateChanged.disconnect(self._validate_macrocycle_dates)

        # Adjust macrocycle dates if needed
        macro_start = self.macrocycle_start_date.date()
        macro_end = self.macrocycle_end_date.date()

        new_macro_start = macro_start
        new_macro_end = macro_end

        # Check if macrocycle start is before season start
        if macro_start < new_start:
            new_macro_start = new_start

        # Check if macrocycle end is after season end
        if macro_end > new_end:
            new_macro_end = new_end

        # Update macrocycle dates if they changed
        if new_macro_start != macro_start:
            self.macrocycle_start_date.setDate(new_macro_start)

        if new_macro_end != macro_end:
            self.macrocycle_end_date.setDate(new_macro_end)

        # Update last valid macrocycle dates
        self.last_valid_macrocycle_start = new_macro_start
        self.last_valid_macrocycle_end = new_macro_end

        # Reconnect signals
        self.macrocycle_start_date.dateChanged.connect(self._validate_macrocycle_dates)
        self.macrocycle_end_date.dateChanged.connect(self._validate_macrocycle_dates)

        # Now adjust invalid microcycle dates
        for row in range(self.microcycle_table.rowCount()):
            micro_start = QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
            micro_end = QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd")

            new_micro_start = micro_start
            new_micro_end = micro_end

            # Check if microcycle start is before macrocycle start
            if micro_start < new_macro_start:
                new_micro_start = new_macro_start

            # Check if microcycle end is after macrocycle end
            if micro_end > new_macro_end:
                new_micro_end = new_macro_end

            # Update microcycle dates if they changed
            if new_micro_start != micro_start:
                self.microcycle_table.setItem(row, 1, QTableWidgetItem(new_micro_start.toString("yyyy-MM-dd")))

            if new_micro_end != micro_end:
                self.microcycle_table.setItem(row, 2, QTableWidgetItem(new_micro_end.toString("yyyy-MM-dd")))

        # Save microcycle data
        self._save_microcycle_data()

    def _adjust_microcycle_dates(self, new_macro_start, new_macro_end, scale_factor=None):
        """Adjust microcycle dates based on new macrocycle dates."""
        old_macro_start = self.last_valid_macrocycle_start
        old_macro_end = self.last_valid_macrocycle_end

        # Calculate the scaling factors if not provided
        if scale_factor is None:
            old_duration = old_macro_start.daysTo(old_macro_end)
            new_duration = new_macro_start.daysTo(new_macro_end)

            if old_duration <= 0 or new_duration <= 0:
                return  # Avoid division by zero

            scale_factor = new_duration / old_duration

        # Adjust each microcycle
        for row in range(self.microcycle_table.rowCount()):
            micro_start = QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
            micro_end = QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd")

            # Calculate days from macrocycle start
            days_from_start = old_macro_start.daysTo(micro_start)
            days_to_end = micro_end.daysTo(old_macro_end)

            # Scale the days
            new_days_from_start = int(days_from_start * scale_factor)
            new_days_to_end = int(days_to_end * scale_factor)

            # Calculate new dates
            new_micro_start = new_macro_start.addDays(new_days_from_start)
            new_micro_end = new_macro_end.addDays(-new_days_to_end)

            # Ensure dates are within macrocycle
            new_micro_start = max(new_micro_start, new_macro_start)
            new_micro_end = min(new_micro_end, new_macro_end)

            # Update microcycle dates
            self.microcycle_table.setItem(row, 1, QTableWidgetItem(new_micro_start.toString("yyyy-MM-dd")))
            self.microcycle_table.setItem(row, 2, QTableWidgetItem(new_micro_end.toString("yyyy-MM-dd")))

        # Save microcycle data
        self._save_microcycle_data()

    def _revert_to_last_valid_season_dates(self):
        """Revert to the last valid season dates."""
        # Temporarily disconnect signals to prevent recursive calls
        self.season_start_date.dateChanged.disconnect(self._validate_season_dates)
        self.season_end_date.dateChanged.disconnect(self._validate_season_dates)

        # Revert to last valid dates
        self.season_start_date.setDate(self.last_valid_season_start)
        self.season_end_date.setDate(self.last_valid_season_end)

        # Hide validation label
        self.date_validation_label.setVisible(False)

    def _validate_macrocycle_dates(self):
        """Validates that the macrocycle dates are within the season dates and end date is after start date."""
        # Temporarily disconnect signals to prevent recursive calls
        self.macrocycle_start_date.dateChanged.disconnect(self._on_macrocycle_date_changed)
        self.macrocycle_end_date.dateChanged.disconnect(self._on_macrocycle_date_changed)

        # Get season dates
        season_start = self.season_start_date.date()
        season_end = self.season_end_date.date()

        # Get macrocycle dates
        macrocycle_start = self.macrocycle_start_date.date()
        macrocycle_end = self.macrocycle_end_date.date()

        # Flag to track if we need to revert
        need_to_revert = False

        # Check if macrocycle end date is after start date
        if macrocycle_end < macrocycle_start:
            self.macrocycle_validation_label.setText(self.tr("Macrocycle end date must be after start date"))
            self.macrocycle_validation_label.setVisible(True)
            need_to_revert = True
        # Check if macrocycle dates are within season dates
        elif macrocycle_start < season_start:
            self.macrocycle_validation_label.setText(self.tr("Macrocycle start date must be within season dates"))
            self.macrocycle_validation_label.setVisible(True)
            need_to_revert = True
        elif macrocycle_end > season_end:
            self.macrocycle_validation_label.setText(self.tr("Macrocycle end date must be within season dates"))
            self.macrocycle_validation_label.setVisible(True)
            need_to_revert = True
        else:
            # If all validations pass
            self.macrocycle_validation_label.setVisible(False)

            # Update last valid dates
            self.last_valid_macrocycle_start = macrocycle_start
            self.last_valid_macrocycle_end = macrocycle_end

        # If validation failed, revert to last valid dates
        if need_to_revert:
            self.macrocycle_start_date.setDate(self.last_valid_macrocycle_start)
            self.macrocycle_end_date.setDate(self.last_valid_macrocycle_end)

            # Show a message box to inform the user
            QMessageBox.warning(
                self,
                self.tr("Invalid Date Range"),
                self.tr("The selected macrocycle date range is invalid. Reverting to the last valid dates.")
            )

        # Reconnect signals
        self.macrocycle_start_date.dateChanged.connect(self._on_macrocycle_date_changed)
        self.macrocycle_end_date.dateChanged.connect(self._on_macrocycle_date_changed)

        return not need_to_revert

    def _validate_evaluation_dates(self):
        """Validates all evaluation dates to ensure they are in order and within season dates."""
        # Get season dates
        season_start = self.season_start_date.date()
        season_end = self.season_end_date.date()

        # Get evaluation dates
        first_eval_start = self.first_eval_start_date.date()
        first_eval_end = self.first_eval_end_date.date()
        second_eval_start = self.second_eval_start_date.date()
        second_eval_end = self.second_eval_end_date.date()
        third_eval_start = self.third_eval_start_date.date()
        third_eval_end = self.third_eval_end_date.date()

        # Reset validation labels
        self.first_eval_validation_label.setVisible(False)
        self.second_eval_validation_label.setVisible(False)
        self.third_eval_validation_label.setVisible(False)

        # Validate 1st evaluation
        if first_eval_end < first_eval_start:
            self.first_eval_validation_label.setText(self.tr("End date must be after start date"))
            self.first_eval_validation_label.setVisible(True)
            return False

        if first_eval_start < season_start:
            self.first_eval_validation_label.setText(self.tr("Start date must be within season dates"))
            self.first_eval_validation_label.setVisible(True)
            return False

        if first_eval_end > season_end:
            self.first_eval_validation_label.setText(self.tr("End date must be within season dates"))
            self.first_eval_validation_label.setVisible(True)
            return False

        # Validate 2nd evaluation
        if second_eval_end < second_eval_start:
            self.second_eval_validation_label.setText(self.tr("End date must be after start date"))
            self.second_eval_validation_label.setVisible(True)
            return False

        if second_eval_start < season_start:
            self.second_eval_validation_label.setText(self.tr("Start date must be within season dates"))
            self.second_eval_validation_label.setVisible(True)
            return False

        if second_eval_end > season_end:
            self.second_eval_validation_label.setText(self.tr("End date must be within season dates"))
            self.second_eval_validation_label.setVisible(True)
            return False

        # Check if 2nd evaluation starts after 1st evaluation ends
        if second_eval_start <= first_eval_end:
            self.second_eval_validation_label.setText(self.tr("2nd evaluation must start after 1st evaluation ends"))
            self.second_eval_validation_label.setVisible(True)
            return False

        # Validate 3rd evaluation
        if third_eval_end < third_eval_start:
            self.third_eval_validation_label.setText(self.tr("End date must be after start date"))
            self.third_eval_validation_label.setVisible(True)
            return False

        if third_eval_start < season_start:
            self.third_eval_validation_label.setText(self.tr("Start date must be within season dates"))
            self.third_eval_validation_label.setVisible(True)
            return False

        if third_eval_end > season_end:
            self.third_eval_validation_label.setText(self.tr("End date must be within season dates"))
            self.third_eval_validation_label.setVisible(True)
            return False

        # Check if 3rd evaluation starts after 2nd evaluation ends
        if third_eval_start <= second_eval_end:
            self.third_eval_validation_label.setText(self.tr("3rd evaluation must start after 2nd evaluation ends"))
            self.third_eval_validation_label.setVisible(True)
            return False

        # If all validations pass
        return True

    def _save_date_settings(self):
        """Saves the current date settings to QSettings."""
        settings = QSettings()

        # Save season dates
        settings.beginGroup("season_dates")
        settings.setValue("start_date", self.season_start_date.date().toString(Qt.DateFormat.ISODate))
        settings.setValue("end_date", self.season_end_date.date().toString(Qt.DateFormat.ISODate))
        settings.endGroup()

        # Save macrocycle dates
        settings.beginGroup("periodization")
        settings.setValue("macrocycle_start", self.macrocycle_start_date.date().toString(Qt.DateFormat.ISODate))
        settings.setValue("macrocycle_end", self.macrocycle_end_date.date().toString(Qt.DateFormat.ISODate))
        settings.endGroup()

        # Save evaluation dates
        self._save_evaluation_dates()

    def _save_evaluation_dates(self):
        """Saves the evaluation dates to QSettings."""
        settings = QSettings()

        # Save evaluation dates
        settings.beginGroup("evaluation_dates")

        # Save 1st evaluation dates
        settings.setValue("first_eval_start", self.first_eval_start_date.date().toString(Qt.DateFormat.ISODate))
        settings.setValue("first_eval_end", self.first_eval_end_date.date().toString(Qt.DateFormat.ISODate))

        # Save 2nd evaluation dates
        settings.setValue("second_eval_start", self.second_eval_start_date.date().toString(Qt.DateFormat.ISODate))
        settings.setValue("second_eval_end", self.second_eval_end_date.date().toString(Qt.DateFormat.ISODate))

        # Save 3rd evaluation dates
        settings.setValue("third_eval_start", self.third_eval_start_date.date().toString(Qt.DateFormat.ISODate))
        settings.setValue("third_eval_end", self.third_eval_end_date.date().toString(Qt.DateFormat.ISODate))

        settings.endGroup()

    def _update_timeline(self):
        """Updates the season timeline with current date information."""
        # Set season and macrocycle dates
        self.season_timeline.set_season_dates(
            self.season_start_date.date(),
            self.season_end_date.date()
        )

        self.season_timeline.set_macrocycle_dates(
            self.macrocycle_start_date.date(),
            self.macrocycle_end_date.date()
        )

        # Set evaluation periods
        evaluation_periods = [
            (self.first_eval_start_date.date(), self.first_eval_end_date.date(), self.tr("1st Evaluation")),
            (self.second_eval_start_date.date(), self.second_eval_end_date.date(), self.tr("2nd Evaluation")),
            (self.third_eval_start_date.date(), self.third_eval_end_date.date(), self.tr("3rd Evaluation"))
        ]
        self.season_timeline.set_evaluation_periods(evaluation_periods)

        # Set competitions
        competitions = []
        for row in range(self.competitions_table.rowCount()):
            try:
                start_date = QDate.fromString(self.competitions_table.item(row, 2).text(), "yyyy-MM-dd")
                end_date = QDate.fromString(self.competitions_table.item(row, 3).text(), "yyyy-MM-dd")
                name = self.competitions_table.item(row, 1).text()
                comp_type = self.competitions_table.item(row, 4).text()
                priority = self.competitions_table.item(row, 6).text()

                competitions.append((start_date, end_date, name, comp_type, priority))
            except (AttributeError, IndexError) as e:
                print(f"Error processing competition at row {row}: {e}")

        self.season_timeline.set_competitions(competitions)

        # Collect mesocycle data
        mesocycles = []

        # Add mesocycles based on the tables
        # For now, we'll just add placeholders for the four mesocycle types
        # In a real implementation, you would extract this data from your tables

        # Example mesocycle data (start_date, end_date, name, mesocycle_type)
        # These would be replaced with actual data from your application
        preparation_start = self.macrocycle_start_date.date()
        preparation_end = preparation_start.addDays(60)
        mesocycles.append((preparation_start, preparation_end, self.tr("Preparation"), "Preparation"))

        basic_start = preparation_end.addDays(1)
        basic_end = basic_start.addDays(90)
        mesocycles.append((basic_start, basic_end, self.tr("Basic"), "Basic"))

        competition_start = basic_end.addDays(1)
        competition_end = competition_start.addDays(150)
        mesocycles.append((competition_start, competition_end, self.tr("Competition"), "Competition"))

        transition_start = competition_end.addDays(1)
        transition_end = self.macrocycle_end_date.date()
        mesocycles.append((transition_start, transition_end, self.tr("Transition"), "Transition"))

        self.season_timeline.set_mesocycles(mesocycles)

        # Collect microcycle data
        microcycles = []

        # Debug info
        print(f"Updating timeline with microcycle data")
        print(f"Preparation microcycles: {self.microcycle_table.rowCount()}")
        print(f"Basic microcycles: {self.basic_microcycle_table.rowCount()}")
        print(f"Competition microcycles: {self.competition_microcycle_table.rowCount()}")
        print(f"Transition microcycles: {self.transition_microcycle_table.rowCount()}")

        # If no microcycles exist in tables, add some example data for visualization
        if (self.microcycle_table.rowCount() == 0 and
            self.basic_microcycle_table.rowCount() == 0 and
            self.competition_microcycle_table.rowCount() == 0 and
            self.transition_microcycle_table.rowCount() == 0):

            print("No microcycles found in tables, adding example data")

            # Add example microcycles for each mesocycle type
            # Preparation microcycle example
            prep_start = preparation_start.addDays(7)
            prep_end = prep_start.addDays(14)
            microcycles.append((prep_start, prep_end, "Prep 1", "Preparation", 7))

            # Basic microcycle example
            basic_start = basic_start.addDays(14)
            basic_end = basic_start.addDays(14)
            microcycles.append((basic_start, basic_end, "Basic 1", "Basic", 8))

            # Competition microcycle example
            comp_start = competition_start.addDays(21)
            comp_end = comp_start.addDays(14)
            microcycles.append((comp_start, comp_end, "Comp 1", "Competition", 9))

            # Transition microcycle example
            trans_start = transition_start.addDays(7)
            trans_end = trans_start.addDays(14)
            microcycles.append((trans_start, trans_end, "Trans 1", "Transition", 5))
        else:
            # Add microcycles from the preparation mesocycle table
            for row in range(self.microcycle_table.rowCount()):
                try:
                    start_date = QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
                    end_date = QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd")
                    name = self.microcycle_table.item(row, 3).text()

                    # Extract intensity from the intensity column (column 7)
                    intensity_text = self.microcycle_table.item(row, 7).text() if self.microcycle_table.item(row, 7) else "50%"
                    # Convert percentage to a value between 0-10
                    intensity = 5  # Default value
                    if "%" in intensity_text:
                        try:
                            intensity = int(intensity_text.replace("%", "")) // 10
                        except ValueError:
                            intensity = 5

                    print(f"Adding preparation microcycle: {name}, {start_date.toString('yyyy-MM-dd')} to {end_date.toString('yyyy-MM-dd')}")
                    microcycles.append((start_date, end_date, name, "Preparation", intensity))
                except (AttributeError, ValueError) as e:
                    # Skip invalid rows
                    print(f"Error processing microcycle row {row}: {e}")
                    continue

            # Add microcycles from the basic mesocycle table
            for row in range(self.basic_microcycle_table.rowCount()):
                try:
                    start_date = QDate.fromString(self.basic_microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
                    end_date = QDate.fromString(self.basic_microcycle_table.item(row, 2).text(), "yyyy-MM-dd")
                    name = self.basic_microcycle_table.item(row, 3).text()

                    # Extract intensity from the intensity column (column 7)
                    intensity_text = self.basic_microcycle_table.item(row, 7).text() if self.basic_microcycle_table.item(row, 7) else "50%"
                    # Convert percentage to a value between 0-10
                    intensity = 5  # Default value
                    if "%" in intensity_text:
                        try:
                            intensity = int(intensity_text.replace("%", "")) // 10
                        except ValueError:
                            intensity = 5

                    print(f"Adding basic microcycle: {name}, {start_date.toString('yyyy-MM-dd')} to {end_date.toString('yyyy-MM-dd')}")
                    microcycles.append((start_date, end_date, name, "Basic", intensity))
                except (AttributeError, ValueError) as e:
                    # Skip invalid rows
                    print(f"Error processing basic microcycle row {row}: {e}")
                    continue

            # Add microcycles from the competition mesocycle table
            for row in range(self.competition_microcycle_table.rowCount()):
                try:
                    start_date = QDate.fromString(self.competition_microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
                    end_date = QDate.fromString(self.competition_microcycle_table.item(row, 2).text(), "yyyy-MM-dd")
                    name = self.competition_microcycle_table.item(row, 3).text()

                    # Extract intensity from the intensity column (column 7)
                    intensity_text = self.competition_microcycle_table.item(row, 7).text() if self.competition_microcycle_table.item(row, 7) else "50%"
                    # Convert percentage to a value between 0-10
                    intensity = 5  # Default value
                    if "%" in intensity_text:
                        try:
                            intensity = int(intensity_text.replace("%", "")) // 10
                        except ValueError:
                            intensity = 5

                    print(f"Adding competition microcycle: {name}, {start_date.toString('yyyy-MM-dd')} to {end_date.toString('yyyy-MM-dd')}")
                    microcycles.append((start_date, end_date, name, "Competition", intensity))
                except (AttributeError, ValueError) as e:
                    # Skip invalid rows
                    print(f"Error processing competition microcycle row {row}: {e}")
                    continue

            # Add microcycles from the transition mesocycle table
            for row in range(self.transition_microcycle_table.rowCount()):
                try:
                    start_date = QDate.fromString(self.transition_microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
                    end_date = QDate.fromString(self.transition_microcycle_table.item(row, 2).text(), "yyyy-MM-dd")
                    name = self.transition_microcycle_table.item(row, 3).text()

                    # Extract intensity from the intensity column (column 7)
                    intensity_text = self.transition_microcycle_table.item(row, 7).text() if self.transition_microcycle_table.item(row, 7) else "50%"
                    # Convert percentage to a value between 0-10
                    intensity = 5  # Default value
                    if "%" in intensity_text:
                        try:
                            intensity = int(intensity_text.replace("%", "")) // 10
                        except ValueError:
                            intensity = 5

                    print(f"Adding transition microcycle: {name}, {start_date.toString('yyyy-MM-dd')} to {end_date.toString('yyyy-MM-dd')}")
                    microcycles.append((start_date, end_date, name, "Transition", intensity))
                except (AttributeError, ValueError) as e:
                    # Skip invalid rows
                    print(f"Error processing transition microcycle row {row}: {e}")
                    continue

        print(f"Total microcycles added to timeline: {len(microcycles)}")

        self.season_timeline.set_microcycles(microcycles)

        # Store the last valid dates for future reference
        self.last_valid_macrocycle_start = self.macrocycle_start_date.date()
        self.last_valid_macrocycle_end = self.macrocycle_end_date.date()

        # Print debug info
        print(f"Saved macrocycle dates: {self.macrocycle_start_date.date().toString('yyyy-MM-dd')} to {self.macrocycle_end_date.date().toString('yyyy-MM-dd')}")

        # Ensure changes are written
        QSettings().sync()

    def _save_zone_settings(self):
        """Saves the current zone configuration (self.zone_data) to QSettings."""
        print("Saving zone settings...")
        settings = QSettings()
        settings.beginGroup("nationality_zones")
        settings.setValue("groups", self.zone_data) # Store the entire dictionary
        settings.endGroup()
        settings.sync() # Ensure changes are written
        print(f"Saved {len(self.zone_data)} zones.")

    def _setup_connections(self):
        """Connect signals and slots for all tabs."""
        # Nationality Zones tab connections
        self.group_list.currentItemChanged.connect(self._on_group_selection_changed)
        self.group_list.itemSelectionChanged.connect(self._update_button_states)
        self.available_list.itemSelectionChanged.connect(self._update_button_states)
        self.assigned_list.itemSelectionChanged.connect(self._update_button_states)

        # Connect Group Management Buttons
        self.add_group_button.clicked.connect(self._add_group)
        self.remove_group_button.clicked.connect(self._remove_group)
        self.rename_group_button.clicked.connect(self._rename_group)

        # Connect Nationality Assignment Buttons
        self.add_nationality_button.clicked.connect(self._add_nationalities)
        self.remove_nationality_button.clicked.connect(self._remove_nationalities)

        # Dates tab connections
        self.season_start_date.dateChanged.connect(self._validate_season_dates)
        self.season_end_date.dateChanged.connect(self._validate_season_dates)

        # Connect macrocycle date changes directly to save method
        self.macrocycle_start_date.dateChanged.connect(self._on_macrocycle_date_changed)
        self.macrocycle_end_date.dateChanged.connect(self._on_macrocycle_date_changed)

        # Load the positions pitch images if they exist
        self._load_positions_pitch_image()
        self._load_positions_pitch_image2()
        self._load_positions_pitch_image3()

        # Restore the highlighted frame if previously selected
        settings = QSettings()
        selected_frame = settings.value("options/selected_pitch_frame", 0, type=int)
        if selected_frame in [1, 2, 3]:
            self._highlight_selected_frame(selected_frame)

        # Load settings after all UI elements and connections are set up
        self._load_settings()

    def _on_macrocycle_date_changed(self):
        """Handle changes to macrocycle dates."""
        print("Macrocycle date changed")
        # First validate the dates
        if self._validate_macrocycle_dates():
            # If valid, explicitly save the settings
            self._save_date_settings()

            # Print current values for debugging
            print(f"After saving - Macrocycle start: {self.macrocycle_start_date.date().toString('yyyy-MM-dd')}")
            print(f"After saving - Macrocycle end: {self.macrocycle_end_date.date().toString('yyyy-MM-dd')}")

            # Force sync to ensure settings are written immediately
            settings = QSettings()
            settings.sync()

    def _validate_and_save_macrocycle_dates(self):
        """Validate macrocycle dates and save if valid when the button is clicked."""
        print("Validate && Save macrocycle dates button clicked")

        # Get current dates
        macro_start = self.macrocycle_start_date.date()
        macro_end = self.macrocycle_end_date.date()
        season_start = self.season_start_date.date()
        season_end = self.season_end_date.date()

        # Perform comprehensive validation
        validation_messages = []

        # Check if end date is after start date
        if macro_end < macro_start:
            validation_messages.append(self.tr("• Macrocycle end date must be after start date"))

        # Check if dates are within season
        if macro_start < season_start:
            validation_messages.append(self.tr("• Macrocycle start date must be within season dates"))

        if macro_end > season_end:
            validation_messages.append(self.tr("• Macrocycle end date must be within season dates"))

        # Check for overlaps between microcycles across all mesocycles
        has_overlaps, overlap_message = self._check_all_microcycle_overlaps()
        if has_overlaps:
            validation_messages.append(self.tr("• {}").format(overlap_message))

        # Check if there are microcycles outside the macrocycle range
        affected_microcycles = []
        for row in range(self.microcycle_table.rowCount()):
            micro_name = self.microcycle_table.item(row, 3).text()
            micro_start = QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
            micro_end = QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd")

            if micro_start < macro_start or micro_end > macro_end:
                affected_microcycles.append(f"• {micro_name} ({micro_start.toString('yyyy-MM-dd')} to {micro_end.toString('yyyy-MM-dd')})")

        # If there are validation issues
        if validation_messages or affected_microcycles:
            message = self.tr("The following issues were found with the macrocycle dates:\n\n")

            if validation_messages:
                message += self.tr("Date Range Issues:\n")
                message += "\n".join(validation_messages)
                message += "\n\n"

            if affected_microcycles:
                message += self.tr("Microcycles Outside Macrocycle Range:\n")
                message += "\n".join(affected_microcycles)
                message += "\n\n"

            message += self.tr("Would you like to fix these issues automatically?")

            # Ask user if they want to fix issues
            result = QMessageBox.question(
                self,
                self.tr("Validation Issues"),
                message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if result == QMessageBox.StandardButton.Yes:
                # Fix issues automatically
                if self._fix_macrocycle_date_issues():
                    # Save after fixing
                    self._save_date_settings()

                    QMessageBox.information(
                        self,
                        self.tr("Issues Fixed"),
                        self.tr("The issues have been fixed and dates have been saved.")
                    )

            return False

        # If all validations pass
        # Save settings
        settings = QSettings()

        # Save macrocycle dates
        settings.beginGroup("periodization")
        settings.setValue("macrocycle_start", macro_start.toString(Qt.DateFormat.ISODate))
        settings.setValue("macrocycle_end", macro_end.toString(Qt.DateFormat.ISODate))
        settings.endGroup()

        # Force sync
        settings.sync()

        # Update last valid dates
        self.last_valid_macrocycle_start = macro_start
        self.last_valid_macrocycle_end = macro_end

        # Show confirmation
        QMessageBox.information(
            self,
            self.tr("Validation Successful"),
            self.tr("All dates are valid and have been saved successfully.")
        )

        # Print debug info
        print(f"Validated and saved macrocycle dates: {macro_start.toString('yyyy-MM-dd')} to {macro_end.toString('yyyy-MM-dd')}")

        return True

    def _fix_macrocycle_date_issues(self):
        """Fix macrocycle date issues automatically."""
        # Get current dates
        macro_start = self.macrocycle_start_date.date()
        macro_end = self.macrocycle_end_date.date()
        season_start = self.season_start_date.date()
        season_end = self.season_end_date.date()

        # Fix macrocycle dates to be within season
        if macro_start < season_start:
            macro_start = season_start
            self.macrocycle_start_date.setDate(macro_start)

        if macro_end > season_end:
            macro_end = season_end
            self.macrocycle_end_date.setDate(macro_end)

        # Ensure end date is after start date
        if macro_end < macro_start:
            macro_end = macro_start.addDays(30)  # Default to 30 days
            if macro_end > season_end:
                macro_end = season_end
            self.macrocycle_end_date.setDate(macro_end)

        # Fix microcycles outside the macrocycle range
        for row in range(self.microcycle_table.rowCount()):
            micro_start = QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
            micro_end = QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd")

            # Fix start date
            if micro_start < macro_start:
                self.microcycle_table.setItem(row, 1, QTableWidgetItem(macro_start.toString("yyyy-MM-dd")))

            # Fix end date
            if micro_end > macro_end:
                self.microcycle_table.setItem(row, 2, QTableWidgetItem(macro_end.toString("yyyy-MM-dd")))

            # Ensure microcycle end date is after start date
            new_micro_start = QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd")
            new_micro_end = QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd")

            if new_micro_end < new_micro_start:
                new_micro_end = new_micro_start.addDays(7)  # Default to 7 days
                if new_micro_end > macro_end:
                    new_micro_end = macro_end
                self.microcycle_table.setItem(row, 2, QTableWidgetItem(new_micro_end.toString("yyyy-MM-dd")))

        # Save microcycle data
        self._save_microcycle_data()

        return True

    def _on_group_selection_changed(self, current_item, _):
        """Updates the nationality lists when the selected group changes."""
        self.available_list.clear()
        self.assigned_list.clear()

        if current_item is None:
            print("Group selection cleared.")
            # Buttons will be disabled by _update_button_states called by signal
            return

        selected_group_name = current_item.text()
        print(f"Group selected: {selected_group_name}")

        assigned_nationalities = set(self.zone_data.get(selected_group_name, []))
        all_nationalities = set(self.all_countries)
        available_nationalities = all_nationalities - assigned_nationalities

        # Populate lists (sorted)
        self.assigned_list.addItems(sorted(list(assigned_nationalities)))
        self.available_list.addItems(sorted(list(available_nationalities)))

        # Buttons state updated via itemSelectionChanged signal connection
        # self._update_button_states() # Not strictly needed here if itemSelectionChanged connected

    def tr(self, text):
        """Translate string using Qt's translation system."""
        return QCoreApplication.translate("OptionsPage", text)

    def retranslateUi(self):
        """Updates all UI elements with translated text when language changes."""
        logger = logging.getLogger(__name__)
        logger.info("OptionsPage.retranslateUi called - UI updated for new language.")

        # Update football competitions table headers
        self._update_football_competitions_headers()

        # Window title
        self.setWindowTitle(self.tr("Options"))

        # Main tabs
        if hasattr(self, 'football_rules_tab') and self.tabs.indexOf(self.football_rules_tab) != -1:
            self.tabs.setTabText(self.tabs.indexOf(self.football_rules_tab), self.tr("Football Rules"))
        if hasattr(self, 'nationality_zones_tab') and self.tabs.indexOf(self.nationality_zones_tab) != -1:
            self.tabs.setTabText(self.tabs.indexOf(self.nationality_zones_tab), self.tr("Nationality Zones"))
        if hasattr(self, 'dates_tab') and self.tabs.indexOf(self.dates_tab) != -1:
            self.tabs.setTabText(self.tabs.indexOf(self.dates_tab), self.tr("Dates"))
        if hasattr(self, 'app_media_tab') and self.tabs.indexOf(self.app_media_tab) != -1:
            self.tabs.setTabText(self.tabs.indexOf(self.app_media_tab), self.tr("App Media"))
        if hasattr(self, 'football_competitions_tab') and self.tabs.indexOf(self.football_competitions_tab) != -1:
            self.tabs.setTabText(self.tabs.indexOf(self.football_competitions_tab), self.tr("Football Competitions"))

        # Dates Tab -> Subtabs
        if hasattr(self, 'season_tab') and self.dates_subtabs.indexOf(self.season_tab) != -1:
            self.dates_subtabs.setTabText(self.dates_subtabs.indexOf(self.season_tab), self.tr("Season"))
        if hasattr(self, 'periodization_tab') and self.dates_subtabs.indexOf(self.periodization_tab) != -1:
            self.dates_subtabs.setTabText(self.dates_subtabs.indexOf(self.periodization_tab), self.tr("Periodization"))
        if hasattr(self, 'evaluation_tab') and self.dates_subtabs.indexOf(self.evaluation_tab) != -1:
            self.dates_subtabs.setTabText(self.dates_subtabs.indexOf(self.evaluation_tab), self.tr("Evaluation"))

        # Season sub-tab
        if hasattr(self, 'season_group'):
            self.season_group.setTitle(self.tr("Season Dates"))
        if hasattr(self, 'season_start_date'):
            self.season_start_date.setToolTip(self.tr("Set the start date of the season"))
        if hasattr(self, 'season_end_date'):
            self.season_end_date.setToolTip(self.tr("Set the end date of the season"))
        if hasattr(self, 'start_date_label'):
            self.start_date_label.setText(self.tr("Start Date:"))
        if hasattr(self, 'end_date_label'):
            self.end_date_label.setText(self.tr("End Date:"))

        if hasattr(self, 'competitions_group'):
            self.competitions_group.setTitle(self.tr("Competitions"))
        if hasattr(self, 'competitions_table'):
            self.competitions_table.setHorizontalHeaderLabels([
                self.tr("ID"), self.tr("Competition"), self.tr("Start Date"), self.tr("End Date"),
                self.tr("Type"), self.tr("Structure"), self.tr("Priority"), self.tr("Notes")
            ])
        if hasattr(self, 'add_competition_button'):
            self.add_competition_button.setText(self.tr("Add Competition"))
        if hasattr(self, 'edit_competition_button'):
            self.edit_competition_button.setText(self.tr("Edit Competition"))
        if hasattr(self, 'remove_competition_button'):
            self.remove_competition_button.setText(self.tr("Remove Competition"))

        if hasattr(self, 'timeline_group'):
            self.timeline_group.setTitle(self.tr("Season Timeline"))

        # Periodization sub-tab
        if hasattr(self, 'periodization_group'):
            self.periodization_group.setTitle(self.tr("Periodization"))
        if hasattr(self, 'macrocycle_start_date'):
            self.macrocycle_start_date.setToolTip(self.tr("Set the start date of the Macrocycle"))
        if hasattr(self, 'macrocycle_end_date'):
            self.macrocycle_end_date.setToolTip(self.tr("Set the end date of the Macrocycle"))
        if hasattr(self, 'start_macrocycle_label'):
            self.start_macrocycle_label.setText(self.tr("Start Macrocycle:"))
        if hasattr(self, 'end_macrocycle_label'):
            self.end_macrocycle_label.setText(self.tr("End Macrocycle:"))

        if hasattr(self, 'validate_macrocycle_button'):
            self.validate_macrocycle_button.setText(self.tr("Validate && Save Dates"))
            self.validate_macrocycle_button.setToolTip(self.tr("Validate the macrocycle dates and save if valid"))
        if hasattr(self, 'periodization_chart_button'):
            self.periodization_chart_button.setText(self.tr("Periodization Chart"))
            self.periodization_chart_button.setToolTip(self.tr("Show a chart of intensity values across all periodization cycles"))

        if hasattr(self, 'preparation_mesocycle_group'):
            self.preparation_mesocycle_group.setTitle(self.tr("Preparation Mesocycle"))
        if hasattr(self, 'microcycle_table'):
            self.microcycle_table.setHorizontalHeaderLabels([
                self.tr("ID"), self.tr("Start Microcycle"), self.tr("End Microcycle"), self.tr("Name"),
                self.tr("Target 1"), self.tr("Target 2"), self.tr("Target 3"),
                self.tr("Intensity"), self.tr("Notes")
            ])
        if hasattr(self, 'add_microcycle_button'):
            self.add_microcycle_button.setText(self.tr("Add Microcycle"))
        if hasattr(self, 'edit_microcycle_button'):
            self.edit_microcycle_button.setText(self.tr("Edit Microcycle"))
        if hasattr(self, 'remove_microcycle_button'):
            self.remove_microcycle_button.setText(self.tr("Remove Microcycle"))

        if hasattr(self, 'basic_mesocycle_group'):
            self.basic_mesocycle_group.setTitle(self.tr("Basic Mesocycle"))
        if hasattr(self, 'basic_microcycle_table'):
            self.basic_microcycle_table.setHorizontalHeaderLabels([
                self.tr("ID"), self.tr("Start Microcycle"), self.tr("End Microcycle"), self.tr("Name"),
                self.tr("Target 1"), self.tr("Target 2"), self.tr("Target 3"),
                self.tr("Intensity"), self.tr("Notes")
            ])
        if hasattr(self, 'add_basic_microcycle_button'):
            self.add_basic_microcycle_button.setText(self.tr("Add Microcycle"))
        if hasattr(self, 'edit_basic_microcycle_button'):
            self.edit_basic_microcycle_button.setText(self.tr("Edit Microcycle"))
        if hasattr(self, 'remove_basic_microcycle_button'):
            self.remove_basic_microcycle_button.setText(self.tr("Remove Microcycle"))

        if hasattr(self, 'competition_mesocycle_group'):
            self.competition_mesocycle_group.setTitle(self.tr("Competition Mesocycle"))
        if hasattr(self, 'competition_microcycle_table'):
            self.competition_microcycle_table.setHorizontalHeaderLabels([
                self.tr("ID"), self.tr("Start Microcycle"), self.tr("End Microcycle"), self.tr("Name"),
                self.tr("Target 1"), self.tr("Target 2"), self.tr("Target 3"),
                self.tr("Intensity"), self.tr("Notes")
            ])
        if hasattr(self, 'add_competition_microcycle_button'):
            self.add_competition_microcycle_button.setText(self.tr("Add Microcycle"))
        if hasattr(self, 'edit_competition_microcycle_button'):
            self.edit_competition_microcycle_button.setText(self.tr("Edit Microcycle"))
        if hasattr(self, 'remove_competition_microcycle_button'):
            self.remove_competition_microcycle_button.setText(self.tr("Remove Microcycle"))

        if hasattr(self, 'transition_mesocycle_group'):
            self.transition_mesocycle_group.setTitle(self.tr("Transition Mesocycle"))
        if hasattr(self, 'transition_microcycle_table'):
            self.transition_microcycle_table.setHorizontalHeaderLabels([
                self.tr("ID"), self.tr("Start Microcycle"), self.tr("End Microcycle"), self.tr("Name"),
                self.tr("Target 1"), self.tr("Target 2"), self.tr("Target 3"),
                self.tr("Intensity"), self.tr("Notes")
            ])
        if hasattr(self, 'add_transition_microcycle_button'):
            self.add_transition_microcycle_button.setText(self.tr("Add Microcycle"))
        if hasattr(self, 'edit_transition_microcycle_button'):
            self.edit_transition_microcycle_button.setText(self.tr("Edit Microcycle"))
        if hasattr(self, 'remove_transition_microcycle_button'):
            self.remove_transition_microcycle_button.setText(self.tr("Remove Microcycle"))

        # Evaluation sub-tab
        if hasattr(self, 'evaluation_group'):
            self.evaluation_group.setTitle(self.tr("Evaluation Dates"))
        if hasattr(self, 'first_eval_group'):
            self.first_eval_group.setTitle(self.tr("1st Evaluation"))
        if hasattr(self, 'first_eval_start_date'):
            self.first_eval_start_date.setToolTip(self.tr("Set the start date of the 1st evaluation"))
        if hasattr(self, 'first_eval_end_date'):
            self.first_eval_end_date.setToolTip(self.tr("Set the end date of the 1st evaluation"))
        if hasattr(self, 'first_eval_start_label'):
            self.first_eval_start_label.setText(self.tr("Start 1st Evaluation:"))
        if hasattr(self, 'first_eval_end_label'):
            self.first_eval_end_label.setText(self.tr("End 1st Evaluation:"))

        if hasattr(self, 'second_eval_group'):
            self.second_eval_group.setTitle(self.tr("2nd Evaluation"))
        if hasattr(self, 'second_eval_start_date'):
            self.second_eval_start_date.setToolTip(self.tr("Set the start date of the 2nd evaluation"))
        if hasattr(self, 'second_eval_end_date'):
            self.second_eval_end_date.setToolTip(self.tr("Set the end date of the 2nd evaluation"))
        if hasattr(self, 'second_eval_start_label'):
            self.second_eval_start_label.setText(self.tr("Start 2nd Evaluation:"))
        if hasattr(self, 'second_eval_end_label'):
            self.second_eval_end_label.setText(self.tr("End 2nd Evaluation:"))

        if hasattr(self, 'third_eval_group'):
            self.third_eval_group.setTitle(self.tr("3rd Evaluation"))
        if hasattr(self, 'third_eval_start_date'):
            self.third_eval_start_date.setToolTip(self.tr("Set the start date of the 3rd evaluation"))
        if hasattr(self, 'third_eval_end_date'):
            self.third_eval_end_date.setToolTip(self.tr("Set the end date of the 3rd evaluation"))
        if hasattr(self, 'third_eval_start_label'):
            self.third_eval_start_label.setText(self.tr("Start 3rd Evaluation:"))
        if hasattr(self, 'third_eval_end_label'):
            self.third_eval_end_label.setText(self.tr("End 3rd Evaluation:"))

        # Nationality Zones Tab
        # Update the labels that were previously not instance members
        if hasattr(self, 'group_label'):
            self.group_label.setText(self.tr("Defined Zones:"))
        if hasattr(self, 'available_label'):
            self.available_label.setText(self.tr("Available Nationalities:"))
        if hasattr(self, 'assigned_label'):
            self.assigned_label.setText(self.tr("Assigned Nationalities:"))

        if hasattr(self, 'group_list'):
            self.group_list.setToolTip(self.tr("Select a zone to view/edit assigned nationalities."))
        if hasattr(self, 'add_group_button'):
            self.add_group_button.setText(self.tr("Add..."))
        if hasattr(self, 'remove_group_button'):
            self.remove_group_button.setText(self.tr("Remove"))
        if hasattr(self, 'rename_group_button'):
            self.rename_group_button.setText(self.tr("Rename..."))

        if hasattr(self, 'available_list'):
            self.available_list.setToolTip(self.tr("Nationalities not assigned to the selected zone."))
        if hasattr(self, 'assigned_list'):
            self.assigned_list.setToolTip(self.tr("Nationalities currently assigned to the selected zone."))
        if hasattr(self, 'add_nationality_button'):
            self.add_nationality_button.setText(self.tr("Add") + " >>")
            self.add_nationality_button.setToolTip(self.tr("Assign selected available nationality to the current zone"))
        if hasattr(self, 'remove_nationality_button'):
            self.remove_nationality_button.setText("<< " + self.tr("Remove")) # Keeps "<<" as non-translatable
            self.remove_nationality_button.setToolTip(self.tr("Remove selected assigned nationality from the current zone"))

        # App Media Tab
        if hasattr(self, 'football_pitch_group'):
            self.football_pitch_group.setTitle(self.tr("Football Pitch"))
        if hasattr(self, 'positions_pitch_group'):
            self.positions_pitch_group.setTitle(self.tr("Positions Pitch"))

        if hasattr(self, 'positions_pitch_frame') and (self.positions_pitch_frame.pixmap() is None or self.positions_pitch_frame.pixmap().isNull()):
            self.positions_pitch_frame.setText(self.tr("Click to upload image"))
        if hasattr(self, 'positions_pitch_frame2') and (self.positions_pitch_frame2.pixmap() is None or self.positions_pitch_frame2.pixmap().isNull()):
            self.positions_pitch_frame2.setText(self.tr("Click to upload image"))
        if hasattr(self, 'positions_pitch_frame3') and (self.positions_pitch_frame3.pixmap() is None or self.positions_pitch_frame3.pixmap().isNull()):
            self.positions_pitch_frame3.setText(self.tr("Click to upload image"))
        # Update the requirements label
        if hasattr(self, 'requirements_label'):
            self.requirements_label.setText(self.tr("Requirements: PNG format, < 200KB, max 400x600 pixels"))

            # Football Competitions Tab    if hasattr(self, 'football_competitions_table'):        self.football_competitions_table.setHorizontalHeaderLabels([            self.tr("ID"), self.tr("Logo"), self.tr("Name"), self.tr("Organization"),            self.tr("Type"), self.tr("Structure"), self.tr("Notes")        ])        # Refresh football competition types to use current language        self._refresh_football_competition_types()
        if hasattr(self, 'add_competition_btn'):
            self.add_competition_btn.setText(self.tr("Add Competition"))
        if hasattr(self, 'edit_competition_btn'):
            self.edit_competition_btn.setText(self.tr("Edit Competition"))
        if hasattr(self, 'remove_competition_btn'):
            self.remove_competition_btn.setText(self.tr("Remove Competition"))

        # Update competition table column headers
        if hasattr(self, 'competitions_table'):
            self.competitions_table.setHorizontalHeaderLabels([
                self.tr("ID"), self.tr("Competition"), self.tr("Start Date"), self.tr("End Date"),
                self.tr("Type"), self.tr("Structure"), self.tr("Priority"), self.tr("Notes")
            ])

            # Refresh the competition types to use current language
            self._refresh_competition_types()

    def _refresh_competition_types(self):
        """
        Refresh competition types column to use the current language.
        This method translates previously saved types (like 'Local', 'National', 'International')
        to use current language translations.
        """
        logger = logging.getLogger(__name__)
        logger.info("Refreshing competition types to use current language")

        # Define mappings for competition types in different languages
        competition_type_mappings = {
            # English options (source)
            "National": self.tr("National"),
            "International": self.tr("International"),
            "Local": self.tr("Local"),
            "Other": self.tr("Other"),

            # Greek translations (handle cases where it was saved in Greek)
            "Εθνικό": self.tr("National"),
            "Διεθνές": self.tr("International"),
            "Τοπικό": self.tr("Local"),
            "Άλλο": self.tr("Other"),

            # Chinese translations (handle cases where it was saved in Chinese)
            "国内": self.tr("National"),
            "国际": self.tr("International"),
            "地方": self.tr("Local"),
            "其他": self.tr("Other"),

            # Albanian translations (handle cases where it was saved in Albanian)
            "Kombëtar": self.tr("National"),
            "Ndërkombëtar": self.tr("International"),
            "Lokal": self.tr("Local")
        }

        # Do the same for competition structures
        competition_structure_mappings = {
            # English (source)
            "League": self.tr("League"),
            "League+playoffs/playouts": self.tr("League+playoffs/playouts"),
            "Group+Knockouts": self.tr("Group+Knockouts"),
            "Knockouts": self.tr("Knockouts"),
            "Tournament": self.tr("Tournament"),
            "Preparation matches": self.tr("Preparation matches"),
            "Charity": self.tr("Charity"),
            "Other": self.tr("Other"),

            # Greek
            "Πρωτάθλημα": self.tr("League"),
            "Πρωτάθλημα+πλέι-οφ/πλέι-άουτ": self.tr("League+playoffs/playouts"),
            "Όμιλοι+Νοκ-άουτ": self.tr("Group+Knockouts"),
            "Νοκ-άουτ": self.tr("Knockouts"),
            "Τουρνουά": self.tr("Tournament"),
            "Φιλικοί αγώνες": self.tr("Preparation matches"),
            "Φιλανθρωπικός": self.tr("Charity"),
            "Άλλο": self.tr("Other")
        }

        # Update the competition types in the table
        for row in range(self.competitions_table.rowCount()):
            # Get the current type text
            current_type_text = self.competitions_table.item(row, 4).text()
            current_structure_text = self.competitions_table.item(row, 5).text()

            # Translate to current language if mapping exists
            if current_type_text in competition_type_mappings:
                translated_type = competition_type_mappings[current_type_text]
                self.competitions_table.item(row, 4).setText(translated_type)
                logger.debug(f"Updated competition type from '{current_type_text}' to '{translated_type}'")

            # Update structure too
            if current_structure_text in competition_structure_mappings:
                translated_structure = competition_structure_mappings[current_structure_text]
                self.competitions_table.item(row, 5).setText(translated_structure)
                logger.debug(f"Updated competition structure from '{current_structure_text}' to '{translated_structure}'")

    def changeEvent(self, event):
        """Handle change events, including language changes."""
        if event.type() == QEvent.Type.LanguageChange:
            logger = logging.getLogger(__name__)
            logger.info("LanguageChange event received in OptionsPage, calling retranslateUi.")
            self.retranslateUi()
            # Notify any listeners (such as the MainWindow) that the language has changed
            self.language_changed.emit()
        elif event.type() == QEvent.Type.PaletteChange:
            logger = logging.getLogger(__name__)
            logger.info("PaletteChange event received in OptionsPage, doing nothing specific yet.")

        super().changeEvent(event) # Call base class implementation

    def _on_positions_pitch_clicked(self, event):
        """Handle click on the positions pitch frame."""
        if event.button() == Qt.MouseButton.LeftButton:
            self._upload_positions_pitch_image()
        # Let the default handler process right-clicks for context menu
        super(OptionsPage, self.positions_pitch_frame.__class__).mousePressEvent(self.positions_pitch_frame, event)

    def _on_positions_pitch2_clicked(self, event):
        """Handle click on the second positions pitch frame."""
        if event.button() == Qt.MouseButton.LeftButton:
            self._upload_positions_pitch_image2()
        # Let the default handler process right-clicks for context menu
        super(OptionsPage, self.positions_pitch_frame2.__class__).mousePressEvent(self.positions_pitch_frame2, event)

    def _on_positions_pitch3_clicked(self, event):
        """Handle click on the third positions pitch frame."""
        if event.button() == Qt.MouseButton.LeftButton:
            self._upload_positions_pitch_image3()
        # Let the default handler process right-clicks for context menu
        super(OptionsPage, self.positions_pitch_frame3.__class__).mousePressEvent(self.positions_pitch_frame3, event)

    def _show_positions_pitch_context_menu(self, position):
        """Show context menu for the positions pitch frame."""
        context_menu = QMenu(self)
        upload_action = context_menu.addAction(self.tr("Upload Image"))
        remove_action = context_menu.addAction(self.tr("Remove Image"))
        add_to_position_action = context_menu.addAction(self.tr("Add to Position"))

        # Disable actions if no image is set
        has_image = not self.positions_pitch_frame.pixmap().isNull() if self.positions_pitch_frame.pixmap() else False
        remove_action.setEnabled(has_image)
        add_to_position_action.setEnabled(has_image)

        # Show the menu and get the selected action
        action = context_menu.exec(self.positions_pitch_frame.mapToGlobal(position))

        if action == upload_action:
            self._upload_positions_pitch_image()
        elif action == remove_action:
            self._remove_positions_pitch_image()
        elif action == add_to_position_action:
            self._add_image_to_position(1)

    def _show_positions_pitch2_context_menu(self, position):
        """Show context menu for the second positions pitch frame."""
        context_menu = QMenu(self)
        upload_action = context_menu.addAction(self.tr("Upload Image"))
        remove_action = context_menu.addAction(self.tr("Remove Image"))
        add_to_position_action = context_menu.addAction(self.tr("Add to Position"))

        # Disable actions if no image is set
        has_image = not self.positions_pitch_frame2.pixmap().isNull() if self.positions_pitch_frame2.pixmap() else False
        remove_action.setEnabled(has_image)
        add_to_position_action.setEnabled(has_image)

        # Show the menu and get the selected action
        action = context_menu.exec(self.positions_pitch_frame2.mapToGlobal(position))

        if action == upload_action:
            self._upload_positions_pitch_image2()
        elif action == remove_action:
            self._remove_positions_pitch_image2()
        elif action == add_to_position_action:
            self._add_image_to_position(2)

    def _show_positions_pitch3_context_menu(self, position):
        """Show context menu for the third positions pitch frame."""
        context_menu = QMenu(self)
        upload_action = context_menu.addAction(self.tr("Upload Image"))
        remove_action = context_menu.addAction(self.tr("Remove Image"))
        add_to_position_action = context_menu.addAction(self.tr("Add to Position"))

        # Disable actions if no image is set
        has_image = not self.positions_pitch_frame3.pixmap().isNull() if self.positions_pitch_frame3.pixmap() else False
        remove_action.setEnabled(has_image)
        add_to_position_action.setEnabled(has_image)

        # Show the menu and get the selected action
        action = context_menu.exec(self.positions_pitch_frame3.mapToGlobal(position))

        if action == upload_action:
            self._upload_positions_pitch_image3()
        elif action == remove_action:
            self._remove_positions_pitch_image3()
        elif action == add_to_position_action:
            self._add_image_to_position(3)

    def _init_football_competitions_tab(self):
        """Initialize the Football Competitions tab."""
        layout = QVBoxLayout(self.football_competitions_tab)

        # Create competitions table
        self.football_competitions_table = QTableWidget()
        self.football_competitions_table.setColumnCount(7)
        # Set column headers (these will be translated in retranslateUi)
        self._update_football_competitions_headers()

        # Create buttons
        button_layout = QHBoxLayout()

        self.add_competition_btn = QPushButton(self.tr("Add Competition"))
        self.edit_competition_btn = QPushButton(self.tr("Edit Competition"))
        self.remove_competition_btn = QPushButton(self.tr("Remove Competition"))

        button_layout.addWidget(self.add_competition_btn)
        button_layout.addWidget(self.edit_competition_btn)
        button_layout.addWidget(self.remove_competition_btn)
        button_layout.addStretch()

        # Add widgets to layout
        layout.addWidget(self.football_competitions_table)
        layout.addLayout(button_layout)

        # Connect signals
        self.add_competition_btn.clicked.connect(self._add_football_competition)
        self.edit_competition_btn.clicked.connect(self._edit_football_competition)
        self.remove_competition_btn.clicked.connect(self._remove_football_competition)
        self.football_competitions_table.doubleClicked.connect(self._edit_football_competition)

        # Load data
        self._load_football_competitions_data()

    def _update_football_competitions_headers(self):
        """Update football competitions table headers with translated text."""
        if hasattr(self, 'football_competitions_table'):
            self.football_competitions_table.setHorizontalHeaderLabels([
                self.tr("ID"),
                self.tr("Logo"),
                self.tr("Name"),
                self.tr("Organization"),
                self.tr("Type"),
                self.tr("Structure"),
                self.tr("Notes")
            ])

            # Set column widths
            self.football_competitions_table.setColumnWidth(0, 40)   # ID
            self.football_competitions_table.setColumnWidth(1, 60)   # Logo
            self.football_competitions_table.setColumnWidth(2, 150)  # Name
            self.football_competitions_table.setColumnWidth(3, 150)  # Organization
            self.football_competitions_table.setColumnWidth(4, 100)  # Type
            self.football_competitions_table.setColumnWidth(5, 150)  # Structure
            self.football_competitions_table.setColumnWidth(6, 200)  # Notes

            # Set table properties
            self.football_competitions_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
            self.football_competitions_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
            self.football_competitions_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
            self.football_competitions_table.verticalHeader().setVisible(False)

            # Set row height to accommodate logos
            self.football_competitions_table.verticalHeader().setDefaultSectionSize(50)

            # Allow horizontal header resizing but not sorting
            header = self.football_competitions_table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
            header.setSortIndicatorShown(False)

    def _upload_positions_pitch_image(self):
        """Upload an image for the positions pitch."""
        # Create media/pitch directory if it doesn't exist
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        media_dir.mkdir(parents=True, exist_ok=True)

        # Open file dialog to select an image
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.tr("Select Positions Pitch Image"),
            "",
            self.tr("PNG Images (*.png)")
        )

        if not file_path:
            return  # User cancelled

        # Validate file type
        if not file_path.lower().endswith('.png'):
            QMessageBox.warning(
                self,
                self.tr("Invalid File Type"),
                self.tr("Only PNG files are allowed.")
            )
            return

        # Validate file size
        file_info = QFileInfo(file_path)
        file_size_kb = file_info.size() / 1024
        if file_size_kb > 200:
            QMessageBox.warning(
                self,
                self.tr("File Too Large"),
                self.tr("The image file must be less than 200KB. Current size: {:.1f}KB").format(file_size_kb)
            )
            return

        # Validate image dimensions
        image = QImage(file_path)
        if image.width() > 400 or image.height() > 600:
            QMessageBox.warning(
                self,
                self.tr("Image Too Large"),
                self.tr("The image dimensions must be at most 400x600 pixels. Current size: {}x{}").format(
                    image.width(), image.height()
                )
            )
            return

        # Copy the file to the media/pitch directory
        target_path = media_dir / "position_pitch_1.png"
        try:
            # Read source file
            with open(file_path, 'rb') as src_file:
                content = src_file.read()

            # Write to target file
            with open(target_path, 'wb') as dst_file:
                dst_file.write(content)

            # Display the image in the frame
            pixmap = QPixmap(str(target_path))
            self.positions_pitch_frame.setPixmap(pixmap.scaled(
                self.positions_pitch_frame.width(),
                self.positions_pitch_frame.height(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))
            self.positions_pitch_frame.setText("")  # Clear text when image is set

            QMessageBox.information(
                self,
                self.tr("Image Uploaded"),
                self.tr("The positions pitch image has been uploaded successfully.")
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Upload Error"),
                self.tr("An error occurred while uploading the image: {}").format(str(e))
            )

    def _remove_positions_pitch_image(self):
        """Remove the positions pitch image."""
        # Check if there's an image to remove
        if self.positions_pitch_frame.pixmap() is None or self.positions_pitch_frame.pixmap().isNull():
            return

        # Confirm with the user
        reply = QMessageBox.question(
            self,
            self.tr("Remove Image"),
            self.tr("Are you sure you want to remove the positions pitch image?"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Remove the image file
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        image_path = media_dir / "position_pitch_1.png"

        try:
            if image_path.exists():
                os.remove(image_path)

            # Clear the image from the frame
            self.positions_pitch_frame.clear()
            self.positions_pitch_frame.setText(self.tr("Click to upload image"))

            QMessageBox.information(
                self,
                self.tr("Image Removed"),
                self.tr("The positions pitch image has been removed successfully.")
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Remove Error"),
                self.tr("An error occurred while removing the image: {}").format(str(e))
            )

    def _upload_positions_pitch_image2(self):
        """Upload an image for the second positions pitch."""
        # Create media/pitch directory if it doesn't exist
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        media_dir.mkdir(parents=True, exist_ok=True)

        # Open file dialog to select an image
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.tr("Select Positions Pitch Image"),
            "",
            self.tr("PNG Images (*.png)")
        )

        if not file_path:
            return  # User cancelled

        # Validate file type
        if not file_path.lower().endswith('.png'):
            QMessageBox.warning(
                self,
                self.tr("Invalid File Type"),
                self.tr("Only PNG files are allowed.")
            )
            return

        # Validate file size
        file_info = QFileInfo(file_path)
        file_size_kb = file_info.size() / 1024
        if file_size_kb > 200:
            QMessageBox.warning(
                self,
                self.tr("File Too Large"),
                self.tr("The image file must be less than 200KB. Current size: {:.1f}KB").format(file_size_kb)
            )
            return

        # Validate image dimensions
        image = QImage(file_path)
        if image.width() > 400 or image.height() > 600:
            QMessageBox.warning(
                self,
                self.tr("Image Too Large"),
                self.tr("The image dimensions must be at most 400x600 pixels. Current size: {}x{}").format(
                    image.width(), image.height()
                )
            )
            return

        # Copy the file to the media/pitch directory
        target_path = media_dir / "position_pitch_2.png"
        try:
            # Read source file
            with open(file_path, 'rb') as src_file:
                content = src_file.read()

            # Write to target file
            with open(target_path, 'wb') as dst_file:
                dst_file.write(content)

            # Display the image in the frame
            pixmap = QPixmap(str(target_path))
            self.positions_pitch_frame2.setPixmap(pixmap.scaled(
                self.positions_pitch_frame2.width(),
                self.positions_pitch_frame2.height(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))
            self.positions_pitch_frame2.setText("")  # Clear text when image is set

            QMessageBox.information(
                self,
                self.tr("Image Uploaded"),
                self.tr("The positions pitch image has been uploaded successfully.")
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Upload Error"),
                self.tr("An error occurred while uploading the image: {}").format(str(e))
            )

    def _remove_positions_pitch_image2(self):
        """Remove the second positions pitch image."""
        # Check if there's an image to remove
        if self.positions_pitch_frame2.pixmap() is None or self.positions_pitch_frame2.pixmap().isNull():
            return

        # Confirm with the user
        reply = QMessageBox.question(
            self,
            self.tr("Remove Image"),
            self.tr("Are you sure you want to remove the positions pitch image?"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Remove the image file
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        image_path = media_dir / "position_pitch_2.png"

        try:
            if image_path.exists():
                os.remove(image_path)

            # Clear the image from the frame
            self.positions_pitch_frame2.clear()
            self.positions_pitch_frame2.setText(self.tr("Click to upload image"))

            QMessageBox.information(
                self,
                self.tr("Image Removed"),
                self.tr("The positions pitch image has been removed successfully.")
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Remove Error"),
                self.tr("An error occurred while removing the image: {}").format(str(e))
            )

    def _upload_positions_pitch_image3(self):
        """Upload an image for the third positions pitch."""
        # Create media/pitch directory if it doesn't exist
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        media_dir.mkdir(parents=True, exist_ok=True)

        # Open file dialog to select an image
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.tr("Select Positions Pitch Image"),
            "",
            self.tr("PNG Images (*.png)")
        )

        if not file_path:
            return  # User cancelled

        # Validate file type
        if not file_path.lower().endswith('.png'):
            QMessageBox.warning(
                self,
                self.tr("Invalid File Type"),
                self.tr("Only PNG files are allowed.")
            )
            return

        # Validate file size
        file_info = QFileInfo(file_path)
        file_size_kb = file_info.size() / 1024
        if file_size_kb > 200:
            QMessageBox.warning(
                self,
                self.tr("File Too Large"),
                self.tr("The image file must be less than 200KB. Current size: {:.1f}KB").format(file_size_kb)
            )
            return

        # Validate image dimensions
        image = QImage(file_path)
        if image.width() > 400 or image.height() > 600:
            QMessageBox.warning(
                self,
                self.tr("Image Too Large"),
                self.tr("The image dimensions must be at most 400x600 pixels. Current size: {}x{}").format(
                    image.width(), image.height()
                )
            )
            return

        # Copy the file to the media/pitch directory
        target_path = media_dir / "position_pitch_3.png"
        try:
            # Read source file
            with open(file_path, 'rb') as src_file:
                content = src_file.read()

            # Write to target file
            with open(target_path, 'wb') as dst_file:
                dst_file.write(content)

            # Display the image in the frame
            pixmap = QPixmap(str(target_path))
            self.positions_pitch_frame3.setPixmap(pixmap.scaled(
                self.positions_pitch_frame3.width(),
                self.positions_pitch_frame3.height(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))
            self.positions_pitch_frame3.setText("")  # Clear text when image is set

            QMessageBox.information(
                self,
                self.tr("Image Uploaded"),
                self.tr("The positions pitch image has been uploaded successfully.")
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Upload Error"),
                self.tr("An error occurred while uploading the image: {}").format(str(e))
            )

    def _remove_positions_pitch_image3(self):
        """Remove the third positions pitch image."""
        # Check if there's an image to remove
        if self.positions_pitch_frame3.pixmap() is None or self.positions_pitch_frame3.pixmap().isNull():
            return

        # Confirm with the user
        reply = QMessageBox.question(
            self,
            self.tr("Remove Image"),
            self.tr("Are you sure you want to remove the positions pitch image?"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # Remove the image file
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        image_path = media_dir / "position_pitch_3.png"

        try:
            if image_path.exists():
                os.remove(image_path)

            # Clear the image from the frame
            self.positions_pitch_frame3.clear()
            self.positions_pitch_frame3.setText(self.tr("Click to upload image"))

            QMessageBox.information(
                self,
                self.tr("Image Removed"),
                self.tr("The positions pitch image has been removed successfully.")
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Remove Error"),
                self.tr("An error occurred while removing the image: {}").format(str(e))
            )

    def _load_positions_pitch_image(self):
        """Load the positions pitch image if it exists."""
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        image_path = media_dir / "position_pitch_1.png"

        if image_path.exists():
            pixmap = QPixmap(str(image_path))
            if not pixmap.isNull():
                self.positions_pitch_frame.setPixmap(pixmap.scaled(
                    self.positions_pitch_frame.width(),
                    self.positions_pitch_frame.height(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                ))
                self.positions_pitch_frame.setText("")  # Clear text when image is set

    def _load_positions_pitch_image2(self):
        """Load the second positions pitch image if it exists."""
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        image_path = media_dir / "position_pitch_2.png"

        if image_path.exists():
            pixmap = QPixmap(str(image_path))
            if not pixmap.isNull():
                self.positions_pitch_frame2.setPixmap(pixmap.scaled(
                    self.positions_pitch_frame2.width(),
                    self.positions_pitch_frame2.height(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                ))
                self.positions_pitch_frame2.setText("")  # Clear text when image is set

    def _load_positions_pitch_image3(self):
        """Load the third positions pitch image if it exists."""
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        image_path = media_dir / "position_pitch_3.png"

        if image_path.exists():
            pixmap = QPixmap(str(image_path))
            if not pixmap.isNull():
                self.positions_pitch_frame3.setPixmap(pixmap.scaled(
                    self.positions_pitch_frame3.width(),
                    self.positions_pitch_frame3.height(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                ))
                self.positions_pitch_frame3.setText("")  # Clear text when image is set

    def _highlight_selected_frame(self, selected_number):
        """Highlight the selected frame with a colored border and reset others.

        Args:
            selected_number: The number of the selected frame (1, 2, or 3)
        """
        # Define styles
        default_style = "background-color: #f0f0f0; border: 1px solid #cccccc;"
        selected_style = "background-color: #f0f0f0; border: 6px solid #FF8C00;" # Orange border (thicker) for selected frame

        # Reset all frames to default style
        self.positions_pitch_frame.setStyleSheet(default_style)
        self.positions_pitch_frame2.setStyleSheet(default_style)
        self.positions_pitch_frame3.setStyleSheet(default_style)

        # Highlight the selected frame
        if selected_number == 1:
            self.positions_pitch_frame.setStyleSheet(selected_style)
        elif selected_number == 2:
            self.positions_pitch_frame2.setStyleSheet(selected_style)
        elif selected_number == 3:
            self.positions_pitch_frame3.setStyleSheet(selected_style)

        # Save the selected frame number to QSettings
        settings = QSettings()
        settings.setValue("options/selected_pitch_frame", selected_number)

    def _add_image_to_position(self, image_number):
        """Add the selected image to the Position tab in the Roster page.

        Args:
            image_number: The number of the image to add (1, 2, or 3)
        """
        # Get the path to the source image
        media_dir = pathlib.Path(os.path.dirname(os.path.abspath(sys.argv[0]))) / "media" / "pitch"
        source_path = media_dir / f"position_pitch_{image_number}.png"

        if not source_path.exists():
            QMessageBox.warning(
                self,
                self.tr("Image Not Found"),
                self.tr("The selected image could not be found.")
            )
            return

        # Create a special file for the Roster Position tab
        target_path = media_dir / "roster_position_pitch.png"

        try:
            # Copy the file
            shutil.copy2(source_path, target_path)

            # Highlight the selected frame
            self._highlight_selected_frame(image_number)

            QMessageBox.information(
                self,
                self.tr("Image Added"),
                self.tr("The image has been added to the Position tab in the Roster page.")
            )

            # Notify any open Roster pages to update their Position tab
            # This is done by setting a special QSettings value that the Roster page can check
            settings = QSettings()
            settings.setValue("roster/position_pitch_updated", True)

        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Error"),
                self.tr("An error occurred while adding the image to the Position tab: {}").format(str(e))
            )

    def closeEvent(self, event):
        """Handle closing the window (e.g., save settings)."""
        # Save all settings when closing the window
        self._save_zone_settings()
        self._save_date_settings()

        # Check for overlaps before saving microcycle data
        has_overlaps, overlap_message = self._check_all_microcycle_overlaps()
        if has_overlaps:
            # Show a warning and ask if the user wants to close without saving
            from PySide6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                self.tr("Microcycle Overlap Warning"),
                self.tr("There are overlapping microcycles in your schedule:\n\n{}\n\nDo you want to close without saving the microcycle data?").format(overlap_message),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.No:
                # User chose not to close, abort the close event
                event.ignore()
                return
            # Otherwise, continue closing without saving microcycle data
        else:
            # No overlaps, save all microcycle data
            self._save_microcycle_data()
            self._save_basic_microcycle_data()
            self._save_competition_microcycle_data()
            self._save_transition_microcycle_data()

        # Validate dates one last time before closing
        self._validate_season_dates()
        self._validate_macrocycle_dates()

        super().closeEvent(event)

    def _add_microcycle(self):
        """Opens a dialog to add a new microcycle."""
        # Create a dialog for adding a new microcycle
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Add Microcycle"))
        dialog.setMinimumWidth(400)

        # Create layout
        layout = QVBoxLayout(dialog)
        form_layout = QFormLayout()

        # Create form fields
        name_input = QLineEdit()

        # Determine the default start date based on existing microcycles
        default_start_date = self.macrocycle_start_date.date()

        # If there are existing microcycles, use the end date of the last one as the default start date
        row_count = self.microcycle_table.rowCount()
        if row_count > 0:
            last_end_date_str = self.microcycle_table.item(row_count - 1, 2).text()
            last_end_date = QDate.fromString(last_end_date_str, "yyyy-MM-dd")

            # Use the day after the last end date as the default start date
            if last_end_date.isValid():
                default_start_date = last_end_date.addDays(1)

        start_date = QDateEdit(calendarPopup=True)
        start_date.setDisplayFormat("yyyy-MM-dd")
        start_date.setDate(default_start_date)

        # Set default end date to 7 days after the start date
        end_date = QDateEdit(calendarPopup=True)
        end_date.setDisplayFormat("yyyy-MM-dd")
        end_date.setDate(default_start_date.addDays(7))

        # Create target combo boxes with the same options
        target_options = ["Physical", "Technical", "Tactical", "Theory", "Other", ""]  # Added empty option

        target1_combo = QComboBox()
        target1_combo.addItems([self.tr(option) for option in target_options])

        target2_combo = QComboBox()
        target2_combo.addItems([self.tr(option) for option in target_options])
        target2_combo.setCurrentIndex(target2_combo.findText(""))  # Default to empty

        target3_combo = QComboBox()
        target3_combo.addItems([self.tr(option) for option in target_options])
        target3_combo.setCurrentIndex(target3_combo.findText(""))  # Default to empty

        # Create intensity combo box with values from 0% to 100% in 5% increments
        intensity_combo = QComboBox()
        intensity_values = [f"{i}%" for i in range(0, 105, 5)]  # 0%, 5%, 10%, ..., 100%
        intensity_combo.addItems(intensity_values)
        intensity_combo.setCurrentIndex(10)  # Default to 50%

        notes_input = QTextEdit()
        notes_input.setMaximumHeight(100)

        # Add fields to form layout
        form_layout.addRow(self.tr("Name:"), name_input)
        form_layout.addRow(self.tr("Start Date:"), start_date)
        form_layout.addRow(self.tr("End Date:"), end_date)
        form_layout.addRow(self.tr("Target 1:"), target1_combo)
        form_layout.addRow(self.tr("Target 2:"), target2_combo)
        form_layout.addRow(self.tr("Target 3:"), target3_combo)
        form_layout.addRow(self.tr("Intensity:"), intensity_combo)
        form_layout.addRow(self.tr("Notes:"), notes_input)

        # Add validation label
        validation_label = QLabel()
        validation_label.setStyleSheet("color: red;")
        validation_label.setVisible(False)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)

        # Add layouts to main layout
        layout.addLayout(form_layout)
        layout.addWidget(validation_label)
        layout.addWidget(button_box)

        # Function to validate dates
        def validate_dates():
            # Get macrocycle dates
            macrocycle_start = self.macrocycle_start_date.date()
            macrocycle_end = self.macrocycle_end_date.date()

            # Get microcycle dates
            micro_start = start_date.date()
            micro_end = end_date.date()

            # Check if end date is after start date
            if micro_end < micro_start:
                validation_label.setText(self.tr("End date must be after start date"))
                validation_label.setVisible(True)
                return False

            # Check if dates are within macrocycle dates
            if micro_start < macrocycle_start or micro_start > macrocycle_end:
                validation_label.setText(self.tr("Start date must be within macrocycle dates"))
                validation_label.setVisible(True)
                return False

            if micro_end < macrocycle_start or micro_end > macrocycle_end:
                validation_label.setText(self.tr("End date must be within macrocycle dates"))
                validation_label.setVisible(True)
                return False

            # Check for overlapping dates with existing microcycles in this mesocycle
            row_count = self.microcycle_table.rowCount()
            if row_count > 0:
                for i in range(row_count):
                    existing_start = QDate.fromString(self.microcycle_table.item(i, 1).text(), "yyyy-MM-dd")
                    existing_end = QDate.fromString(self.microcycle_table.item(i, 2).text(), "yyyy-MM-dd")

                    # Check if the new microcycle overlaps with an existing one
                    if (micro_start <= existing_end and micro_end >= existing_start):
                        validation_label.setText(self.tr("Microcycle dates cannot overlap with existing microcycles in this mesocycle"))
                        validation_label.setVisible(True)
                        return False

            # Check for overlapping dates with microcycles in other mesocycles
            has_overlap, overlap_message = self._check_microcycle_overlap(micro_start, micro_end)
            if has_overlap:
                validation_label.setText(self.tr("Microcycle dates cannot overlap with existing microcycles: {}").format(overlap_message))
                validation_label.setVisible(True)
                return False

            # If all validations pass
            validation_label.setVisible(False)
            return True

        # Connect date changes to validation
        start_date.dateChanged.connect(validate_dates)
        end_date.dateChanged.connect(validate_dates)

        # Initial validation
        validate_dates()

        # Execute dialog
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Validate one more time before accepting
            if not validate_dates():
                return

            # Get values from form
            name = name_input.text()
            start = start_date.date().toString("yyyy-MM-dd")
            end = end_date.date().toString("yyyy-MM-dd")
            target1 = target1_combo.currentText()
            target2 = target2_combo.currentText()
            target3 = target3_combo.currentText()
            intensity = intensity_combo.currentText()
            notes = notes_input.toPlainText()

            # Add new row to table
            row = self.microcycle_table.rowCount()
            self.microcycle_table.insertRow(row)

            # Set ID (row number + 1)
            id_item = QTableWidgetItem(str(row + 1))
            id_item.setFlags(id_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Make ID non-editable
            self.microcycle_table.setItem(row, 0, id_item)

            # Set other values
            self.microcycle_table.setItem(row, 1, QTableWidgetItem(start))
            self.microcycle_table.setItem(row, 2, QTableWidgetItem(end))
            self.microcycle_table.setItem(row, 3, QTableWidgetItem(name))
            self.microcycle_table.setItem(row, 4, QTableWidgetItem(target1))
            self.microcycle_table.setItem(row, 5, QTableWidgetItem(target2))
            self.microcycle_table.setItem(row, 6, QTableWidgetItem(target3))
            self.microcycle_table.setItem(row, 7, QTableWidgetItem(intensity))
            self.microcycle_table.setItem(row, 8, QTableWidgetItem(notes))

            # Save microcycle data
            self._save_microcycle_data()

    def _edit_microcycle(self):
        """Opens a dialog to edit the selected microcycle."""
        # Get selected row
        selected_rows = self.microcycle_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, self.tr("No Selection"),
                               self.tr("Please select a microcycle to edit."))
            return

        # Get row index from any selected item
        row = selected_rows[0].row()

        # Create a dialog for editing the microcycle
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Edit Microcycle"))
        dialog.setMinimumWidth(400)

        # Create layout
        layout = QVBoxLayout(dialog)
        form_layout = QFormLayout()

        # Create form fields and populate with current values
        name_input = QLineEdit(self.microcycle_table.item(row, 3).text())

        start_date = QDateEdit(calendarPopup=True)
        start_date.setDisplayFormat("yyyy-MM-dd")
        start_date.setDate(QDate.fromString(self.microcycle_table.item(row, 1).text(), "yyyy-MM-dd"))

        end_date = QDateEdit(calendarPopup=True)
        end_date.setDisplayFormat("yyyy-MM-dd")
        end_date.setDate(QDate.fromString(self.microcycle_table.item(row, 2).text(), "yyyy-MM-dd"))

        # Create target combo boxes with the same options
        target_options = ["Physical", "Technical", "Tactical", "Theory", "Other", ""]  # Added empty option

        target1_combo = QComboBox()
        target1_combo.addItems([self.tr(option) for option in target_options])
        current_target1 = self.microcycle_table.item(row, 4).text()
        target1_combo.setCurrentText(current_target1)

        target2_combo = QComboBox()
        target2_combo.addItems([self.tr(option) for option in target_options])
        current_target2 = self.microcycle_table.item(row, 5).text()
        target2_combo.setCurrentText(current_target2)

        target3_combo = QComboBox()
        target3_combo.addItems([self.tr(option) for option in target_options])
        current_target3 = self.microcycle_table.item(row, 6).text()
        target3_combo.setCurrentText(current_target3)

        # Create intensity combo box with values from 0% to 100% in 5% increments
        intensity_combo = QComboBox()
        intensity_values = [f"{i}%" for i in range(0, 105, 5)]  # 0%, 5%, 10%, ..., 100%
        intensity_combo.addItems(intensity_values)
        current_intensity = self.microcycle_table.item(row, 7).text()
        # Set to current value or default to 50% if not found
        intensity_index = intensity_combo.findText(current_intensity)
        if intensity_index >= 0:
            intensity_combo.setCurrentIndex(intensity_index)
        else:
            intensity_combo.setCurrentIndex(10)  # Default to 50%

        notes_input = QTextEdit()
        notes_input.setPlainText(self.microcycle_table.item(row, 8).text())
        notes_input.setMaximumHeight(100)

        # Add fields to form layout
        form_layout.addRow(self.tr("Name:"), name_input)
        form_layout.addRow(self.tr("Start Date:"), start_date)
        form_layout.addRow(self.tr("End Date:"), end_date)
        form_layout.addRow(self.tr("Target 1:"), target1_combo)
        form_layout.addRow(self.tr("Target 2:"), target2_combo)
        form_layout.addRow(self.tr("Target 3:"), target3_combo)
        form_layout.addRow(self.tr("Intensity:"), intensity_combo)
        form_layout.addRow(self.tr("Notes:"), notes_input)

        # Add validation label
        validation_label = QLabel()
        validation_label.setStyleSheet("color: red;")
        validation_label.setVisible(False)

        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)

        # Add layouts to main layout
        layout.addLayout(form_layout)
        layout.addWidget(validation_label)
        layout.addWidget(button_box)

        # Function to validate dates
        def validate_dates():
            # Get macrocycle dates
            macrocycle_start = self.macrocycle_start_date.date()
            macrocycle_end = self.macrocycle_end_date.date()

            # Get microcycle dates
            micro_start = start_date.date()
            micro_end = end_date.date()

            # Check if end date is after start date
            if micro_end < micro_start:
                validation_label.setText(self.tr("End date must be after start date"))
                validation_label.setVisible(True)
                return False

            # Check if dates are within macrocycle dates
            if micro_start < macrocycle_start or micro_start > macrocycle_end:
                validation_label.setText(self.tr("Start date must be within macrocycle dates"))
                validation_label.setVisible(True)
                return False

            if micro_end < macrocycle_start or micro_end > macrocycle_end:
                validation_label.setText(self.tr("End date must be within macrocycle dates"))
                validation_label.setVisible(True)
                return False

            # Check if this microcycle's dates conflict with others in this mesocycle
            for i in range(self.microcycle_table.rowCount()):
                if i == row:  # Skip the current row
                    continue

                other_start = QDate.fromString(self.microcycle_table.item(i, 1).text(), "yyyy-MM-dd")
                other_end = QDate.fromString(self.microcycle_table.item(i, 2).text(), "yyyy-MM-dd")

                # Check if the edited microcycle overlaps with an existing one
                if (micro_start <= other_end and micro_end >= other_start):
                    validation_label.setText(self.tr("Microcycle dates cannot overlap with existing microcycles in this mesocycle"))
                    validation_label.setVisible(True)
                    return False

            # Check for overlapping dates with microcycles in other mesocycles
            has_overlap, overlap_message = self._check_microcycle_overlap(micro_start, micro_end, self.microcycle_table, row)
            if has_overlap:
                validation_label.setText(self.tr("Microcycle dates cannot overlap with existing microcycles: {}").format(overlap_message))
                validation_label.setVisible(True)
                return False

            # If all validations pass
            validation_label.setVisible(False)
            return True

        # Connect date changes to validation
        start_date.dateChanged.connect(validate_dates)
        end_date.dateChanged.connect(validate_dates)

        # Initial validation
        validate_dates()

        # Execute dialog
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Validate one more time before accepting
            if not validate_dates():
                return

            # Get values from form
            name = name_input.text()
            start = start_date.date().toString("yyyy-MM-dd")
            end = end_date.date().toString("yyyy-MM-dd")
            target1 = target1_combo.currentText()
            target2 = target2_combo.currentText()
            target3 = target3_combo.currentText()
            intensity = intensity_combo.currentText()
            notes = notes_input.toPlainText()

            # Update row in table
            self.microcycle_table.setItem(row, 1, QTableWidgetItem(start))
            self.microcycle_table.setItem(row, 2, QTableWidgetItem(end))
            self.microcycle_table.setItem(row, 3, QTableWidgetItem(name))
            self.microcycle_table.setItem(row, 4, QTableWidgetItem(target1))
            self.microcycle_table.setItem(row, 5, QTableWidgetItem(target2))
            self.microcycle_table.setItem(row, 6, QTableWidgetItem(target3))
            self.microcycle_table.setItem(row, 7, QTableWidgetItem(intensity))
            self.microcycle_table.setItem(row, 8, QTableWidgetItem(notes))

            # Save microcycle data
            self._save_microcycle_data()

    def _handle_table_double_click(self, _):
        """Handle double-click on table to edit the selected microcycle."""
        # Just call the edit method (we don't need the index parameter)
        self._edit_microcycle()

    def _handle_basic_table_double_click(self, _):
        """Handle double-click on basic table to edit the selected basic microcycle."""
        # Just call the edit method (we don't need the index parameter)
        self._edit_basic_microcycle()

    def _handle_competition_table_double_click(self, _):
        """Handle double-click on competition table to edit the selected competition microcycle."""
        # Just call the edit method (we don't need the index parameter)
        self._edit_competition_microcycle()

    def _handle_transition_table_double_click(self, _):
        """Handle double-click on transition table to edit the selected transition microcycle."""
        # Just call the edit method (we don't need the index parameter)
        self._edit_transition_microcycle()

    def _show_periodization_chart(self):
        """Show the periodization chart dialog."""
        # Collect data from all mesocycle tables
        microcycle_data = collect_microcycle_data(self)

        if not microcycle_data:
            QMessageBox.information(
                self,
                self.tr("No Data"),
                self.tr("No periodization data available. Please add microcycles to at least one mesocycle.")
            )
            return

        # Create and show the chart dialog
        chart_dialog = PeriodizationChartDialog(self, microcycle_data)
        chart_dialog.exec()

    def _remove_microcycle(self):
        """Removes the selected microcycle after confirmation."""
        # Get selected row
        selected_rows = self.microcycle_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, self.tr("No Selection"),
                               self.tr("Please select a microcycle to remove."))
            return

        # Get row index from any selected item
        row = selected_rows[0].row()

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            self.tr("Confirm Removal"),
            self.tr("Are you sure you want to remove this microcycle?"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Remove row from table
            self.microcycle_table.removeRow(row)

            # Update IDs for remaining rows
            for i in range(row, self.microcycle_table.rowCount()):
                id_item = QTableWidgetItem(str(i + 1))
                id_item.setFlags(id_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Make ID non-editable
                self.microcycle_table.setItem(i, 0, id_item)

            # Save microcycle data
            self._save_microcycle_data()

    def _check_all_microcycle_overlaps(self):
        """
        Check for overlaps between all microcycles across all mesocycles.

        Returns:
            tuple: (bool, str) - (True if any overlaps exist, message describing the first overlap found)
        """
        # Get all microcycles from all mesocycles
        all_microcycles = []

        # Tables to check
        tables = [
            (self.microcycle_table, self.tr("Preparation")),
            (self.basic_microcycle_table, self.tr("Basic")),
            (self.competition_microcycle_table, self.tr("Competition")),
            (self.transition_microcycle_table, self.tr("Transition"))
        ]

        # Collect all microcycles
        for table, mesocycle_name in tables:
            row_count = table.rowCount()
            for i in range(row_count):
                start_date = QDate.fromString(table.item(i, 1).text(), "yyyy-MM-dd")
                end_date = QDate.fromString(table.item(i, 2).text(), "yyyy-MM-dd")
                name = table.item(i, 3).text()

                all_microcycles.append({
                    'table': table,
                    'row': i,
                    'start': start_date,
                    'end': end_date,
                    'name': name,
                    'mesocycle': mesocycle_name
                })

        # Check for overlaps
        for i, cycle1 in enumerate(all_microcycles):
            for j, cycle2 in enumerate(all_microcycles):
                if i != j:  # Don't compare a cycle with itself
                    if (cycle1['start'] <= cycle2['end'] and cycle1['end'] >= cycle2['start']):
                        # Found an overlap
                        message = self.tr("Overlap detected between {} Mesocycle microcycle '{}' ({} to {}) and {} Mesocycle microcycle '{}' ({} to {})").format(
                            cycle1['mesocycle'],
                            cycle1['name'],
                            cycle1['start'].toString("yyyy-MM-dd"),
                            cycle1['end'].toString("yyyy-MM-dd"),
                            cycle2['mesocycle'],
                            cycle2['name'],
                            cycle2['start'].toString("yyyy-MM-dd"),
                            cycle2['end'].toString("yyyy-MM-dd")
                        )
                        return True, message

        # No overlaps found
        return False, ""

    def _check_microcycle_overlap(self, start_date, end_date, exclude_table=None, exclude_row=None):
        """
        Check if the given date range overlaps with any microcycle in any mesocycle.

        Args:
            start_date (QDate): Start date to check
            end_date (QDate): End date to check
            exclude_table (QTableWidget, optional): Table to exclude from the check (for editing)
            exclude_row (int, optional): Row to exclude from the check (for editing)

        Returns:
            tuple: (bool, str) - (True if overlap exists, message describing the overlap)
        """
        # Tables to check
        tables = [
            (self.microcycle_table, self.tr("Preparation")),
            (self.basic_microcycle_table, self.tr("Basic")),
            (self.competition_microcycle_table, self.tr("Competition")),
            (self.transition_microcycle_table, self.tr("Transition"))
        ]

        for table, mesocycle_name in tables:
            # Skip the excluded table if specified
            if exclude_table is not None and table == exclude_table:
                continue

            row_count = table.rowCount()
            for i in range(row_count):
                # Skip the excluded row if specified
                if exclude_table is not None and exclude_row is not None and table == exclude_table and i == exclude_row:
                    continue

                existing_start = QDate.fromString(table.item(i, 1).text(), "yyyy-MM-dd")
                existing_end = QDate.fromString(table.item(i, 2).text(), "yyyy-MM-dd")

                # Check if the date range overlaps with this microcycle
                if (start_date <= existing_end and end_date >= existing_start):
                    microcycle_name = table.item(i, 3).text()
                    message = self.tr("Dates overlap with {} Mesocycle microcycle '{}' ({} to {})").format(
                        mesocycle_name,
                        microcycle_name,
                        existing_start.toString("yyyy-MM-dd"),
                        existing_end.toString("yyyy-MM-dd")
                    )
                    return True, message

        # No overlap found
        return False, ""

    def _save_microcycle_data(self):
        """Saves the microcycle data to QSettings."""
        # First check for overlaps
        has_overlaps, overlap_message = self._check_all_microcycle_overlaps()
        if has_overlaps:
            # Show an error and don't save the data
            QMessageBox.critical(
                self,
                self.tr("Microcycle Overlap Error"),
                self.tr("Error: Cannot save because there are overlapping microcycles in your schedule:\n\n{}\n\nPlease fix the overlaps before saving.").format(overlap_message)
            )
            # Return without saving
            return False

        settings = QSettings()
        settings.beginGroup("periodization_microcycles")

        # Clear existing microcycle data
        settings.remove("")

        # Save number of microcycles
        row_count = self.microcycle_table.rowCount()
        settings.setValue("count", row_count)

        # Save each microcycle
        for row in range(row_count):
            prefix = f"microcycle_{row}_"
            settings.setValue(f"{prefix}id", self.microcycle_table.item(row, 0).text())
            settings.setValue(f"{prefix}start", self.microcycle_table.item(row, 1).text())
            settings.setValue(f"{prefix}end", self.microcycle_table.item(row, 2).text())
            settings.setValue(f"{prefix}name", self.microcycle_table.item(row, 3).text())
            settings.setValue(f"{prefix}target1", self.microcycle_table.item(row, 4).text())
            settings.setValue(f"{prefix}target2", self.microcycle_table.item(row, 5).text())
            settings.setValue(f"{prefix}target3", self.microcycle_table.item(row, 6).text())
            settings.setValue(f"{prefix}intensity", self.microcycle_table.item(row, 7).text())
            settings.setValue(f"{prefix}notes", self.microcycle_table.item(row, 8).text())

        settings.endGroup()
        settings.sync()  # Ensure changes are written
        return True

    def _load_microcycle_data(self):
        """Loads the microcycle data from QSettings."""
        settings = QSettings()
        settings.beginGroup("periodization_microcycles")

        # Clear existing table
        self.microcycle_table.setRowCount(0)

        # Get number of microcycles
        row_count = settings.value("count", 0, type=int)

        # Load each microcycle
        for row in range(row_count):
            prefix = f"microcycle_{row}_"

            # Get values
            id_value = settings.value(f"{prefix}id", str(row + 1))
            start = settings.value(f"{prefix}start", "")
            end = settings.value(f"{prefix}end", "")
            name = settings.value(f"{prefix}name", "")

            # Handle backward compatibility - check if we have the new target fields
            if settings.contains(f"{prefix}target1"):
                # New format with multiple targets
                target1 = settings.value(f"{prefix}target1", "")
                target2 = settings.value(f"{prefix}target2", "")
                target3 = settings.value(f"{prefix}target3", "")

                # Check if we have the intensity field (newest format)
                if settings.contains(f"{prefix}intensity"):
                    intensity = settings.value(f"{prefix}intensity", "50%")  # Default to 50% if not found
                    notes = settings.value(f"{prefix}notes", "")
                else:
                    # Format with multiple targets but no intensity
                    intensity = "50%"  # Default intensity
                    notes = settings.value(f"{prefix}notes", "")
            else:
                # Old format with single target - migrate data
                target1 = settings.value(f"{prefix}target", "")
                target2 = ""
                target3 = ""
                intensity = "50%"  # Default intensity
                notes = settings.value(f"{prefix}notes", "")

            # Add row to table
            self.microcycle_table.insertRow(row)

            # Set ID (non-editable)
            id_item = QTableWidgetItem(id_value)
            id_item.setFlags(id_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.microcycle_table.setItem(row, 0, id_item)

            # Set other values
            self.microcycle_table.setItem(row, 1, QTableWidgetItem(start))
            self.microcycle_table.setItem(row, 2, QTableWidgetItem(end))
            self.microcycle_table.setItem(row, 3, QTableWidgetItem(name))
            self.microcycle_table.setItem(row, 4, QTableWidgetItem(target1))
            self.microcycle_table.setItem(row, 5, QTableWidgetItem(target2))
            self.microcycle_table.setItem(row, 6, QTableWidgetItem(target3))
            self.microcycle_table.setItem(row, 7, QTableWidgetItem(intensity))
            self.microcycle_table.setItem(row, 8, QTableWidgetItem(notes))

        settings.endGroup()

    # --- ADD HELPER for button states (call from load and later) ---
    def _update_button_states(self):
        """Enables/disables buttons based on current selections."""
        group_selected = bool(self.group_list.selectedItems())
        available_selected = bool(self.available_list.selectedItems())
        assigned_selected = bool(self.assigned_list.selectedItems())

        self.remove_group_button.setEnabled(group_selected)
        self.rename_group_button.setEnabled(group_selected)

        self.add_nationality_button.setEnabled(group_selected and available_selected)
        self.remove_nationality_button.setEnabled(group_selected and assigned_selected)

    # --- Group Management Methods --- #
    def _add_group(self):
        """Prompts for a new group name and adds it."""
        group_name, ok = QInputDialog.getText(self, self.tr("Add Zone"), self.tr("Enter name for the new zone:"))
        if ok and group_name:
            group_name = group_name.strip()
            if not group_name:
                QMessageBox.warning(self, self.tr("Invalid Name"), self.tr("Zone name cannot be empty."))
                return
            if group_name in self.zone_data:
                QMessageBox.warning(self, self.tr("Duplicate Name"), self.tr("A zone with this name already exists."))
            else:
                print(f"Adding new zone: {group_name}")
                self.zone_data[group_name] = [] # Add with empty nationality list
                # Add to UI list widget and select it
                new_item = QListWidgetItem(group_name)
                self.group_list.addItem(new_item)
                self.group_list.sortItems() # Keep list sorted
                self.group_list.setCurrentItem(new_item) # Select the new item
                self._save_zone_settings() # Save changes
                # _update_button_states will be called via selection signal
        elif ok: # User clicked OK but entered nothing
             QMessageBox.warning(self, self.tr("Invalid Name"), self.tr("Zone name cannot be empty."))

    def _remove_group(self):
        """Removes the currently selected group after confirmation."""
        selected_items = self.group_list.selectedItems()
        if not selected_items:
            return # Should be prevented by button state, but check anyway

        selected_group_name = selected_items[0].text()

        reply = QMessageBox.question(self,
            self.tr("Confirm Removal"),
            self.tr("Are you sure you want to remove the zone '{}'?").format(selected_group_name),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            print(f"Removing zone: {selected_group_name}")
            current_row = self.group_list.row(selected_items[0])
            # Remove from internal data
            if selected_group_name in self.zone_data:
                del self.zone_data[selected_group_name]
                self._save_zone_settings() # Save changes
            else:
                 print(f"Warning: Zone '{selected_group_name}' not found in internal data during removal.")

            # Remove from UI list widget
            self.group_list.takeItem(current_row)
            # Selection will clear, triggering _on_group_selection_changed and _update_button_states

    def _rename_group(self):
        """Prompts for a new name for the selected group."""
        selected_items = self.group_list.selectedItems()
        if not selected_items:
            return

        current_item = selected_items[0]
        old_name = current_item.text()

        new_name, ok = QInputDialog.getText(self, self.tr("Rename Zone"),
                                          self.tr("Enter new name for zone '{}':").format(old_name),
                                          QLineEdit.EchoMode.Normal,
                                          old_name) # Pre-fill with old name

        if ok and new_name:
            new_name = new_name.strip()
            if not new_name:
                 QMessageBox.warning(self, self.tr("Invalid Name"), self.tr("Zone name cannot be empty."))
                 return
            if new_name == old_name:
                return # No change
            if new_name in self.zone_data:
                QMessageBox.warning(self, self.tr("Duplicate Name"), self.tr("A zone with this name already exists."))
                return
            else:
                print(f"Renaming zone '{old_name}' to '{new_name}'")
                # Update internal dictionary key
                nationalities = self.zone_data.pop(old_name, []) # Get data and remove old key
                self.zone_data[new_name] = nationalities # Add new key with old data
                # Update UI item text
                current_item.setText(new_name)
                self.group_list.sortItems() # Keep list sorted
                # Reselect the item (important if sorting changes position)
                # Find the item again by text might be safer if duplicates were allowed (they arent)
                self.group_list.setCurrentItem(current_item)
                self._save_zone_settings() # Save changes
        elif ok: # User clicked OK but entered nothing
             QMessageBox.warning(self, self.tr("Invalid Name"), self.tr("Zone name cannot be empty."))

    # --- Nationality Assignment Methods --- #
    def _add_nationalities(self):
        """Moves selected nationalities from Available to Assigned list."""
        selected_group_item = self.group_list.currentItem()
        selected_available_items = self.available_list.selectedItems()

        if not selected_group_item or not selected_available_items:
            return # Should be prevented by button state

        group_name = selected_group_item.text()
        nationalities_to_add = [item.text() for item in selected_available_items]

        print(f"Adding nationalities {nationalities_to_add} to group '{group_name}'")

        # Update internal data
        current_assigned = set(self.zone_data.get(group_name, []))
        current_assigned.update(nationalities_to_add)
        self.zone_data[group_name] = sorted(list(current_assigned))

        # Update UI Lists (more efficient to rebuild based on updated zone_data)
        self._on_group_selection_changed(selected_group_item, None) # Refresh lists

        self._save_zone_settings()
        # Selection changes implicitly, _update_button_states called via signal

    def _remove_nationalities(self):
        """Moves selected nationalities from Assigned to Available list."""
        selected_group_item = self.group_list.currentItem()
        selected_assigned_items = self.assigned_list.selectedItems()

        if not selected_group_item or not selected_assigned_items:
            return # Should be prevented by button state

        group_name = selected_group_item.text()
        nationalities_to_remove = [item.text() for item in selected_assigned_items]

        print(f"Removing nationalities {nationalities_to_remove} from group '{group_name}'")

        # Update internal data
        current_assigned = set(self.zone_data.get(group_name, []))
        current_assigned.difference_update(nationalities_to_remove) # Remove items
        self.zone_data[group_name] = sorted(list(current_assigned))

        # Update UI Lists (more efficient to rebuild based on updated zone_data)
        self._on_group_selection_changed(selected_group_item, None) # Refresh lists

        self._save_zone_settings()
        # Selection changes implicitly, _update_button_states called via signal

    def closeEvent(self, event):
        """Save settings when the window is closed."""
        self._save_date_settings()

        # Check for overlaps before saving microcycle data
        has_overlaps, overlap_message = self._check_all_microcycle_overlaps()
        if has_overlaps:
            # Show a warning and ask if the user wants to close without saving
            from PySide6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                self.tr("Microcycle Overlap Warning"),
                self.tr("There are overlapping microcycles in your schedule:\n\n{}\n\nDo you want to close without saving the microcycle data?").format(overlap_message),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.No:
                # User chose not to close, abort the close event
                event.ignore()
                return
            # Otherwise, continue closing without saving microcycle data
        else:
            # No overlaps, save all microcycle data
            self._save_microcycle_data()
            self._save_basic_microcycle_data()
            self._save_competition_microcycle_data()
            self._save_transition_microcycle_data()

        super().closeEvent(event)



# Competition management methods
def _add_competition(self):
    """Add a new competition to the table."""
    # Get season dates for validation
    season_start = self.season_start_date.date()
    season_end = self.season_end_date.date()

    # Create dialog
    dialog = QDialog(self)
    dialog.setWindowTitle(self.tr("Add Competition"))
    dialog.setMinimumWidth(400)

    # Create form layout
    form_layout = QFormLayout(dialog)

    # Competition dropdown populated from Football Competitions tab
    competition_combo = QComboBox()
    competition_combo.addItem("", "")  # Empty option

    # Populate from football_competitions_table
    competition_data = {}  # Dictionary to store competition data: {name: (type, structure)}
    for row in range(self.football_competitions_table.rowCount()):
        name = self.football_competitions_table.item(row, 2).text()
        comp_type = self.football_competitions_table.item(row, 4).text()
        structure = self.football_competitions_table.item(row, 5).text()
        competition_data[name] = (comp_type, structure)
        competition_combo.addItem(name)

    form_layout.addRow(self.tr("Competition:"), competition_combo)

    # Start date
    start_date = QDateEdit(calendarPopup=True)
    start_date.setDisplayFormat("yyyy-MM-dd")
    start_date.setDate(season_start)
    form_layout.addRow(self.tr("Start Date:"), start_date)

    # End date
    end_date = QDateEdit(calendarPopup=True)
    end_date.setDisplayFormat("yyyy-MM-dd")
    end_date.setDate(season_end)
    form_layout.addRow(self.tr("End Date:"), end_date)

    # Type combo box (will be auto-populated based on selected competition)
    type_combo = QComboBox()
    type_combo.addItems([
        self.tr("National"),
        self.tr("International"),
        self.tr("Local"),
        self.tr("Other")
    ])
    type_combo.setEnabled(False)  # Disable manual editing
    form_layout.addRow(self.tr("Type:"), type_combo)

    # Structure combo box (will be auto-populated based on selected competition)
    structure_combo = QComboBox()
    structure_combo.addItems([
        self.tr("League"),
        self.tr("League+playoffs/playouts"),
        self.tr("Group+Knockouts"),
        self.tr("Knockouts"),
        self.tr("Tournament"),
        self.tr("Preparation matches"),
        self.tr("Charity"),
        self.tr("Other")
    ])
    structure_combo.setEnabled(False)  # Disable manual editing
    form_layout.addRow(self.tr("Structure:"), structure_combo)

    # Function to update type and structure based on selected competition
    def update_competition_details():
        selected_competition = competition_combo.currentText()
        if selected_competition in competition_data:
            comp_type, structure = competition_data[selected_competition]
            type_combo.setCurrentText(comp_type)
            structure_combo.setCurrentText(structure)

    # Connect the signal
    competition_combo.currentTextChanged.connect(update_competition_details)

    # Priority combo box
    priority_combo = QComboBox()
    priority_combo.addItems([
        self.tr("High"),
        self.tr("Middle"),
        self.tr("Low")
    ])
    form_layout.addRow(self.tr("Priority:"), priority_combo)

    # Notes
    notes = QTextEdit()
    notes.setMaximumHeight(100)
    form_layout.addRow(self.tr("Notes:"), notes)

    # Button box
    button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
    button_box.accepted.connect(dialog.accept)
    button_box.rejected.connect(dialog.reject)
    form_layout.addRow(button_box)

    # Show dialog
    if dialog.exec() == QDialog.DialogCode.Accepted:
        # Validate dates
        if start_date.date() > end_date.date():
            QMessageBox.warning(
                self,
                self.tr("Invalid Dates"),
                self.tr("Start date cannot be after end date.")
            )
            return

        # Validate against season dates
        if start_date.date() < season_start or end_date.date() > season_end:
            QMessageBox.warning(
                self,
                self.tr("Invalid Dates"),
                self.tr("Competition dates must be within the season dates.")
            )
            return

        # Validate that a competition is selected
        if not competition_combo.currentText():
            QMessageBox.warning(
                self,
                self.tr("Missing Competition"),
                self.tr("Please select a competition from the dropdown list.")
            )
            return

        # Add to table
        row = self.competitions_table.rowCount()
        self.competitions_table.insertRow(row)

        # Generate ID (row number + 1)
        comp_id = str(row + 1)

        # Set data
        self.competitions_table.setItem(row, 0, QTableWidgetItem(comp_id))
        self.competitions_table.setItem(row, 1, QTableWidgetItem(competition_combo.currentText()))
        self.competitions_table.setItem(row, 2, QTableWidgetItem(start_date.date().toString("yyyy-MM-dd")))
        self.competitions_table.setItem(row, 3, QTableWidgetItem(end_date.date().toString("yyyy-MM-dd")))
        self.competitions_table.setItem(row, 4, QTableWidgetItem(type_combo.currentText()))
        self.competitions_table.setItem(row, 5, QTableWidgetItem(structure_combo.currentText()))
        self.competitions_table.setItem(row, 6, QTableWidgetItem(priority_combo.currentText()))
        self.competitions_table.setItem(row, 7, QTableWidgetItem(notes.toPlainText()))

        # Save data
        self._save_competition_data()

        # Update timeline
        self._update_timeline()

def _edit_competition(self):
    """Edit the selected competition."""
    # Get selected row
    selected_rows = self.competitions_table.selectedItems()
    if not selected_rows:
        QMessageBox.information(
            self,
            self.tr("No Selection"),
            self.tr("Please select a competition to edit.")
        )
        return

    # Get row index
    row = selected_rows[0].row()

    # Get season dates for validation
    season_start = self.season_start_date.date()
    season_end = self.season_end_date.date()

    # Create dialog
    dialog = QDialog(self)
    dialog.setWindowTitle(self.tr("Edit Competition"))
    dialog.setMinimumWidth(400)

    # Create form layout
    form_layout = QFormLayout(dialog)

    # Get current competition name
    current_competition_name = self.competitions_table.item(row, 1).text()

    # Competition dropdown populated from Football Competitions tab
    competition_combo = QComboBox()
    competition_combo.addItem("", "")  # Empty option

    # Populate from football_competitions_table
    competition_data = {}  # Dictionary to store competition data: {name: (type, structure)}
    for i in range(self.football_competitions_table.rowCount()):
        name = self.football_competitions_table.item(i, 2).text()
        comp_type = self.football_competitions_table.item(i, 4).text()
        structure = self.football_competitions_table.item(i, 5).text()
        competition_data[name] = (comp_type, structure)
        competition_combo.addItem(name)

    # Set current competition if it exists in the dropdown
    index = competition_combo.findText(current_competition_name)
    if index >= 0:
        competition_combo.setCurrentIndex(index)

    form_layout.addRow(self.tr("Competition:"), competition_combo)

    # Start date
    start_date = QDateEdit(calendarPopup=True)
    start_date.setDisplayFormat("yyyy-MM-dd")
    start_date.setDate(QDate.fromString(self.competitions_table.item(row, 2).text(), "yyyy-MM-dd"))
    form_layout.addRow(self.tr("Start Date:"), start_date)

    # End date
    end_date = QDateEdit(calendarPopup=True)
    end_date.setDisplayFormat("yyyy-MM-dd")
    end_date.setDate(QDate.fromString(self.competitions_table.item(row, 3).text(), "yyyy-MM-dd"))
    form_layout.addRow(self.tr("End Date:"), end_date)

    # Type combo box (will be auto-populated based on selected competition)
    type_combo = QComboBox()
    type_combo.addItems([
        self.tr("National"),
        self.tr("International"),
        self.tr("Local"),
        self.tr("Other")
    ])
    type_combo.setCurrentText(self.competitions_table.item(row, 4).text())
    type_combo.setEnabled(False)  # Disable manual editing
    form_layout.addRow(self.tr("Type:"), type_combo)

    # Structure combo box (will be auto-populated based on selected competition)
    structure_combo = QComboBox()
    structure_combo.addItems([
        self.tr("League"),
        self.tr("League+playoffs/playouts"),
        self.tr("Group+Knockouts"),
        self.tr("Knockouts"),
        self.tr("Tournament"),
        self.tr("Preparation matches"),
        self.tr("Charity"),
        self.tr("Other")
    ])
    structure_combo.setCurrentText(self.competitions_table.item(row, 5).text())
    structure_combo.setEnabled(False)  # Disable manual editing
    form_layout.addRow(self.tr("Structure:"), structure_combo)

    # Function to update type and structure based on selected competition
    def update_competition_details():
        selected_competition = competition_combo.currentText()
        if selected_competition in competition_data:
            comp_type, structure = competition_data[selected_competition]
            type_combo.setCurrentText(comp_type)
            structure_combo.setCurrentText(structure)

    # Connect the signal
    competition_combo.currentTextChanged.connect(update_competition_details)

    # Call the function to initialize with current selection
    update_competition_details()

    # Priority combo box
    priority_combo = QComboBox()
    priority_combo.addItems([
        self.tr("High"),
        self.tr("Middle"),
        self.tr("Low")
    ])
    priority_combo.setCurrentText(self.competitions_table.item(row, 6).text())
    form_layout.addRow(self.tr("Priority:"), priority_combo)

    # Notes
    notes = QTextEdit()
    notes.setMaximumHeight(100)
    notes.setText(self.competitions_table.item(row, 7).text())
    form_layout.addRow(self.tr("Notes:"), notes)

    # Button box
    button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
    button_box.accepted.connect(dialog.accept)
    button_box.rejected.connect(dialog.reject)
    form_layout.addRow(button_box)

    # Show dialog
    if dialog.exec() == QDialog.DialogCode.Accepted:
        # Validate dates
        if start_date.date() > end_date.date():
            QMessageBox.warning(
                self,
                self.tr("Invalid Dates"),
                self.tr("Start date cannot be after end date.")
            )
            return

        # Validate against season dates
        if start_date.date() < season_start or end_date.date() > season_end:
            QMessageBox.warning(
                self,
                self.tr("Invalid Dates"),
                self.tr("Competition dates must be within the season dates.")
            )
            return

        # Validate that a competition is selected
        if not competition_combo.currentText():
            QMessageBox.warning(
                self,
                self.tr("Missing Competition"),
                self.tr("Please select a competition from the dropdown list.")
            )
            return

        # Update data
        self.competitions_table.setItem(row, 1, QTableWidgetItem(competition_combo.currentText()))
        self.competitions_table.setItem(row, 2, QTableWidgetItem(start_date.date().toString("yyyy-MM-dd")))
        self.competitions_table.setItem(row, 3, QTableWidgetItem(end_date.date().toString("yyyy-MM-dd")))
        self.competitions_table.setItem(row, 4, QTableWidgetItem(type_combo.currentText()))
        self.competitions_table.setItem(row, 5, QTableWidgetItem(structure_combo.currentText()))
        self.competitions_table.setItem(row, 6, QTableWidgetItem(priority_combo.currentText()))
        self.competitions_table.setItem(row, 7, QTableWidgetItem(notes.toPlainText()))

        # Save data
        self._save_competition_data()

        # Update timeline
        self._update_timeline()

def _remove_competition(self):
    """Remove the selected competition."""
    # Get selected row
    selected_rows = self.competitions_table.selectedItems()
    if not selected_rows:
        QMessageBox.information(
            self,
            self.tr("No Selection"),
            self.tr("Please select a competition to remove.")
        )
        return

    # Get row index
    row = selected_rows[0].row()

    # Confirm deletion
    competition_name = self.competitions_table.item(row, 1).text()
    reply = QMessageBox.question(
        self,
        self.tr("Confirm Deletion"),
        self.tr(f"Are you sure you want to remove the competition '{competition_name}'?"),
        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        QMessageBox.StandardButton.No
    )

    if reply == QMessageBox.StandardButton.Yes:
        # Remove row
        self.competitions_table.removeRow(row)

        # Save data
        self._save_competition_data()

        # Update timeline
        self._update_timeline()

def _save_competition_data(self):
    """Save competition data to settings."""
    settings = QSettings()

    # Clear existing data
    settings.beginGroup("competitions")
    settings.remove("")  # Remove all keys in this group

    # Save number of competitions
    count = self.competitions_table.rowCount()
    settings.setValue("count", count)

    # Define translation maps for storing standardized values
    competition_type_maps = {
        # Map from any language to standard form (English)
        self.tr("National"): "National",
        self.tr("International"): "International",
        self.tr("Local"): "Local",
        self.tr("Other"): "Other",

        # In case we already have English values
        "National": "National",
        "International": "International",
        "Local": "Local",
        "Other": "Other",
    }

    competition_structure_maps = {
        # Map from any language to standard form (English)
        self.tr("League"): "League",
        self.tr("League+playoffs/playouts"): "League+playoffs/playouts",
        self.tr("Group+Knockouts"): "Group+Knockouts",
        self.tr("Knockouts"): "Knockouts",
        self.tr("Tournament"): "Tournament",
        self.tr("Preparation matches"): "Preparation matches",
        self.tr("Charity"): "Charity",
        self.tr("Other"): "Other",

        # In case we already have English values
        "League": "League",
        "League+playoffs/playouts": "League+playoffs/playouts",
        "Group+Knockouts": "Group+Knockouts",
        "Knockouts": "Knockouts",
        "Tournament": "Tournament",
        "Preparation matches": "Preparation matches",
        "Charity": "Charity",
        "Other": "Other",
    }

    # Save each competition
    for row in range(count):
        settings.beginGroup(f"competition_{row}")

        # Get the current displayed values
        current_type = self.competitions_table.item(row, 4).text()
        current_structure = self.competitions_table.item(row, 5).text()

        # Map to standardized values where possible for storage
        standardized_type = competition_type_maps.get(current_type, current_type)
        standardized_structure = competition_structure_maps.get(current_structure, current_structure)

        settings.setValue("id", self.competitions_table.item(row, 0).text())
        settings.setValue("name", self.competitions_table.item(row, 1).text())
        settings.setValue("start_date", self.competitions_table.item(row, 2).text())
        settings.setValue("end_date", self.competitions_table.item(row, 3).text())
        settings.setValue("type", standardized_type)
        settings.setValue("structure", standardized_structure)
        settings.setValue("priority", self.competitions_table.item(row, 6).text())
        settings.setValue("notes", self.competitions_table.item(row, 7).text())

        settings.endGroup()

    settings.endGroup()

def _load_competition_data(self):
    """Load competition data from settings."""
    settings = QSettings()

    # Clear existing table
    self.competitions_table.setRowCount(0)

    # Load competitions
    settings.beginGroup("competitions")
    count = settings.value("count", 0, int)

    for row in range(count):
        settings.beginGroup(f"competition_{row}")

        # Add row
        self.competitions_table.insertRow(row)

        # Get the saved standardized values
        saved_type = settings.value("type", "")
        saved_structure = settings.value("structure", "")

        # Translate standardized values to current language
        translated_type = self.tr(saved_type) if saved_type else ""
        translated_structure = self.tr(saved_structure) if saved_structure else ""

        # Set data
        self.competitions_table.setItem(row, 0, QTableWidgetItem(settings.value("id", "")))
        self.competitions_table.setItem(row, 1, QTableWidgetItem(settings.value("name", "")))
        self.competitions_table.setItem(row, 2, QTableWidgetItem(settings.value("start_date", "")))
        self.competitions_table.setItem(row, 3, QTableWidgetItem(settings.value("end_date", "")))
        self.competitions_table.setItem(row, 4, QTableWidgetItem(translated_type))
        self.competitions_table.setItem(row, 5, QTableWidgetItem(translated_structure))
        self.competitions_table.setItem(row, 6, QTableWidgetItem(settings.value("priority", "")))
        self.competitions_table.setItem(row, 7, QTableWidgetItem(settings.value("notes", "")))

        settings.endGroup()

    settings.endGroup()

# Add the imported methods to the OptionsPage class
OptionsPage._add_basic_microcycle = _add_basic_microcycle
OptionsPage._edit_basic_microcycle = _edit_basic_microcycle
OptionsPage._remove_basic_microcycle = _remove_basic_microcycle
OptionsPage._save_basic_microcycle_data = _save_basic_microcycle_data
OptionsPage._load_basic_microcycle_data = _load_basic_microcycle_data

# Add competition management methods to the OptionsPage class
OptionsPage._add_competition = _add_competition
OptionsPage._edit_competition = _edit_competition
OptionsPage._remove_competition = _remove_competition
OptionsPage._save_competition_data = _save_competition_data
OptionsPage._load_competition_data = _load_competition_data

# Add the Competition Mesocycle methods to the OptionsPage class
OptionsPage._add_competition_microcycle = _add_competition_microcycle
OptionsPage._edit_competition_microcycle = _edit_competition_microcycle
OptionsPage._remove_competition_microcycle = _remove_competition_microcycle
OptionsPage._save_competition_microcycle_data = _save_competition_microcycle_data
OptionsPage._load_competition_microcycle_data = _load_competition_microcycle_data

# Add the Transition Mesocycle methods to the OptionsPage class
OptionsPage._add_transition_microcycle = _add_transition_microcycle
OptionsPage._edit_transition_microcycle = _edit_transition_microcycle
OptionsPage._remove_transition_microcycle = _remove_transition_microcycle
OptionsPage._save_transition_microcycle_data = _save_transition_microcycle_data
OptionsPage._load_transition_microcycle_data = _load_transition_microcycle_data

# Define methods for football competitions tab
def _add_football_competition(self):
    """Add a new football competition."""
    dialog = QDialog(self)
    dialog.setWindowTitle(self.tr("Add Football Competition"))
    dialog.setMinimumWidth(400)

    # Create layout
    layout = QVBoxLayout(dialog)
    form_layout = QFormLayout()

    # Create form fields
    name_input = QLineEdit()
    organization_input = QLineEdit()

    # Create type dropdown
    type_combo = QComboBox()
    type_combo.addItems([
        self.tr("National"),
        self.tr("International"),
        self.tr("Local"),
        self.tr("Other")
    ])

    # Create structure dropdown
    structure_combo = QComboBox()
    structure_combo.addItems([
        self.tr("League"),
        self.tr("League+playoffs/playouts"),
        self.tr("Group+Knockouts"),
        self.tr("Knockouts"),
        self.tr("Tournament"),
        self.tr("Preparation matches"),
        self.tr("Charity"),
        self.tr("Other")
    ])

    # Create notes field
    notes_input = QTextEdit()
    notes_input.setMaximumHeight(100)

    # Create logo upload button and preview
    logo_frame = QFrame()
    logo_frame.setFrameShape(QFrame.Shape.Box)
    logo_frame.setFixedSize(100, 100)
    logo_frame.setStyleSheet("background-color: #f0f0f0;")

    logo_layout = QVBoxLayout(logo_frame)
    logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
    logo_label = QLabel(self.tr("No Logo"))
    logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    logo_layout.addWidget(logo_label)

    # Add fields to form
    form_layout.addRow(self.tr("Name:"), name_input)
    form_layout.addRow(self.tr("Organization:"), organization_input)
    form_layout.addRow(self.tr("Type:"), type_combo)
    form_layout.addRow(self.tr("Structure:"), structure_combo)
    form_layout.addRow(self.tr("Notes:"), notes_input)

    # Add logo section
    logo_section_layout = QHBoxLayout()
    logo_section_layout.addWidget(logo_frame)
    logo_section_layout.addWidget(upload_button)
    form_layout.addRow(self.tr("Logo:"), logo_section_layout)

    # Add form to layout
    layout.addLayout(form_layout)

    # Add buttons
    button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
    button_box.accepted.connect(dialog.accept)
    button_box.rejected.connect(dialog.reject)
    layout.addWidget(button_box)

    # Logo file path
    logo_path = None

    # Handle logo upload
    def upload_logo():
        nonlocal logo_path
        file_path, _ = QFileDialog.getOpenFileName(
            dialog,
            self.tr("Select Logo Image"),
            "",
            self.tr("PNG Files (*.png)")
        )

        if file_path:
            # Check file size
            file_info = QFileInfo(file_path)
            file_size = file_info.size()

            if file_size > 100 * 1024:  # 100KB
                QMessageBox.warning(
                    dialog,
                    self.tr("File Too Large"),
                    self.tr("The logo file must be less than 100KB. Current size: {:.1f}KB").format(file_size / 1024)
                )
                return

            # Load image to check dimensions
            image = QImage(file_path)
            if image.width() > 200 or image.height() > 200:
                QMessageBox.warning(
                    dialog,
                    self.tr("Image Too Large"),
                    self.tr("The logo dimensions must be at most 200x200 pixels. Current size: {}x{}").format(
                        image.width(), image.height()
                    )
                )
                return

            # Display the image in the frame
            pixmap = QPixmap(file_path)
            logo_label.setPixmap(pixmap.scaled(
                logo_frame.width(),
                logo_frame.height(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))
            logo_label.setText("")  # Clear text when image is set

            # Store the path
            logo_path = file_path

    upload_button.clicked.connect(upload_logo)

    # Show dialog
    if dialog.exec() == QDialog.DialogCode.Accepted:
        # Get next ID
        next_id = 1
        if self.football_competitions_table.rowCount() > 0:
            last_id = int(self.football_competitions_table.item(self.football_competitions_table.rowCount() - 1, 0).text())
            next_id = last_id + 1

        # Add new row
        row = self.football_competitions_table.rowCount()
        self.football_competitions_table.insertRow(row)

        # Set ID
        self.football_competitions_table.setItem(row, 0, QTableWidgetItem(str(next_id)))

        # Set logo
        logo_cell = QTableWidgetItem()
        if logo_path:
            # Create media/competitions directory if it doesn't exist
            from pathlib import Path
            media_dir = Path("media/competitions")
            media_dir.mkdir(parents=True, exist_ok=True)

            # Copy the logo file
            target_path = media_dir / f"competition_{next_id}.png"
            try:
                # Read source file
                with open(logo_path, 'rb') as src_file:
                    content = src_file.read()

                # Write to target file
                with open(target_path, 'wb') as dst_file:
                    dst_file.write(content)

                # Set logo icon in table
                pixmap = QPixmap(str(target_path))
                logo_cell.setData(Qt.ItemDataRole.DecorationRole, pixmap.scaled(
                    40, 40, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation
                ))
            except Exception as e:
                print(f"Error saving logo: {e}")

        self.football_competitions_table.setItem(row, 1, logo_cell)

        # Set other fields
        self.football_competitions_table.setItem(row, 2, QTableWidgetItem(name_input.text()))
        self.football_competitions_table.setItem(row, 3, QTableWidgetItem(organization_input.text()))
        self.football_competitions_table.setItem(row, 4, QTableWidgetItem(type_combo.currentText()))
        self.football_competitions_table.setItem(row, 5, QTableWidgetItem(structure_combo.currentText()))
        self.football_competitions_table.setItem(row, 6, QTableWidgetItem(notes_input.toPlainText()))

        # Save data
        self._save_football_competitions_data()

def _edit_football_competition(self):
    """Edit the selected football competition."""
    # Get selected row
    selected_rows = self.football_competitions_table.selectedItems()
    if not selected_rows:
        QMessageBox.information(
            self,
            self.tr("No Selection"),
            self.tr("Please select a competition to edit.")
        )
        return

    row = selected_rows[0].row()

    # Get current values
    comp_id = self.football_competitions_table.item(row, 0).text()
    name = self.football_competitions_table.item(row, 2).text()
    organization = self.football_competitions_table.item(row, 3).text()
    comp_type = self.football_competitions_table.item(row, 4).text()
    structure = self.football_competitions_table.item(row, 5).text()
    notes = self.football_competitions_table.item(row, 6).text()

    # Create dialog
    dialog = QDialog(self)
    dialog.setWindowTitle(self.tr("Edit Football Competition"))
    dialog.setMinimumWidth(400)

    # Create layout
    layout = QVBoxLayout(dialog)
    form_layout = QFormLayout()

    # Create form fields
    name_input = QLineEdit(name)
    organization_input = QLineEdit(organization)

    # Create type dropdown
    type_combo = QComboBox()
    type_combo.addItems([
        self.tr("National"),
        self.tr("International"),
        self.tr("Local"),
        self.tr("Other")
    ])
    type_combo.setCurrentText(comp_type)

    # Create structure dropdown
    structure_combo = QComboBox()
    structure_combo.addItems([
        self.tr("League"),
        self.tr("League+playoffs/playouts"),
        self.tr("Group+Knockouts"),
        self.tr("Knockouts"),
        self.tr("Tournament"),
        self.tr("Preparation matches"),
        self.tr("Charity"),
        self.tr("Other")
    ])
    structure_combo.setCurrentText(structure)

    # Create notes field
    notes_input = QTextEdit()
    notes_input.setPlainText(notes)
    notes_input.setMaximumHeight(100)

    # Create logo upload button and preview
    logo_frame = QFrame()
    logo_frame.setFrameShape(QFrame.Shape.Box)
    logo_frame.setFixedSize(100, 100)
    logo_frame.setStyleSheet("background-color: #f0f0f0;")

    logo_layout = QVBoxLayout(logo_frame)
    logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
    logo_label = QLabel(self.tr("No Logo"))
    logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    logo_layout.addWidget(logo_label)

    # Check if logo exists and display it
    from pathlib import Path
    logo_file = Path(f"media/competitions/competition_{comp_id}.png")
    if logo_file.exists():
        pixmap = QPixmap(str(logo_file))
        logo_label.setPixmap(pixmap.scaled(
            logo_frame.width(),
            logo_frame.height(),
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        ))
        logo_label.setText("")  # Clear text when image is set

    upload_button = QPushButton(self.tr("Upload Logo"))

    # Add fields to form
    form_layout.addRow(self.tr("ID:"), QLabel(comp_id))
    form_layout.addRow(self.tr("Name:"), name_input)
    form_layout.addRow(self.tr("Organization:"), organization_input)
    form_layout.addRow(self.tr("Type:"), type_combo)
    form_layout.addRow(self.tr("Structure:"), structure_combo)
    form_layout.addRow(self.tr("Notes:"), notes_input)

    # Add logo section
    logo_section_layout = QHBoxLayout()
    logo_section_layout.addWidget(logo_frame)
    logo_section_layout.addWidget(upload_button)
    form_layout.addRow(self.tr("Logo:"), logo_section_layout)

    # Add form to layout
    layout.addLayout(form_layout)

    # Add buttons
    button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
    button_box.accepted.connect(dialog.accept)
    button_box.rejected.connect(dialog.reject)
    layout.addWidget(button_box)

    # Logo file path
    logo_path = None

    # Handle logo upload
    def upload_logo():
        nonlocal logo_path
        file_path, _ = QFileDialog.getOpenFileName(
            dialog,
            self.tr("Select Logo Image"),
            "",
            self.tr("PNG Files (*.png)")
        )

        if file_path:
            # Check file size
            file_info = QFileInfo(file_path)
            file_size = file_info.size()

            if file_size > 100 * 1024:  # 100KB
                QMessageBox.warning(
                    dialog,
                    self.tr("File Too Large"),
                    self.tr("The logo file must be less than 100KB. Current size: {:.1f}KB").format(file_size / 1024)
                )
                return

            # Load image to check dimensions
            image = QImage(file_path)
            if image.width() > 200 or image.height() > 200:
                QMessageBox.warning(
                    dialog,
                    self.tr("Image Too Large"),
                    self.tr("The logo dimensions must be at most 200x200 pixels. Current size: {}x{}").format(
                        image.width(), image.height()
                    )
                )
                return

            # Display the image in the frame
            pixmap = QPixmap(file_path)
            logo_label.setPixmap(pixmap.scaled(
                logo_frame.width(),
                logo_frame.height(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            ))
            logo_label.setText("")  # Clear text when image is set

            # Store the path
            logo_path = file_path

    upload_button.clicked.connect(upload_logo)

    # Show dialog
    if dialog.exec() == QDialog.DialogCode.Accepted:
        # Update row
        self.football_competitions_table.setItem(row, 2, QTableWidgetItem(name_input.text()))
        self.football_competitions_table.setItem(row, 3, QTableWidgetItem(organization_input.text()))
        self.football_competitions_table.setItem(row, 4, QTableWidgetItem(type_combo.currentText()))
        self.football_competitions_table.setItem(row, 5, QTableWidgetItem(structure_combo.currentText()))
        self.football_competitions_table.setItem(row, 6, QTableWidgetItem(notes_input.toPlainText()))

        # Update logo if changed
        if logo_path:
            # Create media/competitions directory if it doesn't exist
            from pathlib import Path
            media_dir = Path("media/competitions")
            media_dir.mkdir(parents=True, exist_ok=True)

            # Copy the logo file
            target_path = media_dir / f"competition_{comp_id}.png"
            try:
                # Read source file
                with open(logo_path, 'rb') as src_file:
                    content = src_file.read()

                # Write to target file
                with open(target_path, 'wb') as dst_file:
                    dst_file.write(content)

                # Set logo icon in table
                pixmap = QPixmap(str(target_path))
                logo_cell = QTableWidgetItem()
                logo_cell.setData(Qt.ItemDataRole.DecorationRole, pixmap.scaled(
                    40, 40, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation
                ))
                self.football_competitions_table.setItem(row, 1, logo_cell)
            except Exception as e:
                print(f"Error saving logo: {e}")

        # Save data
        self._save_football_competitions_data()

def _remove_football_competition(self):
    """Remove the selected football competition."""
    # Get selected row
    selected_rows = self.football_competitions_table.selectedItems()
    if not selected_rows:
        QMessageBox.information(
            self,
            self.tr("No Selection"),
            self.tr("Please select a competition to remove.")
        )
        return

    row = selected_rows[0].row()
    comp_id = self.football_competitions_table.item(row, 0).text()
    name = self.football_competitions_table.item(row, 2).text()

    # Check if this competition is used in the Dates tab
    is_used = False
    used_count = 0
    for i in range(self.competitions_table.rowCount()):
        if self.competitions_table.item(i, 1).text() == name:
            is_used = True
            used_count += 1

    if is_used:
        # Competition is in use, show message box with navigation button
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setWindowTitle(self.tr("Competition In Use"))
        msg_box.setText(self.tr("The competition '{}' is currently used in {} entries in:\n\n"
                              "Page: Options\n"
                              "Tab: Dates\n"
                              "Sub-tab: Season\n"
                              "Section: Competitions\n\n"
                              "Please remove these entries first before deleting the competition.").format(name, used_count))

        # Add standard OK button
        ok_button = msg_box.addButton(QMessageBox.StandardButton.Ok)

        # Add custom "Go to Dates" button
        go_to_dates_button = msg_box.addButton(self.tr("Go to Dates"), QMessageBox.ButtonRole.ActionRole)

        msg_box.exec()

        # Check if "Go to Dates" button was clicked
        if msg_box.clickedButton() == go_to_dates_button:
            # Store the competition name to find in the competitions table
            competition_name_to_find = name

            # Navigate to the Dates tab, Season sub-tab
            # Find the correct index for the Dates tab
            dates_tab_index = -1
            for i in range(self.tabs.count()):
                if self.tabs.tabText(i) == self.tr("Dates"):
                    dates_tab_index = i
                    break

            if dates_tab_index != -1:
                # Switch to Dates tab
                self.tabs.setCurrentIndex(dates_tab_index)

                # Find the Season sub-tab in the dates_subtabs
                season_tab_index = 0  # Default to first tab (Season)
                self.dates_subtabs.setCurrentIndex(season_tab_index)

                # Select the rows in the competitions table that contain the competition
                self.competitions_table.clearSelection()
                for row in range(self.competitions_table.rowCount()):
                    if self.competitions_table.item(row, 1).text() == competition_name_to_find:
                        self.competitions_table.selectRow(row)
                        # Scroll to the selected row to make it visible
                        self.competitions_table.scrollToItem(self.competitions_table.item(row, 0))

        # Don't allow deletion
        return

    # Confirm deletion
    confirm = QMessageBox.question(
        self,
        self.tr("Confirm Deletion"),
        self.tr("Are you sure you want to delete the competition '{}'?").format(name),
        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
    )

    if confirm == QMessageBox.StandardButton.Yes:
        # Remove the logo file if it exists
        from pathlib import Path
        logo_file = Path(f"media/competitions/competition_{comp_id}.png")
        if logo_file.exists():
            try:
                logo_file.unlink()
            except Exception as e:
                print(f"Error removing logo file: {e}")

        # Remove the row
        self.football_competitions_table.removeRow(row)

        # Save data
        self._save_football_competitions_data()

def _save_football_competitions_data(self):    """Save football competitions data to settings."""    settings = QSettings()    settings.beginGroup("football_competitions")    # Clear existing data    settings.remove("")    # Save number of competitions    settings.setValue("count", self.football_competitions_table.rowCount())    # Define translation maps for storing standardized values    competition_type_maps = {        # Map from any language to standard form (English)        self.tr("National"): "National",        self.tr("International"): "International",         self.tr("Local"): "Local",        self.tr("Other"): "Other",                # In case we already have English values        "National": "National",        "International": "International",        "Local": "Local",        "Other": "Other",                # For Greek translations        "Εθνικό": "National",        "Διεθνές": "International",        "Τοπικό": "Local",        "Άλλο": "Other"    }        competition_structure_maps = {        # Map from any language to standard form (English)        self.tr("League"): "League",        self.tr("Cup"): "Cup",         self.tr("Tournament"): "Tournament",        self.tr("Friendly"): "Friendly",        self.tr("Other"): "Other",                # In case we already have English values        "League": "League",        "Cup": "Cup",        "Tournament": "Tournament",        "Friendly": "Friendly",        "Other": "Other",                # For Greek translations        "Πρωτάθλημα": "League",        "Κύπελλο": "Cup",        "Τουρνουά": "Tournament",        "Φιλικό": "Friendly",        "Άλλο": "Other"    }    # Save each competition    for row in range(self.football_competitions_table.rowCount()):        # Get type and structure values to standardize        type_text = self.football_competitions_table.item(row, 4).text()        structure_text = self.football_competitions_table.item(row, 5).text()                # Standardize to English storage format        standard_type = competition_type_maps.get(type_text, type_text)        standard_structure = competition_structure_maps.get(structure_text, structure_text)                # Save values using standardized formats for type and structure        settings.setValue(f"{row}/id", self.football_competitions_table.item(row, 0).text())        settings.setValue(f"{row}/name", self.football_competitions_table.item(row, 2).text())        settings.setValue(f"{row}/organization", self.football_competitions_table.item(row, 3).text())        settings.setValue(f"{row}/type", standard_type)        settings.setValue(f"{row}/structure", standard_structure)        settings.setValue(f"{row}/notes", self.football_competitions_table.item(row, 6).text())    settings.endGroup()

def _load_football_competitions_data(self):    """Load football competitions data from settings."""    settings = QSettings()    settings.beginGroup("football_competitions")    # Clear existing rows    self.football_competitions_table.setRowCount(0)    # Get number of competitions    count = settings.value("count", 0, int)        # Create reverse translation maps for display    competition_type_display = {        # English (standard stored values) to current language        "National": self.tr("National"),        "International": self.tr("International"),        "Local": self.tr("Local"),        "Other": self.tr("Other")    }        competition_structure_display = {        # English (standard stored values) to current language        "League": self.tr("League"),        "Cup": self.tr("Cup"),        "Tournament": self.tr("Tournament"),        "Friendly": self.tr("Friendly"),        "Other": self.tr("Other")    }    # Load each competition    for i in range(count):        comp_id = settings.value(f"{i}/id", "")        name = settings.value(f"{i}/name", "")        organization = settings.value(f"{i}/organization", "")        raw_type = settings.value(f"{i}/type", "")        raw_structure = settings.value(f"{i}/structure", "")        notes = settings.value(f"{i}/notes", "")                # Translate type and structure to current language        comp_type = competition_type_display.get(raw_type, raw_type)        structure = competition_structure_display.get(raw_structure, raw_structure)

        # Add row
        row = self.football_competitions_table.rowCount()
        self.football_competitions_table.insertRow(row)

        # Set data
        self.football_competitions_table.setItem(row, 0, QTableWidgetItem(comp_id))

        # Set logo if exists
        logo_cell = QTableWidgetItem()
        from pathlib import Path
        logo_file = Path(f"media/competitions/competition_{comp_id}.png")
        if logo_file.exists():
            pixmap = QPixmap(str(logo_file))
            logo_cell.setData(Qt.ItemDataRole.DecorationRole, pixmap.scaled(
                40, 40, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation
            ))

        self.football_competitions_table.setItem(row, 1, logo_cell)
        self.football_competitions_table.setItem(row, 2, QTableWidgetItem(name))
        self.football_competitions_table.setItem(row, 3, QTableWidgetItem(organization))
        self.football_competitions_table.setItem(row, 4, QTableWidgetItem(comp_type))
        self.football_competitions_table.setItem(row, 5, QTableWidgetItem(structure))
        self.football_competitions_table.setItem(row, 6, QTableWidgetItem(notes))

    settings.endGroup()

# Add the football competitions methods to the OptionsPage class
OptionsPage._add_football_competition = _add_football_competition
OptionsPage._edit_football_competition = _edit_football_competition
OptionsPage._remove_football_competition = _remove_football_competition
OptionsPage._save_football_competitions_data = _save_football_competitions_data
OptionsPage._load_football_competitions_data = _load_football_competitions_data

# Example run block for standalone testing (optional)
if __name__ == '__main__':
    import sys
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)
    # Set org/app names for QSettings if used later
    QCoreApplication.setOrganizationName("YourOrg")
    QCoreApplication.setApplicationName("OptionsPageTest")

    options_window = OptionsPage()
    options_window.show()
    sys.exit(app.exec())

def _refresh_football_competition_types(self):
    """
    Refresh football competition types and structures in the football competitions table
    to display in the current language regardless of which language was active when they were added.
    """
    logger = logging.getLogger(__name__)
    logger.info("Refreshing football competition types to use current language")

    # Define mappings for competition types in different languages
    competition_type_mappings = {
        # English options (source)
        "National": self.tr("National"),
        "International": self.tr("International"),
        "Local": self.tr("Local"),
        "Other": self.tr("Other"),

        # Greek translations (handle cases where it was saved in Greek)
        "Εθνικό": self.tr("National"),
        "Διεθνές": self.tr("International"),
        "Τοπικό": self.tr("Local"),
        "Άλλο": self.tr("Other"),

        # Chinese translations (handle cases where it was saved in Chinese)
        "国内": self.tr("National"),
        "国际": self.tr("International"),
        "地方": self.tr("Local"),
        "其他": self.tr("Other"),

        # Albanian translations (handle cases where it was saved in Albanian)
        "Kombëtar": self.tr("National"),
        "Ndërkombëtar": self.tr("International"),
        "Lokal": self.tr("Local")
    }

    # Do the same for competition structures
    competition_structure_mappings = {
        # English (source)
        "League": self.tr("League"),
        "League+playoffs/playouts": self.tr("League+playoffs/playouts"),
        "Group+Knockouts": self.tr("Group+Knockouts"),
        "Knockouts": self.tr("Knockouts"),
        "Tournament": self.tr("Tournament"),
        "Preparation matches": self.tr("Preparation matches"),
        "Charity": self.tr("Charity"),
        "Other": self.tr("Other"),

        # Greek
        "Πρωτάθλημα": self.tr("League"),
        "Πρωτάθλημα+πλέι-οφ/πλέι-άουτ": self.tr("League+playoffs/playouts"),
        "Όμιλοι+Νοκ-άουτ": self.tr("Group+Knockouts"),
        "Νοκ-άουτ": self.tr("Knockouts"),
        "Τουρνουά": self.tr("Tournament"),
        "Φιλικοί αγώνες": self.tr("Preparation matches"),
        "Φιλανθρωπικός": self.tr("Charity"),
        "Άλλο": self.tr("Other"),

        # Chinese
        "联赛": self.tr("League"),
        "联赛+季后赛/保级赛": self.tr("League+playoffs/playouts"),
        "小组+淘汰赛": self.tr("Group+Knockouts"),
        "淘汰赛": self.tr("Knockouts"),
        "锦标赛": self.tr("Tournament"),
        "热身赛": self.tr("Preparation matches"),
        "慈善赛": self.tr("Charity"),
        "其他": self.tr("Other"),

        # Albanian
        "Kampionat": self.tr("League"),
        "Kampionat+playoff/playout": self.tr("League+playoffs/playouts"),
        "Grupe+Eliminim direkt": self.tr("Group+Knockouts"),
        "Eliminim direkt": self.tr("Knockouts"),
        "Turne": self.tr("Tournament"),
        "Ndeshje përgatitore": self.tr("Preparation matches"),
        "Bamirësi": self.tr("Charity")
    }

    # Update types and structures in the football competitions table
    for row in range(self.football_competitions_table.rowCount()):
        # Get current values
        current_type_text = self.football_competitions_table.item(row, 4).text()
        current_structure_text = self.football_competitions_table.item(row, 5).text()

        # Translate to current language if mapping exists
        if current_type_text in competition_type_mappings:
            translated_type = competition_type_mappings[current_type_text]
            self.football_competitions_table.item(row, 4).setText(translated_type)
            logger.debug(f"Updated football competition type from '{current_type_text}' to '{translated_type}'")

        # Update structure too
        if current_structure_text in competition_structure_mappings:
            translated_structure = competition_structure_mappings[current_structure_text]
            self.football_competitions_table.item(row, 5).setText(translated_structure)
            logger.debug(f"Updated football competition structure from '{current_structure_text}' to '{translated_structure}'")