"""
Locale utilities for FootData application.

This module provides utilities for managing locales and language settings.
"""

from PySide6.QtCore import QLocale, QSettings


def get_locale_for_language(language_code):
    """
    Get the appropriate QLocale for a given language code.

    Args:
        language_code (str): Language code (e.g., 'en', 'el', 'zh', 'sq')

    Returns:
        QLocale: The appropriate locale for the language
    """
    # Map language codes to QLocale language and country combinations
    locale_mapping = {
        'en': (QLocale.Language.English, QLocale.Country.UnitedStates),
        'el': (QLocale.Language.Greek, QLocale.Country.Greece),
        'zh': (QLocale.Language.Chinese, QLocale.Country.China),
        'sq': (QLocale.Language.Albanian, QLocale.Country.Albania),
    }

    # Get the language and country for the code
    if language_code in locale_mapping:
        language, country = locale_mapping[language_code]
        return QLocale(language, country)
    else:
        # Default to English if language code is not found
        return QLocale(QLocale.Language.English, QLocale.Country.UnitedStates)


def get_current_language_locale():
    """
    Get the QLocale for the currently selected language in settings.

    Returns:
        QLocale: The locale for the current language setting
    """
    settings = QSettings()
    current_language = settings.value("language", "en", str)
    return get_locale_for_language(current_language)


def apply_locale_to_date_edit(date_edit):
    """
    Apply the current language locale to a QDateEdit widget.

    Args:
        date_edit (QDateEdit): The date edit widget to apply locale to
    """
    locale = get_current_language_locale()
    date_edit.setLocale(locale)


def get_supported_languages():
    """
    Get a list of supported language codes.

    Returns:
        list: List of supported language codes
    """
    return ['en', 'el', 'zh', 'sq']


def get_language_display_name(language_code):
    """
    Get the display name for a language code.

    Args:
        language_code (str): Language code

    Returns:
        str: Display name for the language
    """
    display_names = {
        'en': 'English',
        'el': 'Ελληνικά (Greek)',
        'zh': '中文 (Chinese)',
        'sq': 'Shqip (Albanian)',
    }

    return display_names.get(language_code, language_code)


def update_all_date_edits_locale(widget):
    """
    Recursively update all QDateEdit widgets in a widget hierarchy to use the current language locale.

    Args:
        widget (QWidget): The root widget to search for QDateEdit widgets
    """
    from PySide6.QtWidgets import QDateEdit

    # Update this widget if it's a QDateEdit
    if isinstance(widget, QDateEdit):
        apply_locale_to_date_edit(widget)

    # Recursively update all child widgets
    for child in widget.findChildren(QDateEdit):
        apply_locale_to_date_edit(child)
