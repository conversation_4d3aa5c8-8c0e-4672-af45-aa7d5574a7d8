<?xml version='1.0' encoding='utf-8'?>
<TS version="2.1" language="zh_CN">
<context>
    <name>PhysicalChartDialog</name>
    <message>
        <source>Physical Profile Chart</source>
        <translation>身体素质图表</translation>
    </message>
    <message>
        <source>BMI</source>
        <translation>BMI</translation>
    </message>
    <message>
        <source>Body Fat %</source>
        <translation>体脂率 %</translation>
    </message>
    <message>
        <source>Lean Mass (kg)</source>
        <translation>瘦体重 (kg)</translation>
    </message>
    <message>
        <source>W/H Ratio</source>
        <translation>腰臀比</translation>
    </message>
    <message>
        <source>W/Ht Ratio</source>
        <translation>腰高比</translation>
    </message>
    <message>
        <source>Player</source>
        <translation>球员</translation>
    </message>
    <message>
        <source>Club Average</source>
        <translation>俱乐部平均值</translation>
    </message>
    <message>
        <source>Club Avg (Male)</source>
        <translation>俱乐部平均值 (男)</translation>
    </message>
    <message>
        <source>Club Avg (Female)</source>
        <translation>俱乐部平均值 (女)</translation>
    </message>
    <message>
        <source>View Physical Profile Chart</source>
        <translation>查看身体素质图表</translation>
    </message>
    <message>
        <source>Insufficient Data</source>
        <translation>数据不足</translation>
    </message>
    <message>
        <source>Not enough physical data available for the selected player to generate a chart.</source>
        <translation>所选球员没有足够的身体数据来生成图表。</translation>
    </message>
    <message>
        <source>Error</source>
        <translation>错误</translation>
    </message>
    <message>
        <source>Failed to retrieve data for the chart.</source>
        <translation>无法获取图表数据。</translation>
    </message>
</context>
<context>
    <name>RosterPage</name>
    <message>
        <source>Show Body Data Columns</source>
        <translation>显示身体数据列</translation>
    </message>
</context>
<context><name>TeamGroupsWidget</name><message><source>Group Id</source><translation>ID</translation></message><message><source>Team Name</source><translation type="unfinished">Team Name</translation></message><message><source>Group Category</source><translation type="unfinished">Group Category</translation></message><message><source>Add Group</source><translation type="unfinished">Add Group</translation></message><message><source>Remove Selected</source><translation type="unfinished">Remove Selected</translation></message><message><source>Search:</source><translation type="unfinished">Search:</translation></message><message><source>Search...</source><translation type="unfinished">Search...</translation></message><message><source>Clear</source><translation type="unfinished">Clear</translation></message><message><source>Filter</source><translation type="unfinished">Filter</translation></message><message><source>Clear Filter</source><translation type="unfinished">Clear Filter</translation></message><message><source>(Unassigned)</source><translation type="unfinished">(Unassigned)</translation></message><message><source>-- Any Coach --</source><translation type="unfinished">-- Any Coach --</translation></message><message><source>Male</source><translation type="unfinished">Male</translation></message><message><source>Female</source><translation type="unfinished">Female</translation></message><message><source>All</source><translation type="unfinished">All</translation></message><message><source>Any</source><translation type="unfinished">Any</translation></message></context><context><name>TeamGroupDelegate</name><message><source>(Unassigned)</source><translation type="unfinished">(Unassigned)</translation></message></context></TS>