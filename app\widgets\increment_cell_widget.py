"""
Custom widget for table cells that support increment/decrement by clicking left/right halves.
"""

from PyQt6.QtWidgets import QWidget, QHBoxLayout, QLabel, QPushButton
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class IncrementCellWidget(QWidget):
    """A custom widget that displays a value and allows increment/decrement by clicking left/right halves."""

    # Signal emitted when value changes (old_value, new_value)
    valueChanged = pyqtSignal(int, int)

    def __init__(self, initial_value=0, min_value=0, max_value=99, parent=None):
        super().__init__(parent)

        self.current_value = initial_value
        self.min_value = min_value
        self.max_value = max_value

        self._setup_ui()
        self._update_display()

    def _setup_ui(self):
        """Set up the widget UI."""
        # Remove margins and spacing
        self.setContentsMargins(0, 0, 0, 0)

        # Create horizontal layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create invisible left button (for decrement)
        self.left_button = QPushButton("−")  # Minus symbol
        self.left_button.setFixedWidth(15)  # Smaller width
        self.left_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: rgba(255, 0, 0, 60);
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(255, 0, 0, 40);
                border: 1px solid rgba(255, 0, 0, 120);
                color: rgba(255, 0, 0, 180);
            }
            QPushButton:pressed {
                background-color: rgba(255, 0, 0, 60);
            }
            QPushButton:disabled {
                color: rgba(128, 128, 128, 60);
            }
        """)
        self.left_button.setToolTip("Click to decrease (-1)")
        self.left_button.clicked.connect(self._decrement)

        # Create center label to display the value
        self.value_label = QLabel()
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setStyleSheet("""
            QLabel {
                background-color: transparent;
                border: none;
                padding: 2px;
            }
        """)

        # Create invisible right button (for increment)
        self.right_button = QPushButton("+")  # Plus symbol
        self.right_button.setFixedWidth(15)  # Smaller width
        self.right_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: rgba(0, 150, 0, 60);
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(0, 150, 0, 40);
                border: 1px solid rgba(0, 150, 0, 120);
                color: rgba(0, 150, 0, 180);
            }
            QPushButton:pressed {
                background-color: rgba(0, 150, 0, 60);
            }
            QPushButton:disabled {
                color: rgba(128, 128, 128, 60);
            }
        """)
        self.right_button.setToolTip("Click to increase (+1)")
        self.right_button.clicked.connect(self._increment)

        # Add widgets to layout
        layout.addWidget(self.left_button)
        layout.addWidget(self.value_label, 1)  # Give label more space
        layout.addWidget(self.right_button)

    def _update_display(self):
        """Update the displayed value."""
        self.value_label.setText(str(self.current_value))

        # Update button states
        self.left_button.setEnabled(self.current_value > self.min_value)
        self.right_button.setEnabled(self.current_value < self.max_value)

    def _increment(self):
        """Increment the value."""
        if self.current_value < self.max_value:
            old_value = self.current_value
            self.current_value += 1
            self._update_display()
            self.valueChanged.emit(old_value, self.current_value)

    def _decrement(self):
        """Decrement the value."""
        if self.current_value > self.min_value:
            old_value = self.current_value
            self.current_value -= 1
            self._update_display()
            self.valueChanged.emit(old_value, self.current_value)

    def setValue(self, value):
        """Set the current value programmatically."""
        if self.min_value <= value <= self.max_value:
            old_value = self.current_value
            self.current_value = value
            self._update_display()
            if old_value != value:
                self.valueChanged.emit(old_value, self.current_value)

    def getValue(self):
        """Get the current value."""
        return self.current_value

    def setRange(self, min_value, max_value):
        """Set the min/max range for the value."""
        self.min_value = min_value
        self.max_value = max_value

        # Clamp current value to new range
        if self.current_value < min_value:
            self.setValue(min_value)
        elif self.current_value > max_value:
            self.setValue(max_value)
        else:
            self._update_display()


class MinuteCellWidget(IncrementCellWidget):
    """Specialized increment cell for minute values (0-120)."""

    def __init__(self, initial_value=0, parent=None):
        super().__init__(initial_value, min_value=0, max_value=120, parent)
        self.setToolTip("Click left half to decrease, right half to increase")


class CardCellWidget(IncrementCellWidget):
    """Specialized increment cell for card values (0-2 for yellow, 0-1 for red)."""

    def __init__(self, initial_value=0, is_red_card=False, parent=None):
        max_val = 1 if is_red_card else 2
        super().__init__(initial_value, min_value=0, max_value=max_val, parent)

        card_type = "red cards" if is_red_card else "yellow cards"
        self.setToolTip(f"Click left half to decrease, right half to increase {card_type}")


class GoalCellWidget(IncrementCellWidget):
    """Specialized increment cell for goal/assist values (0-10)."""

    def __init__(self, initial_value=0, stat_type="goals", parent=None):
        super().__init__(initial_value, min_value=0, max_value=10, parent)
        self.setToolTip(f"Click left half to decrease, right half to increase {stat_type}")
