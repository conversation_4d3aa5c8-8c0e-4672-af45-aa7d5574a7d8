import sys
import os # ADDED os import
import math # ADDED math import
import logging # ADDED
# --- ADD: Import shutil for file operations ---
import shutil
# ------------------------------------------
from PySide6.QtWidgets import (
    QWidget, QH<PERSON><PERSON>Layout, QVBoxLayout, Q<PERSON><PERSON>litter, QTabWidget,
    QTableView, QPushButton, QFormLayout, QLineEdit, QComboBox,
    QDateEdit, QSpinBox, QLabel, QHeaderView, QAbstractItemView,
    QApplication, QDoubleSpinBox, QMessageBox, QMenu, QStyledItemDelegate, QCompleter, QCheckBox,
    QListWidget, QListWidgetItem, QFrame, QFileDialog, QScrollArea, QTextEdit, # ADDED QTextEdit for profile tab
    QGroupBox, QRadioButton # ADDED for position grid
)
from app.widgets.position_grid_widget import PositionGridWidget # ADDED for position grid
# --- ADD: Import QPixmap, QIcon, QImageReader for image handling ---
from PySide6.QtGui import QStandardItemModel, QStandardItem, QAction, QPixmap, QIcon, QImageReader
# --------------------------------------------------------------------
from PySide6.QtCore import Qt, QSettings, QCoreApplication, QStandardPaths, QByteArray, QDate, QEvent, Signal # MODIFIED
# --- ADD: Import QPainter, QFont, QColor, QPen, QBrush for text overlay and styling --- #
from PySide6.QtGui import QPainter, QFont, QColor, QPen, QBrush
# ---------------------------------------------------------------------- #

# Import the manager
from app.data.roster_manager import RosterManager
# --- ADD: Import ClubDataManager ---
from app.data.club_data_manager import ClubDataManager
# ---------------------------------
# Import Path for standalone test
from pathlib import Path
# --- ADD: Import OutlinedLabel --- #
from app.windows.club_window import OutlinedLabel
# -------------------------------- #
# Import countries list
from app.utils.constants import countries
# Import the zone helper function
from app.pages.options_page import get_nationality_zone
# Import color constants
from app.utils.color_constants import get_color
# Import TeamGroupsWidget
from app.widgets.team_groups_widget import TeamGroupsWidget
# --- ADD: Import Chart Dialog --- #
from app.widgets.physical_chart_dialog import PhysicalChartDialog
# --- ADD: Import Metric Calculator Util --- #
from app.utils.metric_calculator import calculate_physical_metrics
# ----------------------------------------- #
# --- ADD: Import Outlier Detection --- #
from app.utils.outlier_detection import outlier_detector, OutlierDetector
from app.utils.roster_outlier_integration import (
    apply_outlier_styling, check_and_highlight_outliers,
    get_outlier_background_color, is_physical_field
)
# ---------------------------------------- #

# --- ADDED Fitness Options ---
FITNESS_OPTIONS = [
    "", # Blank option first
    "Fully Fit",
    "Fit",
    "Conditioning",
    "Unfit",
    "Carrying a Knock",
    "Injured",
    "Recovering",
    "Match Fit",
    "Fatigued",
    "Knocked Out (Recovering from Injury)"
]
# ---------------------------

# --- ADDED Status Tag Options --- #
STATUS_TAG_OPTIONS = [
    ('is_key_player', 'Key Player'),
    ('is_starting_xi', 'Starting XI'),
    ('is_rotation_player', 'Rotation Player'),
    ('is_ext_rotation_player', 'Extended Rotation Player'),
    ('is_squad_player', 'Squad Player'),
    ('is_reserves', 'Reserves'),
    ('is_hot_prospect', 'Hot Prospect'),
    ('is_hot_potential', 'Hot Potential'),
    ('is_stable_performer', 'Stable Performer'),
    ('is_unpredictable_performer', 'Unpredictable Performer'),
    ('is_injury_prone', 'Injury-Prone'),
    ('is_important_player', 'Important Player'),
]
# ----------------------------- #

# --- ADDED Transfer Status Options & Details Mapping --- #
TRANSFER_STATUS_OPTIONS = [
    "", # Blank option first
    "Under Contract",
    "Transfer Listed",
    "Not For Sale",
    "On Loan (Outgoing)",
    "On Loan (Incoming)",
    "Available for Transfer",
    "Available for Loan",
    "Sold",
    "Out of Contract",
    "Released",
    "Youth Team"
]

# Map status text to the keys of the required detail fields
TRANSFER_DETAIL_FIELDS = {
    "Under Contract": ['contract_end_date'],
    "Transfer Listed": ['transfer_list_date'],
    "Not For Sale": [],
    "On Loan (Outgoing)": ['loan_club', 'loan_start_date', 'loan_end_date'],
    "On Loan (Incoming)": ['loan_club', 'loan_start_date', 'loan_end_date'],
    "Available for Transfer": [],
    "Available for Loan": [],
    "Sold": ['sold_club', 'sold_join_date'],
    "Out of Contract": ['contract_end_date'], # Assumes same field
    "Released": ['released_date'],
    "Youth Team": []
}

# Define all possible detail field keys
TRANSFER_ALL_DETAIL_KEYS = [
    'contract_end_date', 'transfer_list_date', 'loan_club',
    'loan_start_date', 'loan_end_date', 'sold_club', 'sold_join_date',
    'released_date'
]
# -------------------------------------------------------- #

# --- NEW FILTERING APPROACH: Define lists for filter combos --- #
# (Consider moving these constants if used elsewhere)
FILTER_POSITIONS = ["", "GK", "DF", "MF", "AT"]
FILTER_FEET = ["", "Left", "Right", "Both"]
FILTER_SEX = ["", "Male", "Female"]
# Zones might need dynamic loading later
FILTER_ZONES = ["", "EU", "Non-EU", "Other"] # Placeholder - TODO: Load dynamically
# ----------------------------------------------------------- #

# --- Custom Delegate for Table Editing ---
class RosterDelegate(QStyledItemDelegate):
    def __init__(self, headers_map, group_options, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.roster.delegate") # ADDED
        # Store mapping of logical column index -> (field_key, header_text)
        self.headers_map = headers_map
        self.positions = ["", "GK", "DF", "MF", "AT"] # Define positions for combobox
        # Define options for new fields (if needed for table editing later)
        self.preferred_foot_options = ["", "Left", "Right", "Both"]
        self.strong_eye_options = ["", "Left", "Right", "Not available"]
        self.sex_options = ["", "Male", "Female"]
        self.fitness_options = FITNESS_OPTIONS # Added
        # Use translation for N/A
        self.na_text = QCoreApplication.translate("RosterPage", "N/A")
        # Store countries list - ADDED
        self.countries = countries
        self._group_options = group_options # Store the passed options
        # self.logger = logging.getLogger("app.roster.delegate") # Logger for the delegate # MOVED UP

    def createEditor(self, parent, option, index):
        logical_index = index.column()
        field_key, _ = self.headers_map.get(logical_index, (None, None))

        if field_key == 'dob':
            editor = QDateEdit(parent, calendarPopup=True)
            editor.setDisplayFormat("yyyy-MM-dd")
            return editor
        elif field_key == 'position':
            editor = QComboBox(parent)
            editor.addItems(self.positions)
            return editor
        elif field_key == 'shirt_number':
            editor = QSpinBox(parent)
            editor.setRange(0, 99) # UPDATED Max range to 99
            editor.setSpecialValueText(self.na_text) # Use N/A for 0
            return editor
        elif field_key == 'height':
             editor = QDoubleSpinBox(parent)
             editor.setRange(0.0, 300.0)
             editor.setDecimals(1)
             editor.setSuffix(" cm")
             # Removed setMinimumWidth to allow compact sizing
             return editor
        elif field_key == 'weight':
             editor = QDoubleSpinBox(parent)
             editor.setRange(0.0, 300.0)
             editor.setDecimals(1)
             editor.setSuffix(" kg")
             # Removed setMinimumWidth to allow compact sizing
             return editor
        elif field_key == 'waist':
             editor = QDoubleSpinBox(parent)
             editor.setRange(0.0, 300.0); editor.setDecimals(1); editor.setSuffix(" cm")
             return editor
        elif field_key == 'hip':
             editor = QDoubleSpinBox(parent)
             editor.setRange(0.0, 300.0); editor.setDecimals(1); editor.setSuffix(" cm")
             return editor
        elif field_key == 'neck':
             editor = QDoubleSpinBox(parent)
             editor.setRange(0.0, 100.0); editor.setDecimals(1); editor.setSuffix(" cm")
             return editor
        elif field_key == 'wrist':
             editor = QDoubleSpinBox(parent)
             editor.setRange(0.0, 50.0); editor.setDecimals(1); editor.setSuffix(" cm")
             return editor
        elif field_key == 'forearm':
             editor = QDoubleSpinBox(parent)
             editor.setRange(0.0, 100.0); editor.setDecimals(1); editor.setSuffix(" cm")
             return editor
        elif field_key == 'thigh':
             editor = QDoubleSpinBox(parent)
             editor.setRange(0.0, 150.0); editor.setDecimals(1); editor.setSuffix(" cm")
             return editor
        elif field_key == 'nationality': # ADDED Nationality editor
            editor = QComboBox(parent)
            editor.addItem("") # Add blank first
            editor.addItems(self.countries)
            editor.setEditable(True) # Allow typing for search
            editor.completer().setCompletionMode(QCompleter.CompletionMode.PopupCompletion)
            editor.completer().setFilterMode(Qt.MatchFlag.MatchContains)
            return editor
        elif field_key == 'fitness': # Added Fitness editor
            editor = QComboBox(parent)
            editor.addItems(self.fitness_options)
            return editor
        elif field_key in ['primary_group_id', 'secondary_group_id']: # NEW Condition
            editor = QComboBox(parent)
            # Populate with "(None)" first
            editor.addItem(self.tr("(None)"), userData=None)
            # Populate with actual groups
            for name, group_id in self._group_options:
                editor.addItem(name, userData=group_id)
            return editor
        # --- ADDED: Explicit editor for Sex --- #
        elif field_key == 'sex':
            editor = QComboBox(parent)
            editor.addItems(self.sex_options) # Uses ["", "Male", "Female"] defined in __init__
            return editor
        # ------------------------------------- #
        # --- ADDED: Explicit editors for Foot and Eye --- #
        elif field_key == 'preferred_foot':
            editor = QComboBox(parent)
            editor.addItems(self.preferred_foot_options)
            return editor
        elif field_key == 'strong_eye':
            editor = QComboBox(parent)
            editor.addItems(self.strong_eye_options)
            return editor
        # ---------------------------------------------- #
        # For other columns (like names, detailed pos), use the default
        return super().createEditor(parent, option, index)

    def setEditorData(self, editor, index):
        # Use EditRole to get the underlying data, not the display text
        value = index.model().data(index, Qt.ItemDataRole.EditRole)
        logical_index = index.column()
        field_key, _ = self.headers_map.get(logical_index, (None, None))

        if field_key == 'dob':
            qdate = QDate.fromString(str(value), "yyyy-MM-dd")
            editor.setDate(qdate if qdate.isValid() else QDate()) # Set invalid/null date if parse fails
        elif field_key == 'position':
            editor.setCurrentText(str(value) if value is not None else "")
        elif field_key == 'nationality': # ADDED Nationality
             editor.setCurrentText(str(value) if value is not None else "")
        elif field_key == 'fitness': # Added Fitness logic
             editor.setCurrentText(str(value) if value is not None else "")
        # --- ADDED: Handle Sex Editor --- #
        elif field_key == 'sex':
            editor.setCurrentText(str(value) if value is not None else "")
        # -------------------------------- #
        elif field_key == 'shirt_number':
             editor.setValue(int(value) if value is not None else 0)
        elif field_key in ['height', 'weight', 'waist', 'hip', 'neck', 'wrist', 'forearm', 'thigh']:
            try:
                 editor.setValue(float(value) if value is not None else 0.0)
            except (ValueError, TypeError):
                 editor.setValue(0.0)
        elif field_key in ['primary_group_id', 'secondary_group_id']: # NEW Condition
            group_id = index.model().data(index, Qt.ItemDataRole.EditRole) # Get the stored ID
            idx = editor.findData(group_id if group_id is not None else None)
            editor.setCurrentIndex(idx if idx != -1 else 0) # Set to None if not found
        else:
            super().setEditorData(editor, index)

    def setModelData(self, editor, model, index):
        logical_index = index.column()
        field_key, _ = self.headers_map.get(logical_index, (None, None))

        self.logger.debug(f"setModelData called for field: {field_key}, column: {logical_index}")

        edit_role_value = None
        display_role_value = None # ADDED: For explicit DisplayRole setting

        if field_key == 'dob':
            qdate = editor.date()
            edit_role_value = qdate.toString("yyyy-MM-dd") if qdate.isValid() else None
            display_role_value = edit_role_value # Display is same as edit for date string
        elif field_key in ['position', 'nationality', 'fitness', 'sex', 'preferred_foot', 'strong_eye']: # Includes foot and eye - OK!
            text = editor.currentText()
            edit_role_value = text if text else None
            display_role_value = edit_role_value # Display is same as edit
        elif field_key == 'shirt_number':
            val = editor.value()
            edit_role_value = val if val != 0 else None
            display_role_value = str(val) if val != 0 else "" # Display string or empty
        elif field_key in ['height', 'weight', 'waist', 'hip', 'neck', 'wrist', 'forearm', 'thigh']:
             val = editor.value()
             edit_role_value = val if val > 0 else None
             display_role_value = f"{val:.1f}" if val > 0 else "" # Display formatted string or empty
        elif field_key in ['primary_group_id', 'secondary_group_id']:
            edit_role_value = editor.currentData() # The group_id
            display_role_value = editor.currentText() # The selected name
            # Ensure display value for None is empty string for consistency with model
            if display_role_value == self.tr("(None)"):
                 display_role_value = ""
            # --- SWAPPED ORDER FOR GROUPS ---
            model.setData(index, edit_role_value, Qt.ItemDataRole.EditRole)      # Set ID first
            model.setData(index, display_role_value, Qt.ItemDataRole.DisplayRole) # Set Name second
            # --- END SWAPPED ORDER ---

            # Explicitly emit itemChanged signal for groups too
            if hasattr(model, 'itemChanged'):
                item = model.itemFromIndex(index)
                if item:
                    self.logger.debug(f"Emitting itemChanged signal for group field: {field_key}")
                    model.itemChanged.emit(item)
            return # Return here since we handled setting data
        else:
            # Fallback for non-handled types (e.g., LineEdits handled by super)
            super().setModelData(editor, model, index)
            return

        # --- Set BOTH Roles (DisplayRole first, EditRole last) --- #
        model.setData(index, display_role_value, Qt.ItemDataRole.DisplayRole) # Set DisplayRole first
        model.setData(index, edit_role_value, Qt.ItemDataRole.EditRole)      # Set EditRole last

        # Explicitly emit itemChanged signal to ensure handlers are triggered
        if hasattr(model, 'itemChanged'):
            item = model.itemFromIndex(index)
            if item:
                self.logger.debug(f"Emitting itemChanged signal for field: {field_key}")
                model.itemChanged.emit(item)

    def updateEditorGeometry(self, editor, option, index):
        # Get the field key for this column
        col = index.column()
        field_key, _ = self.headers_map.get(col, (None, None))

        # For numeric fields, use a more compact width
        if field_key in ['weight', 'height', 'waist', 'hip', 'neck', 'wrist', 'forearm', 'thigh', 'shirt_number', 'age']:
            # Set a more compact width for numeric spinboxes
            rect = option.rect
            if isinstance(editor, (QSpinBox, QDoubleSpinBox)):
                # Limit width to 80 pixels for numeric editors
                rect.setWidth(min(80, rect.width()))
            editor.setGeometry(rect)
        else:
            # Use default geometry for other editors
            editor.setGeometry(option.rect)

    # --- ADDED: Ensure correct display text --- #
    def displayText(self, value, locale):
        # --- ADDED DEBUG --- #
        self.logger.debug(f"RosterDelegate.displayText: value={value}, type={type(value)}") # ADDED
        # --- END DEBUG --- #
        # This relies on _current_index_for_display_text being set before this is called
        # This is a HACK due to lack of direct index access here. Consider alternatives if problematic.
        if not hasattr(self, '_current_index_for_display_text') or not self._current_index_for_display_text.isValid():
            return super().displayText(value, locale)

        logical_index = self._current_index_for_display_text.column()
        field_key, _ = self.headers_map.get(logical_index, (None, None))

        if field_key == 'dob' and value:
            qdate = QDate.fromString(str(value), "yyyy-MM-dd")
            return qdate.toString(Qt.DateFormat.ISODate) if qdate.isValid() else str(value)
        elif field_key == 'shirt_number':
            try:
                num_val = int(value)
                return self.na_text if num_val == 0 else str(num_val)
            except (ValueError, TypeError):
                return str(value) # Or empty string, or N/A
        elif field_key in ['height', 'weight', 'waist', 'hip', 'neck', 'wrist', 'forearm', 'thigh'] and value is not None:
            try:
                return f"{float(value):.1f}"
            except (ValueError, TypeError):
                return str(value)
        # For group IDs, show name if possible, otherwise ID
        elif field_key in ['primary_group_id', 'secondary_group_id'] and value is not None:
            for name, group_id_val in self._group_options:
                if group_id_val == value:
                    return name
            return str(value) # Fallback to ID if name not found

        return super().displayText(value, locale)
    # ------------------------------------------- #

class RosterPage(QWidget):
    """Main page widget for managing the player roster."""

    # Define a signal for when a player is added or removed or data changes
    player_data_changed = Signal() # ADDED this signal

    # Define a signal for when player selections change (attendance, squad list, etc.)
    selection_changed = Signal(str, bool) # Signal with selection_type and selected status

    # --- MODIFIED: Add club_data_manager parameter ---
    def __init__(self, roster_manager=None, club_data_manager=None, parent=None, main_window=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.roster") # ENSURE this is here
        self.manager = roster_manager if roster_manager else RosterManager()
        self.club_manager = club_data_manager # Store ClubDataManager instance
        self.main_window = main_window  # Store reference to MainWindow
        self.setObjectName("RosterPage") # Set object name for geometry persistence
        self.logger.info("Initializing RosterPage...") # ADDED Initial log

        # Use provided managers or create defaults if running standalone
        self.roster_manager = roster_manager if roster_manager else RosterManager()
        # --- ADD: Store ClubDataManager, create default if None --- #
        self.club_data_manager = club_data_manager if club_data_manager else ClubDataManager()
        # ---------------------------------------------------------- #

        # Initialize tooltip manager
        from app.utils.tooltip_manager import TooltipManager
        from app.utils.tooltip_helper import set_tooltip_manager
        self.tooltip_manager = TooltipManager(self)
        set_tooltip_manager(self.tooltip_manager)
        self.logger.info("Tooltip manager initialized and set in helper")

        # Store references to UI elements for synchronization
        self.player_detail_widgets = {}
        self.current_selected_player_id = None
        # --- ADD: Store group options --- #
        self._group_options = [] # List of (name, id) tuples
        # ------------------------------- #
        # --- ADD: Chart Dialog Reference --- #
        self.chart_dialog = None
        # --------------------------------- #

        # --- MODIFIED: Load groups *before* UI init --- #
        self._load_group_options() # Fetch group names/IDs for combos
        self.init_ui() # Now UI can be built with group options available
        # -------------------------------------------- #
        self.load_data()
        self._setup_connections()

        # Restore window geometry after UI is set up
        self._load_geometry()

        # Force update of Profile tab labels to ensure they're properly translated from the beginning
        self._force_update_profile_labels()

        self.player_image_dir = os.path.join(QStandardPaths.writableLocation(QStandardPaths.StandardLocation.AppLocalDataLocation), "player_images")
        os.makedirs(self.player_image_dir, exist_ok=True)

        self.kit_image_dir = os.path.join(QStandardPaths.writableLocation(QStandardPaths.StandardLocation.AppLocalDataLocation), "media", "kit") # For player kit images
        os.makedirs(self.kit_image_dir, exist_ok=True)

        # Default kit text settings (can be overridden by club settings)
        self.default_kit_text_settings = {
            'name_font': QFont("Arial", 12, QFont.Bold),
            'name_color': QColor("white"),
            'name_outline_enabled': False,
            'name_outline_color': QColor("black"),
            'name_outline_thickness': 1,
            'name_y_offset': -30, # Negative for above center
            'number_font': QFont("Arial", 24, QFont.Bold),
            'number_color': QColor("white"),
            'number_outline_enabled': False,
            'number_outline_color': QColor("black"),
            'number_outline_thickness': 1,
            'number_y_offset': 10 # Positive for below center
        }

    def init_ui(self):
        # print("RosterPage: Initializing UI") # Old print (if it existed in 233, if not, this comment is just for illustration)
        self.logger.info("RosterPage: Initializing UI") # ADDED/MODIFIED
        main_layout = QHBoxLayout(self)
        self.splitter = QSplitter(Qt.Orientation.Horizontal) # MODIFIED: Assign to self.splitter
        main_layout.addWidget(self.splitter) # MODIFIED: Use self.splitter

        # Flag to prevent recursive updates during synchronization
        self._is_updating_ui = False

        # Define mandatory columns (using field keys for robustness)
        self.mandatory_column_keys = {'player_id', 'last_name', 'first_name', 'shirt_number', 'position'}

        # --- Define paths for flags --- #
        # Use AppLocalDataLocation first
        self.flags_base_path = Path(QStandardPaths.writableLocation(QStandardPaths.StandardLocation.AppLocalDataLocation)) / "media" / "flags"
        if not self.flags_base_path.exists() or not os.access(self.flags_base_path.parent, os.W_OK):
            project_root = Path(__file__).parent.parent.parent
            self.flags_base_path = project_root / "media" / "flags"
            self.logger.warning(f"AppLocalData flags path not found or writable, using relative path: {self.flags_base_path}") # MODIFIED print to logger.warning
        else:
             self.logger.info(f"Using flags path: {self.flags_base_path}") # MODIFIED print to logger.info
        # We don't create the flags dir here, assuming it's part of deployment

        # --- Define paths for Roster Images (Force Project Relative Path) --- #
        project_root = Path(__file__).parent.parent.parent
        self.roster_images_base_path = project_root / "media" / "roster"
        self.logger.info(f"Forcing roster images path to project relative: {self.roster_images_base_path}") # MODIFIED print to logger.info

        # Ensure the roster images directory exists
        try:
            os.makedirs(self.roster_images_base_path, exist_ok=True)
            self.logger.debug(f"Ensured roster image directory exists: {self.roster_images_base_path}") # MODIFIED print to logger.debug
        except OSError as e:
            self.logger.error(f"Error creating roster image directory {self.roster_images_base_path}: {e}") # MODIFIED print to logger.error
            # Handle error appropriately, maybe disable image feature or show warning

        self.default_player_image_path = self.roster_images_base_path / "default.png"
        self.logger.info(f"Default player image path set to: {self.default_player_image_path}") # MODIFIED print to logger.info

        # --- REMOVED: Logic to copy default.png to AppData as we now force project path --- #
        # -------------------------------------------------------------------------------- #

        # ---------------------------------------- #

        # --- Left Panel (Details/Tabs) ---
        self.left_panel_widget = QWidget()
        left_layout = QVBoxLayout(self.left_panel_widget)
        left_layout.setContentsMargins(5, 5, 5, 5)
        self.create_left_panel(left_layout)
        self.splitter.addWidget(self.left_panel_widget) # MODIFIED: Use self.splitter

        # --- Right Panel (Table) ---
        self.right_panel_widget = QWidget()
        right_layout = QVBoxLayout(self.right_panel_widget)
        right_layout.setContentsMargins(5, 5, 5, 5)
        self.create_right_panel(right_layout)
        self.splitter.addWidget(self.right_panel_widget) # MODIFIED: Use self.splitter

        main_layout.addWidget(self.splitter) # MODIFIED: Use self.splitter

        # Restore splitter state
        self.load_splitter_state()
        # Restore header state
        self._load_header_state()
        # Geometry is loaded in __init__ after UI setup

        # --- ADD MISSING BLOCK: Define paths for Kit Images (Force Project Relative Path) --- #
        self.kit_images_base_path = project_root / "media" / "kit"
        print(f"Forcing kit images path to project relative: {self.kit_images_base_path}")
        self.player_kit_path = self.kit_images_base_path / "kit1_back.png"
        self.gk_kit_path = self.kit_images_base_path / "kit4_back.png"
        print(f"Player Kit Path: {self.player_kit_path}")
        print(f"GK Kit Path: {self.gk_kit_path}")
        # Ensure kit directory exists (optional, can be removed if kit files are guaranteed)
        try:
            os.makedirs(self.kit_images_base_path, exist_ok=True)
        except OSError as e:
            print(f"Warning: Could not ensure kit directory exists: {e}")
        # -------------------------------------------------------------------------------- #

        self.setLayout(main_layout)

    def create_left_panel(self, layout):
        """Creates the left panel with player detail tabs."""
        self.detail_tabs = QTabWidget()
        self.detail_tabs.setEnabled(False) # Disable until player is selected
        layout.addWidget(self.detail_tabs)

        # --- Personal Tab ---
        personal_tab = QWidget()
        personal_tab.setObjectName("personal_tab")  # Add object name for tab identification
        # Create main layout for the tab
        personal_main_layout = QVBoxLayout(personal_tab)
        personal_main_layout.setContentsMargins(5, 5, 5, 5)
        personal_main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Add instruction label
        self.personal_info_label = QLabel(self.tr("Select a player from the list"))
        self.personal_info_label.setStyleSheet("font-style: italic; color: grey;") # Optional styling
        personal_main_layout.addWidget(self.personal_info_label)

        # --- ADDED: Frames Section --- #
        frames_layout = QHBoxLayout() # Horizontal layout for the frames
        frames_layout.setContentsMargins(0, 10, 0, 10) # Add some vertical spacing around frames

        self.frame1 = QFrame() # Frame for Player Image
        self.frame1.setFixedSize(150, 150) # REINSTATED Fixed size
        self.frame1.setFrameShape(QFrame.Shape.NoFrame) # REMOVED Border
        # --- ADD: Layout and Label for Image inside Frame 1 --- #
        frame1_layout = QVBoxLayout(self.frame1) # Layout to hold the label
        frame1_layout.setContentsMargins(0, 0, 0, 0) # Use full frame space
        self.player_image_label = QLabel()
        self.player_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.player_image_label.setToolTip(self.tr("Click to upload player image (PNG, 200x300, <20KB)"))
        self.player_image_label.setCursor(Qt.CursorShape.PointingHandCursor) # Indicate clickable
        frame1_layout.addWidget(self.player_image_label) # Add label to frame's layout
        # Load default image initially
        self._load_player_image(None)

        # --- ADD: Kit Display --- #
        self.frame2 = QFrame() # Frame for Kit Display
        self.frame2.setFixedSize(150, 150) # <-- Try to match frame1
        self.frame2.setFrameShape(QFrame.Shape.NoFrame) # REMOVED Border
        frame2_layout = QVBoxLayout(self.frame2)
        frame2_layout.setContentsMargins(0, 0, 0, 0)
        self.kit_image_label = QLabel()
        self.kit_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.kit_image_label.setScaledContents(False)
        frame2_layout.addWidget(self.kit_image_label)

        # --- ADD: Overlay Labels for Kit Text (Children of kit_image_label) --- #
        self.kit_name_overlay_label = OutlinedLabel("NAME", parent=self.kit_image_label)
        self.kit_number_overlay_label = OutlinedLabel("##", parent=self.kit_image_label)

        # Initial styling (will be overridden by loaded settings)
        self.kit_name_overlay_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.kit_number_overlay_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.kit_name_overlay_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.kit_number_overlay_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        self.kit_name_overlay_label.hide()
        self.kit_number_overlay_label.hide()
        # Positioning will be handled dynamically in _load_player_kit
        # -------------------------------------------------------------------- #

        frames_layout.addWidget(self.frame1)
        frames_layout.addWidget(self.frame2)
        frames_layout.addStretch(1) # Push frames to the left

        personal_main_layout.addLayout(frames_layout)
        # Add the frames layout below the label
        # ---------------------------

        # Create the form layout for details
        personal_form_layout = QFormLayout()
        personal_form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        self.player_id_label = QLabel("-") # Read-only ID display
        self.player_id_label.setObjectName("player_id_label")

        self.last_name_input = QLineEdit()
        self.last_name_input.setObjectName("last_name_input")

        self.first_name_input = QLineEdit()
        self.first_name_input.setObjectName("first_name_input")

        self.shirt_number_spinbox = QSpinBox()
        self.shirt_number_spinbox.setObjectName("shirt_number_spinbox")
        self.shirt_number_spinbox.setRange(0, 99)
        self.shirt_number_spinbox.setSpecialValueText(self.tr("N/A")) # For 0 or unassigned

        self.position_combo = QComboBox()
        self.position_combo.setObjectName("position_combo")
        self.position_combo.addItems(["", "GK", "DF", "MF", "AT"]) # Include blank option

        self.detailed_pos_input = QLineEdit()
        self.detailed_pos_input.setObjectName("detailed_pos_input")

        self.dob_edit = QDateEdit(calendarPopup=True)
        self.dob_edit.setObjectName("dob_edit")
        self.dob_edit.setDisplayFormat("yyyy-MM-dd") # Consistent format

        self.age_label = QLabel("-") # Calculated age
        self.age_label.setObjectName("age_label")

        self.nationality_combo = QComboBox() # ADDED
        self.nationality_combo.setObjectName("nationality_combo")
        self.nationality_combo.addItem("") # Add blank first
        self.nationality_combo.addItems(countries)
        self.nationality_combo.setEditable(True) # Allow typing for search
        self.nationality_combo.completer().setCompletionMode(QCompleter.CompletionMode.PopupCompletion)
        self.nationality_combo.completer().setFilterMode(Qt.MatchFlag.MatchContains)

        # --- ADDED Zone Label --- #
        self.zone_label = QLabel("-") # Read-only zone display
        self.zone_label.setObjectName("zone_label")
        # ------------------------ #

        # --- ADDED Flag Preview ---
        self.flag_preview_label = QLabel()
        self.flag_preview_label.setObjectName("flag_preview_label")
        self.flag_preview_label.setFixedSize(50, 30) # Set desired size
        self.flag_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.flag_preview_label.setStyleSheet("border: 1px solid lightgray; background-color: #f0f0f0;") # Placeholder style
        # -------------------------

        # --- ADDED New Widgets --- #
        self.preferred_foot_combo = QComboBox()
        self.preferred_foot_combo.setObjectName("preferred_foot_combo")
        self.preferred_foot_combo.addItems(["", "Left", "Right", "Both"])

        self.strong_eye_combo = QComboBox()
        self.strong_eye_combo.setObjectName("strong_eye_combo")
        self.strong_eye_combo.addItems(["", "Left", "Right", "Not available"])

        self.sex_combo = QComboBox()
        self.sex_combo.setObjectName("sex_combo")
        # --- FIX: Populate sex_combo with userData --- #
        self.sex_combo.addItem("", userData=None) # Blank option
        self.sex_combo.addItem(self.tr("Male"), userData="Male")
        self.sex_combo.addItem(self.tr("Female"), userData="Female")
        # ------------------------------------------ #
        # --------------------------

        # --- ADDED Team Group Combos --- #
        self.primary_group_combo = QComboBox()
        self.primary_group_combo.setObjectName("primary_group_combo")
        self.secondary_group_combo = QComboBox()
        self.secondary_group_combo.setObjectName("secondary_group_combo")
        self._populate_group_combos() # Populate with loaded options
        # ------------------------------- #

        personal_form_layout.addRow(self.tr("ID:"), self.player_id_label)
        personal_form_layout.addRow(self.tr("Last Name:"), self.last_name_input)
        personal_form_layout.addRow(self.tr("First Name:"), self.first_name_input)
        personal_form_layout.addRow(self.tr("Shirt Number:"), self.shirt_number_spinbox)
        personal_form_layout.addRow(self.tr("Position:"), self.position_combo)
        personal_form_layout.addRow(self.tr("Detailed Position:"), self.detailed_pos_input)
        personal_form_layout.addRow(self.tr("Date of Birth:"), self.dob_edit)
        personal_form_layout.addRow(self.tr("Age:"), self.age_label)
        # Add Nationality and Flag preview side-by-side
        nationality_layout = QHBoxLayout()
        nationality_layout.addWidget(self.nationality_combo)
        nationality_layout.addWidget(self.flag_preview_label)
        nationality_layout.addStretch()
        personal_form_layout.addRow(self.tr("Nationality:"), nationality_layout) # ADDED layout containing combo and flag

        # --- ADDED Zone Row --- #
        personal_form_layout.addRow(self.tr("Zone:"), self.zone_label)
        # ---------------------- #

        # --- ADDED New Rows --- #
        personal_form_layout.addRow(self.tr("Sex:"), self.sex_combo)
        personal_form_layout.addRow(self.tr("Preferred Foot:"), self.preferred_foot_combo)
        personal_form_layout.addRow(self.tr("Strong Eye:"), self.strong_eye_combo)
        # --- ADDED Group Rows --- #
        personal_form_layout.addRow(self.tr("Primary Group:"), self.primary_group_combo)
        personal_form_layout.addRow(self.tr("Secondary Group:"), self.secondary_group_combo)
        # ------------------------ #

        # --- Set Uniform Width for Personal Input Fields --- # # <--- CORRECT SNIPPET START
        personal_target_width = self.nationality_combo.minimumSizeHint().width()
        personal_input_widgets = [
            self.shirt_number_spinbox, self.position_combo, self.dob_edit,
            self.nationality_combo, self.sex_combo, self.preferred_foot_combo,
            self.strong_eye_combo, self.primary_group_combo, self.secondary_group_combo
        ]
        # Exclude LineEdits (last_name, first_name, detailed_pos) as they should expand
        for widget in personal_input_widgets:
             # Check widget exists and has setFixedWidth method
             if hasattr(widget, 'setFixedWidth'):
                 widget.setFixedWidth(personal_target_width)
             else:
                 # Use logger if available, otherwise print for this internal tool window
                 # Use self.logger here as it's initialized
                 self.logger.warning(f"Widget {widget} does not have setFixedWidth method.") # MODIFIED print to logger.warning
        # --------------------------------------------------- # # <--- CORRECT SNIPPET END

        personal_main_layout.addLayout(personal_form_layout)

        self.detail_tabs.addTab(personal_tab, self.tr("Personal"))

        # --- Position Tab ---
        position_tab = QWidget()
        position_tab.setObjectName("position_tab")  # Add object name for tab identification
        position_main_layout = QVBoxLayout(position_tab)
        position_main_layout.setContentsMargins(5, 5, 5, 5)
        position_main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Add instruction label
        self.position_info_label = QLabel(self.tr("Select a player from the list"))
        self.position_info_label.setStyleSheet("font-style: italic; color: grey;") # Optional styling
        position_main_layout.addWidget(self.position_info_label)

        # Create the position grid widget
        self.position_grid = PositionGridWidget()
        self.position_grid.setObjectName("position_grid")

        # Connect the position changed signal
        self.position_grid.positionChanged.connect(self._on_position_changed)

        # Create a container for the position grid with a background image
        self.pitch_frame = QLabel()
        self.pitch_frame.setObjectName("pitch_frame")
        self.pitch_frame.setFixedSize(280, 400)
        self.pitch_frame.setStyleSheet("background-color: #8FBC8F;")  # Light green background
        self.pitch_frame.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Load the position pitch image if it exists
        self._load_position_pitch_image()

        # Create a layout for the position grid that overlays the pitch image
        grid_layout = QVBoxLayout(self.pitch_frame)
        grid_layout.setContentsMargins(0, 0, 0, 0)

        # Make the position grid transparent so the background image shows through
        self.position_grid.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.position_grid.setStyleSheet("background-color: transparent;")

        grid_layout.addWidget(self.position_grid)

        # Add legend for position ratings
        legend_layout = QHBoxLayout()
        legend_layout.setContentsMargins(10, 5, 10, 5)

        # Add legend items using colors from color constants
        legend_items = [
            ("1 - Alternative", "position_alternative"),
            ("2 - Can Play", "position_can_play"),
            ("3 - Best Fit", "position_best_fit")
        ]

        for text, color_key in legend_items:
            color = get_color(color_key)
            legend_label = QLabel(self.tr(text))
            legend_label.setStyleSheet(f"color: {color.name()}; font-weight: bold;")
            legend_layout.addWidget(legend_label)

        # Center the frame in the tab
        pitch_container = QHBoxLayout()
        pitch_container.addStretch()
        pitch_container.addWidget(self.pitch_frame)
        pitch_container.addStretch()

        # Add the legend below the pitch
        position_main_layout.addLayout(pitch_container)
        position_main_layout.addLayout(legend_layout)

        # Add "Clear All Ratings" button
        self.clear_all_ratings_button = QPushButton(self.tr("Clear All Ratings"))
        self.clear_all_ratings_button.setObjectName("clear_all_ratings_button")  # Add object name for identification
        self.clear_all_ratings_button.setToolTip(self.tr("Remove all position ratings for the selected player"))
        self.clear_all_ratings_button.clicked.connect(self._clear_all_position_ratings)

        # Add button in a layout with right alignment
        clear_button_layout = QHBoxLayout()
        clear_button_layout.addStretch()
        clear_button_layout.addWidget(self.clear_all_ratings_button)
        position_main_layout.addLayout(clear_button_layout)

        self.detail_tabs.addTab(position_tab, self.tr("Position"))

        # --- Physical Tab ---
        physical_tab = QWidget()
        physical_tab.setObjectName("physical_tab")  # Add object name for tab identification
        physical_main_layout = QVBoxLayout(physical_tab)
        physical_main_layout.setContentsMargins(5, 5, 5, 5)
        physical_main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Add instruction label
        self.physical_info_label = QLabel(self.tr("Select a player from the list"))
        self.physical_info_label.setStyleSheet("font-style: italic; color: grey;") # Optional styling
        physical_main_layout.addWidget(self.physical_info_label)

        # Create the form layout for details
        physical_form_layout = QFormLayout(physical_tab)
        physical_form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        self.height_spinbox = QDoubleSpinBox() # Use QDoubleSpinBox for potential decimals
        self.height_spinbox.setRange(0.0, 300.0)
        self.height_spinbox.setSuffix(" cm")
        self.height_spinbox.setMinimumWidth(100) # Set minimum width
        self.weight_spinbox = QDoubleSpinBox()
        self.weight_spinbox.setRange(0.0, 300.0)
        self.weight_spinbox.setSuffix(" kg")
        self.weight_spinbox.setMinimumWidth(100) # Set minimum width

        # Add Waist field
        self.waist_spinbox = QDoubleSpinBox()
        self.waist_spinbox.setRange(0.0, 300.0) # Adjust range if needed
        self.waist_spinbox.setDecimals(1)
        self.waist_spinbox.setSuffix(" cm")
        self.waist_spinbox.setMinimumWidth(100) # Set minimum width

        # Add Hip, Neck, Wrist, Forearm, Thigh fields
        self.hip_spinbox = QDoubleSpinBox()
        self.hip_spinbox.setRange(0.0, 300.0)
        self.hip_spinbox.setDecimals(1)
        self.hip_spinbox.setSuffix(" cm")
        self.hip_spinbox.setMinimumWidth(100) # Set minimum width

        self.neck_spinbox = QDoubleSpinBox()
        self.neck_spinbox.setRange(0.0, 100.0) # Smaller range for neck
        self.neck_spinbox.setDecimals(1)
        self.neck_spinbox.setSuffix(" cm")
        self.neck_spinbox.setMinimumWidth(100) # Set minimum width

        self.wrist_spinbox = QDoubleSpinBox()
        self.wrist_spinbox.setRange(0.0, 50.0) # Smaller range for wrist
        self.wrist_spinbox.setDecimals(1)
        self.wrist_spinbox.setSuffix(" cm")
        self.wrist_spinbox.setMinimumWidth(100) # Set minimum width

        self.forearm_spinbox = QDoubleSpinBox()
        self.forearm_spinbox.setRange(0.0, 100.0) # Smaller range for forearm
        self.forearm_spinbox.setDecimals(1)
        self.forearm_spinbox.setSuffix(" cm")
        self.forearm_spinbox.setMinimumWidth(100) # Set minimum width

        self.thigh_spinbox = QDoubleSpinBox()
        self.thigh_spinbox.setRange(0.0, 150.0) # Range for thigh
        self.thigh_spinbox.setDecimals(1)
        self.thigh_spinbox.setSuffix(" cm")
        self.thigh_spinbox.setMinimumWidth(100) # Set minimum width

        # --- ADDED Fitness Field ---
        self.fitness_combo = QComboBox()
        self.fitness_combo.addItems(FITNESS_OPTIONS)
        # ---------------------------

        physical_form_layout.addRow(self.tr("Height:"), self.height_spinbox)
        physical_form_layout.addRow(self.tr("Weight:"), self.weight_spinbox)
        physical_form_layout.addRow(self.tr("Waist:"), self.waist_spinbox) # ADDED Waist
        physical_form_layout.addRow(self.tr("Hip:"), self.hip_spinbox)
        physical_form_layout.addRow(self.tr("Neck:"), self.neck_spinbox)
        physical_form_layout.addRow(self.tr("Wrist:"), self.wrist_spinbox)
        physical_form_layout.addRow(self.tr("Forearm:"), self.forearm_spinbox)
        physical_form_layout.addRow(self.tr("Thigh:"), self.thigh_spinbox)
        physical_form_layout.addRow(self.tr("Fitness:"), self.fitness_combo) # Added row

        # --- Set Uniform Width for Physical Input Fields ---
        target_width = self.fitness_combo.minimumSizeHint().width()
        physical_input_widgets = [
            self.height_spinbox, self.weight_spinbox, self.waist_spinbox,
            self.hip_spinbox, self.neck_spinbox, self.wrist_spinbox,
            self.forearm_spinbox, self.thigh_spinbox
        ]
        for widget in physical_input_widgets:
            widget.setFixedWidth(target_width)
        # -------------------------------------------------

        # --- Calculated Metrics Display --- #
        physical_form_layout.addRow(" ", QLabel("")) # Add a separator

        # --- FIX: Assign labels to self. --- #
        self.bmi_label = QLabel("-")
        self.bfp_label = QLabel("-")
        self.lbm_label = QLabel("-")
        self.whr_label = QLabel("-")
        self.whtr_label = QLabel("-")
        # ---------------------------------- #

        physical_form_layout.addRow(self.tr("BMI:"), self.bmi_label)
        physical_form_layout.addRow(self.tr("Body Fat % (Est. Navy):"), self.bfp_label)
        physical_form_layout.addRow(self.tr("Lean Body Mass (Est.):"), self.lbm_label)
        physical_form_layout.addRow(self.tr("Waist-to-Hip Ratio:"), self.whr_label)
        physical_form_layout.addRow(self.tr("Waist-to-Height Ratio:"), self.whtr_label)
        # ---------------------------------- #

        # --- ADD View Chart Button --- #
        self.view_chart_button = QPushButton(self.tr("View Physical Profile Chart"))
        physical_main_layout.addLayout(physical_form_layout)
        physical_main_layout.addWidget(self.view_chart_button, 0, Qt.AlignmentFlag.AlignRight) # Add button below form
        physical_main_layout.addStretch(1) # Add stretch after button
        # ----------------------------- #

        # physical_main_layout.addLayout(physical_form_layout)
        self.detail_tabs.addTab(physical_tab, self.tr("Physical"))

        # --- ADD Status Tab ---
        status_tab = QWidget()
        status_tab.setObjectName("status_tab")  # Add object name for tab identification
        status_main_layout = QVBoxLayout(status_tab)
        status_main_layout.setContentsMargins(5, 5, 5, 5)
        status_main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.status_info_label = QLabel(self.tr("Select a player from the list"))
        self.status_info_label.setStyleSheet("font-style: italic; color: grey;")
        status_main_layout.addWidget(self.status_info_label)

        # --- Create a Splitter for the two sections --- #
        status_splitter = QSplitter(Qt.Orientation.Vertical)
        status_main_layout.addWidget(status_splitter)

        # --- Top Part of Splitter: Status Tags List --- #
        tags_widget = QWidget()
        tags_layout = QVBoxLayout(tags_widget)
        tags_layout.setContentsMargins(0,0,0,0)

        self.status_tags_label = QLabel(self.tr("Player Status Tags:"))
        tags_layout.addWidget(self.status_tags_label)

        self.status_tags_list_widget = QListWidget()
        self.status_tags_list_widget.setSpacing(2)
        for key, label in STATUS_TAG_OPTIONS:
            item = QListWidgetItem(self.tr(label))
            item.setData(Qt.ItemDataRole.UserRole, key)
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            item.setCheckState(Qt.CheckState.Unchecked)
            self.status_tags_list_widget.addItem(item)
        tags_layout.addWidget(self.status_tags_list_widget)
        self.player_detail_widgets['status_tags_list'] = self.status_tags_list_widget
        status_splitter.addWidget(tags_widget)
        # ------------------------------------------------ #

        # --- Bottom Part of Splitter: Transfer Status --- #
        transfer_widget = QWidget()
        transfer_layout = QVBoxLayout(transfer_widget)
        transfer_layout.setContentsMargins(0,0,0,0)

        # --- Transfer Status ComboBox --- #
        self.transfer_status_label = QLabel(self.tr("Transfer Status:"))
        transfer_layout.addWidget(self.transfer_status_label)

        self.transfer_status_combo = QComboBox()
        self.transfer_status_combo.addItems([self.tr(s) for s in TRANSFER_STATUS_OPTIONS])
        transfer_layout.addWidget(self.transfer_status_combo)
        self.player_detail_widgets['transfer_status'] = self.transfer_status_combo
        # -------------------------------- #

        # --- Container for Dynamic Transfer Details --- #
        self.transfer_details_container = QWidget()
        # Using a FormLayout inside the container
        transfer_details_layout = QFormLayout(self.transfer_details_container)
        transfer_details_layout.setContentsMargins(10, 5, 5, 5) # Indent slightly
        transfer_details_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # Create ALL detail widgets and store references
        self.transfer_detail_labels = {}
        self.transfer_detail_widgets = {}

        # Contract End Date
        self.transfer_detail_labels['contract_end_date'] = QLabel(self.tr("Contract End Date:"))
        self.transfer_detail_widgets['contract_end_date'] = QDateEdit(calendarPopup=True)
        self.transfer_detail_widgets['contract_end_date'].setDisplayFormat("yyyy-MM-dd")

        # Transfer List Date
        self.transfer_detail_labels['transfer_list_date'] = QLabel(self.tr("Date Added to List:"))
        self.transfer_detail_widgets['transfer_list_date'] = QDateEdit(calendarPopup=True)
        self.transfer_detail_widgets['transfer_list_date'].setDisplayFormat("yyyy-MM-dd")

        # Loan Club
        self.transfer_detail_labels['loan_club'] = QLabel(self.tr("Loan Club:"))
        self.transfer_detail_widgets['loan_club'] = QLineEdit()

        # Loan Start Date
        self.transfer_detail_labels['loan_start_date'] = QLabel(self.tr("Loan Start Date:"))
        self.transfer_detail_widgets['loan_start_date'] = QDateEdit(calendarPopup=True)
        self.transfer_detail_widgets['loan_start_date'].setDisplayFormat("yyyy-MM-dd")

        # Loan End Date
        self.transfer_detail_labels['loan_end_date'] = QLabel(self.tr("Loan End Date:"))
        self.transfer_detail_widgets['loan_end_date'] = QDateEdit(calendarPopup=True)
        self.transfer_detail_widgets['loan_end_date'].setDisplayFormat("yyyy-MM-dd")

        # Sold Club
        self.transfer_detail_labels['sold_club'] = QLabel(self.tr("Club Sold To:"))
        self.transfer_detail_widgets['sold_club'] = QLineEdit()

        # Sold Join Date
        self.transfer_detail_labels['sold_join_date'] = QLabel(self.tr("Date Joining New Club:"))
        self.transfer_detail_widgets['sold_join_date'] = QDateEdit(calendarPopup=True)
        self.transfer_detail_widgets['sold_join_date'].setDisplayFormat("yyyy-MM-dd")

        # Released Date
        self.transfer_detail_labels['released_date'] = QLabel(self.tr("Date Released:"))
        self.transfer_detail_widgets['released_date'] = QDateEdit(calendarPopup=True)
        self.transfer_detail_widgets['released_date'].setDisplayFormat("yyyy-MM-dd")

        # Add all rows to the layout (initially hidden by container)
        for key in TRANSFER_ALL_DETAIL_KEYS:
            if key in self.transfer_detail_labels and key in self.transfer_detail_widgets:
                transfer_details_layout.addRow(self.transfer_detail_labels[key], self.transfer_detail_widgets[key])
                # Add to main detail widgets dict for signal blocking
                self.player_detail_widgets[key] = self.transfer_detail_widgets[key]

        transfer_layout.addWidget(self.transfer_details_container)
        self.transfer_details_container.hide() # Hide container initially
        # --------------------------------------------- #

        status_splitter.addWidget(transfer_widget)
        # -------------------------------------------------- #

        self.detail_tabs.addTab(status_tab, self.tr("Status"))

        # --- END ADD Status Tab --- #

        # --- ADD Profile Tab --- #
        profile_tab = QWidget()
        profile_tab.setObjectName("profile_tab")  # Add object name for tab identification
        profile_main_layout = QVBoxLayout(profile_tab)
        profile_main_layout.setContentsMargins(0, 0, 0, 0) # Use full tab space

        # Scroll Area for potentially long content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        profile_main_layout.addWidget(scroll_area)

        # Content widget inside scroll area
        profile_content_widget = QWidget()
        scroll_area.setWidget(profile_content_widget)
        profile_scroll_layout = QVBoxLayout(profile_content_widget) # Main layout inside scroll area
        profile_scroll_layout.setContentsMargins(5, 5, 5, 5)
        profile_scroll_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Add instruction label
        self.profile_info_label = QLabel(self.tr("Select a player from the list"))
        self.profile_info_label.setStyleSheet("font-style: italic; color: grey;")
        profile_scroll_layout.addWidget(self.profile_info_label)

        # --- Form Layout for Profile Details will go here --- #
        self.profile_form_layout = QFormLayout()
        self.profile_form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # --- ADD Initial Profile Fields --- #
        self.profile_date_joined_club = QDateEdit(calendarPopup=True)
        self.profile_date_joined_club.setDisplayFormat("yyyy-MM-dd")
        self.profile_personality_type = QLineEdit()

        # --- ADD: Define widgets for remaining profile fields --- #
        self.profile_mentality = QLineEdit()
        self.profile_work_ethic = QLineEdit()
        self.profile_professionalism_level = QLineEdit()
        self.profile_determination_level = QLineEdit()
        self.profile_team_spirit = QLineEdit()
        self.profile_adaptability = QLineEdit()
        self.profile_temperament = QLineEdit()
        self.profile_ambition_level = QLineEdit()
        self.profile_leadership_qualities = QLineEdit()
        self.profile_charisma = QLineEdit()
        self.profile_emotional_intelligence_notes = QTextEdit()
        self.profile_emotional_intelligence_notes.setAcceptRichText(False)
        self.profile_emotional_intelligence_notes.setMinimumHeight(60)
        self.profile_life_motto_quote = QTextEdit()
        self.profile_life_motto_quote.setAcceptRichText(False)
        self.profile_life_motto_quote.setMinimumHeight(60)
        self.profile_personal_goals_outside_football = QTextEdit()
        self.profile_personal_goals_outside_football.setAcceptRichText(False)
        self.profile_personal_goals_outside_football.setMinimumHeight(60)
        self.profile_hometown = QLineEdit()
        self.profile_current_residence_city_area = QLineEdit()
        self.profile_marital_status = QLineEdit() # Consider ComboBox later
        self.profile_partner_name = QLineEdit()
        self.profile_father_name = QLineEdit()
        self.profile_mother_name = QLineEdit()
        self.profile_parents_occupations = QTextEdit()
        self.profile_parents_occupations.setAcceptRichText(False)
        self.profile_parents_occupations.setMinimumHeight(60)
        self.profile_siblings_summary = QTextEdit()
        self.profile_siblings_summary.setAcceptRichText(False)
        self.profile_siblings_summary.setMinimumHeight(60)
        self.profile_family_background_notes = QTextEdit()
        self.profile_family_background_notes.setAcceptRichText(False)
        self.profile_family_background_notes.setMinimumHeight(80)
        self.profile_number_of_children = QSpinBox()
        self.profile_number_of_children.setRange(0, 30) # Set a reasonable max
        self.profile_pets_description = QTextEdit()
        self.profile_pets_description.setAcceptRichText(False)
        self.profile_pets_description.setMinimumHeight(60)
        self.profile_superstitions_rituals = QTextEdit()
        self.profile_superstitions_rituals.setAcceptRichText(False)
        self.profile_superstitions_rituals.setMinimumHeight(60)
        self.profile_religion_faith = QLineEdit()
        self.profile_religious_practices_notes = QTextEdit()
        self.profile_religious_practices_notes.setAcceptRichText(False)
        self.profile_religious_practices_notes.setMinimumHeight(60)
        self.profile_political_views_summary = QTextEdit()
        self.profile_political_views_summary.setAcceptRichText(False)
        self.profile_political_views_summary.setMinimumHeight(60)
        self.profile_charity_community_involvement = QTextEdit()
        self.profile_charity_community_involvement.setAcceptRichText(False)
        self.profile_charity_community_involvement.setMinimumHeight(80)
        self.profile_volunteer_work_summary = QTextEdit()
        self.profile_volunteer_work_summary.setAcceptRichText(False)
        self.profile_volunteer_work_summary.setMinimumHeight(60)
        self.profile_education_level = QLineEdit()
        self.profile_field_of_study = QLineEdit()
        self.profile_university_school_attended = QLineEdit()
        self.profile_dual_career_other_job = QLineEdit()
        self.profile_business_ventures = QTextEdit()
        self.profile_business_ventures.setAcceptRichText(False)
        self.profile_business_ventures.setMinimumHeight(60)
        self.profile_willingness_to_coach_mentor = QLineEdit() # Consider ComboBox/Checkbox later
        self.profile_public_image_summary = QTextEdit()
        self.profile_public_image_summary.setAcceptRichText(False)
        self.profile_public_image_summary.setMinimumHeight(80)
        self.profile_media_friendliness = QLineEdit() # Consider ComboBox later
        self.profile_media_availability_notes = QTextEdit()
        self.profile_media_availability_notes.setAcceptRichText(False)
        self.profile_media_availability_notes.setMinimumHeight(60)
        self.profile_website_blog = QLineEdit()
        self.profile_personal_contact_email = QLineEdit()
        self.profile_personal_phone_number = QLineEdit()
        self.profile_home_address = QTextEdit()
        self.profile_home_address.setAcceptRichText(False)
        self.profile_home_address.setMinimumHeight(60)
        self.profile_agent_name = QLineEdit()
        self.profile_personal_assistant_manager = QLineEdit()
        self.profile_retirement_plans_summary = QTextEdit()
        self.profile_retirement_plans_summary.setAcceptRichText(False)
        self.profile_retirement_plans_summary.setMinimumHeight(60)
        # --- Biography needs to be defined here before adding to layout --- #
        self.profile_biography = QTextEdit()
        self.profile_biography.setObjectName("profile_biography")  # Add object name for identification
        self.profile_biography.setAcceptRichText(False) # Store plain text
        self.profile_biography.setPlaceholderText(self.tr("Enter player biography notes here..."))
        self.profile_biography.setMinimumHeight(150) # Give it some initial height
        # ---------------------------------------------------------------- #

        # --- Add Widgets to Profile Form Layout --- #
        self.profile_form_layout.addRow(self.tr("Date Joined Club:"), self.profile_date_joined_club)
        self.profile_form_layout.addRow(self.tr("Personality Type:"), self.profile_personality_type)
        # --- Add the new profile fields --- #
        self.profile_form_layout.addRow(self.tr("Mentality:"), self.profile_mentality)
        self.profile_form_layout.addRow(self.tr("Work Ethic:"), self.profile_work_ethic)
        self.profile_form_layout.addRow(self.tr("Professionalism:"), self.profile_professionalism_level)
        self.profile_form_layout.addRow(self.tr("Determination:"), self.profile_determination_level)
        self.profile_form_layout.addRow(self.tr("Team Spirit:"), self.profile_team_spirit)
        self.profile_form_layout.addRow(self.tr("Adaptability:"), self.profile_adaptability)
        self.profile_form_layout.addRow(self.tr("Temperament:"), self.profile_temperament)
        self.profile_form_layout.addRow(self.tr("Ambition:"), self.profile_ambition_level)
        self.profile_form_layout.addRow(self.tr("Leadership:"), self.profile_leadership_qualities)
        self.profile_form_layout.addRow(self.tr("Charisma:"), self.profile_charisma)
        # Re-adding previously problematic fields with proper translations
        self.profile_form_layout.addRow(self.tr("Emotional Intelligence Notes:"), self.profile_emotional_intelligence_notes)
        self.profile_form_layout.addRow(self.tr("Life Motto / Quote:"), self.profile_life_motto_quote)
        self.profile_form_layout.addRow(self.tr("Personal Goals (Outside Football):"), self.profile_personal_goals_outside_football)

        self.profile_form_layout.addRow(self.tr("Hometown:"), self.profile_hometown)
        self.profile_form_layout.addRow(self.tr("Current Residence:"), self.profile_current_residence_city_area)
        self.profile_form_layout.addRow(self.tr("Marital Status:"), self.profile_marital_status)
        self.profile_form_layout.addRow(self.tr("Partner's Name:"), self.profile_partner_name)
        self.profile_form_layout.addRow(self.tr("Father's Name:"), self.profile_father_name)
        self.profile_form_layout.addRow(self.tr("Mother's Name:"), self.profile_mother_name)
        self.profile_form_layout.addRow(self.tr("Parents' Occupations:"), self.profile_parents_occupations)
        self.profile_form_layout.addRow(self.tr("Siblings Summary:"), self.profile_siblings_summary)
        self.profile_form_layout.addRow(self.tr("Family Background Notes:"), self.profile_family_background_notes)
        self.profile_form_layout.addRow(self.tr("Number of Children:"), self.profile_number_of_children)
        self.profile_form_layout.addRow(self.tr("Home Address:"), self.profile_home_address)
        self.profile_form_layout.addRow(self.tr("Religion / Faith:"), self.profile_religion_faith)
        self.profile_form_layout.addRow(self.tr("Education Level:"), self.profile_education_level)
        self.profile_form_layout.addRow(self.tr("Field of Study:"), self.profile_field_of_study)
        self.profile_form_layout.addRow(self.tr("University / School:"), self.profile_university_school_attended)
        self.profile_form_layout.addRow(self.tr("Dual Career / Other Job:"), self.profile_dual_career_other_job)
        self.profile_form_layout.addRow(self.tr("Coach / Mentor Willingness:"), self.profile_willingness_to_coach_mentor)
        self.profile_form_layout.addRow(self.tr("Media Friendliness:"), self.profile_media_friendliness)
        self.profile_form_layout.addRow(self.tr("Website / Blog:"), self.profile_website_blog)
        self.profile_form_layout.addRow(self.tr("Personal Email:"), self.profile_personal_contact_email)
        self.profile_form_layout.addRow(self.tr("Personal Phone:"), self.profile_personal_phone_number)
        self.profile_form_layout.addRow(self.tr("Agent Name:"), self.profile_agent_name)
        self.profile_form_layout.addRow(self.tr("Personal Assistant / Manager:"), self.profile_personal_assistant_manager)

        # Note: The following fields were removed due to translation issues:
        # - Emotional Intelligence Notes
        # - Life Motto / Quote
        # - Personal Goals (Outside Football)
        # - Parents' Occupations
        # - Siblings Summary
        # - Family Background Notes
        # - Pets Description
        # - Superstitions / Rituals
        # - Religious Practices Notes
        # - Political Views Summary
        # - Charity / Community Involvement
        # - Volunteer Work Summary
        # - Business Ventures
        # - Public Image Summary
        # - Media Availability Notes
        # - Home Address
        # - Retirement Plans Summary
        # - Biography
        # ---------------------------------------------------------- #

        profile_scroll_layout.addLayout(self.profile_form_layout)
        # --- List Widgets for multi-value items will go below the form --- #

        self.detail_tabs.addTab(profile_tab, self.tr("Profile"))
        # ------------------------ #

        # --- ADD: Selections Tab ---
        selections_tab = QWidget()
        selections_tab.setObjectName("selections_tab")  # Add object name for tab identification
        selections_main_layout = QVBoxLayout(selections_tab)
        selections_main_layout.setContentsMargins(5, 5, 5, 5)
        selections_main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Add instruction label
        self.selections_info_label = QLabel(self.tr("Select a player from the list"))
        self.selections_info_label.setStyleSheet("font-style: italic; color: grey;")
        selections_main_layout.addWidget(self.selections_info_label)

        # Add description label
        self.selections_description_label = QLabel(self.tr("Manage player selections for different purposes:"))
        selections_main_layout.addWidget(self.selections_description_label)

        # Create a form layout for the selections
        selections_form_layout = QFormLayout()
        selections_form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # Create a container widget to hold the selection checkboxes and reason fields
        self.selections_container = QWidget()
        selections_container_layout = QVBoxLayout(self.selections_container)
        selections_container_layout.setContentsMargins(0, 0, 0, 0)
        selections_container_layout.setSpacing(10)

        # Dictionary to store selection widgets
        self.selection_checkboxes = {}
        self.selection_reason_fields = {}

        # Create checkboxes and reason fields for each selection type
        from app.data.roster_manager import SELECTION_TYPES

        for selection_type in SELECTION_TYPES:
            # Create a group box for each selection type
            group_box = QGroupBox()
            group_layout = QVBoxLayout(group_box)

            # Create checkbox
            checkbox = QCheckBox(self.tr(selection_type.replace('_', ' ').title()))
            checkbox.setObjectName(f"{selection_type}_checkbox")
            group_layout.addWidget(checkbox)

            # Create reason field (initially hidden)
            reason_container = QWidget()
            reason_layout = QHBoxLayout(reason_container)
            reason_layout.setContentsMargins(20, 0, 0, 0)  # Indent the reason field

            reason_label = QLabel(self.tr("Reason:"))
            reason_field = QLineEdit()
            reason_field.setObjectName(f"{selection_type}_reason")
            reason_field.setPlaceholderText(self.tr("Enter reason for exclusion..."))

            reason_layout.addWidget(reason_label)
            reason_layout.addWidget(reason_field)

            group_layout.addWidget(reason_container)

            # Add attendance stats label for the attendance selection type
            if selection_type == 'attendance':
                stats_container = QWidget()
                stats_layout = QHBoxLayout(stats_container)
                stats_layout.setContentsMargins(20, 0, 0, 0)  # Indent the stats field

                stats_label = QLabel(self.tr("Stats:"))
                self.attendance_stats_label = QLabel("-")
                self.attendance_stats_label.setObjectName("attendance_stats_label")

                stats_layout.addWidget(stats_label)
                stats_layout.addWidget(self.attendance_stats_label)

                group_layout.addWidget(stats_container)

            # Store references to widgets
            self.selection_checkboxes[selection_type] = checkbox
            self.selection_reason_fields[selection_type] = reason_field

            # Initially hide the reason field
            reason_container.setVisible(False)

            # Connect checkbox state change to show/hide reason field
            checkbox.stateChanged.connect(lambda state, container=reason_container, field=reason_field:
                                         self._handle_selection_checkbox_changed(state, container, field))

            # Add the group box to the container
            selections_container_layout.addWidget(group_box)

        # Add Select All / Deselect All buttons
        buttons_layout = QHBoxLayout()
        self.select_all_selections_button = QPushButton(self.tr("Select All"))
        self.deselect_all_selections_button = QPushButton(self.tr("Deselect All"))

        buttons_layout.addWidget(self.select_all_selections_button)
        buttons_layout.addWidget(self.deselect_all_selections_button)
        buttons_layout.addStretch()

        # Connect buttons
        self.select_all_selections_button.clicked.connect(self._select_all_selections)
        self.deselect_all_selections_button.clicked.connect(self._deselect_all_selections)

        # Add the container and buttons to the main layout
        selections_main_layout.addWidget(self.selections_container)
        selections_main_layout.addLayout(buttons_layout)
        selections_main_layout.addStretch()

        self.detail_tabs.addTab(selections_tab, self.tr("Selections"))
        # ------------------------ #

        # --- ADD: Consolidated player_detail_widgets Update --- #
        self.player_detail_widgets.update({
            # Personal Tab Widgets
            'player_id': self.player_id_label,
            'last_name': self.last_name_input,
            'first_name': self.first_name_input,
            'shirt_number': self.shirt_number_spinbox,
            'position': self.position_combo,
            'detailed_position': self.detailed_pos_input,
            'dob': self.dob_edit,
            'age': self.age_label,
            'nationality': self.nationality_combo,
            'flag_preview': self.flag_preview_label,
            'zone': self.zone_label,
            'sex': self.sex_combo,
            'preferred_foot': self.preferred_foot_combo,
            'strong_eye': self.strong_eye_combo,
            'primary_group_id': self.primary_group_combo,
            'secondary_group_id': self.secondary_group_combo,
            # Physical Tab Widgets
            'height': self.height_spinbox,
            'weight': self.weight_spinbox,
            'waist': self.waist_spinbox,
            'hip': self.hip_spinbox,
            'neck': self.neck_spinbox,
            'wrist': self.wrist_spinbox,
            'forearm': self.forearm_spinbox,
            'thigh': self.thigh_spinbox,
            'fitness': self.fitness_combo,
             # Status Tab Widgets
            'status_tags_list': self.status_tags_list_widget,
            'transfer_status': self.transfer_status_combo,
            # Transfer Detail Widgets (already added individually in Status Tab creation)
            **self.transfer_detail_widgets, # Unpack the dict created for transfer details
            # Profile Tab Widgets
            'date_joined_club': self.profile_date_joined_club,
            'personality_type': self.profile_personality_type,
            'biography': self.profile_biography,
            # Add other profile widgets here eventually...
            # --- ADD: Mappings for new profile widgets --- #
            'mentality': self.profile_mentality,
            'work_ethic': self.profile_work_ethic,
            'professionalism_level': self.profile_professionalism_level,
            'determination_level': self.profile_determination_level,
            'team_spirit': self.profile_team_spirit,
            'adaptability': self.profile_adaptability,
            'temperament': self.profile_temperament,
            'ambition_level': self.profile_ambition_level,
            'leadership_qualities': self.profile_leadership_qualities,
            'charisma': self.profile_charisma,
            # Re-added previously problematic fields with proper translations
            'emotional_intelligence_notes': self.profile_emotional_intelligence_notes,
            'life_motto_quote': self.profile_life_motto_quote,
            'personal_goals_outside_football': self.profile_personal_goals_outside_football,
            'hometown': self.profile_hometown,
            'current_residence_city_area': self.profile_current_residence_city_area,
            'marital_status': self.profile_marital_status,
            'partner_name': self.profile_partner_name,
            'father_name': self.profile_father_name,
            'mother_name': self.profile_mother_name,
            'parents_occupations': self.profile_parents_occupations,
            'siblings_summary': self.profile_siblings_summary,
            'family_background_notes': self.profile_family_background_notes,
            'number_of_children': self.profile_number_of_children,
            'pets_description': self.profile_pets_description,
            'superstitions_rituals': self.profile_superstitions_rituals,
            'religion_faith': self.profile_religion_faith,
            'religious_practices_notes': self.profile_religious_practices_notes,
            'political_views_summary': self.profile_political_views_summary,
            'charity_community_involvement': self.profile_charity_community_involvement,
            'volunteer_work_summary': self.profile_volunteer_work_summary,
            'education_level': self.profile_education_level,
            'field_of_study': self.profile_field_of_study,
            'university_school_attended': self.profile_university_school_attended,
            'dual_career_other_job': self.profile_dual_career_other_job,
            'business_ventures': self.profile_business_ventures,
            'willingness_to_coach_mentor': self.profile_willingness_to_coach_mentor,
            'public_image_summary': self.profile_public_image_summary,
            'media_friendliness': self.profile_media_friendliness,
            'media_availability_notes': self.profile_media_availability_notes,
            'website_blog': self.profile_website_blog,
            'personal_contact_email': self.profile_personal_contact_email,
            'personal_phone_number': self.profile_personal_phone_number,
            'home_address': self.profile_home_address,
            'agent_name': self.profile_agent_name,
            'personal_assistant_manager': self.profile_personal_assistant_manager,
            'retirement_plans_summary': self.profile_retirement_plans_summary,
            # Selections Tab Widgets
            **{f"{selection_type}_checkbox": checkbox for selection_type, checkbox in self.selection_checkboxes.items()},
            **{f"{selection_type}_reason": reason_field for selection_type, reason_field in self.selection_reason_fields.items()},
        })
        # ------------------------------------------------------ #

    def create_right_panel(self, layout):
        """Creates the right panel with the main player table and controls."""

        # --- Button Controls (Top Right) ---
        button_layout = QHBoxLayout()
        self.add_button = QPushButton(self.tr("Add Player"))
        self.remove_button = QPushButton(self.tr("Remove Player"))
        self.remove_button.setEnabled(False) # Disable initially
        self.select_basic_button = QPushButton(self.tr("Select Basic")) # ADDED
        self.select_extended_button = QPushButton(self.tr("Select Extended")) # ADDED
        self.select_all_button = QPushButton(self.tr("Select All")) # ADDED
        # Add test button for outlier detection
        self.test_outlier_button = QPushButton(self.tr("Test Outliers"))
        self.test_outlier_button.setToolTip(self.tr("Manually trigger outlier detection"))
        button_layout.addStretch() # Push buttons to the right
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.remove_button)
        button_layout.addWidget(self.select_basic_button) # ADDED
        button_layout.addWidget(self.select_extended_button) # ADDED
        button_layout.addWidget(self.select_all_button) # ADDED
        button_layout.addWidget(self.test_outlier_button)
        layout.addLayout(button_layout) # Add button layout FIRST

        # --- Filter Controls (Below Buttons, Above Table) ---
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 5, 0, 5) # Add vertical spacing

        # Global Search Input
        self.global_search_input = QLineEdit()
        self.global_search_input.setPlaceholderText(self.tr("Search all columns..."))
        self.global_search_input.setClearButtonEnabled(True)
        filter_layout.addWidget(self.global_search_input, 1) # Stretch factor 1
        filter_layout.addSpacing(10) # Add spacing after search

        # Helper function to create filter combos
        def create_filter_combo(items, placeholder):
            combo = QComboBox()
            combo.addItem(self.tr(placeholder), "") # Store "" for "All"
            for item in items:
                if isinstance(item, tuple): # Handle (name, id) like groups
                    combo.addItem(item[0], item[0]) # Store NAME as data for filtering
                else:
                    combo.addItem(str(item), str(item)) # Store string itself
            combo.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
            return combo

        # Create and Add Filter Combo Boxes
        self.filter_pos_combo = create_filter_combo(FILTER_POSITIONS, "Position: All")
        self.filter_pgroup_combo = create_filter_combo(self._group_options, "P. Group: All")
        self.filter_sgroup_combo = create_filter_combo(self._group_options, "S. Group: All")
        defined_zone_names = self.roster_manager.get_defined_zone_names()
        self.logger.debug(f"Zones from manager for filter combo: {defined_zone_names}") # MODIFIED print to logger.debug
        self.filter_zone_combo = create_filter_combo(defined_zone_names, "Zone: All")
        self.filter_foot_combo = create_filter_combo(FILTER_FEET, "Foot: All")
        self.filter_sex_combo = create_filter_combo(FILTER_SEX, "Sex: All")

        filter_layout.addWidget(self.filter_pos_combo)
        filter_layout.addWidget(self.filter_pgroup_combo)
        filter_layout.addWidget(self.filter_sgroup_combo)
        filter_layout.addWidget(self.filter_zone_combo)
        filter_layout.addWidget(self.filter_foot_combo)
        filter_layout.addWidget(self.filter_sex_combo)

        # Add the filter layout SECOND (below buttons)
        layout.addLayout(filter_layout)
        # -------------------------------------------------------

        # --- Table View Setup (Remains the same) ---
        self.roster_table_view = QTableView()
        self.roster_table_model = QStandardItemModel(0, 32) # Updated to include selection columns
        self.table_headers = {
            0: ('player_id', self.tr('ID')),
            1: ('last_name', self.tr('Last Name')),
            2: ('first_name', self.tr('Name')),
            3: ('shirt_number', self.tr('No.')),
            4: ('position', self.tr('Pos.')),
            5: ('detailed_position', self.tr('Detailed Pos.')),
            6: ('dob', self.tr('Date of Birth')),
            7: ('age', self.tr('Age')),
            8: ('height', self.tr('Height')),
            9: ('weight', self.tr('Weight')),
            10: ('waist', self.tr('Waist')),
            11: ('hip', self.tr('Hip')),
            12: ('neck', self.tr('Neck')),
            13: ('wrist', self.tr('Wrist')),
            14: ('forearm', self.tr('Forearm')),
            15: ('thigh', self.tr('Thigh')),
            16: ('nationality', self.tr('Nationality')),
            17: ('flag', self.tr('Flag')),
            18: ('sex', self.tr('Sex')),
            19: ('preferred_foot', self.tr('Pref. Foot')),
            20: ('strong_eye', self.tr('Strong Eye')),
            21: ('zone', self.tr('Zone')),
            22: ('fitness', self.tr('Fitness')),
            23: ('primary_group_id', self.tr('Primary Group')),
            24: ('secondary_group_id', self.tr('Secondary Group')),
            25: ('status_tags', self.tr('Status Tags')),
            26: ('transfer_status', self.tr('Transfer Status')),
            # Add selection columns
            27: ('selection_attendance', self.tr('Attendance')),
            28: ('selection_squad_list', self.tr('Squad List')),
            29: ('selection_match_day', self.tr('Match Day')),
            30: ('selection_evaluation', self.tr('Evaluation')),
            31: ('selection_physical_tests', self.tr('Physical Tests'))
        }
        self.roster_table_model.setHorizontalHeaderLabels([h[1] for h in self.table_headers.values()])
        self.roster_table_view.setModel(self.roster_table_model)
        self.roster_table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.roster_table_view.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.roster_table_view.setEditTriggers(QAbstractItemView.EditTrigger.AllEditTriggers)
        self.roster_table_view.verticalHeader().setVisible(False)
        self.roster_table_view.setSortingEnabled(True)
        self.roster_table_view.horizontalHeader().setStretchLastSection(True)
        header = self.roster_table_view.horizontalHeader()
        header.setSectionsMovable(True)
        header.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        # Column width settings...
        self.roster_table_view.setColumnWidth(0, 40) # ID
        self.roster_table_view.setColumnWidth(3, 40) # No.
        self.roster_table_view.setColumnWidth(4, 50) # Pos.
        self.roster_table_view.setColumnWidth(7, 40) # Age
        self.roster_table_view.setColumnWidth(8, 55) # Height (reduced from 60)
        self.roster_table_view.setColumnWidth(9, 55) # Weight (reduced from 60)
        self.roster_table_view.setColumnWidth(10, 50) # Waist (reduced from 60)
        self.roster_table_view.setColumnWidth(11, 50) # Hip (reduced from 60)
        self.roster_table_view.setColumnWidth(12, 50) # Neck (reduced from 60)
        self.roster_table_view.setColumnWidth(13, 50) # Wrist (reduced from 60)
        self.roster_table_view.setColumnWidth(14, 55) # Forearm (reduced from 60)
        self.roster_table_view.setColumnWidth(15, 50) # Thigh (reduced from 60)
        self.roster_table_view.setColumnWidth(16, 100) # Nationality
        self.roster_table_view.setColumnWidth(17, 50) # Flag (ADDED)
        self.roster_table_view.setColumnWidth(18, 60) # Sex
        self.roster_table_view.setColumnWidth(19, 75) # Pref. Foot
        self.roster_table_view.setColumnWidth(20, 75) # Strong Eye
        self.roster_table_view.setColumnWidth(21, 80) # Zone
        self.roster_table_view.setColumnWidth(22, 100) # Fitness (Adjust as needed)
        self.roster_table_view.setColumnWidth(23, 120) # Primary Group
        self.roster_table_view.setColumnWidth(24, 120) # Secondary Group
        self.roster_table_view.setColumnWidth(25, 150) # ADDED Status Tags width (adjust as needed)
        self.roster_table_view.setColumnWidth(26, 120) # ADDED Transfer Status width (adjust as needed)
        # --------------------------------------------
        delegate = RosterDelegate(self.table_headers, self._group_options, self.roster_table_view)
        self.roster_table_view.setItemDelegate(delegate)
        # --------------------------------------------

        # Add table view THIRD (below filters)
        layout.addWidget(self.roster_table_view)
        self.roster_table_view.verticalHeader().setDefaultSectionSize(30) # Keep row height

        # --- Filter layout is now added above, no need to add again ---

    def _load_position_pitch_image(self):
        """Load the position pitch image if it exists."""
        try:
            # Get the path to the position pitch image
            app_dir = Path(os.path.dirname(os.path.abspath(sys.argv[0])))
            image_path = app_dir / "media" / "pitch" / "roster_position_pitch.png"

            if image_path.exists():
                pixmap = QPixmap(str(image_path))
                if not pixmap.isNull():
                    self.pitch_frame.setPixmap(pixmap.scaled(
                        self.pitch_frame.width(),
                        self.pitch_frame.height(),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    ))
                    self.logger.info(f"Loaded position pitch image from {image_path}")
                else:
                    self.logger.warning(f"Failed to load position pitch image from {image_path} - pixmap is null")
            else:
                self.logger.info(f"Position pitch image not found at {image_path}")
        except Exception as e:
            self.logger.error(f"Error loading position pitch image: {e}")

    def _load_player_positions(self, player_id):
        """Load position ratings for the selected player.

        Args:
            player_id (int): The ID of the player to load positions for.
        """
        if not player_id:
            self.logger.info("No player selected, clearing position grid")
            self.position_grid.clear_grid()
            return

        # Get position data from the database
        positions = self.roster_manager.get_player_positions(player_id)
        self.logger.info(f"Loaded {len(positions)} position ratings for player {player_id}: {positions}")

        # Update the position grid
        self.position_grid.set_grid_data(positions)

    def _on_position_changed(self, grid_x, grid_y, rating):
        """Handle position rating changes from the grid widget.

        Args:
            grid_x (int): The x-coordinate in the grid.
            grid_y (int): The y-coordinate in the grid.
            rating (int): The new rating value (0-3).
        """
        # Get the currently selected player
        player_id = self.current_selected_player_id
        if not player_id:
            return

        self.logger.info(f"Position changed for player {player_id}: ({grid_x}, {grid_y}) = {rating}")

        if rating > 0:
            # Set the position rating
            success = self.roster_manager.set_player_position(player_id, grid_x, grid_y, rating)
            if success:
                self.logger.info(f"Successfully set position rating for player {player_id} at ({grid_x}, {grid_y}) to {rating}")
            else:
                self.logger.error(f"Failed to set position rating for player {player_id} at ({grid_x}, {grid_y})")
        else:
            # Clear the position rating
            success = self.roster_manager.clear_player_position(player_id, grid_x, grid_y)
            if success:
                self.logger.info(f"Successfully cleared position rating for player {player_id} at ({grid_x}, {grid_y})")
            else:
                self.logger.error(f"Failed to clear position rating for player {player_id} at ({grid_x}, {grid_y})")

        # Debug: Dump the contents of the player_positions table
        self.roster_manager.debug_dump_player_positions_table()

        # Force reload the positions to ensure the grid is up to date
        self._load_player_positions(player_id)

    def _clear_all_position_ratings(self):
        """Clear all position ratings for the currently selected player."""
        player_id = self.current_selected_player_id
        if not player_id:
            self.logger.warning("Cannot clear position ratings: No player selected")
            return

        # Ask for confirmation
        confirm = QMessageBox.question(
            self,
            self.tr("Clear All Ratings"),
            self.tr("Are you sure you want to clear all position ratings for this player?"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if confirm == QMessageBox.StandardButton.Yes:
            # Clear all positions from the database
            success = self.roster_manager.clear_all_player_positions(player_id)

            if success:
                self.logger.info(f"Successfully cleared all position ratings for player {player_id}")
                # Clear the grid display
                self.position_grid.clear_grid()
                # Debug: Dump the contents of the player_positions table
                self.roster_manager.debug_dump_player_positions_table()
            else:
                self.logger.error(f"Failed to clear position ratings for player {player_id}")
                QMessageBox.warning(
                    self,
                    self.tr("Error"),
                    self.tr("Failed to clear position ratings. Please try again.")
                )

    def _setup_connections(self):
        """Connect signals and slots."""
        self.splitter.splitterMoved.connect(self.save_splitter_state)
        self.roster_table_view.selectionModel().selectionChanged.connect(self._update_tabs_on_selection)

        # Check for position pitch image updates
        settings = QSettings()
        if settings.value("roster/position_pitch_updated", False, type=bool):
            self._load_position_pitch_image()
            settings.setValue("roster/position_pitch_updated", False)  # Reset the flag

        # Connect tab change signal to update translations when switching to Profile tab
        self.detail_tabs.currentChanged.connect(self._on_tab_changed)

        # Connect selection_changed signal to our own handler
        self.selection_changed.connect(self.on_selection_changed)

        # Connect selection checkboxes to update player selections
        for selection_type, checkbox in self.selection_checkboxes.items():
            checkbox.stateChanged.connect(lambda state, st=selection_type: self._update_player_selection(st, bool(state)))

        # Connect tab detail widgets to update model/manager
        # --- Personal Tab --- #
        self.last_name_input.editingFinished.connect(lambda: self._update_player_data('last_name', self.last_name_input.text()))
        self.first_name_input.editingFinished.connect(lambda: self._update_player_data('first_name', self.first_name_input.text()))
        self.shirt_number_spinbox.editingFinished.connect(lambda: self._update_player_data('shirt_number', self.shirt_number_spinbox.value() if self.shirt_number_spinbox.value() != 0 else None))
        self.position_combo.currentTextChanged.connect(lambda text: self._update_player_data('position', text if text else None))
        self.detailed_pos_input.editingFinished.connect(lambda: self._update_player_data('detailed_position', self.detailed_pos_input.text()))
        self.dob_edit.dateChanged.connect(lambda qdate: self._update_player_data('dob', qdate.toString("yyyy-MM-dd") if qdate.isValid() else None))
        self.nationality_combo.currentTextChanged.connect(lambda text: self._update_player_data('nationality', text if text else None))
        self.sex_combo.currentTextChanged.connect(lambda text: self._update_player_data('sex', text if text else None))
        self.preferred_foot_combo.currentTextChanged.connect(lambda text: self._update_player_data('preferred_foot', text if text else None))
        self.strong_eye_combo.currentTextChanged.connect(lambda text: self._update_player_data('strong_eye', text if text else None))
        self.primary_group_combo.currentIndexChanged.connect(lambda index: self._update_player_data('primary_group_id', self.primary_group_combo.currentData()))
        self.secondary_group_combo.currentIndexChanged.connect(lambda index: self._update_player_data('secondary_group_id', self.secondary_group_combo.currentData()))

        # --- Physical Tab --- #
        self.height_spinbox.valueChanged.connect(lambda val: self._update_player_data('height', val if val > 0 else None))
        self.weight_spinbox.valueChanged.connect(lambda val: self._update_player_data('weight', val if val > 0 else None))
        # --- ADD MISSING PHYSICAL CONNECTIONS --- #
        self.waist_spinbox.valueChanged.connect(lambda val: self._update_player_data('waist', val if val > 0 else None))
        self.hip_spinbox.valueChanged.connect(lambda val: self._update_player_data('hip', val if val > 0 else None))
        self.neck_spinbox.valueChanged.connect(lambda val: self._update_player_data('neck', val if val > 0 else None))
        self.wrist_spinbox.valueChanged.connect(lambda val: self._update_player_data('wrist', val if val > 0 else None))
        self.forearm_spinbox.valueChanged.connect(lambda val: self._update_player_data('forearm', val if val > 0 else None))
        self.thigh_spinbox.valueChanged.connect(lambda val: self._update_player_data('thigh', val if val > 0 else None))
        self.fitness_combo.currentTextChanged.connect(lambda text: self._update_player_data('fitness', text if text else None))
        # ---------------------------------------- #

        # --- ADD CONNECTIONS FOR REAL-TIME METRIC RECALCULATION --- #
        self.height_spinbox.valueChanged.connect(lambda: self._recalculate_and_display_metrics(self.current_selected_player_id))
        self.weight_spinbox.valueChanged.connect(lambda: self._recalculate_and_display_metrics(self.current_selected_player_id))
        self.waist_spinbox.valueChanged.connect(lambda: self._recalculate_and_display_metrics(self.current_selected_player_id))
        self.hip_spinbox.valueChanged.connect(lambda: self._recalculate_and_display_metrics(self.current_selected_player_id))
        self.neck_spinbox.valueChanged.connect(lambda: self._recalculate_and_display_metrics(self.current_selected_player_id))
        # Connect Sex combo (defined in Personal Tab setup, signal connected here)
        self.sex_combo.currentIndexChanged.connect(lambda: self._recalculate_and_display_metrics(self.current_selected_player_id))
        # ---------------------------------------------------------- #

        # --- Status Tab --- #
        self.status_tags_list_widget.itemChanged.connect(self._handle_status_tag_changed)
        self.transfer_status_combo.currentTextChanged.connect(self._handle_transfer_status_changed)
        # Connect transfer detail field signals
        for key, widget in self.transfer_detail_widgets.items():
            if isinstance(widget, QDateEdit):
                widget.dateChanged.connect(lambda qdate, k=key: self._update_player_data(k, qdate.toString("yyyy-MM-dd") if qdate.isValid() else None))
            elif isinstance(widget, QLineEdit):
                widget.editingFinished.connect(lambda k=key, w=widget: self._update_player_data(k, w.text() if w.text() else None))
        # --------------------------------- #

        # Connect Add/Remove buttons
        self.add_button.clicked.connect(self._add_player)
        self.remove_button.clicked.connect(self._remove_player)
        self.select_basic_button.clicked.connect(self._select_basic_columns) # ADDED connection
        self.select_extended_button.clicked.connect(self._select_extended_columns) # ADDED connection
        self.select_all_button.clicked.connect(self._select_all_columns) # ADDED connection
        self.test_outlier_button.clicked.connect(self._test_outlier_detection) # ADDED connection

        # Connect table model change signal for synchronization
        self.roster_table_model.itemChanged.connect(self._handle_table_item_changed)

        # Connect header signals
        header = self.roster_table_view.horizontalHeader()
        header.sectionMoved.connect(self._save_header_state)
        header.sectionResized.connect(self._save_header_state)
        header.customContextMenuRequested.connect(self._show_header_context_menu)

        # --- ADD: Connect Chart Button --- #
        self.view_chart_button.clicked.connect(self._show_physical_chart)
        # ------------------------------- #

        # --- NEW FILTERING APPROACH: Connect Filter Widgets to _apply_filters --- #
        self.global_search_input.textChanged.connect(self._apply_filters)
        self.filter_pos_combo.currentIndexChanged.connect(self._apply_filters)
        self.filter_pgroup_combo.currentIndexChanged.connect(self._apply_filters)
        self.filter_sgroup_combo.currentIndexChanged.connect(self._apply_filters)
        self.filter_zone_combo.currentIndexChanged.connect(self._apply_filters)
        self.filter_foot_combo.currentIndexChanged.connect(self._apply_filters)
        self.filter_sex_combo.currentIndexChanged.connect(self._apply_filters)
        # ----------------------------------------------------------------------- #

        # --- ADD: Install event filter on image label --- #
        if hasattr(self, 'player_image_label'): # Ensure label exists
            self.player_image_label.installEventFilter(self)
        # ------------------------------------------------ #

        # --- ADD: Connect Profile Tab Widgets --- #
        # For simple fields, update the specific key in the player_profile table
        self.profile_date_joined_club.dateChanged.connect(
            lambda qdate: self._save_single_profile_field('date_joined_club', qdate.toString("yyyy-MM-dd") if qdate.isValid() else None)
        )
        self.profile_personality_type.editingFinished.connect(
            lambda: self._save_single_profile_field('personality_type', self.profile_personality_type.text())
        )
        # QTextEdit doesn't have editingFinished, textChanged might be too frequent.
        # Option 1: Use textChanged (frequent saves)
        # self.profile_biography.textChanged.connect(
        #     lambda: self._save_single_profile_field('biography', self.profile_biography.toPlainText())
        # )
        # Option 2: Add a Save button for the bio (or the whole tab)? Requires UI change.
        # Option 3 (Chosen): Save when focus is lost (less frequent than textChanged)
        # We'll need to handle this using an event filter later if desired,
        # for now, let's connect textChanged but be aware it saves on every keystroke.
        self.profile_biography.textChanged.connect(
             lambda: self._save_single_profile_field('biography', self.profile_biography.toPlainText())
        )
        # --- Add connections for other profile widgets here --- #
        # ---------------------------------------- #

        # --- ADD: Connect signals for new profile widgets --- #
        self.profile_mentality.editingFinished.connect(lambda: self._save_single_profile_field('mentality', self.profile_mentality.text()))
        self.profile_work_ethic.editingFinished.connect(lambda: self._save_single_profile_field('work_ethic', self.profile_work_ethic.text()))
        self.profile_professionalism_level.editingFinished.connect(lambda: self._save_single_profile_field('professionalism_level', self.profile_professionalism_level.text()))
        self.profile_determination_level.editingFinished.connect(lambda: self._save_single_profile_field('determination_level', self.profile_determination_level.text()))
        self.profile_team_spirit.editingFinished.connect(lambda: self._save_single_profile_field('team_spirit', self.profile_team_spirit.text()))
        self.profile_adaptability.editingFinished.connect(lambda: self._save_single_profile_field('adaptability', self.profile_adaptability.text()))
        self.profile_temperament.editingFinished.connect(lambda: self._save_single_profile_field('temperament', self.profile_temperament.text()))
        self.profile_ambition_level.editingFinished.connect(lambda: self._save_single_profile_field('ambition_level', self.profile_ambition_level.text()))
        self.profile_leadership_qualities.editingFinished.connect(lambda: self._save_single_profile_field('leadership_qualities', self.profile_leadership_qualities.text()))
        self.profile_charisma.editingFinished.connect(lambda: self._save_single_profile_field('charisma', self.profile_charisma.text()))
        # Re-added connections for previously problematic fields
        self.profile_emotional_intelligence_notes.textChanged.connect(lambda: self._save_single_profile_field('emotional_intelligence_notes', self.profile_emotional_intelligence_notes.toPlainText()))
        self.profile_life_motto_quote.textChanged.connect(lambda: self._save_single_profile_field('life_motto_quote', self.profile_life_motto_quote.toPlainText()))
        self.profile_personal_goals_outside_football.textChanged.connect(lambda: self._save_single_profile_field('personal_goals_outside_football', self.profile_personal_goals_outside_football.toPlainText()))
        self.profile_hometown.editingFinished.connect(lambda: self._save_single_profile_field('hometown', self.profile_hometown.text()))
        self.profile_current_residence_city_area.editingFinished.connect(lambda: self._save_single_profile_field('current_residence_city_area', self.profile_current_residence_city_area.text()))
        self.profile_marital_status.editingFinished.connect(lambda: self._save_single_profile_field('marital_status', self.profile_marital_status.text()))
        self.profile_partner_name.editingFinished.connect(lambda: self._save_single_profile_field('partner_name', self.profile_partner_name.text()))
        self.profile_father_name.editingFinished.connect(lambda: self._save_single_profile_field('father_name', self.profile_father_name.text()))
        self.profile_mother_name.editingFinished.connect(lambda: self._save_single_profile_field('mother_name', self.profile_mother_name.text()))
        self.profile_parents_occupations.textChanged.connect(lambda: self._save_single_profile_field('parents_occupations', self.profile_parents_occupations.toPlainText()))
        self.profile_siblings_summary.textChanged.connect(lambda: self._save_single_profile_field('siblings_summary', self.profile_siblings_summary.toPlainText()))
        self.profile_family_background_notes.textChanged.connect(lambda: self._save_single_profile_field('family_background_notes', self.profile_family_background_notes.toPlainText()))
        self.profile_number_of_children.valueChanged.connect(lambda val: self._save_single_profile_field('number_of_children', val))
        self.profile_pets_description.textChanged.connect(lambda: self._save_single_profile_field('pets_description', self.profile_pets_description.toPlainText()))
        self.profile_superstitions_rituals.textChanged.connect(lambda: self._save_single_profile_field('superstitions_rituals', self.profile_superstitions_rituals.toPlainText()))
        self.profile_religion_faith.editingFinished.connect(lambda: self._save_single_profile_field('religion_faith', self.profile_religion_faith.text()))
        self.profile_religious_practices_notes.textChanged.connect(lambda: self._save_single_profile_field('religious_practices_notes', self.profile_religious_practices_notes.toPlainText()))
        self.profile_political_views_summary.textChanged.connect(lambda: self._save_single_profile_field('political_views_summary', self.profile_political_views_summary.toPlainText()))
        self.profile_charity_community_involvement.textChanged.connect(lambda: self._save_single_profile_field('charity_community_involvement', self.profile_charity_community_involvement.toPlainText()))
        self.profile_volunteer_work_summary.textChanged.connect(lambda: self._save_single_profile_field('volunteer_work_summary', self.profile_volunteer_work_summary.toPlainText()))
        self.profile_education_level.editingFinished.connect(lambda: self._save_single_profile_field('education_level', self.profile_education_level.text()))
        self.profile_field_of_study.editingFinished.connect(lambda: self._save_single_profile_field('field_of_study', self.profile_field_of_study.text()))
        self.profile_university_school_attended.editingFinished.connect(lambda: self._save_single_profile_field('university_school_attended', self.profile_university_school_attended.text()))
        self.profile_dual_career_other_job.editingFinished.connect(lambda: self._save_single_profile_field('dual_career_other_job', self.profile_dual_career_other_job.text()))
        self.profile_business_ventures.textChanged.connect(lambda: self._save_single_profile_field('business_ventures', self.profile_business_ventures.toPlainText()))
        self.profile_willingness_to_coach_mentor.editingFinished.connect(lambda: self._save_single_profile_field('willingness_to_coach_mentor', self.profile_willingness_to_coach_mentor.text()))
        self.profile_public_image_summary.textChanged.connect(lambda: self._save_single_profile_field('public_image_summary', self.profile_public_image_summary.toPlainText()))
        self.profile_media_friendliness.editingFinished.connect(lambda: self._save_single_profile_field('media_friendliness', self.profile_media_friendliness.text()))
        self.profile_media_availability_notes.textChanged.connect(lambda: self._save_single_profile_field('media_availability_notes', self.profile_media_availability_notes.toPlainText()))
        self.profile_website_blog.editingFinished.connect(lambda: self._save_single_profile_field('website_blog', self.profile_website_blog.text()))
        self.profile_personal_contact_email.editingFinished.connect(lambda: self._save_single_profile_field('personal_contact_email', self.profile_personal_contact_email.text()))
        self.profile_personal_phone_number.editingFinished.connect(lambda: self._save_single_profile_field('personal_phone_number', self.profile_personal_phone_number.text()))
        self.profile_home_address.textChanged.connect(lambda: self._save_single_profile_field('home_address', self.profile_home_address.toPlainText()))
        self.profile_agent_name.editingFinished.connect(lambda: self._save_single_profile_field('agent_name', self.profile_agent_name.text()))
        self.profile_personal_assistant_manager.editingFinished.connect(lambda: self._save_single_profile_field('personal_assistant_manager', self.profile_personal_assistant_manager.text()))
        self.profile_retirement_plans_summary.textChanged.connect(lambda: self._save_single_profile_field('retirement_plans_summary', self.profile_retirement_plans_summary.toPlainText()))
        # ---------------------------------------------------- #
        # ---------------------------------------- #

    def load_data(self):
        """Loads player data from the manager into the table model."""
        self.logger.info("Loading roster data...") # MODIFIED print to logger.info
        self.roster_table_model.blockSignals(True)
        players = self.roster_manager.get_all_players()
        self.logger.info(f"Fetched {len(players)} players from RosterManager.") # MODIFIED print to logger.info
        self.roster_table_model.setRowCount(0)
        self.roster_table_model.setRowCount(len(players))

        # --- Create ID to Name map from loaded options --- # ADDED
        group_id_to_name = {gid: name for name, gid in self._group_options}
        # ----------------------------------------------- #

        for row, player in enumerate(players):
            # --- Get Status Summary --- #
            status_summary = self._get_status_tags_summary(player)
            # -------------------------- #
            transfer_status_text = player.get('transfer_status', "") # Get transfer status

            for col, (key, _) in self.table_headers.items():
                item = None
                edit_value = player.get(key) # Get raw value for EditRole
                display_text = "" # Initialize display text

                try:
                    if key == 'transfer_status': # --- Handle New Transfer Status Column --- #
                        item = QStandardItem(self.tr(transfer_status_text) if transfer_status_text else "")
                        item.setEditable(False)
                        display_text = transfer_status_text
                        edit_value = transfer_status_text
                    elif key == 'status_tags': # --- Handle New Column --- #
                        item = QStandardItem(status_summary)
                        item.setEditable(False)
                        item.setToolTip(self.tr("Summary of selected status tags."))
                        display_text = status_summary
                        edit_value = status_summary # Store display text if not editable
                    elif key == 'flag':
                        # ... (existing flag logic, sets decoration role) ...
                        item = QStandardItem()
                        nationality = player.get('nationality')
                        flag_path = self._get_flag_path(nationality)
                        item.setEditable(False)
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        if flag_path:
                            item.setData(QIcon(flag_path), Qt.ItemDataRole.DecorationRole)
                        edit_value = None # No edit value for flag
                    elif key == 'zone':
                        # ... (existing zone logic) ...
                        nationality = player.get('nationality')
                        zone_name = get_nationality_zone(nationality)
                        item = QStandardItem(zone_name)
                        item.setEditable(False)
                        item.setToolTip(self.tr("Determined by nationality based on configured zones."))
                        display_text = zone_name
                        edit_value = zone_name # Store zone name for potential future use?
                    elif key.startswith('selection_'):
                        # Handle selection columns
                        selection_type = key.replace('selection_', '')
                        player_id = player.get('player_id')
                        selection_data = self.roster_manager.get_player_selection(player_id, selection_type)

                        item = QStandardItem()
                        if selection_data:
                            selected = selection_data.get('selected', False)
                            reason = selection_data.get('reason', '')

                            if selected:
                                item.setText("✓")
                                item.setToolTip(self.tr("Selected"))
                                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                                item.setForeground(QBrush(QColor("green")))
                            else:
                                item.setText("✗")
                                tooltip = self.tr("Not selected")
                                if reason:
                                    tooltip += f": {reason}"
                                item.setToolTip(tooltip)
                                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                                item.setForeground(QBrush(QColor("red")))
                        else:
                            # No selection data yet
                            item.setText("-")
                            item.setToolTip(self.tr("No selection data"))
                            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                        item.setEditable(False)
                        display_text = item.text()
                        edit_value = display_text
                    elif key in ['primary_group_id', 'secondary_group_id']:
                        group_id = player.get(key)
                        group_name = group_id_to_name.get(group_id, "") if group_id is not None else ""
                        display_text = group_name if group_name else self.tr("(None)")
                        # --- MODIFIED: Create empty item and set roles explicitly --- #
                        item = QStandardItem() # Create empty item
                        item.setData(display_text, Qt.ItemDataRole.DisplayRole) # Set Name for Display
                        item.setData(group_id, Qt.ItemDataRole.EditRole)      # Set ID for Edit/Filter
                        # ----------------------------------------------------------- #
                        edit_value = group_id # edit_value is the ID
                    else:
                        # Default handling for other columns
                        value = player.get(key)
                        display_text = self._format_value_for_display(key, value)
                        item = QStandardItem(display_text) # Sets DisplayRole
                        if key == 'player_id':
                            player_id_value = player.get('player_id') # Use .get for safety
                            # --- DEBUG PRINT --- #
                            self.logger.debug(f"Setting UserRole+1 for row {row}, player_id={player_id_value} (type: {type(player_id_value)})") # MODIFIED print to logger.debug
                            # ----------------- #
                            if player_id_value is not None:
                                item.setData(player_id_value, Qt.ItemDataRole.UserRole + 1)
                            else:
                                self.logger.error(f"player_id is None for row {row}!") # MODIFIED print to logger.error

                        # Set non-editable status and EditRole value based on key
                        non_editable_keys = {'player_id', 'age', 'zone', 'status_tags', 'transfer_status'}
                        if key in non_editable_keys:
                             item.setEditable(False)
                             edit_value = display_text
                        else:
                             item.setData(value, Qt.ItemDataRole.EditRole)
                             edit_value = value
                except Exception as e:
                    # ... (existing error handling) ...
                    item = QStandardItem("Error")
                    item.setEditable(False)
                    display_text = "Error"
                    edit_value = None

                # --- Set the item (either successfully created or Error item) ---
                if item is not None:
                    # Ensure EditRole is set if not done above (redundancy for safety)
                    if item.data(Qt.ItemDataRole.EditRole) is None and edit_value is not None:
                         item.setData(edit_value, Qt.ItemDataRole.EditRole)

                    # --- Set text alignment: Center for all columns except last_name and first_name ---
                    if key not in ['last_name', 'first_name']:
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    # Left alignment is default for last_name and first_name, so no need to set explicitly

                    self.roster_table_model.setItem(row, col, item)
                else:
                    # Fallback if item is still None
                    self.logger.warning(f"Item remained None for row {row}, col {col} (key: {key}). Setting fallback.") # MODIFIED print to logger.warning
                    fallback_item = QStandardItem("?")
                    fallback_item.setEditable(False)

                    # --- Set text alignment for fallback item too ---
                    if key not in ['last_name', 'first_name']:
                        fallback_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                    self.roster_table_model.setItem(row, col, fallback_item)
            # End of for col loop
        # End of for row loop <--- THIS IS THE CORRECT LEVEL FOR THE NEXT BLOCK

        # --- This block should be aligned with the start of the 'for row...' loop --- #
        self.roster_table_model.blockSignals(False)
        self.logger.info(f"Loaded {len(players)} players into table model.") # MODIFIED print to logger.info

        self.roster_table_view.reset()
        self.roster_table_view.setModel(self.roster_table_model)
        self.roster_table_model.layoutChanged.emit()
        self.logger.info("View reset, setModel, and layoutChanged signal emitted after load.") # MODIFIED print to logger.info
        # --- NEW FILTERING APPROACH: Apply initial filters after loading --- #
        self._apply_filters() # Ensure filters are applied to newly loaded data
        # ------------------------------------------------------------------ #

        # --- ADD: Apply outlier detection and highlighting --- #
        self._apply_outlier_detection()
        # ---------------------------------------------------- #

    def _apply_outlier_detection(self):
        """Apply outlier detection and highlighting to physical measurement fields."""
        self.logger.debug("_apply_outlier_detection called - starting outlier detection process")
        try:
            # Check if outlier detection is enabled
            if not outlier_detector.enabled:
                self.logger.info("Outlier detection is disabled, skipping highlighting")
                return

            # Get all player data for outlier calculation
            players = self.roster_manager.get_all_players()
            if not players or len(players) < 2:
                self.logger.info("Insufficient player data for outlier detection (need at least 2 players)")
                return

            self.logger.debug(f"Applying outlier detection to {len(players)} players")

            # Detect outliers
            outliers = outlier_detector.detect_outliers(players)
            averages = outlier_detector.calculate_team_averages(players)
            self.logger.debug(f"Outlier detection results: {outliers}")
            self.logger.debug(f"Team averages: {averages}")

            # Get physical field column indices
            physical_field_columns = {}
            self.logger.debug(f"OutlierDetector.PHYSICAL_FIELDS: {OutlierDetector.PHYSICAL_FIELDS}")
            self.logger.debug(f"Table headers: {self.table_headers}")

            for col_idx, (field_key, _) in self.table_headers.items():
                if field_key in OutlierDetector.PHYSICAL_FIELDS:
                    physical_field_columns[field_key] = col_idx
                    self.logger.debug(f"Mapped field {field_key} to column {col_idx}")

            self.logger.debug(f"Physical field columns: {physical_field_columns}")

            # Apply highlighting to table cells
            for row in range(self.roster_table_model.rowCount()):
                # Get player ID for this row
                player_id_item = self.roster_table_model.item(row, 0)  # ID is in first column
                if not player_id_item:
                    continue

                player_id = str(player_id_item.data(Qt.ItemDataRole.UserRole + 1))
                self.logger.debug(f"Row {row}: player_id = {player_id}")
                if not player_id or player_id == "None":
                    continue

                # Try both string and integer keys for outlier lookup
                player_outliers = outliers.get(player_id, {})
                if not player_outliers and player_id.isdigit():
                    player_outliers = outliers.get(int(player_id), {})
                self.logger.debug(f"Player {player_id} outliers: {player_outliers}")

                # Check each physical field column
                for field_name, col_index in physical_field_columns.items():
                    item = self.roster_table_model.item(row, col_index)
                    if item:
                        is_outlier = player_outliers.get(field_name, False)
                        if is_outlier:
                            self.logger.debug(f"Highlighting outlier: Player {player_id}, Field {field_name}, Value: {item.text()}")
                            # Apply outlier styling with appropriate color
                            from PySide6.QtGui import QBrush
                            try:
                                value = float(item.text())
                                average = averages.get(field_name, 0)
                                color = get_outlier_background_color(value, average)
                                brush = QBrush(color)
                                self.logger.debug(f"Setting background color: {color.name()} for value {value} vs average {average}")
                                item.setBackground(brush)
                            except (ValueError, TypeError):
                                # Fallback to default red if value parsing fails
                                from app.utils.color_constants import get_color
                                color = get_color("roster_outlier_high")
                                brush = QBrush(color)
                                item.setBackground(brush)

                            # Add warning to tooltip
                            current_tooltip = item.toolTip()
                            outlier_warning = "⚠️ Potential data entry error: Value significantly differs from team average"

                            if current_tooltip:
                                item.setToolTip(f"{current_tooltip}\n\n{outlier_warning}")
                            else:
                                item.setToolTip(outlier_warning)
                        else:
                            # Clear outlier styling if previously applied
                            from PySide6.QtGui import QBrush
                            default_brush = QBrush()  # Default empty brush
                            item.setBackground(default_brush)

                            # Remove outlier warning from tooltip if present
                            current_tooltip = item.toolTip()
                            if current_tooltip and "⚠️ Potential data entry error" in current_tooltip:
                                parts = current_tooltip.split("\n\n⚠️ Potential data entry error")
                                item.setToolTip(parts[0] if parts[0] else "")

            # Log outlier detection results
            threshold = outlier_detector.threshold_percentage
            total_outliers = sum(sum(player_outliers.values()) for player_outliers in outliers.values())
            self.logger.info(f"Outlier detection completed: {total_outliers} outliers found with {threshold}% threshold")

        except Exception as e:
            self.logger.error(f"Error in outlier detection: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    def _test_outlier_detection(self):
        """Test method to manually trigger outlier detection."""
        self.logger.info("Manual outlier detection test triggered")

        # Check outlier detector status
        self.logger.info(f"Outlier detection enabled: {outlier_detector.enabled}")
        self.logger.info(f"Outlier detection threshold: {outlier_detector.threshold_percentage}%")

        # Get all players and log their physical data
        players = self.roster_manager.get_all_players()
        self.logger.info(f"Found {len(players)} players for outlier detection")

        for player in players:
            height = player.get('height')
            weight = player.get('weight')
            name = f"{player.get('first_name', '')} {player.get('last_name', '')}"
            self.logger.info(f"Player {name}: height={height}, weight={weight}")

        # Manually trigger outlier detection
        self._apply_outlier_detection()

    def _format_value_for_display(self, key, value):
        """Formats different data types for table/tab display."""
        if value is None:
            return ""
        if key == 'dob' and value:
             # Check if it's already a QDate or needs conversion from string
             if isinstance(value, QDate):
                  return value.toString("yyyy-MM-dd")
             elif isinstance(value, str):
                  # Attempt to parse if needed, maybe already correct format
                  qdate = QDate.fromString(value, "yyyy-MM-dd")
                  return qdate.toString("yyyy-MM-dd") if qdate.isValid() else str(value) # Fallback to string if parse fails
             else:
                  return str(value) # Fallback for unexpected types
        elif key == 'height' and value is not None:
             try:
                 return f"{float(value):.1f}" # One decimal place for height
             except (ValueError, TypeError):
                 return str(value) # Fallback
        elif key == 'weight' and value is not None:
             try:
                 return f"{float(value):.1f}" # One decimal place for weight
             except (ValueError, TypeError):
                 return str(value) # Fallback
        elif key == 'shirt_number' and value == 0:
             return "" # Display empty for 0 shirt number (N/A handled by spinbox)
        elif key in ['sex', 'preferred_foot', 'strong_eye', 'fitness']:
            return str(value) if value is not None else "" # Handle None for fitness too
        else:
            # No special formatting needed for nationality, just return string
            return str(value)

    def _update_tabs_on_selection(self, selected, deselected):
        """Populates the detail tabs when a player is selected in the table."""
        indexes = selected.indexes()
        if not indexes: # No selection or selection cleared
            self.detail_tabs.setEnabled(False)
            self.remove_button.setEnabled(False)
            self.current_selected_player_id = None
            # Optionally clear the detail tabs here
            self._clear_detail_tabs()
            return

        selected_row = indexes[0].row()
        # Get player_id from the UserRole data stored in the first column
        id_item = self.roster_table_model.item(selected_row, 0)
        if not id_item:
             self.logger.error("Could not get item for ID column when updating tabs.") # MODIFIED print to logger.error
             self._clear_detail_tabs()
             self.detail_tabs.setEnabled(False)
             self.remove_button.setEnabled(False)
             return

        # --- DEBUG PRINT --- #
        self.logger.debug(f"_update_tabs_on_selection: Found id_item for row {selected_row}. Text: '{id_item.text()}', DisplayRole: '{id_item.data(Qt.ItemDataRole.DisplayRole)}'") # MODIFIED print to logger.debug
        # ----------------- #

        player_id = id_item.data(Qt.ItemDataRole.UserRole + 1)
        if player_id is None:
            # --- ADDED DETAIL TO ERROR --- #
            self.logger.error(f"Could not retrieve player_id (UserRole+1) from table item data. Item Text: '{id_item.text()}', Row: {selected_row}") # MODIFIED print to logger.error
            # ----------------------------- #
            self._clear_detail_tabs()
            self.detail_tabs.setEnabled(False)
            self.remove_button.setEnabled(False)
            return

        self.current_selected_player_id = player_id
        self.logger.info(f"Player selected, ID: {self.current_selected_player_id}") # MODIFIED print to logger.info

        player_data = self.roster_manager.get_player(self.current_selected_player_id)
        if not player_data:
            self.logger.error(f"Could not retrieve player data for ID {self.current_selected_player_id}") # MODIFIED print to logger.error
            self._clear_detail_tabs()
            self.detail_tabs.setEnabled(False)
            self.remove_button.setEnabled(False)
            return

        # Populate tabs, blocking signals to prevent loops
        self._is_updating_ui = True # Set flag before updating tabs
        self._block_detail_signals(True)
        self.player_id_label.setText(str(player_data.get('player_id', '')))
        self.last_name_input.setText(player_data.get('last_name', ''))
        self.first_name_input.setText(player_data.get('first_name', ''))
        self.shirt_number_spinbox.setValue(player_data.get('shirt_number') or 0) # Use 0 for N/A
        self.position_combo.setCurrentText(player_data.get('position') or "")
        self.detailed_pos_input.setText(player_data.get('detailed_position', ''))
        dob_str = player_data.get('dob')
        if dob_str:
            self.dob_edit.setDate(QDate.fromString(dob_str, "yyyy-MM-dd"))
        else:
            self.dob_edit.clear()
        self.age_label.setText(str(player_data.get('age', '-')))
        self.nationality_combo.setCurrentText(player_data.get('nationality') or "") # ADDED Nationality
        self.height_spinbox.setValue(player_data.get('height') or 0.0)
        self.weight_spinbox.setValue(player_data.get('weight') or 0.0)
        self.waist_spinbox.setValue(player_data.get('waist') or 0.0)
        # Update new physical fields
        self.hip_spinbox.setValue(player_data.get('hip') or 0.0)
        self.neck_spinbox.setValue(player_data.get('neck') or 0.0)
        self.wrist_spinbox.setValue(player_data.get('wrist') or 0.0)
        self.forearm_spinbox.setValue(player_data.get('forearm') or 0.0)
        self.thigh_spinbox.setValue(player_data.get('thigh') or 0.0)
        # --- FIX: Set sex_combo using findData --- #
        sex_value = player_data.get('sex')
        sex_idx = self.sex_combo.findData(sex_value if sex_value else None)
        self.sex_combo.setCurrentIndex(sex_idx if sex_idx != -1 else 0) # Default to blank
        # ---------------------------------------- #
        self.preferred_foot_combo.setCurrentText(player_data.get('preferred_foot') or "")
        self.strong_eye_combo.setCurrentText(player_data.get('strong_eye') or "")
        self.fitness_combo.setCurrentText(player_data.get('fitness') or "") # Added fitness
        # --- ADDED: Set Group Combos --- #
        primary_group_id = player_data.get('primary_group_id')
        primary_idx = self.primary_group_combo.findData(primary_group_id if primary_group_id is not None else None)
        self.primary_group_combo.setCurrentIndex(primary_idx if primary_idx != -1 else 0) # Default to None item

        secondary_group_id = player_data.get('secondary_group_id')
        secondary_idx = self.secondary_group_combo.findData(secondary_group_id if secondary_group_id is not None else None)
        self.secondary_group_combo.setCurrentIndex(secondary_idx if secondary_idx != -1 else 0) # Default to None item
        # ------------------------------- #

        # --- Handle Nationality and Zone --- #
        nationality = player_data.get('nationality')
        zone = get_nationality_zone(nationality)
        self.nationality_combo.setCurrentText(nationality or "")
        self.zone_label.setText(zone if zone else "-")
        # ----------------------------------- #

        # --- ADDED: Set Status Tags List --- #
        self.status_tags_list_widget.blockSignals(True) # Block list signals during update
        for i in range(self.status_tags_list_widget.count()):
            item = self.status_tags_list_widget.item(i)
            key = item.data(Qt.ItemDataRole.UserRole)
            if key:
                player_status_value = player_data.get(key, 0) # Default 0/False
                check_state = Qt.CheckState.Checked if bool(player_status_value) else Qt.CheckState.Unchecked
                item.setCheckState(check_state)
        self.status_tags_list_widget.blockSignals(False) # Unblock list signals
        # ----------------------------------- #

        # --- Update Transfer Status Section --- #
        # 1. Set the main status combo
        current_transfer_status = player_data.get('transfer_status', "") # Get transfer status
        # Find the translated index if necessary, or just set text
        self.transfer_status_combo.setCurrentText(self.tr(current_transfer_status) if current_transfer_status else "")

        # 2. Update visibility based on loaded status *BEFORE* populating details
        self._update_transfer_fields_visibility(current_transfer_status)

        # 3. Populate *visible* detail fields
        for key in TRANSFER_ALL_DETAIL_KEYS:
            widget = self.transfer_detail_widgets.get(key)
            if widget and widget.isVisible(): # Only populate visible fields
                value = player_data.get(key)
                if isinstance(widget, QLineEdit):
                    widget.setText(str(value) if value is not None else "")
                elif isinstance(widget, QDateEdit):
                    if value:
                        qdate = QDate.fromString(str(value), "yyyy-MM-dd")
                        widget.setDate(qdate) if qdate.isValid() else widget.clear()
                    else:
                        widget.clear()
        # ------------------------------------ #

        self._block_detail_signals(False)
        self._is_updating_ui = False # Clear flag after updating tabs

        self.detail_tabs.setEnabled(True)
        self.remove_button.setEnabled(True)

        # --- Hide instruction labels when player is selected --- #
        if hasattr(self, 'personal_info_label'):
            self.personal_info_label.hide()
        if hasattr(self, 'position_info_label'):
            self.position_info_label.hide()
        if hasattr(self, 'physical_info_label'):
            self.physical_info_label.hide()
        if hasattr(self, 'status_info_label'):
            self.status_info_label.hide()
        if hasattr(self, 'selections_info_label'):
            self.selections_info_label.hide()
        # ------------------------------------------------------ #

        # --- Load player selections --- #
        self._load_player_selections(self.current_selected_player_id)
        # ------------------------------ #

        # --- Update Flag and Image Displays --- #
        self._update_flag_display(self.current_selected_player_id, player_data.get('nationality'))
        self._load_player_image(self.current_selected_player_id) # ADDED: Load player image
        self._load_player_kit(player_data) # <<< ADD THIS CALL BACK
        # -------------------------------------- #

        # --- Update Position Grid --- #
        self.logger.info(f"Loading position data for player {self.current_selected_player_id}")
        self._load_player_positions(self.current_selected_player_id)
        # Debug: Dump the contents of the player_positions table
        self.roster_manager.debug_dump_player_positions_table()
        # ---------------------------- #

        # --- Recalculate and Display Physical Metrics using Utility --- #
        # This ensures metrics use the latest UI values if needed,
        # or the just-loaded DB values.
        self._recalculate_and_display_metrics(self.current_selected_player_id)
        # ---------------------------------------------------------- #

        # --- ADD: Populate Profile Tab --- #
        profile_data = self.roster_manager.get_player_profile(self.current_selected_player_id)
        # --- RE-ADD DEBUG --- #
        self.logger.debug(f"_update_tabs_on_selection: Fetched profile_data for player {self.current_selected_player_id}: {profile_data}") # MODIFIED print to logger.debug
        # ------------------- #
        if profile_data is not None: # Check for error vs. empty profile
            # --- RE-ADD Signal Blocking --- #
            self._block_detail_signals(True) # Block profile signals during population
            # Date Joined
            joined_str = profile_data.get('date_joined_club')
            if joined_str:
                self.profile_date_joined_club.setDate(QDate.fromString(joined_str, "yyyy-MM-dd"))
            else:
                self.profile_date_joined_club.clear()
            # Personality Type
            self.profile_personality_type.setText(profile_data.get('personality_type', ''))
            # Biography
            self.profile_biography.setPlainText(profile_data.get('biography', ''))
            # --- Populate other profile fields here --- #

            # --- ADD: Populate new profile fields --- #
            self.profile_mentality.setText(profile_data.get('mentality', ''))
            self.profile_work_ethic.setText(profile_data.get('work_ethic', ''))
            self.profile_professionalism_level.setText(profile_data.get('professionalism_level', ''))
            self.profile_determination_level.setText(profile_data.get('determination_level', ''))
            self.profile_team_spirit.setText(profile_data.get('team_spirit', ''))
            self.profile_adaptability.setText(profile_data.get('adaptability', ''))
            self.profile_temperament.setText(profile_data.get('temperament', ''))
            self.profile_ambition_level.setText(profile_data.get('ambition_level', ''))
            self.profile_leadership_qualities.setText(profile_data.get('leadership_qualities', ''))
            self.profile_charisma.setText(profile_data.get('charisma', ''))
            self.profile_emotional_intelligence_notes.setPlainText(profile_data.get('emotional_intelligence_notes', ''))
            self.profile_life_motto_quote.setPlainText(profile_data.get('life_motto_quote', ''))
            self.profile_personal_goals_outside_football.setPlainText(profile_data.get('personal_goals_outside_football', ''))
            self.profile_hometown.setText(profile_data.get('hometown', ''))
            self.profile_current_residence_city_area.setText(profile_data.get('current_residence_city_area', ''))
            self.profile_marital_status.setText(profile_data.get('marital_status', ''))
            self.profile_partner_name.setText(profile_data.get('partner_name', ''))
            self.profile_father_name.setText(profile_data.get('father_name', ''))
            self.profile_mother_name.setText(profile_data.get('mother_name', ''))
            self.profile_parents_occupations.setPlainText(profile_data.get('parents_occupations', ''))
            self.profile_siblings_summary.setPlainText(profile_data.get('siblings_summary', ''))
            self.profile_family_background_notes.setPlainText(profile_data.get('family_background_notes', ''))
            self.profile_number_of_children.setValue(profile_data.get('number_of_children', 0))
            self.profile_pets_description.setPlainText(profile_data.get('pets_description', ''))
            self.profile_superstitions_rituals.setPlainText(profile_data.get('superstitions_rituals', ''))
            self.profile_religion_faith.setText(profile_data.get('religion_faith', ''))
            self.profile_religious_practices_notes.setPlainText(profile_data.get('religious_practices_notes', ''))
            self.profile_political_views_summary.setPlainText(profile_data.get('political_views_summary', ''))
            self.profile_charity_community_involvement.setPlainText(profile_data.get('charity_community_involvement', ''))
            self.profile_volunteer_work_summary.setPlainText(profile_data.get('volunteer_work_summary', ''))
            self.profile_education_level.setText(profile_data.get('education_level', ''))
            self.profile_field_of_study.setText(profile_data.get('field_of_study', ''))
            self.profile_university_school_attended.setText(profile_data.get('university_school_attended', ''))
            self.profile_dual_career_other_job.setText(profile_data.get('dual_career_other_job', ''))
            self.profile_business_ventures.setPlainText(profile_data.get('business_ventures', ''))
            self.profile_willingness_to_coach_mentor.setText(profile_data.get('willingness_to_coach_mentor', ''))
            self.profile_public_image_summary.setPlainText(profile_data.get('public_image_summary', ''))
            self.profile_media_friendliness.setText(profile_data.get('media_friendliness', ''))
            self.profile_media_availability_notes.setPlainText(profile_data.get('media_availability_notes', ''))
            self.profile_website_blog.setText(profile_data.get('website_blog', ''))
            self.profile_personal_contact_email.setText(profile_data.get('personal_contact_email', ''))
            self.profile_personal_phone_number.setText(profile_data.get('personal_phone_number', ''))
            self.profile_home_address.setPlainText(profile_data.get('home_address', ''))
            self.profile_agent_name.setText(profile_data.get('agent_name', ''))
            self.profile_personal_assistant_manager.setText(profile_data.get('personal_assistant_manager', ''))
            self.profile_retirement_plans_summary.setPlainText(profile_data.get('retirement_plans_summary', ''))
            # ---------------------------------------- #

            self._block_detail_signals(False) # Unblock profile signals after population
            # Show content, hide label
            self.profile_info_label.hide()
            # Ensure scroll area content widget is visible (if previously hidden)
            # scroll_area.widget().show() # Might not be necessary depending on initial state
        else:
            # Handle case where profile fetch failed (manager returned None)
            self.logger.error(f"Failed to fetch profile data for player {self.current_selected_player_id} when updating tabs.") # MODIFIED print to logger.error
            # Clear fields and show placeholder
            self.profile_date_joined_club.clear()
            self.profile_personality_type.clear()
            self.profile_biography.clear()
            self.profile_info_label.setText(self.tr("Error loading profile data."))
            self.profile_info_label.setStyleSheet("color: red;")
            self.profile_info_label.show()

        # --- END: Populate Profile Tab --- #

    def _clear_detail_tabs(self):
         """Clears all input fields in the detail tabs."""
         self._is_updating_ui = True # Prevent signals during clear
         self._block_detail_signals(True)
         self.player_id_label.setText("-")
         self.last_name_input.clear()
         self.first_name_input.clear()
         self.shirt_number_spinbox.setValue(0)
         self.position_combo.setCurrentIndex(0) # Set to blank
         self.detailed_pos_input.clear()
         self.dob_edit.clear()
         self.dob_edit.setSpecialValueText("") # Clear special text too
         self.age_label.setText("-")
         self.nationality_combo.setCurrentIndex(0) # ADDED Nationality - Set to blank
         self.height_spinbox.setValue(0.0)
         self.weight_spinbox.setValue(0.0)
         self.waist_spinbox.setValue(0.0)
         # Clear new physical fields
         self.hip_spinbox.setValue(0.0)
         self.neck_spinbox.setValue(0.0)
         self.wrist_spinbox.setValue(0.0)
         self.forearm_spinbox.setValue(0.0)
         self.thigh_spinbox.setValue(0.0)
         self.fitness_combo.setCurrentIndex(0) # Added: Clear fitness combo
         # --- ADDED: Clear New Fields --- #
         self.sex_combo.setCurrentIndex(0) # Set to blank
         self.preferred_foot_combo.setCurrentIndex(0) # Set to blank
         self.strong_eye_combo.setCurrentIndex(0) # Set to blank
         # -------------------------------- #
         # --- ADDED: Clear flag preview ---
         self.flag_preview_label.clear()
         self.flag_preview_label.setStyleSheet("border: 1px solid lightgray; background-color: #f0f0f0;")
         # ----------------------------------
         # --- Show Position tab info label and clear grid ---
         if hasattr(self, 'position_info_label'):
             self.position_info_label.show()
         if hasattr(self, 'position_grid'):
             self.position_grid.clear_grid()
         # ------------------------------------
         self._block_detail_signals(False)
         self._is_updating_ui = False
         self.strong_eye_combo.setCurrentIndex(0) # Set to blank
         self.primary_group_combo.setCurrentIndex(0) # Added: Clear primary group
         self.secondary_group_combo.setCurrentIndex(0) # Added: Clear secondary group
         self.zone_label.setText("-") # ADDED: Clear Zone Label

         # --- ADDED: Clear Status List --- #
         self.status_tags_list_widget.blockSignals(True)
         for i in range(self.status_tags_list_widget.count()):
             self.status_tags_list_widget.item(i).setCheckState(Qt.CheckState.Unchecked)
         self.status_tags_list_widget.blockSignals(False)
         # -------------------------------- #

         # --- Clear Selections Tab --- #
         for checkbox in self.selection_checkboxes.values():
             checkbox.blockSignals(True)
             checkbox.setChecked(False)
             checkbox.blockSignals(False)

         for field in self.selection_reason_fields.values():
             field.clear()

         # Show instruction label in Selections tab
         if hasattr(self, 'selections_info_label'):
             self.selections_info_label.show()
         # ---------------------------- #

         self._block_detail_signals(False)
         self._is_updating_ui = False

         # --- Clear Calculated Metrics --- #
         self.bmi_label.setText("-")
         self.bfp_label.setText("-")
         self.lbm_label.setText("-")
         self.whr_label.setText("-")
         self.whtr_label.setText("-")
         # -------------------------------- #

         # --- ADDED: Clear player image --- #
         self._load_player_image(None)
         # --------------------------------- #

         # --- ADD: Clear Profile Tab --- #
         if hasattr(self, 'profile_date_joined_club'): self.profile_date_joined_club.clear()
         if hasattr(self, 'profile_personality_type'): self.profile_personality_type.clear()
         if hasattr(self, 'profile_biography'): self.profile_biography.clear()
         # --- Clear other profile fields here --- #
         # --- ADD: Clear new profile fields --- #
         profile_widgets_to_clear = [
             self.profile_mentality, self.profile_work_ethic, self.profile_professionalism_level,
             self.profile_determination_level, self.profile_team_spirit, self.profile_adaptability,
             self.profile_temperament, self.profile_ambition_level, self.profile_leadership_qualities,
             self.profile_charisma, self.profile_emotional_intelligence_notes, self.profile_life_motto_quote,
             self.profile_personal_goals_outside_football, self.profile_hometown,
             self.profile_current_residence_city_area, self.profile_marital_status,
             self.profile_partner_name, self.profile_father_name, self.profile_mother_name,
             self.profile_parents_occupations, self.profile_siblings_summary,
             self.profile_family_background_notes, self.profile_number_of_children, # Spinbox handled below
             self.profile_pets_description, self.profile_superstitions_rituals,
             self.profile_religion_faith, self.profile_religious_practices_notes,
             self.profile_political_views_summary, self.profile_charity_community_involvement,
             self.profile_volunteer_work_summary, self.profile_education_level,
             self.profile_field_of_study, self.profile_university_school_attended,
             self.profile_dual_career_other_job, self.profile_business_ventures,
             self.profile_willingness_to_coach_mentor, self.profile_public_image_summary,
             self.profile_media_friendliness, self.profile_media_availability_notes,
             self.profile_website_blog, self.profile_personal_contact_email,
             self.profile_personal_phone_number, self.profile_home_address,
             self.profile_agent_name, self.profile_personal_assistant_manager,
             self.profile_retirement_plans_summary
         ]
         for widget in profile_widgets_to_clear:
             if hasattr(self, widget.objectName()): # Check attribute exists
                 actual_widget = getattr(self, widget.objectName())
                 if hasattr(actual_widget, 'clear'):
                     actual_widget.clear()
         # Clear SpinBox separately
         if hasattr(self, 'profile_number_of_children'):
             self.profile_number_of_children.setValue(0)
         # ----------------------------------- #
         if hasattr(self, 'profile_info_label'):
             self.profile_info_label.setText(self.tr("Select a player from the list"))
             self.profile_info_label.setStyleSheet("font-style: italic; color: grey;")

    def _block_detail_signals(self, block):
        """Blocks or unblocks signals for all detail input widgets."""
        # Includes list widgets now
        for widget in self.player_detail_widgets.values():
            if hasattr(widget, 'blockSignals'):
                widget.blockSignals(block)

    def _update_player_data(self, field_key, value):
        """Updates the manager and table model when a detail tab field changes."""
        # --- DEBUG START ---
        self.logger.debug(f"-> ENTER _update_player_data: key='{field_key}', value='{value}', type='{type(value)}'") # MODIFIED
        # ----------------- #

        # Prevent updates triggered by UI synchronization
        if self._is_updating_ui:
             self.logger.debug(f"   SKIPPING tab update for '{field_key}' due to UI sync flag.") # MODIFIED
             return

        if self.current_selected_player_id is None:
            self.logger.debug(f"   SKIPPING tab update for '{field_key}', no player selected.") # MODIFIED
            return

        self.logger.debug(f"   Processing update for player ID: {self.current_selected_player_id}") # MODIFIED

        # --- Input Validation ---
        original_data = self.roster_manager.get_player(self.current_selected_player_id) # Fetch original data
        if not original_data:
            self.logger.error(f"   Could not retrieve original data for player {self.current_selected_player_id}. Aborting update.") # MODIFIED
            return
        original_value = original_data.get(field_key)

        validated_value = value # Start with the raw input

        # --- (Existing Validation Logic for numbers, date, shirt number uniqueness...) ---
        # ... (keep existing validation blocks) ...
        # --- DEBUG: After validation ---
        self.logger.debug(f"   After validation: key='{field_key}', validated_value='{validated_value}', type='{type(validated_value)}'") # MODIFIED
        # ----------------------------- #

        update_dict = {field_key: validated_value}
        new_age = None
        new_zone = None

        # Update age if DOB changed
        if field_key == 'dob':
             new_age = self.roster_manager.calculate_age(validated_value)
             self.logger.debug(f"   Calculated new age: {new_age}") # MODIFIED
             self._is_updating_ui = True # Prevent signals from age label update
             self.age_label.setText(str(new_age) if new_age is not None else "-")
             self._is_updating_ui = False
             # Age in table will be updated via _update_table_cell below

        elif field_key == 'nationality':
             new_zone = get_nationality_zone(validated_value)
             self.logger.debug(f"   Calculated new zone: {new_zone}") # MODIFIED
             # --- ADDED: Update Zone Label in Tab --- #
             self.zone_label.setText(new_zone if new_zone else "-")
             # ---------------------------------------- #

        # --- Update Manager ---
        self.logger.debug(f"   Attempting DB update for player {self.current_selected_player_id} with: {update_dict}") # MODIFIED
        success = self.roster_manager.update_player(self.current_selected_player_id, update_dict)
        self.logger.debug(f"   DB update result: {success}") # MODIFIED

        if success:
            # Update the corresponding cell(s) in the table model
            # Block table signal temporarily to prevent _handle_table_item_changed loop
            self.roster_table_model.blockSignals(True)
            self.logger.debug(f"   Attempting table cell update for key '{field_key}', value '{validated_value}'") # MODIFIED
            self._update_table_cell(self.current_selected_player_id, field_key, validated_value)
            if field_key == 'dob' and new_age is not None:
                 self.logger.debug(f"   Attempting table cell update for key 'age', value '{new_age}'") # MODIFIED
                 self._update_table_cell(self.current_selected_player_id, 'age', new_age)
            # --- Ensure Zone column in TABLE is updated too --- #
            if field_key == 'nationality' and new_zone is not None:
                 self.logger.debug(f"   Attempting table cell update for key 'zone', value '{new_zone}'") # MODIFIED
                 self._update_table_cell(self.current_selected_player_id, 'zone', new_zone)
            # -------------------------------------------------- #
            self.roster_table_model.blockSignals(False)
            self.logger.debug(f"   Table cell updates complete for '{field_key}'. Forcing viewport update.") # MODIFIED
            # Force viewport update to ensure immediate visual change in table
            self.roster_table_view.viewport().update()

            # --- ADDED: Update flag display if nationality changed ---
            if field_key == 'nationality':
                 self.logger.debug("   Updating flag display due to nationality change.") # MODIFIED
                 self._update_flag_display(self.current_selected_player_id, validated_value)
            # ------------------------------------------------------

            # --- ADDED: Re-apply filters after successful update from tab --- #
            self.logger.debug(f"   Triggering filter re-application after successful update for '{field_key}'.") # MODIFIED
            self._apply_filters()
            # ---------------------------------------------------------------- #

            # --- ADD: Refresh outlier detection if physical field was updated --- #
            if field_key in OutlierDetector.PHYSICAL_FIELDS:
                self.logger.debug(f"Physical field '{field_key}' updated, refreshing outlier detection")
                self.logger.debug(f"Calling _apply_outlier_detection via QTimer for field: {field_key}")
                # Use QTimer to ensure UI updates properly after the current edit is complete
                from PySide6.QtCore import QTimer
                QTimer.singleShot(50, self._apply_outlier_detection)  # 50ms delay for UI to settle
            # ------------------------------------------------------------------ #
        else:
            self.logger.warning(f"   DB update failed for {field_key}. Reverting tab widget.") # MODIFIED
            QMessageBox.warning(self, self.tr("Update Failed"), self.tr("Could not save changes to the database."))
            # Revert the UI change in the tab
            self._revert_tab_widget(field_key, original_value)

        # --- DEBUG END ---
        self.logger.debug(f"<- LEAVE _update_player_data: key='{field_key}'") # MODIFIED
        # --------------- #

    def _revert_tab_widget(self, field_key, original_value):
         """Reverts a tab widget to its original value."""
         self.logger.debug(f"Reverting tab widget '{field_key}' to '{original_value}'")
         widget = self.player_detail_widgets.get(field_key)
         if not widget: return

         self._is_updating_ui = True
         widget.blockSignals(True)

         if isinstance(widget, QLineEdit):
             widget.setText(str(original_value) if original_value is not None else "")
         elif isinstance(widget, QSpinBox):
             widget.setValue(int(original_value) if original_value is not None else 0)
         elif isinstance(widget, QDoubleSpinBox):
             widget.setValue(float(original_value) if original_value is not None else 0.0)
         elif isinstance(widget, QComboBox):
             widget.setCurrentText(str(original_value) if original_value is not None else "")
         elif isinstance(widget, QDateEdit):
             if original_value:
                 qdate = QDate.fromString(original_value, "yyyy-MM-dd")
                 widget.setDate(qdate) if qdate.isValid() else widget.clear()
             else:
                 widget.clear()

         widget.blockSignals(False)
         self._is_updating_ui = False

    def _handle_table_item_changed(self, item: QStandardItem):
        """Handles data changes directly made in the table view (triggered by delegate)."""
        self.logger.debug(f"_handle_table_item_changed called for item: {item.text() if item else 'None'}")

        # Prevent updates triggered by UI synchronization from tabs
        if self._is_updating_ui:
             self.logger.debug("Skipping table update due to UI sync.")
             return

        row = item.row()
        col = item.column()

        # Get player ID from the first column of the changed row
        id_item = self.roster_table_model.item(row, 0)
        if not id_item:
             self.logger.error(f"_handle_table_item_changed: Cannot get ID item for changed row {row}") # MODIFIED
             return

        player_id = id_item.data(Qt.ItemDataRole.UserRole + 1)
        if player_id is None:
            self.logger.error(f"_handle_table_item_changed: Cannot get player_id for changed row {row}") # MODIFIED
            return

        # Get the field key corresponding to the column
        field_key, header_text = self.table_headers.get(col, (None, None))
        if field_key is None:
            self.logger.error(f"_handle_table_item_changed: Cannot determine field_key for Col {col}") # MODIFIED
            return

        self.logger.debug(f"HANDLE: Item changed Row={row}, Col={col}, Field='{field_key}', PlayerID={player_id}") # MODIFIED

        # --- Handle Group ID Changes Separately ---
        if field_key in ['primary_group_id', 'secondary_group_id']:
            # Get the EditRole value *just set* by the delegate
            # This might be called twice, once with ID, once (incorrectly?) with Name
            group_id_value = item.model().data(item.index(), Qt.ItemDataRole.EditRole)
            self.logger.debug(f"  HANDLE GROUP: Field='{field_key}', EditRole value='{group_id_value}' (Type: {type(group_id_value)})") # MODIFIED

            # --- FIX: Only process if the EditRole value is the expected type (int or None) ---
            if not isinstance(group_id_value, (int, type(None))):
                self.logger.debug(f"  HANDLE GROUP: Skipping processing for Field='{field_key}' because EditRole value type is {type(group_id_value)} (expected int or None). Likely second signal.") # MODIFIED
                return # Ignore signal if EditRole is unexpectedly a string (name)
            # --------------------------------------------------------------------------------

            # Proceed with DB update and Tab sync using the correct group_id_value
            update_dict = {field_key: group_id_value}
            self.logger.debug(f"  HANDLE GROUP: Updating manager with: {update_dict}") # MODIFIED
            # --- Retrieve original value *before* DB update for potential revert ---
            original_data = self.roster_manager.get_player(player_id)
            original_value = original_data.get(field_key) if original_data else None
            # -------------------------------------------------------------------
            success = self.roster_manager.update_player(player_id, update_dict)

            if success:
                self.logger.debug(f"  HANDLE GROUP: DB updated successfully.") # MODIFIED
                if player_id == self.current_selected_player_id:
                    self.logger.debug(f"  HANDLE GROUP: Syncing tab for Field='{field_key}' with ID='{group_id_value}'") # MODIFIED
                    tab_widget = self.player_detail_widgets.get(field_key)
                    if tab_widget and isinstance(tab_widget, QComboBox):
                        self._is_updating_ui = True
                        tab_widget.blockSignals(True)
                        try:
                            idx = tab_widget.findData(group_id_value) # Use the validated ID
                            self.logger.debug(f"    SYNC GROUP: Found index {idx} for ID {group_id_value}") # MODIFIED
                            tab_widget.setCurrentIndex(idx if idx != -1 else 0) # Set tab combo
                        finally:
                            tab_widget.blockSignals(False)
                            self._is_updating_ui = False
                        self.logger.debug(f"    SYNC GROUP: Tab sync complete.") # MODIFIED
                    else:
                        self.logger.warning(f"  HANDLE GROUP: Could not find tab ComboBox for key '{field_key}'") # MODIFIED
            else:
                self.logger.warning(f"  HANDLE GROUP: DB update FAILED for {field_key}. Reverting model.") # MODIFIED
                # Revert the model item using the original value
                self.roster_table_model.blockSignals(True)
                group_id_to_name = {gid: name for name, gid in self._group_options}
                original_display_name = group_id_to_name.get(original_value, "") if original_value is not None else ""
                item.model().setData(item.index(), original_value, Qt.ItemDataRole.EditRole)
                item.setText(original_display_name if original_display_name else self.tr("(None)"))
                self.roster_table_model.blockSignals(False)
                QMessageBox.warning(self, self.tr("Update Failed"), self.tr("Could not save group change to the database."))

            return # Handled group ID change, exit handler

        # --- Handle Non-Group, Editable Field Changes ---
        # (Exclude calculated/fixed fields handled elsewhere or non-editable)
        elif field_key not in ['player_id', 'age', 'zone', 'flag', 'status_tags', 'transfer_status']:
            # Get the EditRole value committed by the delegate for other fields
            new_model_value = item.model().data(item.index(), Qt.ItemDataRole.EditRole)
            self.logger.debug(f"  HANDLE OTHER: Field='{field_key}', New EditRole Value='{new_model_value}' (Type: {type(new_model_value)})") # MODIFIED

            # --- Retrieve original value *before* DB update for potential revert ---
            original_data = self.roster_manager.get_player(player_id)
            original_value = original_data.get(field_key) if original_data else None
            # -------------------------------------------------------------------

            # --- Perform validation if needed (e.g., shirt number) ---
            if field_key == 'shirt_number':
                 # Value from model is already validated by delegate's setModelData for range,
                 # but we need to check uniqueness here before DB update.
                 if new_model_value is not None and not self.roster_manager.is_shirt_number_unique(new_model_value, player_id):
                     QMessageBox.warning(self, self.tr("Invalid Input"), self.tr("Shirt number {} is already assigned.").format(new_model_value))
                     # Revert model safely
                     self.roster_table_model.blockSignals(True)
                     item.model().setData(item.index(), original_value, Qt.ItemDataRole.EditRole)
                     item.setText(self._format_value_for_display(field_key, original_value))
                     self.roster_table_model.blockSignals(False)
                     self.logger.debug(f"  HANDLE OTHER: Reverted shirt number due to non-uniqueness.") # MODIFIED
                     return # Stop update

            # --- Update Manager ---
            update_dict = {field_key: new_model_value}
            new_age, new_zone = None, None # For calculated fields
            if field_key == 'dob': new_age = self.roster_manager.calculate_age(new_model_value)
            if field_key == 'nationality': new_zone = get_nationality_zone(new_model_value)

            self.logger.debug(f"  HANDLE OTHER: Updating manager with: {update_dict}") # MODIFIED
            success = self.roster_manager.update_player(player_id, update_dict)

            if success:
                self.logger.debug(f"  HANDLE OTHER: DB updated successfully.") # MODIFIED
                # Update calculated fields in the table model if necessary
                if new_age is not None: self._update_table_cell(player_id, 'age', new_age)
                if new_zone is not None:
                    self._update_flag_display(player_id, new_model_value) # Update flag icon
                    self._update_table_cell(player_id, 'zone', new_zone) # Update zone cell

                # Sync change back to detail tabs if the edited player is selected
                if player_id == self.current_selected_player_id:
                    self.logger.debug(f"  HANDLE OTHER: Syncing tab for Field='{field_key}' with Value='{new_model_value}'") # MODIFIED
                    tab_widget = self.player_detail_widgets.get(field_key)
                    if tab_widget:
                        self._is_updating_ui = True
                        tab_widget.blockSignals(True)
                        try:
                            # Simplified sync logic (assumes correct widget types)
                            if isinstance(tab_widget, QLineEdit): tab_widget.setText(str(new_model_value) if new_model_value is not None else "")
                            elif isinstance(tab_widget, QSpinBox): tab_widget.setValue(int(new_model_value) if new_model_value is not None else 0)
                            elif isinstance(tab_widget, QDoubleSpinBox): tab_widget.setValue(float(new_model_value) if new_model_value is not None else 0.0)
                            elif isinstance(tab_widget, QComboBox): # Specific handling for Sex combo data role needed? Check _update_tabs_on_selection
                                 if field_key == 'sex':
                                     sex_idx = tab_widget.findData(new_model_value if new_model_value else None)
                                     tab_widget.setCurrentIndex(sex_idx if idx != -1 else 0)
                                 else: # Other combos like position, nationality, fitness, foot, eye
                                     tab_widget.setCurrentText(str(new_model_value) if new_model_value is not None else "")
                            elif isinstance(tab_widget, QDateEdit):
                                 qdate = QDate.fromString(str(new_model_value), "yyyy-MM-dd") if new_model_value else QDate()
                                 tab_widget.setDate(qdate) if qdate.isValid() else tab_widget.clear()

                            # Update calculated fields in TABS
                            if new_age is not None: self.age_label.setText(str(new_age))
                            if new_zone is not None: self.zone_label.setText(new_zone if new_zone else "-")
                            # Recalculate metrics if relevant physical fields changed
                            if field_key in ['height', 'weight', 'waist', 'hip', 'neck', 'sex']:
                                self._recalculate_and_display_metrics(player_id)
                        finally:
                            tab_widget.blockSignals(False)
                            self._is_updating_ui = False
                        self.logger.debug(f"    SYNC OTHER: Tab sync complete.") # MODIFIED
                    else:
                         self.logger.warning(f"  HANDLE OTHER: Could not find tab widget for key '{field_key}'") # MODIFIED

                # --- ADD: Refresh outlier detection if physical field was updated --- #
                if field_key in OutlierDetector.PHYSICAL_FIELDS:
                    self.logger.debug(f"Physical field '{field_key}' updated via table, refreshing outlier detection")
                    # Use QTimer to ensure UI updates properly after the current edit is complete
                    from PySide6.QtCore import QTimer
                    QTimer.singleShot(50, self._apply_outlier_detection)  # 50ms delay for UI to settle
                # ------------------------------------------------------------------ #
            else:
                self.logger.warning(f"  HANDLE OTHER: DB update FAILED for {field_key}. Reverting model.") # MODIFIED
                # Revert model data
                self.roster_table_model.blockSignals(True)
                item.model().setData(item.index(), original_value, Qt.ItemDataRole.EditRole)
                item.setText(self._format_value_for_display(field_key, original_value))
                self.roster_table_model.blockSignals(False)
                QMessageBox.warning(self, self.tr("Update Failed"), self.tr("Could not save change for {} to the database.").format(header_text)) # Use header text for user message

        else:
             # This path is for non-group, non-editable fields like player_id, age, zone, flag etc.
             # No action needed here as they are not directly editable in the table or handled above.
             # self.logger.debug(f"HANDLE: Skipping update for non-editable/calculated field: '{field_key}'") # Keep commented unless debugging
             pass

    def _update_table_cell(self, player_id, field_key, value):
        """Finds the row for the player_id and updates the specific column's display text."""
        # Prevent table updates causing itemChanged signals during sync
        if self._is_updating_ui:
             self.logger.debug(f"Skipping table cell update for '{field_key}' due to UI sync flag.") # MODIFIED
             return

        target_column = -1
        for col, (key, _) in self.table_headers.items():
            if key == field_key:
                target_column = col
                break

        if target_column == -1:
            self.logger.warning(f"Column key '{field_key}' not found in table headers during _update_table_cell.") # MODIFIED
            return

        # Find the row corresponding to the player_id
        target_row = -1
        for row in range(self.roster_table_model.rowCount()):
            id_item = self.roster_table_model.item(row, 0) # ID is always col 0
            if id_item and id_item.data(Qt.ItemDataRole.UserRole + 1) == player_id:
                target_row = row
                break

        if target_row == -1:
             self.logger.warning(f"Could not find row for player ID {player_id} to update cell '{field_key}' in _update_table_cell.") # MODIFIED
             return

        # Get the item to update
        item_to_update = self.roster_table_model.item(target_row, target_column)

        # Ensure item exists
        if not item_to_update:
             item_to_update = QStandardItem()
             if field_key in ['player_id', 'age', 'zone']:
                 item_to_update.setEditable(False)
             self.roster_table_model.setItem(target_row, target_column, item_to_update)

        # Determine display and edit values based on field_key
        display_value = ""
        edit_role_value = value # Incoming 'value' is usually the correct edit role data

        if field_key == 'transfer_status': # --- Handle Transfer Status Column --- #
             # Value is the status text (or None/empty)
             # Make sure we're using the original (untranslated) status text for the edit role
             # and the translated text for display
             if value:
                 # Store original value for EditRole
                 edit_role_value = value
                 # Use translated value for display
                 display_value = self.tr(str(value))
                 self.logger.debug(f"Transfer status: original='{value}', translated='{display_value}'")
             else:
                 display_value = ""
                 edit_role_value = None
        elif field_key == 'status_tags': # --- Handle Status Summary --- #
            display_value = str(value) # Value is the summary string
            edit_role_value = display_value # Store same for edit role as it's non-editable
        elif field_key in ['primary_group_id', 'secondary_group_id']:
            # value is the group ID (int or None)
            group_id_to_name = {gid: name for name, gid in self._group_options}
            group_name = group_id_to_name.get(value, "") if value is not None else ""
            display_value = group_name if group_name else self.tr("(None)")
            edit_role_value = value # ID is correct for EditRole
            # ... (existing debug prints) ...
            self.logger.debug(f"_update_table_cell: Calculated display_value='{display_value}' for group ID.") # MODIFIED
        elif field_key == 'age':
            # Age is calculated, display and edit are the same string representation
            display_value = str(value) if value is not None else "-"
            edit_role_value = display_value
        elif field_key == 'zone':
            # Zone is calculated, display and edit are the same string
            display_value = str(value) if value else ""
            edit_role_value = display_value
        else:
             # Format other values for display
             display_value = self._format_value_for_display(field_key, value)
             edit_role_value = value # Assume incoming value is correct for EditRole

        # Check if display OR edit data changed
        current_display = item_to_update.text()
        current_edit = item_to_update.data(Qt.ItemDataRole.EditRole)

        # Need to handle potential type differences for comparison (e.g., None vs. 0.0)
        # This basic comparison might need refinement for specific types if issues arise
        edit_changed = (current_edit != edit_role_value)
        display_changed = (current_display != display_value)

        if display_changed or edit_changed:
             if display_changed:
                 self.logger.debug(f"_update_table_cell: Display text changing from '{current_display}' to '{display_value}'.") # MODIFIED
             if edit_changed:
                 self.logger.debug(f"_update_table_cell: EditRole data changing from '{current_edit}' to '{edit_role_value}'.") # MODIFIED

             self.roster_table_model.blockSignals(True)
             roles_changed = []
             if display_changed:
                 item_to_update.setText(display_value) # Set DisplayRole
                 roles_changed.append(Qt.ItemDataRole.DisplayRole)
             if edit_changed and field_key not in ['primary_group_id', 'secondary_group_id']:
                 # --- MODIFICATION: Only set EditRole here if NOT a group ID ---
                 # (Group ID EditRole is set reliably by the delegate's setModelData)
                 item_to_update.setData(edit_role_value, Qt.ItemDataRole.EditRole) # Set EditRole
                 roles_changed.append(Qt.ItemDataRole.EditRole)

             # --- Emit dataChanged signal for all changed roles ---
             if roles_changed:
                 model_index = self.roster_table_model.index(target_row, target_column)
                 self.roster_table_model.dataChanged.emit(model_index, model_index, roles_changed)
             # ---------------------------------------------------
             self.roster_table_model.blockSignals(False)
        else:
             # ... (existing debug print) ...
             self.logger.debug(f"_update_table_cell: Both Display ('{display_value}') and Edit ('{edit_role_value}') data match. No UI update needed.") # MODIFIED
             pass

    def _add_player(self):
        """Adds a new default player and refreshes the table."""
        self.logger.info("Add Player button clicked.") # MODIFIED print to logger.info
        # Add a player with default/empty values
        new_player_data = {
            'last_name': self.tr('New'),
            'first_name': self.tr('Player'),
            # Consider adding default nulls or blanks for other fields
            'shirt_number': None,
            'position': None,
            'detailed_position': None,
            'dob': None,
            'height': None,
            'weight': None,
            'nationality': None,
            'sex': None,
            'preferred_foot': None,
            'strong_eye': None
        }
        new_id = self.roster_manager.add_player(new_player_data)
        if new_id:
            self.load_data() # Reload the entire table
            # Find and select the newly added player row
            for row in range(self.roster_table_model.rowCount()):
                id_item = self.roster_table_model.item(row, 0)
                if id_item and id_item.data(Qt.ItemDataRole.UserRole + 1) == new_id:
                    self.roster_table_view.selectRow(row)
                    # Ensure tabs update for the new selection
                    self._update_tabs_on_selection(self.roster_table_view.selectionModel().selection(), None)
                    break
        else:
            QMessageBox.warning(self, self.tr("Error"), self.tr("Could not add new player to the database."))

    def _remove_player(self):
        """Removes the selected player."""
        if self.current_selected_player_id is None:
            return

        player_data = self.roster_manager.get_player(self.current_selected_player_id)
        player_name = f"{player_data.get('first_name','')} {player_data.get('last_name','')}".strip()
        if not player_name:
            player_name = f"ID {self.current_selected_player_id}"

        reply = QMessageBox.question(self,
            self.tr("Confirm Removal"),
            self.tr("Are you sure you want to remove player: {}?").format(player_name),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            success = self.roster_manager.delete_player(self.current_selected_player_id)
            if success:
                # Get the row index *before* reloading data
                selected_row = -1
                selection_model = self.roster_table_view.selectionModel()
                if selection_model and selection_model.hasSelection():
                     selected_row = selection_model.currentIndex().row()

                self.current_selected_player_id = None # Clear selection tracking first
                self.load_data() # Reload table data
                self._clear_detail_tabs() # Clear detail tabs
                self.detail_tabs.setEnabled(False) # Disable tabs
                self.remove_button.setEnabled(False) # Disable remove button
                # Optionally try to select the next row if one exists
                if selected_row != -1 and self.roster_table_model.rowCount() > 0:
                     new_selection_row = min(selected_row, self.roster_table_model.rowCount() - 1)
                     self.roster_table_view.selectRow(new_selection_row)

            else:
                QMessageBox.warning(self, self.tr("Error"), self.tr("Could not remove the selected player from the database."))

    # --- ADD: Method to load player image --- #
    def _load_player_image(self, player_id):
        """Loads the player's image or the default image into the image label."""
        target_path = None
        if player_id:
            image_filename = f"{player_id}.png"
            target_path = self.roster_images_base_path / image_filename
            self.logger.debug(f"Attempting to load player image: {target_path}") # MODIFIED

        # Fallback to default if player-specific image doesn't exist or no player_id
        if not target_path or not target_path.exists() or not target_path.is_file():
            target_path = self.default_player_image_path
            self.logger.info(f"Player image {image_filename if player_id else 'for new player'} not found or invalid. Loading default: {target_path}") # MODIFIED
        else:
            self.logger.debug(f"Found player image: {target_path}") # MODIFIED

        if target_path.exists() and target_path.is_file():
            pixmap = QPixmap(str(target_path))
            if not pixmap.isNull():
                # Scale pixmap to fit the label (200x300 frame size)
                scaled_pixmap = pixmap.scaled(self.frame1.size(),
                                              Qt.AspectRatioMode.KeepAspectRatio,
                                              Qt.TransformationMode.SmoothTransformation)
                self.player_image_label.setPixmap(scaled_pixmap)
                self.player_image_label.setStyleSheet("") # Clear background style
            else:
                self.logger.error(f"Could not load QPixmap from {target_path}. Clearing label.") # MODIFIED
                self.player_image_label.clear()
                # Keep placeholder style if default.png itself is corrupted/missing
                if target_path == self.default_player_image_path:
                     self.player_image_label.setStyleSheet("background-color: #e0e0e0;") # Placeholder
        else:
            self.logger.error(f"Target image path does not exist or is not a file: {target_path}. Clearing label.") # MODIFIED
            self.player_image_label.clear()
            self.player_image_label.setStyleSheet("background-color: #e0e0e0;") # Placeholder
    # ---------------------------------------- #

    # --- ADD: Method to handle image label click --- #
    def _handle_image_label_click(self):
        """Handles clicks on the player image label to upload a new image."""
        if self.current_selected_player_id is None:
            QMessageBox.information(self, self.tr("Select Player"),
                                    self.tr("Please select a player before uploading an image."))
            return
        self.logger.info(f"Upload image initiated for player ID: {self.current_selected_player_id}") # ADDED

        # Open File Dialog
        file_dialog = QFileDialog(self, self.tr("Select Player Image"))
        file_dialog.setNameFilter(self.tr("PNG Images (*.png)"))
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)

        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            if not selected_files:
                self.logger.info("Image selection dialog cancelled by user.") # ADDED
                return # User cancelled

            source_path_str = selected_files[0]
            source_path = Path(source_path_str)
            self.logger.info(f"Image selected for upload: {source_path}") # MODIFIED

            # --- Validation --- #
            # 1. File Type (already filtered by dialog, but double-check extension)
            if source_path.suffix.lower() != ".png":
                QMessageBox.warning(self, self.tr("Invalid File Type"),
                                    self.tr("Please select a PNG image file."))
                return

            # 2. File Size (Max 20KB)
            max_size_kb = 20
            max_size_bytes = max_size_kb * 1024
            try:
                file_size = source_path.stat().st_size
                if file_size > max_size_bytes:
                    QMessageBox.warning(self, self.tr("File Too Large"),
                                        self.tr("Image file size must be less than {}KB. Selected size: {:.1f}KB")
                                        .format(max_size_kb, file_size / 1024.0))
                    return
                self.logger.debug(f"File size check OK: {file_size / 1024.0:.1f}KB") # MODIFIED
            except OSError as e:
                QMessageBox.critical(self, self.tr("Error Reading File"),
                                     self.tr("Could not read file properties: {}").format(e))
                return

            # 3. Dimensions (Must be 200x300)
            reader = QImageReader(source_path_str)
            if not reader.canRead():
                QMessageBox.warning(self, self.tr("Cannot Read Image"),
                                    self.tr("Could not read image data. The file might be corrupted."))
                return

            image_size = reader.size()
            required_width = 200
            required_height = 300
            # --- CORRECTED: Check if dimensions are LESS THAN OR EQUAL TO required size --- #
            if image_size.width() > required_width or image_size.height() > required_height:
                QMessageBox.warning(self, self.tr("Incorrect Dimensions"),
                                    self.tr("Image dimensions must be no larger than {}x{} pixels. Selected dimensions: {}x{} pixels.")
                                    .format(required_width, required_height, image_size.width(), image_size.height()))
                return
            # ------------------------------------------------------------------------------ #
            self.logger.debug(f"Dimensions check OK: {image_size.width()}x{image_size.height()}") # MODIFIED
            # --- End Validation --- #

            # --- Copy and Save --- #
            destination_filename = f"{self.current_selected_player_id}.png"
            destination_path = self.roster_images_base_path / destination_filename

            try:
                shutil.copy2(source_path, destination_path) # copy2 preserves metadata
                self.logger.info(f"Image successfully copied from {source_path} to: {destination_path} for player {self.current_selected_player_id}") # MODIFIED
                # Update the display immediately
                self._load_player_image(self.current_selected_player_id)
                QMessageBox.information(self, self.tr("Upload Successful"),
                                        self.tr("Player image updated successfully."))
            except Exception as e:
                self.logger.error(f"Error copying image from {source_path} to {destination_path}: {e}") # MODIFIED
                QMessageBox.critical(self, self.tr("Upload Failed"),
                                     self.tr("Could not save the image: {}").format(e))
                # Optionally, reload the previous/default image if save fails
                self._load_player_image(self.current_selected_player_id)
            # --- End Copy --- #
        else:
            # User closed the dialog (X button) or cancelled
            self.logger.info("Image selection dialog closed or cancelled by user.") # MODIFIED
            # No action needed, fallback to existing/default image happens automatically
            # or is handled by _load_player_image if called elsewhere.
            pass
    # ----------------------------------------------- #

    def save_splitter_state(self):
        """Saves the splitter position."""
        settings = QSettings()
        settings.setValue("rosterPage/splitterSizes", QByteArray(self.splitter.saveState()))
        self.logger.debug("Splitter state saved") # MODIFIED

    def load_splitter_state(self):
        """Restores the splitter position."""
        settings = QSettings()
        splitter_state = settings.value("rosterPage/splitterSizes")
        if isinstance(splitter_state, QByteArray):
            if self.splitter.restoreState(splitter_state):
                self.logger.debug("Splitter state restored") # MODIFIED
            else:
                self.logger.warning("Failed to restore splitter state, using default.") # MODIFIED
                # Adjust default split based on typical use (e.g., more space for table)
                default_split = [max(250, self.width() // 3), self.width() * 2 // 3]
                self.splitter.setSizes(default_split)
        elif splitter_state:
             self.logger.warning("Warning: Splitter state found in unexpected format, using default.") # MODIFIED
             default_split = [max(250, self.width() // 3), self.width() * 2 // 3]
             self.splitter.setSizes(default_split)
        else:
            self.logger.info("No splitter state found, using default.") # MODIFIED
            # Default split with more space for table
            default_split = [max(250, self.width() // 3), self.width() * 2 // 3]
            self.splitter.setSizes(default_split)

    def _save_header_state(self):
        """Saves the horizontal header state (column order and sizes)."""
        settings = QSettings()
        header = self.roster_table_view.horizontalHeader()
        state_data = QByteArray(header.saveState()) # Capture state first
        settings.setValue("rosterPage/headerState", state_data)
        # DEBUGGING: Print confirmation and size of saved data
        self.logger.debug(f"Header state saved. Data size: {len(state_data)} bytes") # MODIFIED

    def _load_header_state(self):
        """Restores the horizontal header state."""
        settings = QSettings()
        header_state = settings.value("rosterPage/headerState")
        header = self.roster_table_view.horizontalHeader()
        if isinstance(header_state, QByteArray) and not header_state.isEmpty(): # Check if not empty
            # DEBUGGING: Print confirmation before restoring
            self.logger.debug(f"Restoring header state. Data size: {len(header_state)} bytes") # MODIFIED
            if header.restoreState(header_state):
                self.logger.debug("Header state restored successfully.") # MODIFIED
            else:
                self.logger.warning("Failed to restore header state using restoreState().") # MODIFIED
        elif isinstance(header_state, QByteArray) and header_state.isEmpty():
             self.logger.warning("Warning: Found empty header state in settings, using default.") # MODIFIED
        elif header_state:
             self.logger.warning(f"Header state found in unexpected format ({type(header_state)}), using default order.") # MODIFIED
        else:
            self.logger.info("No header state found in settings, using default order.") # MODIFIED

    def _load_geometry(self):
        """Restores the window size and position."""
        settings = QSettings()
        geometry = settings.value("rosterPage/geometry")
        if isinstance(geometry, QByteArray):
            if self.restoreGeometry(geometry):
                self.logger.debug("Roster window geometry restored.") # MODIFIED
            else:
                self.logger.warning("Failed to restore roster window geometry.") # MODIFIED
                # Optionally set a default size if restore fails
                # self.resize(1000, 700)
        else:
            self.logger.info("No roster window geometry found, using default.") # MODIFIED
            # Set a default size if no geometry is saved
            self.resize(1000, 700)

    def _save_geometry(self):
        """Saves the window size and position."""
        settings = QSettings()
        settings.setValue("rosterPage/geometry", self.saveGeometry())
        self.logger.debug("Roster window geometry saved.") # MODIFIED



    def changeEvent(self, event):
        """Handle change events, including language changes."""
        if event.type() == QEvent.LanguageChange:
            self.logger.info("Language change event received, retranslating UI")
            self.retranslateUi()

            # DIRECT APPROACH: Manually update all problematic labels
            # This is the most reliable way to ensure all labels are properly translated

            # Define the exact translation keys for each problematic label
            problematic_labels = [
                "Emotional Intelligence Notes:",
                "Life Motto / Quote:",
                "Personal Goals (Outside Football):",
                "Parents' Occupations:",
                "Siblings Summary:",
                "Family Background Notes:",
                "Pets Description:",
                "Superstitions / Rituals:",
                "Religious Practices Notes:",
                "Political Views Summary:",
                "Charity / Community Involvement:",
                "Volunteer Work Summary:",
                "Business Ventures:",
                "Public Image Summary:",
                "Media Availability Notes:",
                "Home Address:",
                "Retirement Plans Summary:",
                "Biography:"
            ]

            # Find all QLabel widgets in the form layout
            for row in range(self.profile_form_layout.rowCount()):
                label_item = self.profile_form_layout.itemAt(row, QFormLayout.ItemRole.LabelRole)
                if label_item and label_item.widget() and isinstance(label_item.widget(), QLabel):
                    label = label_item.widget()

                    # Check if this is one of our problematic labels
                    for key in problematic_labels:
                        # Try to match with or without colon
                        if key[:-1] in label.text() or key in label.text():
                            # Force update with the translated text
                            translated_text = self.tr(key)
                            label.setText(translated_text)
                            self.logger.info(f"DIRECT UPDATE: '{label.text()}' -> '{translated_text}'")
                            break

            # Also directly update all QLabel widgets in the Profile tab
            # This is a more targeted approach that doesn't require rebuilding the tab
            profile_tab = None
            for i in range(self.detail_tabs.count()):
                widget = self.detail_tabs.widget(i)
                if widget.findChild(QTextEdit, "profile_biography"):
                    profile_tab = widget
                    break

            if profile_tab:
                # Get all QLabel widgets in the Profile tab
                all_labels = profile_tab.findChildren(QLabel)
                self.logger.info(f"Found {len(all_labels)} QLabel widgets in Profile tab")

                # Update all labels that match our problematic labels
                for label in all_labels:
                    for key in problematic_labels:
                        # Try to match with or without colon
                        if key[:-1] in label.text() or key in label.text():
                            # Force update with the translated text
                            translated_text = self.tr(key)
                            label.setText(translated_text)
                            self.logger.info(f"DIRECT UPDATE: '{label.text()}' -> '{translated_text}'")
                            break

            # Force update of profile labels as a final measure
            self._force_update_profile_labels()

        super().changeEvent(event)

    def _on_tab_changed(self, index):
        """Handle tab changes and update translations when switching to the Profile tab."""
        # Find the Profile tab index
        profile_tab_index = -1
        for i in range(self.detail_tabs.count()):
            widget = self.detail_tabs.widget(i)
            if widget.findChild(QTextEdit, "profile_biography"):
                profile_tab_index = i
                break

        # If switching to the Profile tab, force update of translations
        if index == profile_tab_index:
            self.logger.info("Switched to Profile tab, forcing update of translations")
            self._force_update_profile_labels()

        # Find the Selections tab index
        selections_tab_index = -1
        for i in range(self.detail_tabs.count()):
            widget = self.detail_tabs.widget(i)
            if widget.objectName() == "selections_tab":
                selections_tab_index = i
                break

        # If switching to the Selections tab, load player selections
        if index == selections_tab_index and self.current_selected_player_id:
            self.logger.info(f"Switched to Selections tab, loading selections for player {self.current_selected_player_id}")
            self._load_player_selections(self.current_selected_player_id)

    def _handle_selection_checkbox_changed(self, state, container, field):
        """Handle selection checkbox state changes.

        Args:
            state (int): The checkbox state (Qt.Checked or Qt.Unchecked).
            container (QWidget): The container widget for the reason field.
            field (QLineEdit): The reason field.
        """
        # Show/hide reason field based on checkbox state
        is_checked = state == Qt.CheckState.Checked.value
        container.setVisible(not is_checked)

        # Clear reason field if checkbox is checked
        if is_checked:
            field.clear()

    def _update_player_selection(self, selection_type, selected):
        """Update player selection in the database.

        Args:
            selection_type (str): The type of selection (e.g., 'attendance', 'squad_list').
            selected (bool): Whether the player is selected (True) or not (False).
        """
        player_id = self.current_selected_player_id
        if not player_id:
            return

        # Get the reason field for this selection type
        reason_field = self.selection_reason_fields.get(selection_type)
        reason = None

        if not selected and reason_field:
            reason = reason_field.text()

        # Check if we're unselecting a player from attendance and they have attendance data
        if selection_type == 'attendance' and not selected:
            # Get the attendance manager from the main window
            attendance_manager = None
            if self.main_window and hasattr(self.main_window, 'attendance_manager'):
                attendance_manager = self.main_window.attendance_manager

            # If we have an attendance manager, check if the player has attendance data
            if attendance_manager and attendance_manager.has_attendance_data(player_id):
                # Get player name for the message
                player_data = self.roster_manager.get_player(player_id)
                player_name = f"{player_data.get('last_name', '')}, {player_data.get('first_name', '')}"

                # Show confirmation dialog
                confirmation = QMessageBox.question(
                    self,
                    self.tr("Confirm Unselect"),
                    self.tr("Player {0} already has attendance data. Are you sure you want to unselect them from attendance?").format(player_name),
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No  # Default is No to prevent accidental unselection
                )

                if confirmation != QMessageBox.StandardButton.Yes:
                    # User cancelled, revert the checkbox state
                    checkbox = self.selection_checkboxes.get(selection_type)
                    if checkbox:
                        checkbox.blockSignals(True)
                        checkbox.setChecked(True)
                        checkbox.blockSignals(False)

                        # Hide the reason field
                        if reason_field:
                            for container in checkbox.parent().findChildren(QWidget):
                                if container != checkbox and reason_field in container.findChildren(QLineEdit):
                                    container.setVisible(False)
                                    break
                    return

        # Update the selection in the database
        success = self.roster_manager.set_player_selection(player_id, selection_type, selected, reason)

        if success:
            self.logger.info(f"Updated selection for player {player_id}, type {selection_type}: selected={selected}, reason={reason}")
            # Update the table to show the selection status
            self._update_table_selection_indicators()

            # Update attendance statistics if attendance selection changed
            if selection_type == 'attendance':
                self._update_attendance_stats(player_id)

            # Emit the selection_changed signal
            self.logger.info(f"Emitting selection_changed signal: {selection_type}, {selected}")
            try:
                # Check if anyone is connected to this signal
                connection_count = self.selection_changed.receivers()
                self.logger.info(f"Number of connections to selection_changed signal: {connection_count}")

                # Emit the signal
                self.selection_changed.emit(selection_type, selected)
                self.logger.info(f"Signal emitted successfully")
            except Exception as e:
                self.logger.error(f"Error emitting selection_changed signal: {e}")
                import traceback
                self.logger.error(traceback.format_exc())

            # Direct method call to MainWindow to refresh attendance window
            if selection_type == 'attendance' and self.main_window:
                self.logger.info("Directly calling MainWindow.refresh_attendance_window()")
                try:
                    self.main_window.refresh_attendance_window(selection_type)
                    self.logger.info("MainWindow.refresh_attendance_window() called successfully")
                except Exception as e:
                    self.logger.error(f"Error calling MainWindow.refresh_attendance_window(): {e}")
                    import traceback
                    self.logger.error(traceback.format_exc())
        else:
            self.logger.error(f"Failed to update selection for player {player_id}, type {selection_type}")

    def _select_all_selections(self):
        """Select all selection checkboxes for the current player."""
        if not self.current_selected_player_id:
            return

        for selection_type, checkbox in self.selection_checkboxes.items():
            # Check the checkbox
            checkbox.blockSignals(True)
            checkbox.setChecked(True)
            checkbox.blockSignals(False)

            # Hide the reason field
            reason_field = self.selection_reason_fields.get(selection_type)
            if reason_field:
                for container in checkbox.parent().findChildren(QWidget):
                    if container != checkbox and reason_field in container.findChildren(QLineEdit):
                        container.setVisible(False)
                        break

        # Update all selections in the database
        for selection_type in self.selection_checkboxes.keys():
            self._update_player_selection(selection_type, True)

    def _deselect_all_selections(self):
        """Deselect all selection checkboxes for the current player."""
        if not self.current_selected_player_id:
            return

        for selection_type, checkbox in self.selection_checkboxes.items():
            # Uncheck the checkbox
            checkbox.blockSignals(True)
            checkbox.setChecked(False)
            checkbox.blockSignals(False)

            # Show the reason field
            reason_field = self.selection_reason_fields.get(selection_type)
            if reason_field:
                for container in checkbox.parent().findChildren(QWidget):
                    if container != checkbox and reason_field in container.findChildren(QLineEdit):
                        container.setVisible(True)
                        break

        # Update all selections in the database
        for selection_type in self.selection_checkboxes.keys():
            self._update_player_selection(selection_type, False)

    def _update_table_selection_indicators(self):
        """Update the selection indicators in the main roster table."""
        if not self.current_selected_player_id:
            return

        # Get all selections for the current player
        selections = self.roster_manager.get_player_selections(self.current_selected_player_id)
        if not selections:
            return

        # Create a dictionary of selection types and their status
        selection_status = {}
        for selection in selections:
            selection_type = selection.get('selection_type')
            selected = selection.get('selected', False)
            reason = selection.get('reason', '')
            selection_status[selection_type] = (selected, reason)

        # Find the row for the current player
        target_row = -1
        for row in range(self.roster_table_model.rowCount()):
            id_item = self.roster_table_model.item(row, 0)
            if id_item and id_item.data(Qt.ItemDataRole.UserRole + 1) == self.current_selected_player_id:
                target_row = row
                break

        if target_row == -1:
            return

        # Update the selection columns
        self.roster_table_model.blockSignals(True)

        # Map selection types to column indices
        selection_columns = {
            'attendance': 27,
            'squad_list': 28,
            'match_day': 29,
            'evaluation': 30,
            'physical_tests': 31
        }

        # Update each selection column
        for selection_type, col_index in selection_columns.items():
            selected, reason = selection_status.get(selection_type, (False, ''))

            # Create or get the item
            item = self.roster_table_model.item(target_row, col_index)
            if not item:
                item = QStandardItem()
                self.roster_table_model.setItem(target_row, col_index, item)

            # Set the display text and tooltip
            if selected:
                item.setText("✓")
                item.setToolTip(self.tr("Selected"))
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                item.setForeground(QBrush(QColor("green")))
            else:
                item.setText("✗")
                tooltip = self.tr("Not selected")
                if reason:
                    tooltip += f": {reason}"
                item.setToolTip(tooltip)
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                item.setForeground(QBrush(QColor("red")))

        self.roster_table_model.blockSignals(False)
        self.roster_table_view.viewport().update()

    def on_selection_changed(self, selection_type, selected):
        """Handle selection changes from other parts of the application.

        Args:
            selection_type (str): The type of selection that changed.
            selected (bool): Whether the selection is now selected (True) or not (False).
        """
        self.logger.info(f"RosterPage.on_selection_changed called: {selection_type}, {selected}")

        # Update the table to show the selection status
        self._update_table_selection_indicators()

        # Update the selection checkboxes if a player is selected
        if self.current_selected_player_id:
            self._load_player_selections(self.current_selected_player_id)

        # Update attendance statistics if attendance selection changed
        if selection_type == 'attendance' and self.current_selected_player_id:
            self._update_attendance_stats(self.current_selected_player_id)

    def _update_attendance_stats(self, player_id):
        """Update the attendance statistics label for the selected player.

        Args:
            player_id (int): The ID of the player to update attendance statistics for.
        """
        if not player_id or not hasattr(self, 'attendance_stats_label'):
            self.logger.warning("Cannot update attendance stats: player_id is missing or attendance_stats_label not found")
            return

        # Get the attendance manager from the main window
        attendance_manager = None
        if self.main_window and hasattr(self.main_window, 'attendance_manager'):
            attendance_manager = self.main_window.attendance_manager
            self.logger.info(f"Using attendance_manager from main_window")

        if not attendance_manager:
            self.logger.warning("No attendance_manager available, creating a temporary one")
            from app.data.attendance_manager import AttendanceManager
            attendance_manager = AttendanceManager()

        # Get attendance summary for the player
        self.logger.info(f"Getting attendance summary for player {player_id}")
        summary = attendance_manager.get_attendance_summary(player_id=player_id)

        if not summary or summary.get('total', 0) == 0:
            self.logger.info(f"No attendance data found for player {player_id}")
            self.attendance_stats_label.setText("-")
            return

        # Format the statistics as "P=X, A=Y, I=Z, IP=W, S=V"
        stats_text = ", ".join([f"{status}={count}" for status, count in summary.items()
                               if status in ['P', 'A', 'I', 'IP', 'S'] and count > 0])

        self.logger.info(f"Setting attendance stats for player {player_id}: {stats_text}")
        self.attendance_stats_label.setText(stats_text)

    def _load_player_selections(self, player_id):
        """Load selection data for the selected player.

        Args:
            player_id (int): The ID of the player to load selections for.
        """
        if not player_id:
            # Clear all checkboxes and reason fields
            for checkbox in self.selection_checkboxes.values():
                checkbox.blockSignals(True)
                checkbox.setChecked(False)
                checkbox.blockSignals(False)

            for field in self.selection_reason_fields.values():
                field.clear()

            # Show all reason fields when no player is selected
            for selection_type, checkbox in self.selection_checkboxes.items():
                reason_field = self.selection_reason_fields.get(selection_type)
                if reason_field:
                    for container in checkbox.parent().findChildren(QWidget):
                        if container != checkbox and reason_field in container.findChildren(QLineEdit):
                            container.setVisible(True)
                            break

            # Clear attendance stats
            if hasattr(self, 'attendance_stats_label'):
                self.attendance_stats_label.setText("-")

            return

        # Get selection data from the database
        selections = self.roster_manager.get_player_selections(player_id)
        self.logger.info(f"Loaded {len(selections)} selections for player {player_id}")

        # Create a dictionary of selection types and their status
        selection_status = {}
        for selection in selections:
            selection_type = selection.get('selection_type')
            selected = selection.get('selected', False)
            reason = selection.get('reason', '')
            selection_status[selection_type] = (selected, reason)

        # Update all checkboxes and reason fields
        for selection_type, checkbox in self.selection_checkboxes.items():
            reason_field = self.selection_reason_fields[selection_type]
            selected, reason = selection_status.get(selection_type, (False, ''))

            # Update checkbox state
            checkbox.blockSignals(True)
            checkbox.setChecked(selected)
            checkbox.blockSignals(False)

            # Update reason field
            reason_field.setText(reason or '')

            # Show/hide reason field based on selection state
            for container in checkbox.parent().findChildren(QWidget):
                if container != checkbox and reason_field in container.findChildren(QLineEdit):
                    container.setVisible(not selected)
                    break

        # Update attendance statistics
        self._update_attendance_stats(player_id)

    def _force_update_profile_labels(self):
        """Directly updates the labels for QTextEdit widgets in the Profile tab.
        This ensures that all labels are properly translated, even if the form layout structure is complex."""
        self.logger.info("Forcing update of Profile tab labels")

        try:
            # Find the Profile tab
            profile_tab = None
            for i in range(self.detail_tabs.count()):
                widget = self.detail_tabs.widget(i)
                if widget.findChild(QTextEdit, "profile_biography"):
                    profile_tab = widget
                    break

            if not profile_tab:
                self.logger.warning("Could not find Profile tab")
                return

            # Get all QLabel widgets in the Profile tab
            all_labels = profile_tab.findChildren(QLabel)
            self.logger.info(f"Found {len(all_labels)} QLabel widgets in Profile tab")

            # Update all labels with their translations
            for label in all_labels:
                original_text = label.text()
                if original_text:
                    translated_text = self.tr(original_text)
                    if translated_text != original_text:
                        label.setText(translated_text)
                        self.logger.debug(f"Updated label: '{original_text}' -> '{translated_text}'")

            self.logger.info("Profile tab labels updated successfully")
        except Exception as e:
            self.logger.error(f"Error updating Profile tab labels: {e}")

    def _update_tooltips(self):
        """Update tooltips for all UI elements."""
        self.logger.info("Updating tooltips for RosterPage")

        try:
            # Import tooltip utilities
            from app.utils.tooltip_helper import set_tooltip, apply_tab_tooltips, apply_window_tooltip
            import logging

            logger = logging.getLogger(__name__)
            logger.info("Applying tooltips to RosterPage")

            # Apply tooltip to the window itself
            apply_window_tooltip(self)

            # Apply tooltips to all tabs
            if hasattr(self, 'detail_tabs'):
                apply_tab_tooltips(self.detail_tabs)

            # A. Buttons
            if hasattr(self, 'add_player_button'):
                set_tooltip(self.add_player_button, "Add a new player to the roster.")
            if hasattr(self, 'remove_player_button'):
                set_tooltip(self.remove_player_button, "Remove the selected player from the roster.")
            if hasattr(self, 'show_basic_columns_button'):
                set_tooltip(self.show_basic_columns_button, "Show only basic columns in the table.")
            if hasattr(self, 'show_extended_columns_button'):
                set_tooltip(self.show_extended_columns_button, "Show only extended columns in the table.")
            if hasattr(self, 'show_all_columns_button'):
                set_tooltip(self.show_all_columns_button, "Show all columns in the table.")
            if hasattr(self, 'view_chart_button'):
                set_tooltip(self.view_chart_button, "View a chart of the selected player's physical metrics.")

            # Alternative button names
            if hasattr(self, 'add_button'):
                set_tooltip(self.add_button, "Add a new player to the roster.")
            if hasattr(self, 'remove_button'):
                set_tooltip(self.remove_button, "Remove the selected player from the roster.")
            if hasattr(self, 'basic_view_button'):
                set_tooltip(self.basic_view_button, "Show only basic columns in the table.")
            if hasattr(self, 'extended_view_button'):
                set_tooltip(self.extended_view_button, "Show only extended columns in the table.")
            if hasattr(self, 'all_view_button'):
                set_tooltip(self.all_view_button, "Show all columns in the table.")
            if hasattr(self, 'chart_button'):
                set_tooltip(self.chart_button, "View a chart of the selected player's physical metrics.")

            # C. Form Fields - Personal Tab
            if hasattr(self, 'player_detail_widgets'):
                # Personal tab fields
                if 'last_name_input' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['last_name_input'], "Enter the player's last name.")
                if 'first_name_input' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['first_name_input'], "Enter the player's first name.")
                if 'dob_edit' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['dob_edit'], "Enter the player's date of birth.")
                if 'nationality_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['nationality_combo'], "Select the player's nationality.")
                if 'position_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['position_combo'], "Select the player's primary position.")
                if 'detailed_position_input' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['detailed_position_input'], "Enter the player's detailed position.")
                if 'shirt_number_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['shirt_number_spinbox'], "Enter the player's shirt number.")
                if 'preferred_foot_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['preferred_foot_combo'], "Select the player's preferred foot.")
                if 'strong_eye_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['strong_eye_combo'], "Select the player's strong eye.")
                if 'sex_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['sex_combo'], "Select the player's sex.")
                if 'primary_group_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['primary_group_combo'], "Select the player's primary group.")
                if 'secondary_group_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['secondary_group_combo'], "Select the player's secondary group.")

                # Physical tab fields
                if 'height_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['height_spinbox'], "Enter the player's height in centimeters.")
                if 'weight_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['weight_spinbox'], "Enter the player's weight in kilograms.")
                if 'waist_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['waist_spinbox'], "Enter the player's waist measurement in centimeters.")
                if 'hip_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['hip_spinbox'], "Enter the player's hip measurement in centimeters.")
                if 'neck_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['neck_spinbox'], "Enter the player's neck measurement in centimeters.")
                if 'wrist_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['wrist_spinbox'], "Enter the player's wrist measurement in centimeters.")
                if 'forearm_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['forearm_spinbox'], "Enter the player's forearm measurement in centimeters.")
                if 'thigh_spinbox' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['thigh_spinbox'], "Enter the player's thigh measurement in centimeters.")

                # Status tab fields
                if 'fitness_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['fitness_combo'], "Select the player's current fitness status.")
                if 'transfer_status_combo' in self.player_detail_widgets:
                    set_tooltip(self.player_detail_widgets['transfer_status_combo'], "Select the player's current transfer status.")

                # Status Tags
                for tag_key, tag_label in STATUS_TAG_OPTIONS:
                    checkbox_key = f'status_{tag_key}'
                    if checkbox_key in self.player_detail_widgets:
                        set_tooltip(self.player_detail_widgets[checkbox_key], f"Check to mark this player as {tag_label}.")

                # Selections Tab
                for selection_type in self.selection_checkboxes.keys():
                    checkbox_key = f"{selection_type}_checkbox"
                    reason_key = f"{selection_type}_reason"

                    if checkbox_key in self.player_detail_widgets:
                        set_tooltip(self.player_detail_widgets[checkbox_key], f"Check to include this player in the {selection_type.replace('_', ' ')} selection.")

                    if reason_key in self.player_detail_widgets:
                        set_tooltip(self.player_detail_widgets[reason_key], f"Enter reason for excluding this player from the {selection_type.replace('_', ' ')} selection.")

                # Selection buttons
                if hasattr(self, 'select_all_selections_button'):
                    set_tooltip(self.select_all_selections_button, "Select all selection options for this player.")
                if hasattr(self, 'deselect_all_selections_button'):
                    set_tooltip(self.deselect_all_selections_button, "Deselect all selection options for this player.")

            # D. Table Headers
            if hasattr(self, 'roster_table_view') and self.roster_table_view:
                set_tooltip(self.roster_table_view.horizontalHeader(), "Right-click to show/hide columns.")
            elif hasattr(self, 'table') and self.table:
                set_tooltip(self.table.horizontalHeader(), "Right-click to show/hide columns.")

            # E. Filter Controls
            if hasattr(self, 'search_input'):
                set_tooltip(self.search_input, "Type to search all columns.")
            if hasattr(self, 'search_edit'):
                set_tooltip(self.search_edit, "Type to search all columns.")
            if hasattr(self, 'global_search_input'):
                set_tooltip(self.global_search_input, "Type to search all columns.")

            # Filter combos
            if hasattr(self, 'position_filter_combo'):
                set_tooltip(self.position_filter_combo, "Filter the table by position.")
            if hasattr(self, 'group_filter_combo'):
                set_tooltip(self.group_filter_combo, "Filter the table by group.")
            if hasattr(self, 'nationality_zone_filter_combo'):
                set_tooltip(self.nationality_zone_filter_combo, "Filter the table by nationality zone.")
            if hasattr(self, 'zone_filter_combo'):
                set_tooltip(self.zone_filter_combo, "Filter the table by nationality zone.")
            if hasattr(self, 'foot_filter_combo'):
                set_tooltip(self.foot_filter_combo, "Filter the table by preferred foot.")
            if hasattr(self, 'sex_filter_combo'):
                set_tooltip(self.sex_filter_combo, "Filter the table by sex.")

            # F. Image/Kit Labels
            if hasattr(self, 'player_image_label'):
                set_tooltip(self.player_image_label, "Right-click for more options.")
            if hasattr(self, 'kit_image_label'):
                set_tooltip(self.kit_image_label, "Displays the player's kit with name and number.")
            if hasattr(self, 'kit_label'):
                set_tooltip(self.kit_label, "Displays the player's kit with name and number.")

            self.logger.info("Tooltips updated successfully for RosterPage")
        except Exception as e:
            self.logger.error(f"Error updating tooltips: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

            # NUCLEAR OPTION: Completely rebuild all QLabel widgets in the form layout
            # This is the most aggressive approach to ensure all labels are properly translated

            # Define the exact English text and translation key for each problematic label
            # This ensures we can identify and translate them regardless of current language
            problematic_labels = [
                ("Emotional Intelligence Notes:", "Emotional Intelligence Notes:"),
                ("Life Motto / Quote:", "Life Motto / Quote:"),
                ("Personal Goals (Outside Football):", "Personal Goals (Outside Football):"),
                ("Parents' Occupations:", "Parents' Occupations:"),
                ("Siblings Summary:", "Siblings Summary:"),
                ("Family Background Notes:", "Family Background Notes:"),
                ("Pets Description:", "Pets Description:"),
                ("Superstitions / Rituals:", "Superstitions / Rituals:"),
                ("Religious Practices Notes:", "Religious Practices Notes:"),
                ("Political Views Summary:", "Political Views Summary:"),
                ("Charity / Community Involvement:", "Charity / Community Involvement:"),
                ("Volunteer Work Summary:", "Volunteer Work Summary:"),
                ("Business Ventures:", "Business Ventures:"),
                ("Public Image Summary:", "Public Image Summary:"),
                ("Media Availability Notes:", "Media Availability Notes:"),
                ("Home Address:", "Home Address:"),
                ("Retirement Plans Summary:", "Retirement Plans Summary:"),
                ("Biography:", "Biography:")
            ]

            # Also include variations without the colon
            for english, key in list(problematic_labels):
                if english.endswith(':'):
                    problematic_labels.append((english[:-1], key))

            # Create a mapping of QTextEdit widgets to their label texts
            textedit_to_label = {
                self.profile_emotional_intelligence_notes: "Emotional Intelligence Notes:",
                self.profile_life_motto_quote: "Life Motto / Quote:",
                self.profile_personal_goals_outside_football: "Personal Goals (Outside Football):",
                self.profile_parents_occupations: "Parents' Occupations:",
                self.profile_siblings_summary: "Siblings Summary:",
                self.profile_family_background_notes: "Family Background Notes:",
                self.profile_pets_description: "Pets Description:",
                self.profile_superstitions_rituals: "Superstitions / Rituals:",
                self.profile_religious_practices_notes: "Religious Practices Notes:",
                self.profile_political_views_summary: "Political Views Summary:",
                self.profile_charity_community_involvement: "Charity / Community Involvement:",
                self.profile_volunteer_work_summary: "Volunteer Work Summary:",
                self.profile_business_ventures: "Business Ventures:",
                self.profile_public_image_summary: "Public Image Summary:",
                self.profile_media_availability_notes: "Media Availability Notes:",
                self.profile_home_address: "Home Address:",
                self.profile_retirement_plans_summary: "Retirement Plans Summary:",
                self.profile_biography: "Biography:"
            }

            # APPROACH 1: Directly recreate all QLabel widgets for QTextEdit fields
            # This is the most reliable way to ensure all labels are properly translated
            for textedit, label_text in textedit_to_label.items():
                # Find the row containing this QTextEdit
                for row in range(self.profile_form_layout.rowCount()):
                    field_item = self.profile_form_layout.itemAt(row, QFormLayout.ItemRole.FieldRole)
                    if field_item and field_item.widget() and field_item.widget() == textedit:
                        # Found the QTextEdit, now check if there's a QLabel in the same row
                        label_item = self.profile_form_layout.itemAt(row, QFormLayout.ItemRole.LabelRole)
                        if label_item and label_item.widget() and isinstance(label_item.widget(), QLabel):
                            # Create a completely new QLabel with the translated text
                            old_label = label_item.widget()
                            translated_text = self.tr(label_text)

                            # Create new label with same properties but updated text
                            new_label = QLabel(translated_text)
                            new_label.setAlignment(old_label.alignment())
                            new_label.setFont(old_label.font())
                            new_label.setStyleSheet(old_label.styleSheet())

                            # Replace the old label with the new one
                            self.profile_form_layout.removeItem(label_item)
                            old_label.setParent(None)
                            self.profile_form_layout.setWidget(row, QFormLayout.ItemRole.LabelRole, new_label)
                            self.logger.info(f"REBUILT LABEL: '{label_text}' -> '{translated_text}'")

                        # Also check if there's a QLabel in the row above (for multi-row items)
                        if row > 0:
                            prev_label_item = self.profile_form_layout.itemAt(row-1, QFormLayout.ItemRole.LabelRole)
                            if prev_label_item and prev_label_item.widget() and isinstance(prev_label_item.widget(), QLabel):
                                # Create a completely new QLabel with the translated text
                                old_label = prev_label_item.widget()
                                translated_text = self.tr(label_text)

                                # Create new label with same properties but updated text
                                new_label = QLabel(translated_text)
                                new_label.setAlignment(old_label.alignment())
                                new_label.setFont(old_label.font())
                                new_label.setStyleSheet(old_label.styleSheet())

                                # Replace the old label with the new one
                                self.profile_form_layout.removeItem(prev_label_item)
                                old_label.setParent(None)
                                self.profile_form_layout.setWidget(row-1, QFormLayout.ItemRole.LabelRole, new_label)
                                self.logger.info(f"REBUILT LABEL (prev row): '{label_text}' -> '{translated_text}'")

            # APPROACH 2: Find all standalone QLabel widgets in the form layout
            # This catches any labels that might not be directly associated with a QTextEdit
            for row in range(self.profile_form_layout.rowCount()):
                label_item = self.profile_form_layout.itemAt(row, QFormLayout.ItemRole.LabelRole)
                if label_item and label_item.widget() and isinstance(label_item.widget(), QLabel):
                    label = label_item.widget()
                    label_text = label.text()

                    # Check if this is one of our problematic labels
                    for english_text, key in problematic_labels:
                        if english_text in label_text:
                            # Create a completely new QLabel with the translated text
                            translated_text = self.tr(key)

                            # Create new label with same properties but updated text
                            new_label = QLabel(translated_text)
                            new_label.setAlignment(label.alignment())
                            new_label.setFont(label.font())
                            new_label.setStyleSheet(label.styleSheet())

                            # Replace the old label with the new one
                            self.profile_form_layout.removeItem(label_item)
                            label.setParent(None)
                            self.profile_form_layout.setWidget(row, QFormLayout.ItemRole.LabelRole, new_label)
                            self.logger.info(f"REBUILT STANDALONE LABEL: '{label_text}' -> '{translated_text}'")
                            break

            # APPROACH 3: Find all QLabel widgets in the entire Profile tab
            # This is a catch-all for any labels we might have missed
            profile_tab = None
            for i in range(self.detail_tabs.count()):
                widget = self.detail_tabs.widget(i)
                if widget.findChild(QTextEdit, "profile_biography"):
                    profile_tab = widget
                    break

            if profile_tab:
                # Get all QLabel widgets in the Profile tab
                all_labels = profile_tab.findChildren(QLabel)
                self.logger.info(f"Found {len(all_labels)} QLabel widgets in Profile tab")

                # Update all labels that match our problematic labels
                for label in all_labels:
                    label_text = label.text()
                    for english_text, key in problematic_labels:
                        if english_text in label_text:
                            translated_text = self.tr(key)
                            label.setText(translated_text)
                            self.logger.info(f"UPDATED TAB LABEL: '{label_text}' -> '{translated_text}'")
                            break

            self.logger.info("COMPLETE REBUILD of Profile tab labels completed")

    def tr(self, text):
        """Helper for translation."""
        return QCoreApplication.translate("RosterPage", text)

    def retranslateUi(self):
        """Retranslates all static UI elements."""
        self.logger.info("Retranslating RosterPage UI") # MODIFIED
        self._is_updating_ui = True
        self.roster_table_model.blockSignals(True)

        # --- Improved Tab Identification Logic --- #
        # Directly translate all tabs
        tab_texts = {
            "personal_tab": self.tr("Personal"),
            "position_tab": self.tr("Position"),
            "physical_tab": self.tr("Physical"),
            "status_tab": self.tr("Status"),
            "profile_tab": self.tr("Profile"),
            "selections_tab": self.tr("Selections")
        }

        # Iterate through all tabs and translate them based on object names or content
        for i in range(self.detail_tabs.count()):
            tab_widget = self.detail_tabs.widget(i)
            tab_name = tab_widget.objectName()

            # Try to translate by object name first (most reliable)
            if tab_name in tab_texts:
                self.detail_tabs.setTabText(i, tab_texts[tab_name])
            # Fallbacks based on content
            elif tab_widget.findChild(QLineEdit, "last_name_input"):
                self.detail_tabs.setTabText(i, self.tr("Personal"))
            elif tab_widget.findChild(QDoubleSpinBox, "height_spinbox"):
                self.detail_tabs.setTabText(i, self.tr("Physical"))
            elif tab_widget.findChild(QLabel, "status_info_label"):
                self.detail_tabs.setTabText(i, self.tr("Status"))
            elif tab_widget.findChild(QTextEdit, "profile_biography"):
                self.detail_tabs.setTabText(i, self.tr("Profile"))
            elif tab_widget.objectName() == "selections_tab" or tab_widget.findChild(QLabel, "selections_info_label"):
                self.detail_tabs.setTabText(i, self.tr("Selections"))

        # --- Personal Tab Labels --- #
        # Find the personal tab widget for better reliability
        personal_tab_widget = None
        for i in range(self.detail_tabs.count()):
            widget = self.detail_tabs.widget(i)
            if widget.objectName() == "personal_tab" or widget.findChild(QLineEdit, "last_name_input"):
                personal_tab_widget = widget
                break

        if personal_tab_widget:
            # Find all form layouts in the widget
            form_layouts = personal_tab_widget.findChildren(QFormLayout)
            for form_layout in form_layouts:
                try:
                    # Create a mapping for field-to-label translations
                    field_label_map = {
                        self.player_id_label: self.tr("ID:"),
                        self.last_name_input: self.tr("Last Name:"),
                        self.first_name_input: self.tr("First Name:"),
                        self.shirt_number_spinbox: self.tr("Shirt Number:"),
                        self.position_combo: self.tr("Position:"),
                        self.detailed_pos_input: self.tr("Detailed Position:"),
                        self.dob_edit: self.tr("Date of Birth:"),
                        self.age_label: self.tr("Age:"),
                        self.zone_label: self.tr("Zone:"),
                        self.sex_combo: self.tr("Sex:"),
                        self.preferred_foot_combo: self.tr("Preferred Foot:"),
                        self.strong_eye_combo: self.tr("Strong Eye:"),
                        self.primary_group_combo: self.tr("Primary Group:"),
                        self.secondary_group_combo: self.tr("Secondary Group:")
                    }

                    # Apply all translations
                    for field, label_text in field_label_map.items():
                        label = form_layout.labelForField(field)
                        if label:
                            label.setText(label_text)

                    # Special handling for nationality with layout
                    for i in range(form_layout.rowCount()):
                        field_item = form_layout.itemAt(i, QFormLayout.ItemRole.FieldRole)
                        if field_item and field_item.layout():
                            layout = field_item.layout()
                            # Check if this layout contains the nationality combo
                            if layout.indexOf(self.nationality_combo) != -1:
                                label_item = form_layout.itemAt(i, QFormLayout.ItemRole.LabelRole)
                                if label_item and label_item.widget():
                                    label_item.widget().setText(self.tr("Nationality:"))
                except AttributeError as e:
                    self.logger.warning(f"Could not find labels for some personal fields during retranslate: {e}")
        else:
            self.logger.warning("Could not find personal tab widget during retranslate")

        # --- Position Tab Labels --- #
        position_tab_widget = None
        for i in range(self.detail_tabs.count()): # Find position tab reliably
            widget = self.detail_tabs.widget(i)
            if widget.objectName() == "position_tab" or widget.findChild(QLineEdit, "primary_position_input"):
                position_tab_widget = widget
                break

        if position_tab_widget:
            # Find the form layout directly
            form_layouts = position_tab_widget.findChildren(QFormLayout)
            if form_layouts:
                form_layout = form_layouts[0]  # Use the first form layout found
                try:
                    form_layout.labelForField(self.position_tab_combo).setText(self.tr("Position:"))
                    form_layout.labelForField(self.detailed_pos_tab_input).setText(self.tr("Detailed Position:"))
                    form_layout.labelForField(self.primary_position_input).setText(self.tr("Primary Position:"))
                    form_layout.labelForField(self.secondary_position_input).setText(self.tr("Secondary Position:"))
                    form_layout.labelForField(self.position_notes).setText(self.tr("Position Notes:"))
                except AttributeError as e:
                    self.logger.warning(f"Could not find labels for some position fields during retranslate: {e}")
            else:                self.logger.warning("Could not find QFormLayout in Position tab during retranslate.")                            # Translate position legend labels            legend_labels = position_tab_widget.findChildren(QLabel)                        # Find and update legend labels based on their style sheets            for label in legend_labels:                if label.styleSheet() and "color: #FFEB3B" in label.styleSheet():                    label.setText(self.tr("1 - Alternative"))                elif label.styleSheet() and "color: #FF9800" in label.styleSheet():                    label.setText(self.tr("2 - Can Play"))                elif label.styleSheet() and "color: #F44336" in label.styleSheet():                    label.setText(self.tr("3 - Best Fit"))                                # Update clear all ratings button if it exists            clear_button = position_tab_widget.findChild(QPushButton, "clear_all_ratings_button")            if clear_button:                clear_button.setText(self.tr("Clear All Ratings"))                clear_button.setToolTip(self.tr("Remove all position ratings for the selected player"))

        # --- Physical Tab Labels --- #
        physical_tab_widget = None
        for i in range(self.detail_tabs.count()): # Find physical tab reliably
             widget = self.detail_tabs.widget(i)
             if widget.objectName() == "physical_tab" or widget.findChild(QDoubleSpinBox, "height_spinbox"):
                 physical_tab_widget = widget
                 break

        if physical_tab_widget:
            # Find the form layout directly
            form_layouts = physical_tab_widget.findChildren(QFormLayout)
            if form_layouts:
                 form_layout = form_layouts[0]  # Use the first form layout found
                 try:
                     form_layout.labelForField(self.height_spinbox).setText(self.tr("Height:"))
                     form_layout.labelForField(self.weight_spinbox).setText(self.tr("Weight:"))
                     form_layout.labelForField(self.waist_spinbox).setText(self.tr("Waist:"))
                     form_layout.labelForField(self.hip_spinbox).setText(self.tr("Hip:"))
                     form_layout.labelForField(self.neck_spinbox).setText(self.tr("Neck:"))
                     form_layout.labelForField(self.wrist_spinbox).setText(self.tr("Wrist:"))
                     form_layout.labelForField(self.forearm_spinbox).setText(self.tr("Forearm:"))
                     form_layout.labelForField(self.thigh_spinbox).setText(self.tr("Thigh:"))
                     form_layout.labelForField(self.fitness_combo).setText(self.tr("Fitness:"))
                     # --- Retranslate Calculated Metric Labels --- #
                     form_layout.labelForField(self.bmi_label).setText(self.tr("BMI:"))
                     form_layout.labelForField(self.bfp_label).setText(self.tr("Body Fat % (Est. Navy):"))
                     form_layout.labelForField(self.lbm_label).setText(self.tr("Lean Body Mass (Est.):"))
                     form_layout.labelForField(self.whr_label).setText(self.tr("Waist-to-Hip Ratio:"))
                     form_layout.labelForField(self.whtr_label).setText(self.tr("Waist-to-Height Ratio:"))
                     # -------------------------------------------- #
                 except AttributeError as e:
                      self.logger.warning(f"Could not find labels for some physical fields during retranslate: {e}") # MODIFIED
            else:
                 self.logger.warning("Could not find QFormLayout in Physical tab during retranslate.") # MODIFIED

            # Translate view chart button if it exists
            if hasattr(self, 'view_chart_button'):
                self.view_chart_button.setText(self.tr("View Physical Profile Chart"))

        # --- Retranslate instruction labels --- #
        if hasattr(self, 'personal_info_label'):
            self.personal_info_label.setText(self.tr("Select a player from the list"))
        if hasattr(self, 'position_info_label'):
            self.position_info_label.setText(self.tr("Select a player from the list"))
        if hasattr(self, 'physical_info_label'):
            self.physical_info_label.setText(self.tr("Select a player from the list"))
        if hasattr(self, 'status_info_label'): # ADDED Status label
            self.status_info_label.setText(self.tr("Select a player from the list"))
        # --------------------------------------------- #

        # ... Retranslate Table Headers ...

        # ... Retranslate Buttons ...

        # ... Retranslate Spinbox N/A ...

        # Re-enable signals
        self.roster_table_model.blockSignals(False)

        # Retranslate Profile Tab
        profile_tab_index = -1
        for i in range(self.detail_tabs.count()):
            widget = self.detail_tabs.widget(i)
            if widget.findChild(QTextEdit, "profile_biography"):
                profile_tab_index = i
                break

        if profile_tab_index != -1:
            self.detail_tabs.setTabText(profile_tab_index, self.tr("Profile"))

        # Retranslate Profile form labels
        if hasattr(self, 'profile_form_layout'):
            try:
                # Map field widgets to their translated labels
                profile_label_map = {
                    self.profile_date_joined_club: self.tr("Date Joined Club:"),
                    self.profile_personality_type: self.tr("Personality Type:"),
                    self.profile_mentality: self.tr("Mentality:"),
                    self.profile_work_ethic: self.tr("Work Ethic:"),
                    self.profile_professionalism_level: self.tr("Professionalism:"),
                    self.profile_determination_level: self.tr("Determination:"),
                    self.profile_team_spirit: self.tr("Team Spirit:"),
                    self.profile_adaptability: self.tr("Adaptability:"),
                    self.profile_temperament: self.tr("Temperament:"),
                    self.profile_ambition_level: self.tr("Ambition:"),
                    self.profile_leadership_qualities: self.tr("Leadership:"),
                    self.profile_charisma: self.tr("Charisma:"),
                    self.profile_hometown: self.tr("Hometown:"),
                    self.profile_current_residence_city_area: self.tr("Current Residence:"),
                    self.profile_marital_status: self.tr("Marital Status:"),
                    self.profile_partner_name: self.tr("Partner's Name:"),
                    self.profile_father_name: self.tr("Father's Name:"),
                    self.profile_mother_name: self.tr("Mother's Name:"),
                    self.profile_number_of_children: self.tr("Number of Children:"),
                    self.profile_religion_faith: self.tr("Religion / Faith:"),
                    self.profile_education_level: self.tr("Education Level:"),
                    self.profile_field_of_study: self.tr("Field of Study:"),
                    self.profile_university_school_attended: self.tr("University/School:"),
                    self.profile_dual_career_other_job: self.tr("Other Job:"),
                    self.profile_willingness_to_coach_mentor: self.tr("Coaching Interest:"),
                    self.profile_media_friendliness: self.tr("Media Friendliness:"),
                    self.profile_website_blog: self.tr("Website/Blog:"),
                    self.profile_personal_contact_email: self.tr("Email:"),
                    self.profile_personal_phone_number: self.tr("Phone:"),
                    self.profile_agent_name: self.tr("Agent Name:"),
                    self.profile_personal_assistant_manager: self.tr("Personal Assistant:")
                }

                # Set translated labels for widgets with direct form row associations
                for widget, label_text in profile_label_map.items():
                    label = self.profile_form_layout.labelForField(widget)
                    if label:
                        label.setText(label_text)

                # Handle QLabel + QTextEdit pairs (where no direct labelForField exists)
                label_text_pairs = [
                    (self.tr("Emotional Intelligence Notes:"), self.profile_emotional_intelligence_notes),
                    (self.tr("Life Motto / Quote:"), self.profile_life_motto_quote),
                    (self.tr("Personal Goals (Outside Football):"), self.profile_personal_goals_outside_football),
                    (self.tr("Parents' Occupations:"), self.profile_parents_occupations),
                    (self.tr("Siblings Summary:"), self.profile_siblings_summary),
                    (self.tr("Family Background Notes:"), self.profile_family_background_notes),
                    (self.tr("Pets Description:"), self.profile_pets_description),
                    (self.tr("Superstitions / Rituals:"), self.profile_superstitions_rituals),
                    (self.tr("Religious Practices Notes:"), self.profile_religious_practices_notes),
                    (self.tr("Political Views Summary:"), self.profile_political_views_summary),
                    (self.tr("Charity/Community:"), self.profile_charity_community_involvement),
                    (self.tr("Volunteer Work:"), self.profile_volunteer_work_summary),
                    (self.tr("Business Ventures:"), self.profile_business_ventures),
                    (self.tr("Public Image:"), self.profile_public_image_summary),
                    (self.tr("Media Notes:"), self.profile_media_availability_notes),
                    (self.tr("Home Address:"), self.profile_home_address),
                    (self.tr("Retirement Plans:"), self.profile_retirement_plans_summary)
                ]

                # Update QLabel texts for text edit fields
                # Improved approach: Find QLabels that are directly followed by QTextEdit widgets
                # This is more reliable than using a simple row counter

                # Create a map of QTextEdit widgets to their translated labels
                textedit_to_label = {widget: label for label, widget in label_text_pairs}

                # First pass: Find all QTextEdit widgets in the form layout
                textedit_rows = {}
                for row in range(self.profile_form_layout.rowCount()):
                    field_item = self.profile_form_layout.itemAt(row, QFormLayout.ItemRole.FieldRole)
                    if field_item and field_item.widget() and isinstance(field_item.widget(), QTextEdit):
                        textedit_rows[row] = field_item.widget()

                # Second pass: Find QLabels that are directly above QTextEdit widgets
                for row in range(self.profile_form_layout.rowCount()):
                    # Check if the previous row has a QLabel and the current row has a QTextEdit
                    if row > 0 and row-1 in textedit_rows:
                        label_item = self.profile_form_layout.itemAt(row-1, QFormLayout.ItemRole.LabelRole)
                        textedit = textedit_rows[row-1]

                        # If we have a label and it corresponds to a QTextEdit in our mapping
                        if (label_item and label_item.widget() and isinstance(label_item.widget(), QLabel)
                            and textedit in textedit_to_label):
                            # Update the label text
                            label_item.widget().setText(textedit_to_label[textedit])

                # Third pass: Find QLabels in the same row as QTextEdits (for single-row items)
                for row in range(self.profile_form_layout.rowCount()):
                    field_item = self.profile_form_layout.itemAt(row, QFormLayout.ItemRole.FieldRole)
                    if field_item and field_item.widget() and isinstance(field_item.widget(), QTextEdit):
                        textedit = field_item.widget()
                        label_item = self.profile_form_layout.itemAt(row, QFormLayout.ItemRole.LabelRole)

                        # If we have a label and it corresponds to a QTextEdit in our mapping
                        if (label_item and label_item.widget() and isinstance(label_item.widget(), QLabel)
                            and textedit in textedit_to_label):
                            # Update the label text
                            label_item.widget().setText(textedit_to_label[textedit])

                # Final pass: Direct approach for specific QTextEdit widgets
                # This ensures that important fields are always translated correctly
                for label_text, textedit in label_text_pairs:
                    # Find the row containing this QTextEdit
                    for row in range(self.profile_form_layout.rowCount()):
                        field_item = self.profile_form_layout.itemAt(row, QFormLayout.ItemRole.FieldRole)
                        if field_item and field_item.widget() and field_item.widget() == textedit:
                            # Look for a QLabel in the row above or the same row
                            for check_row in [row-1, row]:
                                if check_row >= 0:
                                    label_item = self.profile_form_layout.itemAt(check_row, QFormLayout.ItemRole.LabelRole)
                                    if label_item and label_item.widget() and isinstance(label_item.widget(), QLabel):
                                        label_item.widget().setText(label_text)
                                        break
            except Exception as e:
                self.logger.error(f"Error retranslating profile labels: {e}") # MODIFIED

        # If there's a profile info label, update it
        if hasattr(self, 'profile_info_label'):
            self.profile_info_label.setText(self.tr("Select a player from the list"))

        # Force update of Profile tab labels
        self._force_update_profile_labels()

        self._is_updating_ui = False
        self.logger.info("RosterPage UI retranslated.") # MODIFIED

        # Retranslate new tab (using the widget instance)
        # Example if you had a TeamGroupsWidget:
        # tab_index = self.detail_tabs.indexOf(self.groups_widget)
        # if tab_index != -1: self.detail_tabs.setTabText(tab_index, self.tr("Team Groups"))

        # Retranslate child widgets if they have their own retranslateUi method
        # Example:
        # if hasattr(self.groups_widget, 'retranslateUi'):
        #     self.groups_widget.retranslateUi()

        # --- Retranslate Group Combo None item --- #
        self._populate_group_combos() # Re-populating also updates the (None) text
        # ------------------------------------------------ #

        # --- Retranslate Status Tab Elements --- #
        if hasattr(self, 'status_tags_label'):
            self.status_tags_label.setText(self.tr("Player Status Tags:"))

        if hasattr(self, 'status_tags_list_widget'):
            self.status_tags_list_widget.blockSignals(True)
            key_to_label = {key: label for key, label in STATUS_TAG_OPTIONS}
            for i in range(self.status_tags_list_widget.count()):
                item = self.status_tags_list_widget.item(i)
                key = item.data(Qt.ItemDataRole.UserRole)
                if key and key in key_to_label:
                    item.setText(self.tr(key_to_label[key]))
                else:
                    print(f"Warning: Could not find original label for status tag key '{key}' during retranslate.")
            self.status_tags_list_widget.blockSignals(False)
        # ------------------------------------- #

        # ... Retranslate Table Headers ...
        # ... Retranslate Buttons ...
        # ... Retranslate Spinbox N/A ...

        self.roster_table_model.blockSignals(False)
        self._is_updating_ui = False

        # Update tooltips
        self._update_tooltips()

        self.logger.info("RosterPage UI retranslated.") # MODIFIED

        # ... Retranslate other potential child widgets ...

        self._populate_group_combos()

        # --- Retranslate Transfer Status Elements --- #
        if hasattr(self, 'transfer_status_label'):
            self.transfer_status_label.setText(self.tr("Transfer Status:"))
        if hasattr(self, 'transfer_status_combo'):
            self.transfer_status_combo.blockSignals(True)

            # Get the current index instead of text (preserves selection across languages)
            current_index = self.transfer_status_combo.currentIndex()

            # Clear and repopulate with translated items
            self.transfer_status_combo.clear()
            self.transfer_status_combo.addItems([self.tr(s) for s in TRANSFER_STATUS_OPTIONS])

            # Restore selection by index
            if current_index >= 0:
                self.transfer_status_combo.setCurrentIndex(current_index)

            self.transfer_status_combo.blockSignals(False)

        # Retranslate detail field labels
        for key, label_widget in self.transfer_detail_labels.items():
            if key == 'contract_end_date': label_widget.setText(self.tr("Contract End Date:"))
            elif key == 'transfer_list_date': label_widget.setText(self.tr("Date Added to List:"))
            elif key == 'loan_club': label_widget.setText(self.tr("Loan Club:"))
            elif key == 'loan_start_date': label_widget.setText(self.tr("Loan Start Date:"))
            elif key == 'loan_end_date': label_widget.setText(self.tr("Loan End Date:"))
            elif key == 'sold_club': label_widget.setText(self.tr("Club Sold To:"))
            elif key == 'sold_join_date': label_widget.setText(self.tr("Date Joining New Club:"))
            elif key == 'released_date': label_widget.setText(self.tr("Date Released:"))
        # ------------------------------------------ #

        # ... Retranslate Table Headers ...
        # ... Retranslate Buttons ...
        # ... Retranslate Spinbox N/A ...

        self.roster_table_model.blockSignals(False)
        self._is_updating_ui = False
        self.logger.info("RosterPage UI retranslated.") # MODIFIED

        # ... Retranslate Group Combo None item ...
        self._populate_group_combos()

        # --- Retranslate Table Headers --- #
        self.table_headers = {
            # ... (0-24 existing headers) ...
            25: ('status_tags', self.tr('Status Tags')),
            26: ('transfer_status', self.tr('Transfer Status')) # ADDED Transfer Status Header
        }
        self.roster_table_model.setHorizontalHeaderLabels([h[1] for h in self.table_headers.values()])
        # ----------------------------------- #

        # Update all table headers with their translated texts
        self.table_headers = {
            0: ('player_id', self.tr('ID')),
            1: ('last_name', self.tr('Last Name')),
            2: ('first_name', self.tr('Name')),
            3: ('shirt_number', self.tr('No.')),
            4: ('position', self.tr('Pos.')),
            5: ('detailed_position', self.tr('Detailed Pos.')),
            6: ('dob', self.tr('Date of Birth')),
            7: ('age', self.tr('Age')),
            8: ('height', self.tr('Height')),
            9: ('weight', self.tr('Weight')),
            10: ('waist', self.tr('Waist')),
            11: ('hip', self.tr('Hip')),
            12: ('neck', self.tr('Neck')),
            13: ('wrist', self.tr('Wrist')),
            14: ('forearm', self.tr('Forearm')),
            15: ('thigh', self.tr('Thigh')),
            16: ('nationality', self.tr('Nationality')),
            17: ('flag', self.tr('Flag')),
            18: ('sex', self.tr('Sex')),
            19: ('preferred_foot', self.tr('Pref. Foot')),
            20: ('strong_eye', self.tr('Strong Eye')),
            21: ('zone', self.tr('Zone')),
            22: ('fitness', self.tr('Fitness')),
            23: ('primary_group_id', self.tr('Primary Group')),
            24: ('secondary_group_id', self.tr('Secondary Group')),
            25: ('status_tags', self.tr('Status Tags')),
            26: ('transfer_status', self.tr('Transfer Status'))
        }
        # Apply updated translations to table headers
        self.roster_table_model.setHorizontalHeaderLabels([h[1] for h in self.table_headers.values()])

        # Update button texts
        if hasattr(self, 'add_button'):
            self.add_button.setText(self.tr("Add Player"))
        if hasattr(self, 'remove_button'):
            self.remove_button.setText(self.tr("Remove Player"))
        if hasattr(self, 'select_basic_button'):
            self.select_basic_button.setText(self.tr("Basic Columns"))
        if hasattr(self, 'select_extended_button'):
            self.select_extended_button.setText(self.tr("Extended Columns"))
        if hasattr(self, 'select_all_button'):
            self.select_all_button.setText(self.tr("All Columns"))

        # Update filter combo placeholder texts
        if hasattr(self, 'global_search_input'):
            self.global_search_input.setPlaceholderText(self.tr("Search all columns..."))

        # Update combo box texts (like dropdown items)
        if hasattr(self, 'sex_combo'):
            self.sex_combo.blockSignals(True)
            current_value = self.sex_combo.currentData()  # Remember current value
            self.sex_combo.clear()
            self.sex_combo.addItem("", userData=None)
            self.sex_combo.addItem(self.tr("Male"), userData="Male")
            self.sex_combo.addItem(self.tr("Female"), userData="Female")
            # Try to restore the selection
            index = self.sex_combo.findData(current_value)
            if index >= 0:
                self.sex_combo.setCurrentIndex(index)
            self.sex_combo.blockSignals(False)

        # Do the same for other combo boxes like preferred_foot, fitness, etc.
        if hasattr(self, 'preferred_foot_combo'):
            self.preferred_foot_combo.blockSignals(True)
            current_text = self.preferred_foot_combo.currentText()
            self.preferred_foot_combo.clear()
            self.preferred_foot_combo.addItems(["", self.tr("Left"), self.tr("Right"), self.tr("Both")])
            # Try to restore based on index/position rather than text
            if current_text in ["Left", self.tr("Left")]:
                self.preferred_foot_combo.setCurrentIndex(1)
            elif current_text in ["Right", self.tr("Right")]:
                self.preferred_foot_combo.setCurrentIndex(2)
            elif current_text in ["Both", self.tr("Both")]:
                self.preferred_foot_combo.setCurrentIndex(3)
            else:
                self.preferred_foot_combo.setCurrentIndex(0)  # Default to blank
            self.preferred_foot_combo.blockSignals(False)

        if hasattr(self, 'strong_eye_combo'):
            self.strong_eye_combo.blockSignals(True)
            current_text = self.strong_eye_combo.currentText()
            self.strong_eye_combo.clear()
            self.strong_eye_combo.addItems(["", self.tr("Left"), self.tr("Right"), self.tr("Not available")])
            # Try to restore selection
            if current_text in ["Left", self.tr("Left")]:
                self.strong_eye_combo.setCurrentIndex(1)
            elif current_text in ["Right", self.tr("Right")]:
                self.strong_eye_combo.setCurrentIndex(2)
            elif current_text in ["Not available", self.tr("Not available")]:
                self.strong_eye_combo.setCurrentIndex(3)
            else:
                self.strong_eye_combo.setCurrentIndex(0)
            self.strong_eye_combo.blockSignals(False)

    def _show_header_context_menu(self, pos):
        """Shows a context menu to toggle column visibility."""
        header = self.roster_table_view.horizontalHeader()
        menu = QMenu(self)

        # Map column index to field key for checking mandatory status
        index_to_key_map = {idx: key for idx, (key, _) in self.table_headers.items()}

        # --- Define Body Data Column Keys ---
        body_data_keys = {'height', 'weight', 'waist', 'hip', 'neck', 'wrist', 'forearm', 'thigh'}
        body_data_indices = [idx for idx, (key, _) in self.table_headers.items() if key in body_data_keys]
        # ------------------------------------

        # --- Check initial visibility for the group action ---
        all_body_columns_visible = all(not header.isSectionHidden(idx) for idx in body_data_indices)
        # --------------------------------------------------

        # Create an action for each column
        for logical_index in range(self.roster_table_model.columnCount()):
            # Get current visual index (important if columns are moved)
            # visual_index = header.visualIndex(logical_index)
            # We need the key and label based on the *logical* index from our map
            field_key, header_label = self.table_headers.get(logical_index, (None, f"Column {logical_index}"))
            if field_key is None: continue # Skip if somehow out of sync

            action = QAction(header_label, self, checkable=True)
            action.setChecked(not header.isSectionHidden(logical_index))

            # Check if column is mandatory OR a Group ID column
            is_mandatory = field_key in self.mandatory_column_keys
            is_group_id_column = field_key in ['primary_group_id', 'secondary_group_id'] # ADDED Check

            if is_mandatory or is_group_id_column: # MODIFIED Condition
                action.setEnabled(False)
                # --- CORRECTED: Only force mandatory columns to be checked ---
                if is_mandatory:
                    action.setChecked(True) # Force mandatory columns to appear checked
                    action.setToolTip(self.tr("This column cannot be hidden."))
                else: # It's a group_id_column
                    # The checked state is already correctly set above based on isSectionHidden
                    # Tooltip explains why it's disabled
                    action.setToolTip(self.tr("Group ID columns cannot be toggled here."))
                # --------------------------------------------------------------
            else:
                 # Use lambda to capture the logical index for the slot
                 # Connect only if NOT disabled
                 action.toggled.connect(lambda checked, index=logical_index: self._toggle_column_visibility(index, checked))

            menu.addAction(action)

        # --- Add Group Toggle Action ---
        menu.addSeparator()
        group_action = QAction(self.tr("Show Body Data Columns"), self, checkable=True)
        group_action.setChecked(all_body_columns_visible)
        group_action.toggled.connect(self._toggle_body_columns_visibility)
        menu.addAction(group_action)
        # ------------------------------

        # Show the menu at the cursor position
        menu.exec(header.mapToGlobal(pos))

    def _toggle_column_visibility(self, logical_index, visible):
        """Hides or shows the column based on the context menu action."""
        header = self.roster_table_view.horizontalHeader()

        # Get the key to double-check mandatory status before hiding
        field_key, _ = self.table_headers.get(logical_index, (None, None))
        if field_key in self.mandatory_column_keys and not visible:
             self.logger.warning(f"Attempted to hide mandatory column: {field_key}. Preventing.") # MODIFIED
             # Optionally re-check the action in the menu if it could be forced unchecked
             return # Do not hide mandatory columns

        self.logger.debug(f"Toggling visibility for column index {logical_index} to {visible}") # MODIFIED
        header.setSectionHidden(logical_index, not visible)
        # Save the header state which includes hidden sections
        self._save_header_state()

    # --- ADDED: Handler for Body Data Group Toggle ---
    def _toggle_body_columns_visibility(self, checked):
        """Shows or hides all body data columns based on the group action."""
        self.logger.debug(f"Toggling body data columns visibility to: {checked}") # MODIFIED
        body_data_keys = {'height', 'weight', 'waist', 'hip', 'neck', 'wrist', 'forearm', 'thigh'}

        header = self.roster_table_view.horizontalHeader()
        header.blockSignals(True) # Block signals during bulk update

        try:
            for logical_index in range(self.roster_table_model.columnCount()):
                field_key, _ = self.table_headers.get(logical_index, (None, None))
                if field_key in body_data_keys:
                    # If checked is True, we want visible (not hidden)
                    # If checked is False, we want hidden
                    header.setSectionHidden(logical_index, not checked)
        finally:
            header.blockSignals(False) # Unblock signals

        self._save_header_state() # Save the new state
    # -------------------------------------------------

    def _get_flag_path(self, nationality):
        """Constructs the expected path for the flag image based on nationality."""
        if not nationality: # Handle empty/None nationality
            return None
        # Construct filename (e.g., "Greece.png")
        # Basic sanitization: replace space with underscore? Or assume exact match.
        # Assuming exact match for now based on constants list.
        filename = f"{nationality}.png"
        flag_path = self.flags_base_path / filename
        if flag_path.exists() and flag_path.is_file():
             return str(flag_path) # Return as string
        else:
             # print(f"Flag not found for: {nationality} at {flag_path}") # Optional debug
             return None

    def _update_flag_display(self, player_id, nationality):
        """Updates the flag display in the table cell and the tab preview.

        Args:
            player_id (int): The ID of the player being updated.
            nationality (str or None): The player's nationality.
        """
        flag_path = self._get_flag_path(nationality)
        flag_icon = QIcon(flag_path) if flag_path else QIcon() # Create QIcon (empty if no path)

        # --- Update Table Cell ---
        target_row = -1
        for row in range(self.roster_table_model.rowCount()):
            id_item = self.roster_table_model.item(row, 0)
            if id_item and id_item.data(Qt.ItemDataRole.UserRole + 1) == player_id:
                target_row = row
                break

        if target_row != -1:
            flag_col_index = -1
            for col, (key, _) in self.table_headers.items():
                if key == 'flag':
                    flag_col_index = col
                    break

            if flag_col_index != -1:
                flag_item = self.roster_table_model.item(target_row, flag_col_index)
                if not flag_item:
                    flag_item = QStandardItem()
                    flag_item.setEditable(False)
                    flag_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.roster_table_model.setItem(target_row, flag_col_index, flag_item)

                # Block signals to prevent potential loops if itemChanged was connected
                self.roster_table_model.blockSignals(True)
                flag_item.setData(flag_icon, Qt.ItemDataRole.DecorationRole) # Set icon
                flag_item.setText("") # Ensure no text is shown in the flag cell
                self.roster_table_model.blockSignals(False)
                # print(f"Updated flag decoration for player {player_id} in table.") # Optional debug
            else:
                self.logger.warning("Could not find 'flag' column index.") # MODIFIED
        else:
            self.logger.warning(f"Could not find row for player {player_id} to update flag.") # MODIFIED

        # --- Update Tab Preview (if current player) ---
        if player_id == self.current_selected_player_id:
            if flag_path:
                pixmap = QPixmap(flag_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(self.flag_preview_label.size(),
                                                  Qt.AspectRatioMode.KeepAspectRatio,
                                                  Qt.TransformationMode.SmoothTransformation)
                    self.flag_preview_label.setPixmap(scaled_pixmap)
                    self.flag_preview_label.setStyleSheet("") # Clear placeholder style
                else:
                    # Clear if pixmap loading failed
                    self.flag_preview_label.clear()
                    self.flag_preview_label.setStyleSheet("border: 1px solid lightgray; background-color: #f0f0f0;")
            else:
                # Clear if no flag path
                self.flag_preview_label.clear()
                self.flag_preview_label.setStyleSheet("border: 1px solid lightgray; background-color: #f0f0f0;")

    # --- ADDED: Method to load group options --- #
    def _load_group_options(self):
        """Fetches team groups from the manager and stores them for combo boxes."""
        self._group_options = []
        try:
            groups = self.roster_manager.get_all_team_groups()
            # Sort groups by team_name for consistent dropdown order
            sorted_groups = sorted(groups, key=lambda g: g.get('team_name', ''))
            # Store as (display_name, id) tuples
            self._group_options = [(g['team_name'], g['group_id']) for g in sorted_groups]
            self.logger.info(f"Loaded {len(self._group_options)} group options.") # MODIFIED
        except Exception as e:
            self.logger.error(f"Error loading group options: {e}") # MODIFIED
            # Handle error appropriately, maybe show message?

    def _populate_group_combos(self):
        """Populates the group combo boxes using the stored options."""
        self.primary_group_combo.clear()
        self.secondary_group_combo.clear()

        # Add a blank/unassigned option first
        none_text = self.tr("(None)")
        self.primary_group_combo.addItem(none_text, userData=None)
        self.secondary_group_combo.addItem(none_text, userData=None)

        # Add fetched groups
        for name, group_id in self._group_options:
            self.primary_group_combo.addItem(name, userData=group_id)
            self.secondary_group_combo.addItem(name, userData=group_id)
    # --------------------------------------------- #

    # --- ADDED: Handler for Status List Changes --- #
    def _handle_status_tag_changed(self, item: QListWidgetItem):
        """Handles check state changes and updates the table summary."""
        if self._is_updating_ui:
             # ... (skip message) ...
             return
        if self.current_selected_player_id is None:
            # ... (skip message) ...
            return

        status_key = item.data(Qt.ItemDataRole.UserRole)
        is_checked = (item.checkState() == Qt.CheckState.Checked)

        if status_key:
            self.logger.info(f"Status Tag List changed: Key '{status_key}', Checked: {is_checked}, Player ID: {self.current_selected_player_id}") # MODIFIED
            # Call main update function - it returns True/False
            success = self.roster_manager.update_player(self.current_selected_player_id, {status_key: is_checked})

            if success:
                # --- Refresh Table Summary --- #
                # Refetch player data to get the complete current state
                updated_player_data = self.roster_manager.get_player(self.current_selected_player_id)
                if updated_player_data:
                    summary_string = self._get_status_tags_summary(updated_player_data)
                    self._update_table_cell(self.current_selected_player_id, 'status_tags', summary_string)
                else:
                     self.logger.warning(f"Could not refetch player data for ID {self.current_selected_player_id} after status tag update.") # MODIFIED
                # --------------------------- #
            else:
                # --- Revert List Item on Failure --- #
                self.logger.error(f"Database update failed for {status_key}. Reverting checkbox.") # MODIFIED
                original_value = not is_checked # The opposite of what we tried to set
                self.status_tags_list_widget.blockSignals(True)
                reverted = False
                for i in range(self.status_tags_list_widget.count()):
                    list_item = self.status_tags_list_widget.item(i)
                    if list_item.data(Qt.ItemDataRole.UserRole) == status_key:
                        original_check_state = Qt.CheckState.Checked if bool(original_value) else Qt.CheckState.Unchecked
                        list_item.setCheckState(original_check_state)
                        reverted = True
                        break
                self.status_tags_list_widget.blockSignals(False)
                if not reverted: self.logger.warning(f"Could not find item with key '{status_key}' to revert check state.") # MODIFIED
                # ----------------------------------- #
        else:
             self.logger.warning(f"Status tag item '{item.text()}' changed but has no key stored.") # MODIFIED

    # --- ADDED: Helper for Status Summary --- #
    def _get_status_tags_summary(self, player_data):
        """Generates a comma-separated string of active status tag labels."""
        if not player_data: return ""
        active_tags = []
        for key, label in STATUS_TAG_OPTIONS:
            if player_data.get(key): # Check if the status flag is true
                active_tags.append(self.tr(label)) # Use translated label
        return ", ".join(active_tags)
    # ---------------------------------------- #

    # --- ADDED: Handler for Transfer Status Combo Change --- #
    def _handle_transfer_status_changed(self, status_text):
        """Handles changes in the transfer status combo box."""
        if self._is_updating_ui: return
        if self.current_selected_player_id is None: return

        self.logger.info(f"Transfer Status Combo changed to: '{status_text}' for Player ID: {self.current_selected_player_id}") # MODIFIED

        # Get the original (untranslated) status text based on the index
        current_index = self.transfer_status_combo.currentIndex()
        if current_index >= 0 and current_index < len(TRANSFER_STATUS_OPTIONS):
            original_status = TRANSFER_STATUS_OPTIONS[current_index]
        else:
            original_status = status_text  # Fallback to the translated text

        # 1. Update UI Visibility - use original status for consistency
        self._update_transfer_fields_visibility(original_status)

        # 2. Save the new transfer_status itself - store the original status
        status_to_save = original_status if original_status else None
        # Call _update_player_data which returns True/False
        save_success = self._update_player_data('transfer_status', status_to_save)

        if save_success:
            # --- ADDED: Update Table Cell --- #
            self._update_table_cell(self.current_selected_player_id, 'transfer_status', status_to_save)
            # ------------------------------ #

            # 3. Optional: Clear irrelevant detail fields in DB
            # ... (existing logic to clear details) ...
            pass # Keep the clearing logic
        else:
            # If saving the main status failed, revert the combo box
            self.logger.error(f"Failed to save main transfer status '{status_text}'. Reverting combo box.") # MODIFIED
            original_data = self.roster_manager.get_player(self.current_selected_player_id)
            original_status = original_data.get('transfer_status') if original_data else ""
            self._revert_tab_widget('transfer_status', original_status)
            # Also revert visibility based on original status
            self._update_transfer_fields_visibility(original_status)

    # --- ADDED: Function to Manage Transfer Detail Visibility --- #
    def _update_transfer_fields_visibility(self, status_text):
        """Shows/hides transfer detail fields based on the selected status."""
        # Make sure we're using the original (untranslated) status text
        # This ensures consistent behavior across languages

        # If status_text is not in TRANSFER_DETAIL_FIELDS, it might be a translated value
        if status_text not in TRANSFER_DETAIL_FIELDS:
            # Try to find the original status by comparing translations
            for original_status in TRANSFER_STATUS_OPTIONS:
                if self.tr(original_status) == status_text:
                    status_text = original_status
                    self.logger.debug(f"Found original status '{original_status}' for translated '{status_text}'")
                    break

        required_keys = TRANSFER_DETAIL_FIELDS.get(status_text, [])
        any_visible = False

        self.logger.debug(f"Updating transfer fields visibility for status: '{status_text}', required keys: {required_keys}")

        # Hide all first
        for key in TRANSFER_ALL_DETAIL_KEYS:
            if key in self.transfer_detail_widgets:
                self.transfer_detail_widgets[key].hide()
            if key in self.transfer_detail_labels:
                self.transfer_detail_labels[key].hide()

        # Show required ones
        for key in required_keys:
            if key in self.transfer_detail_widgets:
                self.transfer_detail_widgets[key].show()
                any_visible = True
            if key in self.transfer_detail_labels:
                self.transfer_detail_labels[key].show()

        # Show/hide the whole container
        if any_visible:
            self.transfer_details_container.show()
        else:
            self.transfer_details_container.hide()
    # --------------------------------------------------------- #

    # --- ADDED: Physical Metrics Calculation --- #
    def _calculate_physical_metrics(self, player_data):
        """Calculates BMI, BFP (US Navy), LBM, WHR, WHtR based on player data."""
        metrics = {'bmi': None, 'bfp': None, 'lbm': None, 'whr': None, 'whtr': None}
        self.logger.debug(f"_calculate_physical_metrics called with player_data: {player_data}") # MODIFIED
        if not player_data: return metrics

        height_cm = player_data.get('height')
        weight_kg = player_data.get('weight')
        waist_cm = player_data.get('waist')
        hip_cm = player_data.get('hip')
        neck_cm = player_data.get('neck')
        sex = player_data.get('sex') # Expects 'Male' or 'Female'

        # --- DEBUG: Print extracted values --- #
        self.logger.debug(f"height={height_cm}, weight={weight_kg}, waist={waist_cm}, hip={hip_cm}, neck={neck_cm}, sex={sex}") # MODIFIED
        # ----------------------------------- #

        # --- BMI --- #
        if height_cm and weight_kg and height_cm > 0:
            height_m = height_cm / 100.0
            try:
                metrics['bmi'] = weight_kg / (height_m ** 2)
                self.logger.debug(f"BMI calculated: {metrics['bmi']}") # MODIFIED
            except ZeroDivisionError:
                pass

        # --- Waist-to-Hip Ratio (WHR) --- #
        if waist_cm and hip_cm and hip_cm > 0:
            try:
                metrics['whr'] = waist_cm / hip_cm
                self.logger.debug(f"WHR calculated: {metrics['whr']}") # MODIFIED
            except ZeroDivisionError:
                pass

        # --- Waist-to-Height Ratio (WHtR) --- #
        if waist_cm and height_cm and height_cm > 0:
            try:
                metrics['whtr'] = waist_cm / height_cm
                self.logger.debug(f"WHtR calculated: {metrics['whtr']}") # MODIFIED
            except ZeroDivisionError:
                pass

        # --- Body Fat % (US Navy Method) --- #
        IN_PER_CM = 0.393701
        bfp = None
        self.logger.debug(f"Attempting BFP calculation...") # MODIFIED
        if height_cm and neck_cm and waist_cm and sex:
            height_in = height_cm * IN_PER_CM
            neck_in = neck_cm * IN_PER_CM
            waist_in = waist_cm * IN_PER_CM
            self.logger.debug(f"BFP inputs (inches): height={height_in}, neck={neck_in}, waist={waist_in}, sex={sex}") # MODIFIED

            try:
                if sex == 'Male':
                    self.logger.debug("Using Male BFP formula") # MODIFIED
                    # Male formula: BFP = 86.010 * log10(waist - neck) - 70.041 * log10(height) + 36.76
                    if waist_in > neck_in and height_in > 0:
                         log_term1 = math.log10(waist_in - neck_in)
                         log_term2 = math.log10(height_in)
                         self.logger.debug(f"Male log terms: log10({waist_in - neck_in})={log_term1}, log10({height_in})={log_term2}") # MODIFIED
                         bfp = 86.010 * log_term1 - 70.041 * log_term2 + 36.76
                    else:
                         self.logger.debug("Male BFP condition not met (waist > neck and height > 0)") # MODIFIED
                elif sex == 'Female':
                    self.logger.debug("Using Female BFP formula") # MODIFIED
                    # Female formula: BFP = 163.205 * log10(waist + hip - neck) - 97.684 * log10(height) - 78.387
                    hip_in = hip_cm * IN_PER_CM if hip_cm else None
                    self.logger.debug(f"Female hip_in = {hip_in}") # MODIFIED
                    if hip_in and (waist_in + hip_in) > neck_in and height_in > 0:
                        log_term1 = math.log10(waist_in + hip_in - neck_in)
                        log_term2 = math.log10(height_in)
                        self.logger.debug(f"Female log terms: log10({waist_in + hip_in - neck_in})={log_term1}, log10({height_in})={log_term2}") # MODIFIED
                        bfp = 163.205 * log_term1 - 97.684 * log_term2 - 78.387
                    else:
                         self.logger.debug("Female BFP condition not met (hip exists and waist+hip > neck and height > 0)") # MODIFIED

                if bfp is not None:
                     metrics['bfp'] = max(0, min(100, bfp)) # Clamp
                     self.logger.debug(f"BFP calculated: {metrics['bfp']}") # MODIFIED
                else:
                     self.logger.debug("BFP calculation resulted in None") # MODIFIED

            except (ValueError, TypeError, ZeroDivisionError) as e:
                self.logger.error(f"Error calculating BFP: {e}") # MODIFIED
                bfp = None
                metrics['bfp'] = None
        else:
            self.logger.debug("BFP calculation skipped (missing input: height, neck, waist, or sex)") # MODIFIED

        # --- Lean Body Mass (LBM) --- #
        if weight_kg and metrics['bfp'] is not None:
            fat_mass = weight_kg * (metrics['bfp'] / 100.0)
            metrics['lbm'] = weight_kg - fat_mass
            self.logger.debug(f"LBM calculated: {metrics['lbm']}") # MODIFIED
        else:
             self.logger.debug("LBM calculation skipped (missing weight or BFP)") # MODIFIED

        self.logger.debug(f"_calculate_physical_metrics returning: {metrics}") # MODIFIED
        return metrics
    # --------------------------------------------- #

    # --- ADDED: Physical Metrics Display Update --- #
    def _update_physical_metrics_display(self, metrics):
        """Updates the physical metric labels with calculated values."""
        if not metrics: return

        # BMI
        bmi_val = metrics.get('bmi')
        self.bmi_label.setText(f"{bmi_val:.1f}" if bmi_val is not None else "-")

        # BFP
        bfp_val = metrics.get('bfp')
        self.bfp_label.setText(f"{bfp_val:.1f} %" if bfp_val is not None else "-")

        # LBM
        lbm_val = metrics.get('lbm')
        self.lbm_label.setText(f"{lbm_val:.1f} kg" if lbm_val is not None else "-")

        # WHR
        whr_val = metrics.get('whr')
        self.whr_label.setText(f"{whr_val:.2f}" if whr_val is not None else "-")

        # WHtR
        whtr_val = metrics.get('whtr')
        self.whtr_label.setText(f"{whtr_val:.2f}" if whtr_val is not None else "-")
    # ---------------------------------------------- #

    # --- ADDED: Helper to Recalculate and Display Metrics --- #
    def _recalculate_and_display_metrics(self, player_id):
        """Recalculates physical metrics based on CURRENT UI values and updates display."""
        if player_id is None:
            # Clear metrics if no player is selected or data is unavailable
            self._update_physical_metrics_display({
                'bmi': None, 'bfp': None, 'lbm': None, 'whr': None, 'whtr': None
            })
            return

        # Fetch current values DIRECTLY from UI widgets
        try:
            # Use .value() for QDoubleSpinBox and .currentData() for QComboBox
            current_physical_data = {
                'height': self.height_spinbox.value(),
                'weight': self.weight_spinbox.value(),
                'waist': self.waist_spinbox.value(),
                'hip': self.hip_spinbox.value(),
                'neck': self.neck_spinbox.value(),
                'sex': self.sex_combo.currentData() # Get data role (e.g., 'Male', 'Female')
            }
            self.logger.debug(f"Recalculating metrics for player {player_id} with UI values: {current_physical_data}") # MODIFIED

            # Calculate metrics using current UI data
            metrics = self._calculate_physical_metrics(current_physical_data)
            self.logger.debug(f"Calculated metrics: {metrics}") # MODIFIED

            # Update the display labels
            self._update_physical_metrics_display(metrics)

        except Exception as e:
            self.logger.error(f"Failed to recalculate or display metrics: {e}") # MODIFIED
            # Optionally clear display on error, or leave stale data
            self._update_physical_metrics_display({
                'bmi': None, 'bfp': None, 'lbm': None, 'whr': None, 'whtr': None
            })

    # --- ADDED: Slot to Show Chart Dialog --- #
    def _show_physical_chart(self):
        """Creates/shows the physical chart dialog for the selected player."""
        if self.current_selected_player_id is None:
            QMessageBox.information(self, self.tr("No Player Selected"),
                                    self.tr("Please select a player from the roster first."))
            return

        # Get the latest calculated metrics for the current player
        # We can reuse the recalculate function to ensure fresh data
        # Note: This assumes player_id is valid, checked above
        try:
            current_physical_data = {
                'height': self.height_spinbox.value(),
                'weight': self.weight_spinbox.value(),
                'waist': self.waist_spinbox.value(),
                'hip': self.hip_spinbox.value(),
                'neck': self.neck_spinbox.value(),
                'sex': self.sex_combo.currentData()
            }
            metrics = self._calculate_physical_metrics(current_physical_data)
            self.logger.debug(f"_show_physical_chart: Metrics for chart: {metrics}") # MODIFIED

            if not metrics or all(v is None for v in metrics.values()):
                 QMessageBox.warning(self, self.tr("Insufficient Data"),
                                     self.tr("Not enough physical data available for the selected player to generate a chart."))
                 return

        except Exception as e:
            self.logger.error(f"Error retrieving metrics for chart: {e}") # MODIFIED
            QMessageBox.critical(self, self.tr("Error"), self.tr("Failed to retrieve data for the chart."))
            return

        # --- ADD: Get Club Averages --- #
        try:
            player_sex = current_physical_data.get('sex') # Get selected player's sex
            average_metrics = self.roster_manager.get_average_physical_metrics(sex_filter=player_sex)
            self.logger.debug(f"_show_physical_chart: Average metrics requested for filter '{player_sex}'. Result: {average_metrics}") # MODIFIED
        except Exception as e:
            self.logger.error(f"Error retrieving average metrics for chart: {e}") # MODIFIED
            # Continue without averages if there's an error
            average_metrics = None
        # ---------------------------- #

        # Create dialog if it doesn't exist or has been closed
        # Check if the window was closed (destroyed)
        if self.chart_dialog is None or not self.chart_dialog.isVisible():
            self.logger.debug("_show_physical_chart: Creating new chart dialog instance.") # MODIFIED
            self.chart_dialog = PhysicalChartDialog(self) # Pass self as parent

        # Update the chart with the latest metrics and show
        # --- MODIFIED: Pass averages and filter to update_chart --- #
        self.chart_dialog.update_chart(metrics, average_metrics, player_sex)
        # ---------------------------------------------------------- #
        self.chart_dialog.show()
        self.chart_dialog.activateWindow() # Bring it to the front
    # ---------------------------------------- #

    # --- NEW FILTERING APPROACH: Method to apply filters --- #
    def _apply_filters(self):
        """Iterates through table rows and hides/shows based on filter criteria."""
        self.logger.debug("Applying filters...") # MODIFIED
        # Get current filter values from UI widgets
        search_term = self.global_search_input.text().lower().strip()
        filter_pos = self.filter_pos_combo.currentData() # Gets the stored value (e.g., "GK", "" for All)
        filter_pgroup_name = self.filter_pgroup_combo.currentData() # Gets the group ID or ""
        filter_sgroup_name = self.filter_sgroup_combo.currentData() # Gets the group ID or ""
        filter_zone = self.filter_zone_combo.currentData() # Gets "EU", "Non-EU", etc. or ""
        filter_foot = self.filter_foot_combo.currentData() # Gets "Left", "Right", etc. or ""
        filter_sex = self.filter_sex_combo.currentData() # Gets "Male", "Female", or ""

        # --- DEBUG: Print current filter criteria --- #
        self.logger.debug(f"  Search: '{search_term}'") # MODIFIED
        self.logger.debug(f"  Pos: '{filter_pos}'") # MODIFIED
        self.logger.debug(f"  P.Group: '{filter_pgroup_name}' (Type: {type(filter_pgroup_name)})") # MODIFIED
        self.logger.debug(f"  S.Group: '{filter_sgroup_name}' (Type: {type(filter_sgroup_name)})") # MODIFIED
        self.logger.debug(f"  Zone: '{filter_zone}'") # MODIFIED
        self.logger.debug(f"  Foot: '{filter_foot}'") # MODIFIED
        self.logger.debug(f"  Sex: '{filter_sex}'") # MODIFIED
        # ------------------------------------------- #

        # Map column keys to their logical indices for quick lookup
        key_to_col = {key: col for col, (key, _) in self.table_headers.items()}

        # Iterate through all rows in the model
        for row in range(self.roster_table_model.rowCount()):
            is_match = True # Assume match initially

            # 1. Check Specific Column Filters
            # Position
            if filter_pos: # Only filter if a specific position is selected
                pos_col = key_to_col.get('position')
                if pos_col is not None:
                    item = self.roster_table_model.item(row, pos_col)
                    if not item or item.text() != filter_pos:
                        is_match = False
                else: self.logger.warning("Warning: 'position' column key not found for filtering.") # MODIFIED

            # Primary Group (compare NAMES by looking up ID)
            if is_match and filter_pgroup_name != "": # Only filter if a specific group selected ("" means All)
                pgroup_col = key_to_col.get('primary_group_id')
                if pgroup_col is not None:
                    item = self.roster_table_model.item(row, pgroup_col)
                    # --- MODIFIED: Use EditRole (ID) and lookup name ---
                    item_edit_data = item.data(Qt.ItemDataRole.EditRole) if item else None
                    group_id_to_name = {gid: name for name, gid in self._group_options}
                    actual_item_name = group_id_to_name.get(item_edit_data) if item_edit_data is not None else ""
                    # Handle case where name is empty or ID is None -> map to "(None)" display
                    # FIX: Handle None ID case correctly for comparison with "(None)" filter text
                    if item_edit_data is None and not actual_item_name:
                        actual_item_name = self.tr("(None)")
                    # --------------------------------------------------
                    # --- UPDATED DEBUG --- #
                    self.logger.debug(f"  DEBUG FILTER P.Group [Row {row}]: Filter='{filter_pgroup_name}', Item EditRole='{item_edit_data}', Looked up Name='{actual_item_name}' -> Match? {actual_item_name == filter_pgroup_name}") # MODIFIED
                    # --------------------- #
                    if actual_item_name != filter_pgroup_name:
                        is_match = False
                else: self.logger.warning("Warning: 'primary_group_id' column key not found for filtering.") # MODIFIED

            # Secondary Group (compare NAMES by looking up ID)
            if is_match and filter_sgroup_name != "": # Only filter if a specific group selected
                sgroup_col = key_to_col.get('secondary_group_id')
                if sgroup_col is not None:
                    item = self.roster_table_model.item(row, sgroup_col)
                    # --- MODIFIED: Use EditRole (ID) and lookup name ---
                    item_edit_data = item.data(Qt.ItemDataRole.EditRole) if item else None
                    group_id_to_name = {gid: name for name, gid in self._group_options}
                    actual_item_name = group_id_to_name.get(item_edit_data) if item_edit_data is not None else ""
                    # FIX: Handle None ID case correctly for comparison with "(None)" filter text
                    if item_edit_data is None and not actual_item_name:
                        actual_item_name = self.tr("(None)")
                    # --------------------------------------------------
                    # --- UPDATED DEBUG --- #
                    self.logger.debug(f"  DEBUG FILTER S.Group [Row {row}]: Filter='{filter_sgroup_name}', Item EditRole='{item_edit_data}', Looked up Name='{actual_item_name}' -> Match? {actual_item_name == filter_sgroup_name}") # MODIFIED
                    # --------------------- #
                    if actual_item_name != filter_sgroup_name:
                         is_match = False
                else: self.logger.warning("Warning: 'secondary_group_id' column key not found for filtering.") # MODIFIED

            # Zone (compare strings)
            if is_match and filter_zone:
                zone_col = key_to_col.get('zone')
                if zone_col is not None:
                    item = self.roster_table_model.item(row, zone_col)
                    if not item or item.text() != filter_zone:
                        is_match = False
                else: self.logger.warning("Warning: 'zone' column key not found for filtering.") # MODIFIED

            # Preferred Foot (compare strings)
            if is_match and filter_foot:
                foot_col = key_to_col.get('preferred_foot')
                if foot_col is not None:
                    item = self.roster_table_model.item(row, foot_col)
                    if not item or item.text() != filter_foot:
                        is_match = False
                else: self.logger.warning("Warning: 'preferred_foot' column key not found for filtering.") # MODIFIED

            # Sex (compare strings)
            if is_match and filter_sex:
                sex_col = key_to_col.get('sex')
                if sex_col is not None:
                    item = self.roster_table_model.item(row, sex_col)
                    if not item or item.text() != filter_sex:
                        is_match = False
                else: self.logger.warning("Warning: 'sex' column key not found for filtering.") # MODIFIED

            # 2. Check Global Search Term (if still matching or no other filters active)
            if is_match and search_term:
                row_matches_search = False
                for col in range(self.roster_table_model.columnCount()):
                    item = self.roster_table_model.item(row, col)
                    if item and search_term in item.text().lower():
                        row_matches_search = True
                        break # Found a match in this row, no need to check other columns
                if not row_matches_search:
                    is_match = False # Row didn't contain the search term

            # 3. Show/Hide Row
            self.roster_table_view.setRowHidden(row, not is_match)
            # --- DEBUG: Print match status --- #
            # print(f"  Row {row}: Match = {is_match}")
            # --------------------------------- #
        self.logger.debug("Filtering complete.") # MODIFIED
    # ------------------------------------------------------- #

    # --- ADDED: Method to show only basic columns ---
    def _select_basic_columns(self):
        """Hides all columns except for a predefined basic set."""
        self.logger.info("Setting basic columns visibility...") # MODIFIED
        basic_keys = {
            'player_id', 'last_name', 'first_name', 'shirt_number',
            'position', 'preferred_foot', 'age', 'flag', 'sex'
        }

        header = self.roster_table_view.horizontalHeader()
        self.roster_table_model.blockSignals(True) # Block signals during bulk update
        header.blockSignals(True)

        try:
            for logical_index in range(self.roster_table_model.columnCount()):
                field_key, header_label = self.table_headers.get(logical_index, (None, None))

                if field_key:
                    is_visible = field_key in basic_keys
                    current_hidden_state = header.isSectionHidden(logical_index)
                    # Only change if necessary
                    if current_hidden_state == is_visible: # If hidden state needs flipping
                         header.setSectionHidden(logical_index, not is_visible)
                         self.logger.debug(f"  Column '{header_label}' (Key: {field_key}, Index: {logical_index}): Set Hidden={not is_visible}") # MODIFIED
                    # else:
                    #      print(f"  Column '{header_label}' (Key: {field_key}, Index: {logical_index}): Already Hidden={not is_visible}")
                else:
                    self.logger.warning(f"No field key found for logical index {logical_index} while setting basic columns.") # MODIFIED
                    # Optionally hide columns without keys? For now, leave them.
                    # header.setSectionHidden(logical_index, True)
        finally:
            header.blockSignals(False)
            self.roster_table_model.blockSignals(False) # Unblock signals

        self._save_header_state() # Save the new state
        self.logger.info("Basic columns visibility set and state saved.") # MODIFIED
    # -------------------------------------------------

    # --- ADDED: Method to show only extended columns ---
    def _select_extended_columns(self):
        """Hides all columns except for a predefined extended set."""
        self.logger.info("Setting extended columns visibility...") # MODIFIED
        extended_keys = {
            'player_id', 'last_name', 'first_name', 'shirt_number',
            'position', 'detailed_position', 'preferred_foot', 'age',
            'nationality', 'flag', 'zone', 'sex', 'dob', 'fitness', 'status_tags'
        }

        header = self.roster_table_view.horizontalHeader()
        self.roster_table_model.blockSignals(True) # Block signals during bulk update
        header.blockSignals(True)

        try:
            for logical_index in range(self.roster_table_model.columnCount()):
                field_key, header_label = self.table_headers.get(logical_index, (None, None))

                if field_key:
                    is_visible = field_key in extended_keys
                    current_hidden_state = header.isSectionHidden(logical_index)
                    # Only change if necessary
                    if current_hidden_state == is_visible: # If hidden state needs flipping
                         header.setSectionHidden(logical_index, not is_visible)
                         self.logger.debug(f"  Column '{header_label}' (Key: {field_key}, Index: {logical_index}): Set Hidden={not is_visible}") # MODIFIED
                else:
                    self.logger.warning(f"No field key found for logical index {logical_index} while setting extended columns.") # MODIFIED
                    # header.setSectionHidden(logical_index, True)
        finally:
            header.blockSignals(False)
            self.roster_table_model.blockSignals(False) # Unblock signals

        self._save_header_state() # Save the new state
        self.logger.info("Extended columns visibility set and state saved.") # MODIFIED
    # ---------------------------------------------------

    # --- ADDED: Method to show all non-excluded columns ---
    def _select_all_columns(self):
        """Shows all columns except for specifically excluded ones."""
        self.logger.info("Setting 'all' columns visibility...") # MODIFIED
        excluded_keys = {'primary_group_id', 'secondary_group_id'} # Keys to keep hidden

        header = self.roster_table_view.horizontalHeader()
        self.roster_table_model.blockSignals(True) # Block signals during bulk update
        header.blockSignals(True)

        try:
            for logical_index in range(self.roster_table_model.columnCount()):
                field_key, header_label = self.table_headers.get(logical_index, (None, None))

                # Determine desired visibility: hidden if excluded, visible otherwise
                should_be_hidden = field_key in excluded_keys if field_key else True # Hide if excluded or no key

                current_hidden_state = header.isSectionHidden(logical_index)

                # Only change if the current state is different from desired state
                if current_hidden_state != should_be_hidden:
                    header.setSectionHidden(logical_index, should_be_hidden)
                    self.logger.debug(f"  Column '{header_label}' (Key: {field_key}, Index: {logical_index}): Set Hidden={should_be_hidden}") # MODIFIED
                # else:
                #     print(f"  Column '{header_label}' (Key: {field_key}, Index: {logical_index}): Already Hidden={should_be_hidden}")

        finally:
            header.blockSignals(False)
            self.roster_table_model.blockSignals(False) # Unblock signals

        self._save_header_state() # Save the new state
        self.logger.info("'All' columns visibility set and state saved.") # MODIFIED
    # -----------------------------------------------------

    # --- ADD: Event filter for image label click --- #
    def eventFilter(self, obj, event):
        """Filters events to detect clicks on the player image label."""
        if obj is self.player_image_label: # Check if the event is for our label
            if event.type() == QEvent.Type.MouseButtonPress:
                if event.button() == Qt.MouseButton.LeftButton:
                    # It's a left-click on the image label
                    self.logger.debug("Image label left-clicked.") # MODIFIED
                    self._handle_image_label_click()
                    return True # Event was handled
                elif event.button() == Qt.MouseButton.RightButton:
                    # It's a right-click on the image label
                    self.logger.debug("Image label right-clicked.") # MODIFIED
                    self._show_image_context_menu(event.globalPos())
                    return True # Event was handled
        # Pass the event on to the parent class for default processing
        return super().eventFilter(obj, event)
    # ----------------------------------------------- #

    # --- ADD: Context Menu for Image Label --- #
    def _show_image_context_menu(self, position):
        """Shows a context menu for the player image label."""
        if self.current_selected_player_id is None:
            self.logger.debug("Attempted to show image context menu, but no player selected.") # ADDED
            return # Don't show menu if no player selected
        self.logger.debug(f"Showing image context menu for player ID: {self.current_selected_player_id} at pos {position}") # ADDED

        menu = QMenu(self)

        # Upload Action (always enabled)
        upload_action = QAction(self.tr("Upload Photo"), self)
        upload_action.triggered.connect(self._handle_image_label_click)
        menu.addAction(upload_action)

        # Remove Action (enabled only if a player-specific photo exists)
        player_image_path = self.roster_images_base_path / f"{self.current_selected_player_id}.png"
        can_remove = player_image_path.exists() and player_image_path.is_file()

        remove_action = QAction(self.tr("Remove Photo"), self)
        remove_action.setEnabled(can_remove)
        if can_remove:
            remove_action.triggered.connect(self._remove_player_image)
        else:
            remove_action.setToolTip(self.tr("No specific image to remove for this player."))
        menu.addAction(remove_action)

        # Show the menu at the cursor's global position
        menu.exec(position)
    # ------------------------------------------- #

    # --- ADD: Method to Remove Player Image --- #
    def _remove_player_image(self):
        """Removes the specific image file for the current player."""
        if self.current_selected_player_id is None:
            self.logger.warning("Remove player image called but no player selected.") # MODIFIED
            return

        player_image_path = self.roster_images_base_path / f"{self.current_selected_player_id}.png"
        self.logger.info(f"Attempting to remove player image: {player_image_path} for player ID: {self.current_selected_player_id}") # ADDED

        if not player_image_path.exists() or not player_image_path.is_file():
            self.logger.info(f"No specific image file to remove at {player_image_path} for player {self.current_selected_player_id}.") # ADDED
            QMessageBox.information(self, self.tr("No Image"),
                                    self.tr("There is no specific image file to remove for this player."))
            return

        # Confirmation Dialog
        reply = QMessageBox.question(self,
            self.tr("Confirm Removal"),
            self.tr("Are you sure you want to remove the image for player ID {}?").format(self.current_selected_player_id),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                os.remove(player_image_path)
                self.logger.info(f"Removed player image: {player_image_path}") # MODIFIED
                QMessageBox.information(self, self.tr("Image Removed"),
                                        self.tr("Player image removed successfully."))
                # Reload image (will now fallback to default)
                self._load_player_image(self.current_selected_player_id)
            except OSError as e:
                self.logger.error(f"Error removing player image {player_image_path}: {e}") # MODIFIED
                QMessageBox.critical(self, self.tr("Removal Failed"),
                                     self.tr("Could not remove the image file: {}").format(e))
        else:
            self.logger.info("Player image removal cancelled by user.") # MODIFIED
    # ------------------------------------------ #

    # ------------------------------------------ #

    # --- REFACTORED: Method to get kit text settings --- #
    def _get_kit_text_settings(self, kit_type='player'): # ADDED kit_type parameter
        """Retrieves the text settings for the 1st kit's back name/number.

        Reads settings (font, color, size, offset, outline) from ClubDataManager
        based on whether the kit_type is 'player' (uses kit_1 settings) or
        'gk' (uses kit_4 settings).

        Returns:
            dict: A dictionary containing nested dictionaries for 'name' and 'number' settings.
        """
        settings = {
            'name': {},
            'number': {}
        }
        if not self.club_data_manager:
            self.logger.warning("ClubDataManager not available in RosterPage.") # MODIFIED
            return settings # Return empty/default structure

        # --- ADD: Determine kit ID prefix based on kit_type --- #
        if kit_type == 'gk':
            kit_id = '4' # Use 1st GK kit settings
            self.logger.debug("Fetching GK kit (kit_4) text settings.") # MODIFIED
        else:
            kit_id = '1' # Use standard 1st kit settings
            self.logger.debug("Fetching Player kit (kit_1) text settings.") # MODIFIED
        # ------------------------------------------------------- #

        # Define keys and default values
        # Defaults should match those in ClubDataManager._get_default_data
        setting_keys = {
            'name': {
                # --- MODIFIED: Use kit_id --- #
                'color': (f'kit_{kit_id}_name_color', "#000000"),
                'font_family': (f'kit_{kit_id}_name_font_family', "Arial"),
                'font_size': (f'kit_{kit_id}_name_font_size', 10),
                'vpos': (f'kit_{kit_id}_name_vpos', 0),
                'outline_enabled': (f'kit_{kit_id}_name_outline_enabled', False),
                'outline_color': (f'kit_{kit_id}_name_outline_color', "#FFFFFF"),
                'outline_thickness': (f'kit_{kit_id}_name_outline_thickness', 1)
                # -------------------------- #
            },
            'number': {
                # --- MODIFIED: Use kit_id --- #
                'color': (f'kit_{kit_id}_number_color', "#000000"),
                'font_family': (f'kit_{kit_id}_number_font_family', "Arial"),
                'font_size': (f'kit_{kit_id}_number_font_size', 24),
                'vpos': (f'kit_{kit_id}_number_vpos', 0),
                'outline_enabled': (f'kit_{kit_id}_number_outline_enabled', False),
                'outline_color': (f'kit_{kit_id}_number_outline_color', "#FFFFFF"),
                'outline_thickness': (f'kit_{kit_id}_number_outline_thickness', 1)
                # -------------------------- #
            }
        }

        # Populate settings dictionary
        for element, keys in setting_keys.items():
            for setting_name, (key, default_value) in keys.items():
                settings[element][setting_name] = self.club_data_manager.get_data(key, default_value)

        # print(f"DEBUG: Retrieved kit text settings: {settings}") # Debug
        return settings
    # -------------------------------------------- #

    # --- Method to display kit settings (uses the refactored method now) --- #
    def _show_kit_settings_message(self):
        """Fetches and displays the 1st kit text settings in a message box."""
        # --- MODIFIED: Determine kit type based on current player --- #
        kit_type = 'player' # Default
        if self.current_selected_player_id:
            player_data = self.roster_manager.get_player(self.current_selected_player_id)
            if player_data and player_data.get('position') == 'GK':
                kit_type = 'gk'

        settings = self._get_kit_text_settings(kit_type) # Pass the determined kit_type
        # ---------------------------------------------------------- #

        # Format the settings into a string
        title_suffix = f" ({self.tr('GK')})" if kit_type == 'gk' else ""
        message = f"""{self.tr("1st Kit Back Text Settings")}{title_suffix}:\n\n"""

        # Name settings

    # --- ADD: Method to apply text overlay to kit --- #
    def _apply_kit_text_overlay(self, base_pixmap, name_text, number_text, settings):
        """Draws name and number onto the base kit pixmap using provided settings.

        Uses QPainter to render the text with specific font, color, size, offset,
        and outline according to the 'settings' dictionary fetched by
        _get_kit_text_settings.

        This function contains the core drawing logic and can potentially be reused
        or adapted for other views (like a team plan) that need to display customized kits.

        Args:
            base_pixmap (QPixmap): The base kit image (e.g., kit1_back.png).
            name_text (str): The player's last name.
            number_text (str): The player's shirt number.
            settings (dict): The dictionary of text settings from _get_kit_text_settings.

        Returns:
            QPixmap or None: The modified pixmap with text overlay, or None if an error occurs.
        """
        if not base_pixmap or base_pixmap.isNull():
            self.logger.error("Base pixmap is null in _apply_kit_text_overlay.") # MODIFIED
            return None

        # Work on a copy to avoid modifying the original if it's cached elsewhere
        pixmap = base_pixmap.copy()
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setRenderHint(QPainter.RenderHint.TextAntialiasing)

        # Add import for QPainterPath if not already present
        from PySide6.QtGui import QPainterPath

        try:
            # --- Draw Name ---
            name_settings = settings.get('name', {})
            name_color = QColor(name_settings.get('color', '#000000'))
            name_font_family = name_settings.get('font_family', 'Arial')
            name_font_size = name_settings.get('font_size', 10)
            name_vpos = name_settings.get('vpos', 0)
            name_outline_enabled = name_settings.get('outline_enabled', False)
            name_outline_color = QColor(name_settings.get('outline_color', '#FFFFFF'))
            name_outline_thickness = name_settings.get('outline_thickness', 1)

            name_font = QFont(name_font_family, name_font_size)
            painter.setFont(name_font)

            # Calculate position (centered horizontally, adjusted vertically)
            name_metrics = painter.fontMetrics()
            name_rect = name_metrics.boundingRect(name_text)
            name_x = (pixmap.width() - name_rect.width()) / 2
            name_y = name_metrics.ascent() + name_vpos # Adjust based on baseline and offset

            # Draw outline if enabled
            if name_outline_enabled:
                path = QPainterPath()
                # Adjust Y slightly for path drawing to align better with drawText? (Experiment if needed)
                path.addText(name_x, name_y, name_font, name_text)
                pen = QPen(name_outline_color, name_outline_thickness, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap, Qt.PenJoinStyle.RoundJoin)
                painter.setPen(Qt.PenStyle.NoPen) # Don't draw text fill when stroking path
                painter.setBrush(name_color) # Brush needed? Maybe not if we draw separately. Test. For now, rely on separate drawText.
                painter.strokePath(path, pen)


            # Draw text
            painter.setPen(QPen(name_color)) # Set main text color
            painter.setBrush(Qt.BrushStyle.NoBrush) # Ensure no fill when drawing text after path
            painter.drawText(int(name_x), int(name_y), name_text)

            # --- Draw Number ---
            number_settings = settings.get('number', {})
            number_color = QColor(number_settings.get('color', '#000000'))
            number_font_family = number_settings.get('font_family', 'Arial')
            number_font_size = number_settings.get('font_size', 24)
            number_vpos = number_settings.get('vpos', 0)
            number_outline_enabled = number_settings.get('outline_enabled', False)
            number_outline_color = QColor(number_settings.get('outline_color', '#FFFFFF'))
            number_outline_thickness = number_settings.get('outline_thickness', 1)

            number_font = QFont(number_font_family, number_font_size)
            painter.setFont(number_font)

            # Calculate position (centered horizontally, below name, adjusted vertically)
            number_metrics = painter.fontMetrics()
            number_rect = number_metrics.boundingRect(number_text)
            number_x = (pixmap.width() - number_rect.width()) / 2
            # Position below name's baseline + name height + number ascent + offset
            number_y = name_y + name_rect.height() + number_metrics.descent() + number_metrics.ascent() + number_vpos

            # Draw outline if enabled
            if number_outline_enabled:
                path = QPainterPath()
                path.addText(number_x, number_y, number_font, number_text)
                pen = QPen(number_outline_color, number_outline_thickness, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap, Qt.PenJoinStyle.RoundJoin)
                painter.setPen(Qt.PenStyle.NoPen)
                painter.setBrush(number_color) # Test if brush is needed
                painter.strokePath(path, pen)

            # Draw text
            painter.setPen(QPen(number_color))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawText(int(number_x), int(number_y), number_text)

        except Exception as e:
            self.logger.error(f"Error during QPainter operations: {e}") # MODIFIED
            painter.end() # Ensure painter is ended even on error
            return None # Return None if painting fails

        painter.end()
        return pixmap
    # ------------------------------------------------ #

    # --- ADD: Method to remove player image --- #

    # --- ADD: Method to load player kit image --- #
    def _load_player_kit(self, player_data):
        """Loads the appropriate base kit image, then configures and positions overlay labels."""
        kit_path = None
        kit_type = 'player' # Default
        position = None
        last_name = ""
        shirt_number = ""

        # --- Clear/Hide previous state --- #
        self.kit_image_label.clear() # Clear base image
        self.kit_image_label.setStyleSheet("background-color: #f0f0f0;") # Placeholder style
        if hasattr(self, 'kit_name_overlay_label'): self.kit_name_overlay_label.hide()
        if hasattr(self, 'kit_number_overlay_label'): self.kit_number_overlay_label.hide()
        # -------------------------------- #

        if player_data:
            position = player_data.get('position')
            last_name = player_data.get('last_name', '')
            shirt_number = str(player_data.get('shirt_number', '')) # Ensure string
            self.logger.debug(f"Load Kit (Overlay): Pos={position}, Name={last_name}, Num={shirt_number}") # MODIFIED

            if position == 'GK':
                kit_path = self.gk_kit_path
                kit_type = 'gk'
            elif position in ['DF', 'MF', 'AT']:
                kit_path = self.player_kit_path
                kit_type = 'player'
            else:
                self.logger.debug("Position is not GK/DF/MF/AT, kit view remains empty.") # MODIFIED
                return # Nothing more to do, labels are already hidden
        else:
            self.logger.debug("No player data, kit view remains empty.") # MODIFIED
            return # Nothing more to do, labels are already hidden

        # --- Load and Scale BASE image --- #
        if not kit_path or not kit_path.exists() or not kit_path.is_file():
            self.logger.error(f"Base kit image path not found: {kit_path}. Kit view remains empty.") # MODIFIED
            return

        base_pixmap = QPixmap(str(kit_path))
        if base_pixmap.isNull():
            self.logger.error(f"Failed to load base kit QPixmap from: {kit_path}. Kit view remains empty.") # MODIFIED
            return

        # Scale base pixmap to fit the kit_image_label
        scaled_base_pixmap = base_pixmap.scaled(
            self.kit_image_label.size(), # Target size is the label itself
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )
        self.kit_image_label.setPixmap(scaled_base_pixmap)
        self.kit_image_label.setStyleSheet("") # Clear background placeholder
        # --------------------------------- #

        # --- Configure Overlay Labels --- #
        if not hasattr(self, 'kit_name_overlay_label') or not hasattr(self, 'kit_number_overlay_label'):
            self.logger.error("Overlay labels not found.") # MODIFIED
            return

        settings = self._get_kit_text_settings(kit_type)
        name_settings = settings.get('name', {})
        number_settings = settings.get('number', {})

        # Configure Name Label
        name_label = self.kit_name_overlay_label
        name_font = QFont(name_settings.get('font_family', 'Arial'),
                          name_settings.get('font_size', 10),
                          QFont.Weight.Bold)
        name_label.setFont(name_font)
        name_label.setTextColor(QColor(name_settings.get('color', '#000000')))
        name_label.setOutlineEnabled(name_settings.get('outline_enabled', False))
        if name_label._outline_enabled:
            name_label.setOutlineColor(QColor(name_settings.get('outline_color', '#FFFFFF')))
            name_label.setOutlineThickness(name_settings.get('outline_thickness', 1))
        name_label.setText(last_name)
        name_label.adjustSize() # Adjust size to fit text *before* positioning

        # Configure Number Label
        number_label = self.kit_number_overlay_label
        number_font = QFont(number_settings.get('font_family', 'Arial'),
                            number_settings.get('font_size', 24),
                            QFont.Weight.Bold)
        number_label.setFont(number_font)
        number_label.setTextColor(QColor(number_settings.get('color', '#000000')))
        number_label.setOutlineEnabled(number_settings.get('outline_enabled', False))
        if number_label._outline_enabled:
            number_label.setOutlineColor(QColor(number_settings.get('outline_color', '#FFFFFF')))
            number_label.setOutlineThickness(number_settings.get('outline_thickness', 1))
        number_label.setText(shirt_number)
        number_label.adjustSize() # Adjust size to fit text *before* positioning

        # --- Position Overlay Labels --- #
        parent_width = self.kit_image_label.width()
        parent_height = self.kit_image_label.height()

        def position_label(label, base_y_factor, v_offset):
            size = label.sizeHint()
            x = (parent_width - size.width()) / 2
            base_y = (parent_height * base_y_factor) - (size.height() / 2)
            y = base_y + v_offset
            # Clamp position within parent bounds
            y = max(0, min(y, parent_height - size.height()))
            x = max(0, min(x, parent_width - size.width()))
            label.move(int(x), int(y))
            self.logger.debug(f"  Positioning {label.text()}: x={int(x)}, y={int(y)}") # MODIFIED

        # Use base factors similar to ClubWindow for initial placement
        num_base_y_factor = 0.55
        name_base_y_factor = 0.30

        position_label(number_label, num_base_y_factor, number_settings.get('vpos', 0))
        position_label(name_label, name_base_y_factor, name_settings.get('vpos', 0))

        # --- Show Labels --- #
        name_label.show()
        number_label.show()
        self.logger.debug(f"Displayed kit image with overlay successfully: {kit_path}") # MODIFIED
    # ------------------------------------------ #

    # --- ADD: Helper to save single profile field --- #
    def _save_single_profile_field(self, field_key, value):
        """Updates a single field in the player_profile table for the current player."""
        if self._is_updating_ui or self.current_selected_player_id is None:
            return # Don't save during UI sync or if no player selected

        print(f"Profile Tab Saving: PlayerID={self.current_selected_player_id}, Key='{field_key}', Value='{str(value)[:50]}...'") # Log truncated value

        profile_update_data = {field_key: value}

        # We pass the single field update to the manager's update_player_profile
        # which handles INSERT OR REPLACE correctly.
        success = self.roster_manager.update_player_profile(self.current_selected_player_id, profile_update_data)

        if not success:
            # Optionally provide feedback to the user if saving fails
            print(f"ERROR: Failed to save profile field '{field_key}' for player {self.current_selected_player_id}.")
            # We might want to avoid a pop-up for every failed keystroke save (e.g., from QTextEdit)
            # QMessageBox.warning(self, self.tr("Save Failed"), self.tr("Could not save change for {}").format(field_key))
        # No need to update the table view as profile data isn't shown there
    # ------------------------------------------------ #

    def _handle_table_item_changed(self, item: QStandardItem):
        """Handles data changes directly made in the table view (triggered by delegate)."""
        # Prevent updates triggered by UI synchronization from tabs
        if self._is_updating_ui:
             # self.logger.debug("Skipping table update due to UI sync.") # Keep commented unless debugging
             return

        row = item.row()
        col = item.column()

        # Get player ID from the first column of the changed row
        id_item = self.roster_table_model.item(row, 0)
        if not id_item:
             self.logger.error(f"_handle_table_item_changed: Cannot get ID item for changed row {row}") # MODIFIED
             return

        player_id = id_item.data(Qt.ItemDataRole.UserRole + 1)
        if player_id is None:
            self.logger.error(f"_handle_table_item_changed: Cannot get player_id for changed row {row}") # MODIFIED
            return

        # Get the field key corresponding to the column
        field_key, header_text = self.table_headers.get(col, (None, None))
        if field_key is None:
            self.logger.error(f"_handle_table_item_changed: Cannot determine field_key for Col {col}") # MODIFIED
            return

        self.logger.debug(f"HANDLE: Item changed Row={row}, Col={col}, Field='{field_key}', PlayerID={player_id}") # MODIFIED

        # --- Handle Group ID Changes Separately ---
        if field_key in ['primary_group_id', 'secondary_group_id']:
            # Get the EditRole value *just set* by the delegate
            # This might be called twice, once with ID, once (incorrectly?) with Name
            group_id_value = item.model().data(item.index(), Qt.ItemDataRole.EditRole)
            self.logger.debug(f"  HANDLE GROUP: Field='{field_key}', EditRole value='{group_id_value}' (Type: {type(group_id_value)})") # MODIFIED

            # --- FIX: Only process if the EditRole value is the expected type (int or None) ---
            if not isinstance(group_id_value, (int, type(None))):
                self.logger.debug(f"  HANDLE GROUP: Skipping processing for Field='{field_key}' because EditRole value type is {type(group_id_value)} (expected int or None). Likely second signal.") # MODIFIED
                return # Ignore signal if EditRole is unexpectedly a string (name)
            # --------------------------------------------------------------------------------

            # Proceed with DB update and Tab sync using the correct group_id_value
            update_dict = {field_key: group_id_value}
            self.logger.debug(f"  HANDLE GROUP: Updating manager with: {update_dict}") # MODIFIED
            # --- Retrieve original value *before* DB update for potential revert ---
            original_data = self.roster_manager.get_player(player_id)
            original_value = original_data.get(field_key) if original_data else None
            # -------------------------------------------------------------------
            success = self.roster_manager.update_player(player_id, update_dict)

            if success:
                self.logger.debug(f"  HANDLE GROUP: DB updated successfully.") # MODIFIED
                if player_id == self.current_selected_player_id:
                    self.logger.debug(f"  HANDLE GROUP: Syncing tab for Field='{field_key}' with ID='{group_id_value}'") # MODIFIED
                    tab_widget = self.player_detail_widgets.get(field_key)
                    if tab_widget and isinstance(tab_widget, QComboBox):
                        self._is_updating_ui = True
                        tab_widget.blockSignals(True)
                        try:
                            idx = tab_widget.findData(group_id_value) # Use the validated ID
                            self.logger.debug(f"    SYNC GROUP: Found index {idx} for ID {group_id_value}") # MODIFIED
                            tab_widget.setCurrentIndex(idx if idx != -1 else 0) # Set tab combo
                        finally:
                            tab_widget.blockSignals(False)
                            self._is_updating_ui = False
                        self.logger.debug(f"    SYNC GROUP: Tab sync complete.") # MODIFIED
                    else:
                        self.logger.warning(f"  HANDLE GROUP: Could not find tab ComboBox for key '{field_key}'") # MODIFIED
            else:
                self.logger.warning(f"  HANDLE GROUP: DB update FAILED for {field_key}. Reverting model.") # MODIFIED
                # Revert the model item using the original value
                self.roster_table_model.blockSignals(True)
                group_id_to_name = {gid: name for name, gid in self._group_options}
                original_display_name = group_id_to_name.get(original_value, "") if original_value is not None else ""
                item.model().setData(item.index(), original_value, Qt.ItemDataRole.EditRole)
                item.setText(original_display_name if original_display_name else self.tr("(None)"))
                self.roster_table_model.blockSignals(False)
                QMessageBox.warning(self, self.tr("Update Failed"), self.tr("Could not save group change to the database."))

            return # Handled group ID change, exit handler

        # --- Handle Non-Group, Editable Field Changes ---
        # (Exclude calculated/fixed fields handled elsewhere or non-editable)
        elif field_key not in ['player_id', 'age', 'zone', 'flag', 'status_tags', 'transfer_status']:
            # Get the EditRole value committed by the delegate for other fields
            new_model_value = item.model().data(item.index(), Qt.ItemDataRole.EditRole)
            self.logger.debug(f"  HANDLE OTHER: Field='{field_key}', New EditRole Value='{new_model_value}' (Type: {type(new_model_value)})") # MODIFIED

            # --- Retrieve original value *before* DB update for potential revert ---
            original_data = self.roster_manager.get_player(player_id)
            original_value = original_data.get(field_key) if original_data else None
            # -------------------------------------------------------------------

            # --- Perform validation if needed (e.g., shirt number) ---
            if field_key == 'shirt_number':
                 # Value from model is already validated by delegate's setModelData for range,
                 # but we need to check uniqueness here before DB update.
                 if new_model_value is not None and not self.roster_manager.is_shirt_number_unique(new_model_value, player_id):
                     QMessageBox.warning(self, self.tr("Invalid Input"), self.tr("Shirt number {} is already assigned.").format(new_model_value))
                     # Revert model safely
                     self.roster_table_model.blockSignals(True)
                     item.model().setData(item.index(), original_value, Qt.ItemDataRole.EditRole)
                     item.setText(self._format_value_for_display(field_key, original_value))
                     self.roster_table_model.blockSignals(False)
                     self.logger.debug(f"  HANDLE OTHER: Reverted shirt number due to non-uniqueness.") # MODIFIED
                     return # Stop update

            # --- Update Manager ---
            update_dict = {field_key: new_model_value}
            new_age, new_zone = None, None # For calculated fields
            if field_key == 'dob': new_age = self.roster_manager.calculate_age(new_model_value)
            if field_key == 'nationality': new_zone = get_nationality_zone(new_model_value)

            self.logger.debug(f"  HANDLE OTHER: Updating manager with: {update_dict}") # MODIFIED
            success = self.roster_manager.update_player(player_id, update_dict)

            if success:
                self.logger.debug(f"  HANDLE OTHER: DB updated successfully.") # MODIFIED
                # Update calculated fields in the table model if necessary
                if new_age is not None: self._update_table_cell(player_id, 'age', new_age)
                if new_zone is not None:
                    self._update_flag_display(player_id, new_model_value) # Update flag icon
                    self._update_table_cell(player_id, 'zone', new_zone) # Update zone cell

                # Sync change back to detail tabs if the edited player is selected
                if player_id == self.current_selected_player_id:
                    self.logger.debug(f"  HANDLE OTHER: Syncing tab for Field='{field_key}' with Value='{new_model_value}'") # MODIFIED
                    tab_widget = self.player_detail_widgets.get(field_key)
                    if tab_widget:
                        self._is_updating_ui = True
                        tab_widget.blockSignals(True)
                        try:
                            # Simplified sync logic (assumes correct widget types)
                            if isinstance(tab_widget, QLineEdit): tab_widget.setText(str(new_model_value) if new_model_value is not None else "")
                            elif isinstance(tab_widget, QSpinBox): tab_widget.setValue(int(new_model_value) if new_model_value is not None else 0)
                            elif isinstance(tab_widget, QDoubleSpinBox): tab_widget.setValue(float(new_model_value) if new_model_value is not None else 0.0)
                            elif isinstance(tab_widget, QComboBox): # Specific handling for Sex combo data role needed? Check _update_tabs_on_selection
                                 if field_key == 'sex':
                                     sex_idx = tab_widget.findData(new_model_value if new_model_value else None)
                                     tab_widget.setCurrentIndex(sex_idx if idx != -1 else 0)
                                 else: # Other combos like position, nationality, fitness, foot, eye
                                     tab_widget.setCurrentText(str(new_model_value) if new_model_value is not None else "")
                            elif isinstance(tab_widget, QDateEdit):
                                 qdate = QDate.fromString(str(new_model_value), "yyyy-MM-dd") if new_model_value else QDate()
                                 tab_widget.setDate(qdate) if qdate.isValid() else tab_widget.clear()

                            # Update calculated fields in TABS
                            if new_age is not None: self.age_label.setText(str(new_age))
                            if new_zone is not None: self.zone_label.setText(new_zone if new_zone else "-")
                            # Recalculate metrics if relevant physical fields changed
                            if field_key in ['height', 'weight', 'waist', 'hip', 'neck', 'sex']:
                                self._recalculate_and_display_metrics(player_id)
                        finally:
                            tab_widget.blockSignals(False)
                            self._is_updating_ui = False
                        self.logger.debug(f"    SYNC OTHER: Tab sync complete.") # MODIFIED
                    else:
                         self.logger.warning(f"  HANDLE OTHER: Could not find tab widget for key '{field_key}'") # MODIFIED
            else:
                self.logger.warning(f"  HANDLE OTHER: DB update FAILED for {field_key}. Reverting model.") # MODIFIED
                # Revert model data
                self.roster_table_model.blockSignals(True)
                item.model().setData(item.index(), original_value, Qt.ItemDataRole.EditRole)
                item.setText(self._format_value_for_display(field_key, original_value))
                self.roster_table_model.blockSignals(False)
                QMessageBox.warning(self, self.tr("Update Failed"), self.tr("Could not save change for {} to the database.").format(header_text)) # Use header text for user message

        else:
             # This path is for non-group, non-editable fields like player_id, age, zone, flag etc.
             # No action needed here as they are not directly editable in the table or handled above.
             # self.logger.debug(f"HANDLE: Skipping update for non-editable/calculated field: '{field_key}'") # Keep commented unless debugging
             pass

    def closeEvent(self, event):
        """Handle the close event for the page."""
        self.logger.debug("RosterPage close event triggered.")

        # Save window geometry
        self._save_geometry()

        # Save any pending changes
        self.save_splitter_state()

        # Ensure all database changes are committed
        if hasattr(self, 'roster_manager'):
            self.logger.info("Closing roster page, committing database changes")
            self.roster_manager.close_db()

        # Call parent close event
        super().closeEvent(event)

# Example run block for standalone testing
if __name__ == '__main__':
    app = QApplication(sys.argv)
    # Ensure QSettings has organization/app name for saving state
    QCoreApplication.setOrganizationName("TestOrg")
    QCoreApplication.setApplicationName("RosterPageTest")

    # Use a temporary DB for standalone testing
    # Note: RosterManager uses its own path logic, so this manager
    # will create/use test_roster.db in the data directory relative to itself.
    # To ensure clean tests, delete it beforehand if needed.
    test_db_file = Path(__file__).parent.parent / "data" / "test_roster.db"
    if test_db_file.exists():
        try:
            test_db_file.unlink()
            print(f"Removed old test database: {test_db_file}")
        except OSError as e:
            print(f"Error removing old test database {test_db_file}: {e}")

    test_manager = RosterManager(db_name="test_roster.db")

    # Add some initial data for testing UI population
    test_manager.add_player({'last_name': 'Tester', 'first_name': 'Rob', 'dob': '1995-01-10', 'position': 'MF', 'height': 180.5, 'weight': 75.2})
    test_manager.add_player({'last_name': 'Sample', 'first_name': 'Sue', 'dob': '2001-11-22', 'position': 'DF', 'shirt_number': 5})

    main_window = RosterPage(roster_manager=test_manager)
    main_window.setWindowTitle("Roster Page Test")
    main_window.resize(1000, 700) # Increased size for better view
    main_window.show()

    # Ensure DB is closed when app exits
    app.aboutToQuit.connect(test_manager.close_db)

    sys.exit(app.exec())