import sys
import logging
from datetime import datetime

from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTabWidget, QLabel, QLineEdit, QComboBox, QDateEdit, QTimeEdit,
    QSpinBox, QDoubleSpinBox, QCheckBox, QRadioButton, QButtonGroup,
    QGroupBox, QFormLayout, QTableWidget, QTableWidgetItem, QHeaderView,
    QPushButton, QStackedWidget, QScrollArea, QFrame, QSizePolicy,
    QMessageBox, QToolButton, QMenu, QGridLayout, QAbstractItemView,
    QDialog, QListWidget, QListWidgetItem
)
from app.widgets.collapsible_group_box import CollapsibleGroupBox
from PySide6.QtCore import (
    Qt, QDate, QTime, QSettings, QCoreApplication, QTimer, QEvent
)
from PySide6.QtGui import QColor, QIcon, QBrush, QKeyEvent, QAction

# Import managers
from app.data.roster_manager import RosterManager
from app.data.matches_manager import MatchesManager
from app.data.club_data_manager import ClubDataManager
from app.utils.color_constants import get_color
from app.utils.constants import countries

class MatchesPage(QWidget):
    """Page for managing matches (add, edit, browse)."""

    def __init__(self, roster_manager=None, matches_manager=None, club_data_manager=None, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.matches")

        # Use provided managers or create defaults
        self.roster_manager = roster_manager if roster_manager else RosterManager()
        self.matches_manager = matches_manager if matches_manager else MatchesManager()
        self.club_data_manager = club_data_manager  # Store club data manager reference

        # Set window properties
        self.setObjectName("MatchesPage")
        self.setWindowTitle(self.tr("Matches"))
        self.setWindowFlags(Qt.WindowType.Window)
        self.setWindowModality(Qt.WindowModality.NonModal)

        # Initialize state variables
        self.current_match_id = None
        self.is_edit_mode = False
        self.current_season = None
        self.current_competition = None
        self.sort_order = Qt.SortOrder.AscendingOrder
        self.sort_column = 0  # Default sort by date

        # References to collapsible sections
        self.collapsible_sections = {}

        # Initialize UI
        self._init_ui()

        # Load initial data
        self._load_competitions()
        self._load_seasons()
        self._load_weather_conditions()
        self._load_matches()

        # Restore header state (column order, widths, and sort)
        self._restore_header_state()

        # Connect additional signals
        self.competition_field.currentIndexChanged.connect(self._on_competition_changed)
        self.et_duration_spinner.valueChanged.connect(self._update_match_duration_calculation)
        self.opponent_field.textChanged.connect(self._update_team_names_in_stats)

        # Connect score-related signals to update result label and final score
        self.team_score.valueChanged.connect(self._update_result_label)
        self.opponent_score.valueChanged.connect(self._update_result_label)
        self.et_team_score.valueChanged.connect(self._update_result_label)
        self.et_opponent_score.valueChanged.connect(self._update_result_label)
        self.penalties_team_score.valueChanged.connect(self._update_result_label)
        self.penalties_opponent_score.valueChanged.connect(self._update_result_label)
        # Home/Away affects result interpretation
        self.home_radio.toggled.connect(self._update_result_label)
        self.away_radio.toggled.connect(self._update_result_label)

        # Connect signals to update final score in statistics
        self.team_score.valueChanged.connect(self._update_final_score)
        self.opponent_score.valueChanged.connect(self._update_final_score)
        self.et_team_score.valueChanged.connect(self._update_final_score)
        self.et_opponent_score.valueChanged.connect(self._update_final_score)
        self.penalties_team_score.valueChanged.connect(self._update_final_score)
        self.penalties_opponent_score.valueChanged.connect(self._update_final_score)
        self.extra_time_check.toggled.connect(self._update_final_score)
        self.penalties_check.toggled.connect(self._update_final_score)
        self.home_radio.toggled.connect(self._update_final_score)
        self.away_radio.toggled.connect(self._update_final_score)

        # Load saved section states
        self._load_section_states()

        # Auto-populate venue and city fields if Home is selected (which is the default)
        self._populate_venue_from_club_data()

        # Set a reasonable default size for the window
        self.resize(1200, 800)

    def _init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout(self)

        # Top controls section
        top_controls = self._create_top_controls()
        main_layout.addWidget(top_controls)

        # Main content area with stacked widget for browse/edit modes
        self.main_stack = QStackedWidget()

        # Create browse mode widget
        browse_widget = self._create_browse_mode()
        self.main_stack.addWidget(browse_widget)

        # Create edit mode widget
        edit_widget = self._create_edit_mode()
        self.main_stack.addWidget(edit_widget)

        main_layout.addWidget(self.main_stack)

        # Set initial mode to browse
        self.main_stack.setCurrentIndex(0)

    def _create_top_controls(self):
        """Create the top controls section."""
        top_frame = QFrame()
        top_layout = QHBoxLayout(top_frame)

        # Mode toggle
        mode_group = QGroupBox(self.tr("View Mode"))
        mode_layout = QHBoxLayout(mode_group)

        self.browse_radio = QRadioButton(self.tr("Browse Matches"))
        self.edit_radio = QRadioButton(self.tr("Add/Edit Match"))
        self.browse_radio.setChecked(True)

        mode_layout.addWidget(self.browse_radio)
        mode_layout.addWidget(self.edit_radio)

        # Connect mode toggle signals
        self.browse_radio.toggled.connect(self._on_mode_toggled)

        # Create filter controls (for browse mode)
        self.filter_widget = QWidget()
        filter_layout = QHBoxLayout(self.filter_widget)
        filter_layout.setContentsMargins(0, 0, 0, 0)

        # Season filter
        season_group = QGroupBox(self.tr("Season"))
        season_layout = QHBoxLayout(season_group)
        self.season_combo = QComboBox()
        self.season_combo.currentIndexChanged.connect(self._on_filter_changed)
        season_layout.addWidget(self.season_combo)

        # Competition filter
        comp_group = QGroupBox(self.tr("Competition"))
        comp_layout = QHBoxLayout(comp_group)
        self.competition_combo = QComboBox()
        self.competition_combo.currentIndexChanged.connect(self._on_filter_changed)
        comp_layout.addWidget(self.competition_combo)

        # Result filter
        result_group = QGroupBox(self.tr("Result"))
        result_layout = QHBoxLayout(result_group)
        self.result_combo = QComboBox()
        self.result_combo.addItem(self.tr("All Results"), "all")
        self.result_combo.addItem(self.tr("Win"), "win")
        self.result_combo.addItem(self.tr("Draw"), "draw")
        self.result_combo.addItem(self.tr("Loss"), "loss")
        self.result_combo.currentIndexChanged.connect(self._on_filter_changed)
        result_layout.addWidget(self.result_combo)

        # Home/Away filter
        home_away_group = QGroupBox(self.tr("Home/Away"))
        home_away_layout = QHBoxLayout(home_away_group)
        self.home_away_combo = QComboBox()
        self.home_away_combo.addItem(self.tr("All Matches"), "all")
        self.home_away_combo.addItem(self.tr("Home"), "home")
        self.home_away_combo.addItem(self.tr("Away"), "away")
        self.home_away_combo.currentIndexChanged.connect(self._on_filter_changed)
        home_away_layout.addWidget(self.home_away_combo)

        # Match Finish filter
        finish_group = QGroupBox(self.tr("Match Finish"))
        finish_layout = QHBoxLayout(finish_group)
        self.finish_combo = QComboBox()
        self.finish_combo.addItem(self.tr("All Types"), "all")
        self.finish_combo.addItem(self.tr("Full Time"), "ft")
        self.finish_combo.addItem(self.tr("Extra Time"), "et")
        self.finish_combo.addItem(self.tr("Penalties"), "pen")
        self.finish_combo.currentIndexChanged.connect(self._on_filter_changed)
        finish_layout.addWidget(self.finish_combo)

        # Date range filter
        date_group = QGroupBox(self.tr("Date Range"))
        date_layout = QHBoxLayout(date_group)

        # From date
        from_label = QLabel(self.tr("From:"))
        self.from_date = QDateEdit()
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(QDate.currentDate().addMonths(-3))  # Default to 3 months ago
        self.from_date.dateChanged.connect(self._on_filter_changed)

        # To date
        to_label = QLabel(self.tr("To:"))
        self.to_date = QDateEdit()
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(QDate.currentDate())  # Default to today
        self.to_date.dateChanged.connect(self._on_filter_changed)

        # Clear dates button
        self.clear_dates_btn = QPushButton(self.tr("Clear Dates"))
        self.clear_dates_btn.clicked.connect(self._clear_date_filters)

        date_layout.addWidget(from_label)
        date_layout.addWidget(self.from_date)
        date_layout.addWidget(to_label)
        date_layout.addWidget(self.to_date)
        date_layout.addWidget(self.clear_dates_btn)

        # Search box
        search_group = QGroupBox(self.tr("Search"))
        search_layout = QHBoxLayout(search_group)
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText(self.tr("Search matches..."))
        self.search_box.textChanged.connect(self._on_search_text_changed)
        search_layout.addWidget(self.search_box)

        # Clear all filters button
        self.clear_all_filters_btn = QPushButton(self.tr("Clear All Filters"))
        self.clear_all_filters_btn.clicked.connect(self._clear_all_filters)

        # Add filter widgets to filter layout
        filter_layout.addWidget(season_group)
        filter_layout.addWidget(comp_group)
        filter_layout.addWidget(result_group)
        filter_layout.addWidget(home_away_group)
        filter_layout.addWidget(finish_group)
        filter_layout.addWidget(date_group)
        filter_layout.addWidget(search_group)
        filter_layout.addWidget(self.clear_all_filters_btn)

        # Create action buttons (for edit mode)
        self.action_widget = QWidget()
        action_layout = QHBoxLayout(self.action_widget)
        action_layout.setContentsMargins(0, 0, 0, 0)

        # Save button
        self.save_btn_top = QPushButton(self.tr("Save Match"))
        self.save_btn_top.setIcon(QIcon("app/resources/icons/save.png"))
        self.save_btn_top.clicked.connect(self._on_save_match)

        # Cancel button
        self.cancel_btn_top = QPushButton(self.tr("Cancel"))
        self.cancel_btn_top.setIcon(QIcon("app/resources/icons/cancel.png"))
        self.cancel_btn_top.clicked.connect(self._on_cancel_edit)

        # Delete button
        self.delete_btn_top = QPushButton(self.tr("Delete Match"))
        self.delete_btn_top.setIcon(QIcon("app/resources/icons/delete.png"))
        self.delete_btn_top.clicked.connect(self._on_delete_match)

        # Add action buttons to action layout
        action_layout.addWidget(self.save_btn_top)
        action_layout.addWidget(self.cancel_btn_top)
        action_layout.addWidget(self.delete_btn_top)

        # Initially hide action buttons (start in browse mode)
        self.action_widget.setVisible(False)

        # Create browse mode action buttons
        self.browse_actions_widget = QWidget()
        browse_actions_layout = QHBoxLayout(self.browse_actions_widget)
        browse_actions_layout.setContentsMargins(0, 0, 0, 0)

        # Edit button
        self.edit_btn_top = QPushButton(self.tr("Edit Match"))
        self.edit_btn_top.setIcon(QIcon("app/resources/icons/edit.png"))
        self.edit_btn_top.clicked.connect(self._on_edit_btn_clicked)
        self.edit_btn_top.setEnabled(False)  # Initially disabled

        # Delete button
        self.delete_btn_browse = QPushButton(self.tr("Delete Match"))
        self.delete_btn_browse.setIcon(QIcon("app/resources/icons/delete.png"))
        self.delete_btn_browse.clicked.connect(self._on_delete_btn_clicked)
        self.delete_btn_browse.setEnabled(False)  # Initially disabled

        # Duplicate button
        self.duplicate_btn = QPushButton(self.tr("Duplicate Match"))
        self.duplicate_btn.setIcon(QIcon("app/resources/icons/copy.png"))
        self.duplicate_btn.clicked.connect(self._on_duplicate_btn_clicked)
        self.duplicate_btn.setEnabled(False)  # Initially disabled

        # Add New Match button
        self.add_match_btn = QPushButton(self.tr("Add New Match"))
        self.add_match_btn.setIcon(QIcon("app/resources/icons/add.png"))
        self.add_match_btn.clicked.connect(self._on_add_match_clicked)

        # Add buttons to layout
        browse_actions_layout.addWidget(self.edit_btn_top)
        browse_actions_layout.addWidget(self.delete_btn_browse)
        browse_actions_layout.addWidget(self.duplicate_btn)
        browse_actions_layout.addWidget(self.add_match_btn)

        # Add widgets to top layout
        top_layout.addWidget(mode_group)
        top_layout.addWidget(self.filter_widget)
        top_layout.addWidget(self.action_widget)
        top_layout.addWidget(self.browse_actions_widget)

        return top_frame

    def _create_browse_mode(self):
        """Create the browse mode widget."""
        browse_widget = QWidget()
        browse_layout = QVBoxLayout(browse_widget)

        # Create match table
        self.match_table = QTableWidget()
        self.match_table.setColumnCount(7)  # Added Home/Away and Match Finish columns
        self.match_table.setHorizontalHeaderLabels([
            self.tr("Date"),
            self.tr("Competition"),
            self.tr("Opponent"),
            self.tr("Home/Away"),
            self.tr("Score"),
            self.tr("Result"),
            self.tr("Match Finish")
        ])

        # Enable double-click to edit
        self.match_table.doubleClicked.connect(self._on_table_double_clicked)

        # Enable context menu
        self.match_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.match_table.customContextMenuRequested.connect(self._show_context_menu)

        # Connect selection changed signal to enable/disable buttons
        self.match_table.itemSelectionChanged.connect(self._on_table_selection_changed)

        # Set table properties
        self.match_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.match_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)

        # Enable column resizing by setting ResizeMode to Interactive
        header = self.match_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)

        # Keep the Actions column with fixed width
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)

        # Enable column reordering
        header.setSectionsMovable(True)

        # Enable sorting with left click
        header.setSortIndicatorShown(True)
        header.setSectionsClickable(True)
        header.sectionClicked.connect(self._on_header_clicked)

        # Save header state when columns are moved or resized
        header.sectionMoved.connect(self._save_header_state)
        header.sectionResized.connect(self._save_header_state)

        browse_layout.addWidget(self.match_table)

        return browse_widget

    def _create_edit_mode(self):
        """Create the edit mode widget."""
        edit_widget = QWidget()
        edit_layout = QHBoxLayout(edit_widget)

        # Create splitter for left panel and tabbed content
        self.edit_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Create left panel for match details
        left_panel = self._create_match_details_panel()

        # Create right panel with tabs
        right_panel = self._create_tabbed_panel()

        # Add panels to splitter
        self.edit_splitter.addWidget(left_panel)
        self.edit_splitter.addWidget(right_panel)

        # Set initial sizes (40% left, 60% right)
        self.edit_splitter.setSizes([400, 600])

        edit_layout.addWidget(self.edit_splitter)

        return edit_widget

    def _create_match_details_panel(self):
        """Create the left panel with match details."""
        left_panel = QScrollArea()
        left_panel.setWidgetResizable(True)
        left_panel.setFrameShape(QFrame.Shape.NoFrame)

        content_widget = QWidget()
        left_layout = QVBoxLayout(content_widget)

        # Match Identity Section
        identity_group = QGroupBox(self.tr("Match Identity"))
        identity_layout = QFormLayout(identity_group)

        self.match_id_field = QLineEdit()
        self.match_id_field.setReadOnly(True)
        self.match_id_field.setPlaceholderText(self.tr("Auto-generated"))

        # Get season dates for validation
        season_start, season_end = self._get_season_dates()

        # Determine initial date (today if within season, otherwise season start)
        today = QDate.currentDate()
        initial_date = today if (today >= season_start and today <= season_end) else season_start

        self.match_date = QDateEdit()
        self.match_date.setCalendarPopup(True)
        self.match_date.setDate(initial_date)

        # Set min/max dates to prevent selecting dates outside season range
        self.match_date.setMinimumDate(season_start)
        self.match_date.setMaximumDate(season_end)

        # Connect date changed signal to validate in real-time
        self.match_date.dateChanged.connect(self._validate_match_date)

        self.match_time = QTimeEdit()
        self.match_time.setTime(QTime(18, 0))  # Default to 6:00 PM

        self.competition_field = QComboBox()
        self.season_field = QComboBox()
        self.matchday_field = QLineEdit()

        identity_layout.addRow(self.tr("Match ID:"), self.match_id_field)
        identity_layout.addRow(self.tr("Date:"), self.match_date)
        identity_layout.addRow(self.tr("Time:"), self.match_time)
        identity_layout.addRow(self.tr("Competition:"), self.competition_field)
        identity_layout.addRow(self.tr("Season:"), self.season_field)
        identity_layout.addRow(self.tr("Matchday/Round:"), self.matchday_field)

        # Teams and Score Section
        teams_group = QGroupBox(self.tr("Teams and Score"))
        teams_layout = QFormLayout(teams_group)

        home_away_widget = QWidget()
        home_away_layout = QHBoxLayout(home_away_widget)
        home_away_layout.setContentsMargins(0, 0, 0, 0)

        self.home_radio = QRadioButton(self.tr("Home"))
        self.away_radio = QRadioButton(self.tr("Away"))
        self.home_radio.setChecked(True)

        # Connect home/away radio buttons to handler
        self.home_radio.toggled.connect(self._on_home_away_toggled)

        home_away_layout.addWidget(self.home_radio)
        home_away_layout.addWidget(self.away_radio)

        self.opponent_field = QLineEdit()

        score_widget = QWidget()
        score_layout = QHBoxLayout(score_widget)
        score_layout.setContentsMargins(0, 0, 0, 0)

        self.team_score = QSpinBox()
        score_separator = QLabel("-")
        self.opponent_score = QSpinBox()

        score_layout.addWidget(self.team_score)
        score_layout.addWidget(score_separator)
        score_layout.addWidget(self.opponent_score)

        # Half-time checkbox
        self.halftime_check = QCheckBox(self.tr("Half-time Score Available"))
        self.halftime_check.toggled.connect(self._on_halftime_toggled)
        self.halftime_check.setChecked(True)  # Default to checked

        ht_score_widget = QWidget()
        ht_score_layout = QHBoxLayout(ht_score_widget)
        ht_score_layout.setContentsMargins(0, 0, 0, 0)

        self.ht_team_score = QSpinBox()
        self.ht_team_score.setRange(0, 99)
        ht_score_separator = QLabel("-")
        self.ht_opponent_score = QSpinBox()
        self.ht_opponent_score.setRange(0, 99)

        ht_score_layout.addWidget(self.ht_team_score)
        ht_score_layout.addWidget(ht_score_separator)
        ht_score_layout.addWidget(self.ht_opponent_score)

        self.extra_time_check = QCheckBox(self.tr("Extra Time Played"))
        self.extra_time_check.toggled.connect(self._on_extra_time_toggled)

        et_score_widget = QWidget()
        et_score_layout = QHBoxLayout(et_score_widget)
        et_score_layout.setContentsMargins(0, 0, 0, 0)

        self.et_team_score = QSpinBox()
        et_score_separator = QLabel("-")
        self.et_opponent_score = QSpinBox()

        et_score_layout.addWidget(self.et_team_score)
        et_score_layout.addWidget(et_score_separator)
        et_score_layout.addWidget(self.et_opponent_score)

        # Extra time duration field
        self.et_duration_widget = QWidget()
        self.et_duration_widget.setVisible(False)  # Initially hidden
        et_duration_layout = QHBoxLayout(self.et_duration_widget)
        et_duration_layout.setContentsMargins(0, 0, 0, 0)

        et_duration_label = QLabel(self.tr("Duration:"))
        self.et_duration_spinner = QSpinBox()
        self.et_duration_spinner.setRange(1, 60)
        self.et_duration_spinner.setValue(30)  # Default value
        self.et_duration_spinner.setSuffix(self.tr(" min"))

        et_duration_layout.addWidget(et_duration_label)
        et_duration_layout.addWidget(self.et_duration_spinner)
        et_duration_layout.addStretch()

        self.penalties_check = QCheckBox(self.tr("Penalty Shootout"))
        self.penalties_check.toggled.connect(self._on_penalties_toggled)

        penalties_widget = QWidget()
        penalties_layout = QHBoxLayout(penalties_widget)
        penalties_layout.setContentsMargins(0, 0, 0, 0)

        self.penalties_team_score = QSpinBox()
        penalties_separator = QLabel("-")
        self.penalties_opponent_score = QSpinBox()

        penalties_layout.addWidget(self.penalties_team_score)
        penalties_layout.addWidget(penalties_separator)
        penalties_layout.addWidget(self.penalties_opponent_score)

        self.result_label = QLabel(self.tr("Result: "))

        teams_layout.addRow(self.tr("Home/Away:"), home_away_widget)
        teams_layout.addRow(self.tr("Opponent:"), self.opponent_field)
        teams_layout.addRow(self.tr("Full Time Score:"), score_widget)
        teams_layout.addRow("", self.halftime_check)
        teams_layout.addRow(self.tr("Half-time Score:"), ht_score_widget)
        teams_layout.addRow("", self.extra_time_check)
        teams_layout.addRow(self.tr("Extra Time Score:"), et_score_widget)
        teams_layout.addRow("", self.et_duration_widget)  # Extra time duration
        teams_layout.addRow("", self.penalties_check)
        teams_layout.addRow(self.tr("Penalty Shootout:"), penalties_widget)
        teams_layout.addRow(self.tr("Result:"), self.result_label)

        # Match duration section - vertical layout
        # Create match duration row
        match_duration_widget = QWidget()
        match_duration_layout = QHBoxLayout(match_duration_widget)
        match_duration_layout.setContentsMargins(0, 0, 0, 0)

        # Match duration value (auto-populated from competition)
        self.match_duration_value = QLabel("90")  # Default value
        self.match_duration_value.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.match_duration_value.setMinimumWidth(30)
        match_duration_suffix = QLabel(self.tr("min"))

        match_duration_layout.addWidget(self.match_duration_value)
        match_duration_layout.addWidget(match_duration_suffix)
        match_duration_layout.addStretch()

        # Create stoppage time row
        stoppage_time_widget = QWidget()
        stoppage_time_layout = QHBoxLayout(stoppage_time_widget)
        stoppage_time_layout.setContentsMargins(0, 0, 0, 0)

        # Stoppage time spinner
        self.stoppage_time_spinner = QSpinBox()
        self.stoppage_time_spinner.setRange(0, 200)
        self.stoppage_time_spinner.setValue(0)
        self.stoppage_time_spinner.setSuffix(self.tr(" min"))
        self.stoppage_time_spinner.valueChanged.connect(self._on_stoppage_time_changed)

        stoppage_time_layout.addWidget(self.stoppage_time_spinner)
        stoppage_time_layout.addStretch()

        # Create full match row
        full_match_widget = QWidget()
        full_match_layout = QHBoxLayout(full_match_widget)
        full_match_layout.setContentsMargins(0, 0, 0, 0)

        # Full match duration calculation
        self.full_match_value = QLabel("90")  # Default value
        self.full_match_value.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.full_match_value.setMinimumWidth(30)
        full_match_suffix = QLabel(self.tr("min"))

        full_match_layout.addWidget(self.full_match_value)
        full_match_layout.addWidget(full_match_suffix)
        full_match_layout.addStretch()

        # Add to form layout - each field on its own row
        teams_layout.addRow(self.tr("Match duration:"), match_duration_widget)
        teams_layout.addRow(self.tr("Stoppage time:"), stoppage_time_widget)
        teams_layout.addRow(self.tr("Full match:"), full_match_widget)

        # Venue Section (Collapsible)
        self.venue_group = CollapsibleGroupBox(self.tr("Venue"))
        venue_content = QWidget()
        venue_layout = QFormLayout(venue_content)

        self.venue_field = QLineEdit()
        self.city_field = QLineEdit()
        self.country_field = QComboBox()
        self.country_field.setEditable(True)
        self.country_field.addItems([""] + countries)

        venue_layout.addRow(self.tr("Venue/Stadium:"), self.venue_field)
        venue_layout.addRow(self.tr("City:"), self.city_field)
        venue_layout.addRow(self.tr("Country:"), self.country_field)

        self.venue_group.add_widget(venue_content)
        self.venue_group.collapsedChanged.connect(lambda collapsed: self._save_section_state("venue", collapsed))
        self.collapsible_sections["venue"] = self.venue_group

        # Match Conditions Section (Collapsible)
        self.conditions_group = CollapsibleGroupBox(self.tr("Match Conditions"))
        conditions_content = QWidget()
        conditions_layout = QFormLayout(conditions_content)

        self.weather_combo = QComboBox()
        self.attendance_field = QSpinBox()
        self.attendance_field.setMaximum(999999)
        self.broadcast_field = QLineEdit()

        conditions_layout.addRow(self.tr("Weather:"), self.weather_combo)
        conditions_layout.addRow(self.tr("Attendance:"), self.attendance_field)
        conditions_layout.addRow(self.tr("Broadcast:"), self.broadcast_field)

        self.conditions_group.add_widget(conditions_content)
        self.conditions_group.collapsedChanged.connect(lambda collapsed: self._save_section_state("conditions", collapsed))
        self.collapsible_sections["conditions"] = self.conditions_group

        # Officials Section (Collapsible)
        self.officials_group = CollapsibleGroupBox(self.tr("Officials"))
        officials_content = QWidget()
        officials_layout = QFormLayout(officials_content)

        self.referee_fields = []
        for i in range(6):
            referee_field = QLineEdit()
            self.referee_fields.append(referee_field)
            officials_layout.addRow(self.tr(f"Referee {i+1}:"), referee_field)

        self.referee_rating = QSpinBox()
        self.referee_rating.setMinimum(1)
        self.referee_rating.setMaximum(10)
        officials_layout.addRow(self.tr("Referee Rating:"), self.referee_rating)

        # Add comments field
        from PySide6.QtWidgets import QTextEdit
        self.referee_comments = QTextEdit()
        self.referee_comments.setMaximumHeight(80)
        officials_layout.addRow(self.tr("Comments:"), self.referee_comments)

        self.officials_group.add_widget(officials_content)
        self.officials_group.collapsedChanged.connect(lambda collapsed: self._save_section_state("officials", collapsed))
        self.collapsible_sections["officials"] = self.officials_group

        # Match Statistics Section (Collapsible)
        self.stats_group = CollapsibleGroupBox(self.tr("Match Statistics"))
        stats_content = QWidget()
        stats_layout = QVBoxLayout(stats_content)

        # Create header with team names
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Empty label for the first column (stat name)
        header_layout.addWidget(QLabel(""), 1)

        # Team name labels
        self.team_name_label = QLabel(self.tr("My Team"))
        self.opponent_name_label = QLabel(self.tr("Opponent"))

        # Set alignment and style for team name labels
        for label in [self.team_name_label, self.opponent_name_label]:
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            font = label.font()
            font.setBold(True)
            label.setFont(font)

        header_layout.addWidget(self.team_name_label, 1)
        header_layout.addWidget(self.opponent_name_label, 1)

        stats_layout.addWidget(header_widget)

        # Create grid for statistics
        stats_grid_widget = QWidget()
        stats_grid = QGridLayout(stats_grid_widget)
        stats_grid.setContentsMargins(0, 0, 0, 0)

        # Define statistics fields
        stat_fields = [
            "final_score",  # New field for final score
            "attempts",
            "on_target",
            "passes",
            "completed_passes",
            "fouls",
            "yellow_cards",
            "red_cards",
            "offsides",
            "corners",
            "possession",
            "xgoals"
        ]

        # Define display names for statistics
        stat_display_names = {
            "final_score": self.tr("Final Score"),
            "attempts": self.tr("Attempts"),
            "on_target": self.tr("On Target"),
            "passes": self.tr("Passes"),
            "completed_passes": self.tr("Completed Passes"),
            "fouls": self.tr("Fouls Committed"),
            "yellow_cards": self.tr("Yellow Cards"),
            "red_cards": self.tr("Red Cards"),
            "offsides": self.tr("Offsides"),
            "corners": self.tr("Corners"),
            "possession": self.tr("Possession (%)"),
            "xgoals": self.tr("xGoals")
        }

        # Create spinboxes for each statistic
        self.team_stats = {}
        self.opponent_stats = {}

        # Add each statistic to the grid
        for row, field in enumerate(stat_fields):
            # Add label
            label = QLabel(stat_display_names[field])

            # Make the Final Score label bold and more visible
            if field == "final_score":
                font = label.font()
                font.setBold(True)
                font.setPointSize(font.pointSize() + 1)  # Make it slightly larger
                label.setFont(font)

            stats_grid.addWidget(label, row, 0)

            # Special handling for Final Score field
            if field == "final_score":
                # Create labels instead of spinboxes for final score
                team_score_label = QLabel("0")
                opponent_score_label = QLabel("0")

                # Style the score labels
                for score_label in [team_score_label, opponent_score_label]:
                    score_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    font = score_label.font()
                    font.setBold(True)
                    score_label.setFont(font)

                # Add labels to the grid
                stats_grid.addWidget(team_score_label, row, 1)
                stats_grid.addWidget(opponent_score_label, row, 2)

                # Store references to the labels
                self.team_stats[field] = team_score_label
                self.opponent_stats[field] = opponent_score_label

                continue  # Skip the rest of the loop for this field

            # Create spinboxes with appropriate ranges and settings
            # Use QDoubleSpinBox for xGoals, QSpinBox for others
            if field == "xgoals":
                team_spinbox = QDoubleSpinBox()
                opponent_spinbox = QDoubleSpinBox()
                # Set decimal precision and range for xGoals
                team_spinbox.setDecimals(2)
                opponent_spinbox.setDecimals(2)
                team_spinbox.setRange(0, 10.0)
                opponent_spinbox.setRange(0, 10.0)
                team_spinbox.setSingleStep(0.1)
                opponent_spinbox.setSingleStep(0.1)
            else:
                team_spinbox = QSpinBox()
                opponent_spinbox = QSpinBox()

            # Set ranges based on the statistic type
            if field == "possession":
                team_spinbox.setRange(0, 100)
                opponent_spinbox.setRange(0, 100)
                team_spinbox.setSuffix("%")
                opponent_spinbox.setSuffix("%")

                # Connect signals for possession to ensure they sum to 100%
                team_spinbox.valueChanged.connect(lambda value, sb=opponent_spinbox: sb.setValue(100 - value))
                opponent_spinbox.valueChanged.connect(lambda value, sb=team_spinbox: sb.setValue(100 - value))

                # Set default values
                team_spinbox.setValue(50)
                opponent_spinbox.setValue(50)
            elif field == "on_target":
                # On target must be <= Attempts, so we need to connect to the attempts field
                team_spinbox.setRange(0, 0)  # Will be updated when attempts changes
                opponent_spinbox.setRange(0, 0)  # Will be updated when attempts changes
            elif field in ["yellow_cards", "red_cards"]:
                # Reasonable max for cards
                team_spinbox.setRange(0, 10)
                opponent_spinbox.setRange(0, 10)
            elif field == "attempts":
                # Set a reasonable max for attempts
                team_spinbox.setRange(0, 99)
                opponent_spinbox.setRange(0, 99)

                # We'll connect these to the on_target fields after all fields are created
            else:
                # For other statistics, use appropriate ranges
                team_spinbox.setRange(0, 99)
                opponent_spinbox.setRange(0, 99)

            # Add spinboxes to the grid
            stats_grid.addWidget(team_spinbox, row, 1)
            stats_grid.addWidget(opponent_spinbox, row, 2)

            # Store references to the spinboxes
            self.team_stats[field] = team_spinbox
            self.opponent_stats[field] = opponent_spinbox

        # Add the grid to the layout
        stats_layout.addWidget(stats_grid_widget)

        # Now that all fields are created, connect the attempts fields to the on_target fields
        # to ensure on_target <= attempts
        if "attempts" in self.team_stats and "on_target" in self.team_stats:
            # Update the on_target max value when attempts changes
            def update_team_on_target_max(value):
                self.team_stats["on_target"].setMaximum(value)
                # If current on_target value is greater than new max, adjust it
                if self.team_stats["on_target"].value() > value:
                    self.team_stats["on_target"].setValue(value)

            # Connect the signals
            self.team_stats["attempts"].valueChanged.connect(update_team_on_target_max)

            # Initialize with current attempts value
            update_team_on_target_max(self.team_stats["attempts"].value())

        if "attempts" in self.opponent_stats and "on_target" in self.opponent_stats:
            # Update the on_target max value when attempts changes
            def update_opponent_on_target_max(value):
                self.opponent_stats["on_target"].setMaximum(value)
                # If current on_target value is greater than new max, adjust it
                if self.opponent_stats["on_target"].value() > value:
                    self.opponent_stats["on_target"].setValue(value)

            # Connect the signals
            self.opponent_stats["attempts"].valueChanged.connect(update_opponent_on_target_max)

            # Initialize with current attempts value
            update_opponent_on_target_max(self.opponent_stats["attempts"].value())

        # Connect passes fields to completed_passes fields to ensure completed_passes <= passes
        if "passes" in self.team_stats and "completed_passes" in self.team_stats:
            # Update the completed_passes max value when passes changes
            def update_team_completed_passes_max(value):
                self.team_stats["completed_passes"].setMaximum(value)
                # If current completed_passes value is greater than new max, adjust it
                if self.team_stats["completed_passes"].value() > value:
                    self.team_stats["completed_passes"].setValue(value)

            # Connect the signals
            self.team_stats["passes"].valueChanged.connect(update_team_completed_passes_max)

            # Initialize with current passes value
            update_team_completed_passes_max(self.team_stats["passes"].value())

        if "passes" in self.opponent_stats and "completed_passes" in self.opponent_stats:
            # Update the completed_passes max value when passes changes
            def update_opponent_completed_passes_max(value):
                self.opponent_stats["completed_passes"].setMaximum(value)
                # If current completed_passes value is greater than new max, adjust it
                if self.opponent_stats["completed_passes"].value() > value:
                    self.opponent_stats["completed_passes"].setValue(value)

            # Connect the signals
            self.opponent_stats["passes"].valueChanged.connect(update_opponent_completed_passes_max)

            # Initialize with current passes value
            update_opponent_completed_passes_max(self.opponent_stats["passes"].value())

        # Add the content to the collapsible group
        self.stats_group.add_widget(stats_content)
        self.stats_group.collapsedChanged.connect(lambda collapsed: self._save_section_state("stats", collapsed))
        self.collapsible_sections["stats"] = self.stats_group

        # Create Advanced Match Statistics Section (Collapsible)
        self.adv_stats_group = CollapsibleGroupBox(self.tr("Advanced Match Statistics"))
        adv_stats_content = QWidget()
        adv_stats_layout = QVBoxLayout(adv_stats_content)

        # Create header with team names (reuse the same team names from basic stats)
        adv_header_widget = QWidget()
        adv_header_layout = QHBoxLayout(adv_header_widget)
        adv_header_layout.setContentsMargins(0, 0, 0, 0)

        # Empty label for the first column (stat name)
        adv_header_layout.addWidget(QLabel(""), 1)

        # Team name labels (create new labels with the same text)
        self.adv_team_name_label = QLabel(self.tr("My Team"))
        self.adv_opponent_name_label = QLabel(self.tr("Opponent"))

        # Set alignment and style for team name labels
        for label in [self.adv_team_name_label, self.adv_opponent_name_label]:
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            font = label.font()
            font.setBold(True)
            label.setFont(font)

        adv_header_layout.addWidget(self.adv_team_name_label, 1)
        adv_header_layout.addWidget(self.adv_opponent_name_label, 1)

        adv_stats_layout.addWidget(adv_header_widget)

        # Create grid for advanced statistics
        adv_stats_grid_widget = QWidget()
        adv_stats_grid = QGridLayout(adv_stats_grid_widget)
        adv_stats_grid.setContentsMargins(0, 0, 0, 0)

        # Define advanced statistics fields
        adv_stat_fields = [
            "crosses",
            "touches",
            "tackles",
            "interceptions",
            "aerials_won",
            "clearances",
            "long_balls",
            "saves"
        ]

        # Define display names for advanced statistics
        adv_stat_display_names = {
            "crosses": self.tr("Crosses"),
            "touches": self.tr("Touches"),
            "tackles": self.tr("Tackles"),
            "interceptions": self.tr("Interceptions"),
            "aerials_won": self.tr("Aerials Won"),
            "clearances": self.tr("Clearances"),
            "long_balls": self.tr("Long Balls"),
            "saves": self.tr("Saves")
        }

        # Create spinboxes for each advanced statistic
        self.team_adv_stats = {}
        self.opponent_adv_stats = {}

        # Add each advanced statistic to the grid
        for row, field in enumerate(adv_stat_fields):
            # Add label
            label = QLabel(adv_stat_display_names[field])
            adv_stats_grid.addWidget(label, row, 0)

            # Create spinboxes for team and opponent
            team_spinbox = QSpinBox()
            opponent_spinbox = QSpinBox()

            # Set reasonable ranges for all advanced statistics
            team_spinbox.setRange(0, 99)
            opponent_spinbox.setRange(0, 99)

            # Add spinboxes to the grid
            adv_stats_grid.addWidget(team_spinbox, row, 1)
            adv_stats_grid.addWidget(opponent_spinbox, row, 2)

            # Store references to the spinboxes
            self.team_adv_stats[field] = team_spinbox
            self.opponent_adv_stats[field] = opponent_spinbox

        # Add the grid to the layout
        adv_stats_layout.addWidget(adv_stats_grid_widget)

        # Add the content to the collapsible group
        self.adv_stats_group.add_widget(adv_stats_content)
        self.adv_stats_group.collapsedChanged.connect(lambda collapsed: self._save_section_state("adv_stats", collapsed))
        self.collapsible_sections["adv_stats"] = self.adv_stats_group

        # Add all sections to the layout
        left_layout.addWidget(identity_group)
        left_layout.addWidget(teams_group)
        left_layout.addWidget(self.stats_group)
        left_layout.addWidget(self.adv_stats_group)
        left_layout.addWidget(self.venue_group)
        left_layout.addWidget(self.conditions_group)
        left_layout.addWidget(self.officials_group)
        left_layout.addStretch()

        left_panel.setWidget(content_widget)
        return left_panel

    def _create_tabbed_panel(self):
        """Create the right panel with tabs."""
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Create tab widget
        self.detail_tabs = QTabWidget()

        # Create tabs
        self.players_stats_tab = QWidget()
        tactics_tab = QWidget()
        timeline_tab = QWidget()
        team_staff_tab = QWidget()

        # Add content to tabs
        self._create_players_stats_tab()
        self._add_placeholder_content(tactics_tab, self.tr("Tactics"))
        self._add_placeholder_content(timeline_tab, self.tr("Timeline"))
        self._add_placeholder_content(team_staff_tab, self.tr("Team Staff"))

        # Add tabs to tab widget
        self.detail_tabs.addTab(self.players_stats_tab, self.tr("Players Stats"))
        self.detail_tabs.addTab(tactics_tab, self.tr("Tactics"))
        self.detail_tabs.addTab(timeline_tab, self.tr("Timeline"))
        self.detail_tabs.addTab(team_staff_tab, self.tr("Team Staff"))

        right_layout.addWidget(self.detail_tabs)

        return right_panel

    def _create_players_stats_tab(self):
        """Create the Players Stats tab with a table for player statistics."""
        layout = QVBoxLayout(self.players_stats_tab)

        # Create top controls
        top_controls = QWidget()
        top_layout = QHBoxLayout(top_controls)

        # Add player button
        self.add_player_btn = QPushButton(self.tr("Add Player"))
        self.add_player_btn.setIcon(QIcon("app/resources/icons/add.png"))
        self.add_player_btn.clicked.connect(self._on_add_player_clicked)

        # Remove player button
        self.remove_player_btn = QPushButton(self.tr("Remove Player"))
        self.remove_player_btn.setIcon(QIcon("app/resources/icons/delete.png"))
        self.remove_player_btn.clicked.connect(self._on_remove_player_clicked)
        self.remove_player_btn.setEnabled(False)  # Initially disabled

        # Basic view button
        self.basic_view_btn = QPushButton(self.tr("Basic"))
        self.basic_view_btn.setIcon(QIcon("app/resources/icons/view.png"))
        self.basic_view_btn.clicked.connect(self._toggle_basic_view)
        self.basic_view_btn.setCheckable(True)  # Make it toggleable

        top_layout.addWidget(self.add_player_btn)
        top_layout.addWidget(self.remove_player_btn)
        top_layout.addWidget(self.basic_view_btn)
        top_layout.addStretch()

        # Create player stats table
        self.player_stats_table = QTableWidget()
        self.player_stats_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectItems)
        self.player_stats_table.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        # Allow single-click editing for cells
        self.player_stats_table.setEditTriggers(QAbstractItemView.EditTrigger.SelectedClicked | QAbstractItemView.EditTrigger.EditKeyPressed)
        self.player_stats_table.setAlternatingRowColors(False)
        self.player_stats_table.setShowGrid(True)
        self.player_stats_table.setStyleSheet("QTableWidget { gridline-color: #CCCCCC; }")

        # Set up columns
        self._setup_player_stats_columns()

        # Connect signals
        self.player_stats_table.itemSelectionChanged.connect(self._on_player_selection_changed)
        self.player_stats_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.player_stats_table.customContextMenuRequested.connect(self._show_player_stats_context_menu)
        self.player_stats_table.itemChanged.connect(self._on_player_stats_item_changed)
        self.player_stats_table.itemDoubleClicked.connect(self._on_player_stats_item_double_clicked)
        self.player_stats_table.clicked.connect(self._on_player_stats_item_clicked)

        # Set up context menu for column headers
        header = self.player_stats_table.horizontalHeader()
        header.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        header.customContextMenuRequested.connect(self._show_header_context_menu)

        # Install event filter for keyboard shortcuts
        self.player_stats_table.installEventFilter(self)

        # Add widgets to layout
        layout.addWidget(top_controls)
        layout.addWidget(self.player_stats_table)

        # Store player data
        self.player_stats_data = []

    def _setup_player_stats_columns(self):
        """Set up the columns for the player stats table."""
        # Define columns
        columns = [
            {"name": self.tr("Lineup"), "key": "lineup", "width": 60, "hideable": False, "tooltip": self.tr("Player is in the starting lineup")},
            {"name": self.tr("No"), "key": "shirt_number", "width": 40, "hideable": False, "tooltip": self.tr("Shirt number")},
            {"name": self.tr("Player"), "key": "player_name", "width": 150, "hideable": False, "tooltip": self.tr("Player name")},
            {"name": self.tr("Pos"), "key": "position", "width": 50, "hideable": False, "tooltip": self.tr("Position (GK, DF, MF, AT)")},
            {"name": self.tr("(C)"), "key": "captain", "width": 40, "hideable": False, "tooltip": self.tr("Captain")},
            {"name": self.tr("Sub In"), "key": "sub_in", "width": 60, "hideable": False, "tooltip": self.tr("Substitution in minute")},
            {"name": self.tr("Sub Out"), "key": "sub_out", "width": 60, "hideable": False, "tooltip": self.tr("Substitution out minute")},
            {"name": self.tr("Min"), "key": "minutes_played", "width": 50, "hideable": True, "tooltip": self.tr("Minutes played")},
            {"name": self.tr("Goals"), "key": "goals", "width": 50, "hideable": False, "tooltip": self.tr("Goals scored")},
            {"name": self.tr("Assists"), "key": "assists", "width": 50, "hideable": False, "tooltip": self.tr("Assists provided")},
            {"name": self.tr("Y"), "key": "yellow_cards", "width": 30, "hideable": False, "tooltip": self.tr("Yellow cards (0-2)")},
            {"name": self.tr("R"), "key": "red_cards", "width": 30, "hideable": False, "tooltip": self.tr("Red cards (0-1)")},
            {"name": self.tr("Inj"), "key": "injury_minute", "width": 40, "hideable": False, "tooltip": self.tr("Injury minute")},
            {"name": self.tr("Injury Description"), "key": "injury_description", "width": 150, "hideable": True, "tooltip": self.tr("Description of the injury")},
            # Additional stats columns
            {"name": self.tr("Attempts"), "key": "attempts", "width": 70, "hideable": True, "tooltip": self.tr("Shot attempts")},
            {"name": self.tr("On Target"), "key": "on_target", "width": 70, "hideable": True, "tooltip": self.tr("Shots on target")},
            {"name": self.tr("Touches"), "key": "touches", "width": 70, "hideable": True, "tooltip": self.tr("Ball touches")},
            {"name": self.tr("Tackles"), "key": "tackles", "width": 70, "hideable": True, "tooltip": self.tr("Tackles attempted")},
            {"name": self.tr("Tackles Won"), "key": "tackles_won", "width": 90, "hideable": True, "tooltip": self.tr("Tackles won")},
            {"name": self.tr("Interceptions"), "key": "interceptions", "width": 90, "hideable": True, "tooltip": self.tr("Interceptions")},
            {"name": self.tr("Shot Blocks"), "key": "shot_blocks", "width": 90, "hideable": True, "tooltip": self.tr("Shots blocked")},
            {"name": self.tr("Pass Blocks"), "key": "pass_blocks", "width": 90, "hideable": True, "tooltip": self.tr("Passes blocked")},
            {"name": self.tr("Clearances"), "key": "clearances", "width": 90, "hideable": True, "tooltip": self.tr("Clearances")},
            {"name": self.tr("Error → Shot"), "key": "error_opp_shot", "width": 90, "hideable": True, "tooltip": self.tr("Errors that led to opponent shots")},
            {"name": self.tr("xG"), "key": "xgoals", "width": 50, "hideable": True, "tooltip": self.tr("Expected goals")},
            {"name": self.tr("GCA"), "key": "goal_creating_actions", "width": 50, "hideable": True, "tooltip": self.tr("Goal creating actions")},
            {"name": self.tr("Passes Att"), "key": "passes_attempted", "width": 80, "hideable": True, "tooltip": self.tr("Passes attempted")},
            {"name": self.tr("Passes Comp"), "key": "passes_completed", "width": 90, "hideable": True, "tooltip": self.tr("Passes completed")},
            {"name": self.tr("Passes Prog"), "key": "passes_progressive", "width": 90, "hideable": True, "tooltip": self.tr("Progressive passes")},
            {"name": self.tr("Carries Prog"), "key": "carries_progressive", "width": 90, "hideable": True, "tooltip": self.tr("Progressive carries")},
            {"name": self.tr("Take-ons Att"), "key": "take_ons_attempted", "width": 90, "hideable": True, "tooltip": self.tr("Take-ons attempted")},
            {"name": self.tr("Take-ons Succ"), "key": "take_ons_successful", "width": 100, "hideable": True, "tooltip": self.tr("Take-ons successful")},
            {"name": self.tr("Crosses"), "key": "crosses", "width": 70, "hideable": True, "tooltip": self.tr("Crosses")},
            {"name": self.tr("Aerial Won"), "key": "aerial_won", "width": 80, "hideable": True, "tooltip": self.tr("Aerial duels won")},
            {"name": self.tr("Aerial Lost"), "key": "aerial_lost", "width": 80, "hideable": True, "tooltip": self.tr("Aerial duels lost")},
            {"name": self.tr("Fouls Com"), "key": "fouls_committed", "width": 80, "hideable": True, "tooltip": self.tr("Fouls committed")},
            {"name": self.tr("Fouls Suf"), "key": "fouls_suffered", "width": 80, "hideable": True, "tooltip": self.tr("Fouls suffered")},
            {"name": self.tr("Offsides"), "key": "offsides", "width": 70, "hideable": True, "tooltip": self.tr("Offsides")},
            {"name": self.tr("Distance"), "key": "distance", "width": 70, "hideable": True, "tooltip": self.tr("Distance covered (km)")},
            {"name": self.tr("Events"), "key": "events", "width": 200, "hideable": True, "tooltip": self.tr("Visual summary of player match events")},
            # --- Performance Evaluation Columns ---
            {"name": self.tr("Work Rate"), "key": "work_rate", "width": 110, "hideable": True, "tooltip": self.tr("Player's work rate during the match")},
            {"name": self.tr("Focus & Discipline"), "key": "focus_discipline", "width": 130, "hideable": True, "tooltip": self.tr("Player's focus and discipline")},
            {"name": self.tr("Coachability"), "key": "coachability", "width": 110, "hideable": True, "tooltip": self.tr("Willingness to learn and adapt to coaching")},
            {"name": self.tr("Positioning"), "key": "positioning", "width": 110, "hideable": True, "tooltip": self.tr("Defensive and offensive positioning")},
            {"name": self.tr("Tactical Discipline"), "key": "tactical_discipline", "width": 130, "hideable": True, "tooltip": self.tr("Adherence to tactical instructions")},
            {"name": self.tr("Decision Making"), "key": "decision_making", "width": 120, "hideable": True, "tooltip": self.tr("Quality of decisions under pressure")},
            {"name": self.tr("Passing Quality"), "key": "passing_quality", "width": 120, "hideable": True, "tooltip": self.tr("Accuracy and creativity in passing")},
            {"name": self.tr("Ball Control"), "key": "ball_control", "width": 110, "hideable": True, "tooltip": self.tr("First touch and control under pressure")},
            {"name": self.tr("Dribbling"), "key": "dribbling", "width": 100, "hideable": True, "tooltip": self.tr("Ability to beat opponents with the ball")},
            {"name": self.tr("Shooting-Finishing"), "key": "shooting_finishing", "width": 130, "hideable": True, "tooltip": self.tr("Quality of shooting and finishing")},
            {"name": self.tr("Crossing-Distribution"), "key": "crossing_distribution", "width": 140, "hideable": True, "tooltip": self.tr("Crossing and distribution skills")},
            {"name": self.tr("Marking-Tracking"), "key": "marking_tracking", "width": 130, "hideable": True, "tooltip": self.tr("Ability to mark and track opponents")},
            {"name": self.tr("Tackling"), "key": "tackling_eval", "width": 100, "hideable": True, "tooltip": self.tr("Tackling effectiveness (evaluation)")},
            {"name": self.tr("Pressure-Pressing"), "key": "pressure_pressing", "width": 130, "hideable": True, "tooltip": self.tr("Pressing and applying pressure")},
            {"name": self.tr("Recovery Runs-Defensive Transitions"), "key": "recovery_runs", "width": 180, "hideable": True, "tooltip": self.tr("Recovery runs and defensive transitions")},
            {"name": self.tr("Composure Under Pressure"), "key": "composure_pressure", "width": 160, "hideable": True, "tooltip": self.tr("Composure under pressure")},
            {"name": self.tr("Leadership-Communication"), "key": "leadership_communication", "width": 160, "hideable": True, "tooltip": self.tr("Leadership and communication")},
            {"name": self.tr("Confidence"), "key": "confidence", "width": 110, "hideable": True, "tooltip": self.tr("Confidence during the match")},
            {"name": self.tr("Speed-Agility"), "key": "speed_agility", "width": 120, "hideable": True, "tooltip": self.tr("Speed and agility")},
            {"name": self.tr("Strength"), "key": "strength_eval", "width": 100, "hideable": True, "tooltip": self.tr("Physical strength (evaluation)")},
            {"name": self.tr("Stamina-Fitness"), "key": "stamina_fitness", "width": 130, "hideable": True, "tooltip": self.tr("Stamina and fitness")},
            {"name": self.tr("Role Understanding"), "key": "role_understanding", "width": 140, "hideable": True, "tooltip": self.tr("Understanding of role and responsibilities")},
            {"name": self.tr("Overall Performance"), "key": "overall_performance", "width": 140, "hideable": True, "tooltip": self.tr("Overall match performance evaluation")},
        ]

        # Set column count
        self.player_stats_table.setColumnCount(len(columns))

        # Set column headers
        header_labels = [col["name"] for col in columns]
        self.player_stats_table.setHorizontalHeaderLabels(header_labels)

        # Set tooltips for column headers
        header = self.player_stats_table.horizontalHeader()
        for i, col in enumerate(columns):
            if "tooltip" in col:
                header.model().setHeaderData(i, Qt.Orientation.Horizontal, col["tooltip"], Qt.ItemDataRole.ToolTipRole)

        # Set column widths and hide injury description column by default
        header = self.player_stats_table.horizontalHeader()

        # Enable drag and drop for column reordering
        header.setSectionsMovable(True)
        header.setDragDropMode(QAbstractItemView.DragDropMode.InternalMove)

        for i, col in enumerate(columns):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Interactive)
            self.player_stats_table.setColumnWidth(i, col["width"])

            # Hide the injury description column and most additional stats columns by default
            # But keep attempts and on_target visible (columns 14 and 15)
            if col["key"] == "injury_description" or (i >= 16):  # Column 16 and beyond are hidden by default
                self.player_stats_table.setColumnHidden(i, True)

        # Store column info for context menu
        self.player_stats_columns = columns

        # Restore header state (column visibility)
        self._restore_player_stats_header_state()

    def _on_add_player_clicked(self):
        """Handle adding a player to the stats table."""
        # Create player selection dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Select Player"))
        dialog.setMinimumWidth(400)

        dialog_layout = QVBoxLayout(dialog)

        # Create player list with multiple selection mode
        player_list = QListWidget()
        player_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)  # Allow multiple selection

        # Get players with match day selection enabled
        players = self.roster_manager.get_players_by_selection("match_day", True)

        # Sort players by position (GK, DF, MF, AT)
        position_order = {"GK": 0, "DF": 1, "MF": 2, "AT": 3}
        players.sort(key=lambda p: (position_order.get(p.get("position", ""), 4), p.get("last_name", "")))

        # Add players to list
        for player in players:
            shirt_number = player.get("shirt_number", "")
            last_name = player.get("last_name", "")
            first_name = player.get("first_name", "")
            position = player.get("position", "")

            # Format: "10 Smith John (MF)"
            display_text = f"{shirt_number} {last_name} {first_name} ({position})"

            item = QListWidgetItem(display_text)
            item.setData(Qt.ItemDataRole.UserRole, player)

            # Color goalkeepers blue
            if position == "GK":
                item.setForeground(QBrush(QColor("blue")))

            player_list.addItem(item)

        dialog_layout.addWidget(player_list)

        # Add buttons
        button_box = QWidget()
        button_layout = QHBoxLayout(button_box)

        add_btn = QPushButton(self.tr("Add Selected Players"))  # Changed to plural
        done_btn = QPushButton(self.tr("Done"))

        button_layout.addWidget(add_btn)
        button_layout.addWidget(done_btn)

        dialog_layout.addWidget(button_box)

        # Function to add the selected players
        def add_selected_players():
            selected_items = player_list.selectedItems()
            if selected_items:
                for item in selected_items:
                    selected_player = item.data(Qt.ItemDataRole.UserRole)
                    self._add_player_to_stats(selected_player)
                # Clear the selection to indicate the players were added
                player_list.clearSelection()

        # Function to handle double-click on a player
        def handle_double_click(item):
            selected_player = item.data(Qt.ItemDataRole.UserRole)
            self._add_player_to_stats(selected_player)
            # Clear only this item from selection
            item.setSelected(False)

        # Connect signals
        add_btn.clicked.connect(add_selected_players)
        done_btn.clicked.connect(dialog.accept)
        player_list.itemDoubleClicked.connect(handle_double_click)

        # Show dialog
        dialog.exec()

    def _add_player_to_stats(self, player):
        """Add a player to the stats table."""
        # Check if player is already in the table
        for i in range(self.player_stats_table.rowCount()):
            player_id_item = self.player_stats_table.item(i, 0)
            if player_id_item and player_id_item.data(Qt.ItemDataRole.UserRole) == player["player_id"]:
                # Player already in table, select it
                self.player_stats_table.selectRow(i)
                return

        # Get current number of players in lineup
        lineup_count = 0
        for i in range(self.player_stats_table.rowCount()):
            lineup_item = self.player_stats_table.item(i, 0)
            if lineup_item and lineup_item.checkState() == Qt.CheckState.Checked:
                lineup_count += 1

        # Get max players for this competition
        max_players = self._get_competition_x_a_side()

        # Block signals during table update
        self.player_stats_table.blockSignals(True)

        # Remember the player ID to select after sorting
        player_id = player["player_id"]

        try:
            # Add new row
            row = self.player_stats_table.rowCount()
            self.player_stats_table.insertRow(row)

            # Lineup checkbox (column 0)
            lineup_item = QTableWidgetItem()
            lineup_item.setFlags(lineup_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)

            # Set checked if we haven't reached max players yet
            if lineup_count < max_players:
                lineup_item.setCheckState(Qt.CheckState.Checked)
            else:
                lineup_item.setCheckState(Qt.CheckState.Unchecked)

            # Store player ID in user role
            lineup_item.setData(Qt.ItemDataRole.UserRole, player_id)
            self.player_stats_table.setItem(row, 0, lineup_item)

            # Shirt number (column 1)
            shirt_number = player.get("shirt_number", "")
            shirt_item = QTableWidgetItem(str(shirt_number) if shirt_number else "")
            shirt_item.setFlags(shirt_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            shirt_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 1, shirt_item)

            # Player name (column 2)
            player_name = f"{player.get('last_name', '')} {player.get('first_name', '')}"
            name_item = QTableWidgetItem(player_name.strip())
            name_item.setFlags(name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.player_stats_table.setItem(row, 2, name_item)

            # Position (column 3)
            position = player.get("position", "")
            pos_item = QTableWidgetItem(position)
            pos_item.setFlags(pos_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            pos_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 3, pos_item)

            # Captain (column 4)
            captain_item = QTableWidgetItem()
            captain_item.setFlags(captain_item.flags() | Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
            captain_item.setCheckState(Qt.CheckState.Unchecked)
            # Set text to empty string to ensure checkbox is displayed
            captain_item.setText("")
            self.player_stats_table.setItem(row, 4, captain_item)

            # Sub in (column 5)
            sub_in_item = QTableWidgetItem("")
            # Disable for lineup players
            if lineup_count < max_players:
                sub_in_item.setFlags(sub_in_item.flags() & ~Qt.ItemFlag.ItemIsEnabled)
            sub_in_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 5, sub_in_item)

            # Sub out (column 6)
            sub_out_item = QTableWidgetItem("")
            # Disable initially (enable only for lineup players or players with sub in)
            if lineup_count >= max_players:
                sub_out_item.setFlags(sub_out_item.flags() & ~Qt.ItemFlag.ItemIsEnabled)
            sub_out_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 6, sub_out_item)

            # Minutes played (column 7) - calculated field
            # Calculate initial value based on lineup status
            minutes_played = self._get_total_match_duration() if lineup_count < max_players else 0
            minutes_item = QTableWidgetItem(str(minutes_played))
            minutes_item.setFlags(minutes_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            minutes_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 7, minutes_item)

            # Goals (column 8)
            goals_item = QTableWidgetItem("0")
            goals_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 8, goals_item)

            # Assists (column 9)
            assists_item = QTableWidgetItem("0")
            assists_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 9, assists_item)

            # Yellow cards (column 10)
            yellow_cards_item = QTableWidgetItem("0")
            yellow_cards_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 10, yellow_cards_item)

            # Red cards (column 11)
            red_cards_item = QTableWidgetItem("0")
            red_cards_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 11, red_cards_item)

            # Injury minute (column 12)
            injury_item = QTableWidgetItem("")
            injury_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 12, injury_item)

            # Injury description (column 13) - hidden column
            injury_desc_item = QTableWidgetItem("")
            injury_desc_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 13, injury_desc_item)

            # Initialize all additional stats columns with "0"
            for col in range(14, self.player_stats_table.columnCount() - 1):  # Exclude the last column (Events)
                # Special handling for xG which can have decimal values
                if col == 24:  # xG column
                    stat_item = QTableWidgetItem("0.0")
                else:
                    stat_item = QTableWidgetItem("0")
                # Ensure the item is editable
                stat_item.setFlags(stat_item.flags() | Qt.ItemFlag.ItemIsEditable)
                # Center align all stats columns
                stat_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.player_stats_table.setItem(row, col, stat_item)

            # Events column (last column) - Create events widget with real data
            events_col = self.player_stats_table.columnCount() - 1
            from app.widgets.player_events_widget import PlayerEventsLabel
            events_widget = PlayerEventsLabel()

            # Get player data for events from the table items just created
            goals = int(self.player_stats_table.item(row, 8).text()) if self.player_stats_table.item(row, 8) else 0
            assists = int(self.player_stats_table.item(row, 9).text()) if self.player_stats_table.item(row, 9) else 0
            sub_in_text = self.player_stats_table.item(row, 5).text().strip() if self.player_stats_table.item(row, 5) else ""
            sub_out_text = self.player_stats_table.item(row, 6).text().strip() if self.player_stats_table.item(row, 6) else ""
            yellow_cards = int(self.player_stats_table.item(row, 10).text()) if self.player_stats_table.item(row, 10) else 0
            red_cards = int(self.player_stats_table.item(row, 11).text()) if self.player_stats_table.item(row, 11) else 0
            injury_text = self.player_stats_table.item(row, 12).text().strip() if self.player_stats_table.item(row, 12) else ""

            # Convert text to appropriate values
            sub_in = sub_in_text if sub_in_text else None
            sub_out = sub_out_text if sub_out_text else None
            injury_minute = injury_text if injury_text else None

            # Set events data
            events_widget.set_events(goals, assists, sub_in, sub_out, yellow_cards, red_cards, injury_minute)
            self.player_stats_table.setCellWidget(row, events_col, events_widget)
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

        # Sort the table to ensure proper order
        self._sort_player_stats_table()

        # Find and select the newly added player after sorting
        for i in range(self.player_stats_table.rowCount()):
            item = self.player_stats_table.item(i, 0)
            if item and item.data(Qt.ItemDataRole.UserRole) == player_id:
                self.player_stats_table.selectRow(i)
                break

    def _on_remove_player_clicked(self):
        """Remove selected players from the stats table."""
        selected_rows = set()
        for item in self.player_stats_table.selectedItems():
            selected_rows.add(item.row())

        # Remove rows in reverse order to avoid index issues
        for row in sorted(selected_rows, reverse=True):
            self.player_stats_table.removeRow(row)

        # Update button state
        self.remove_player_btn.setEnabled(False)

    def _on_player_selection_changed(self):
        """Handle player selection changes in the stats table."""
        # Enable remove button if any rows are selected
        has_selection = len(self.player_stats_table.selectedIndexes()) > 0
        self.remove_player_btn.setEnabled(has_selection)

    def _update_player_events_widget(self, row):
        """Update the Events widget for a specific player row."""
        events_col = self.player_stats_table.columnCount() - 1
        events_widget = self.player_stats_table.cellWidget(row, events_col)

        if events_widget and hasattr(events_widget, 'set_events'):
            # Get current player data from table items
            goals = int(self.player_stats_table.item(row, 8).text()) if self.player_stats_table.item(row, 8) else 0
            assists = int(self.player_stats_table.item(row, 9).text()) if self.player_stats_table.item(row, 9) else 0
            sub_in_text = self.player_stats_table.item(row, 5).text().strip() if self.player_stats_table.item(row, 5) else ""
            sub_out_text = self.player_stats_table.item(row, 6).text().strip() if self.player_stats_table.item(row, 6) else ""
            yellow_cards = int(self.player_stats_table.item(row, 10).text()) if self.player_stats_table.item(row, 10) else 0
            red_cards = int(self.player_stats_table.item(row, 11).text()) if self.player_stats_table.item(row, 11) else 0
            injury_text = self.player_stats_table.item(row, 12).text().strip() if self.player_stats_table.item(row, 12) else ""

            # Convert text to appropriate values
            sub_in = sub_in_text if sub_in_text else None
            sub_out = sub_out_text if sub_out_text else None
            injury_minute = injury_text if injury_text else None

            # Update events widget
            events_widget.set_events(goals, assists, sub_in, sub_out, yellow_cards, red_cards, injury_minute)

    def _show_player_stats_context_menu(self, pos):
        """Show context menu for player stats table."""
        # Create context menu
        menu = QMenu(self)

        # Get the item at the clicked position
        item = self.player_stats_table.itemAt(pos)
        if item:
            column = item.column()
            row = item.row()

            # If clicked on Goals or Assists column, show special menu
            if column == 8 or column == 9:  # Goals or Assists column
                field_name = "Goals" if column == 8 else "Assists"

                # Add value options
                for value in ["1", "2", "3", "4", "5"]:
                    action = menu.addAction(value)
                    action.setData((row, column, value))
                    action.triggered.connect(self._set_stat_value)

                menu.addSeparator()

                # Add increment/decrement options
                increment_action = menu.addAction(self.tr("+ Increment"))
                increment_action.setData((row, column, "+"))
                increment_action.triggered.connect(self._set_stat_value)

                decrement_action = menu.addAction(self.tr("- Decrement"))
                decrement_action.setData((row, column, "-"))
                decrement_action.triggered.connect(self._set_stat_value)

                menu.addSeparator()

                # Add clear option
                clear_action = menu.addAction(self.tr("Clear (set to 0)"))
                clear_action.setData((row, column, "0"))
                clear_action.triggered.connect(self._set_stat_value)

            # If clicked on Yellow Cards column, show special menu
            elif column == 10:  # Yellow Cards column
                # Add value options with descriptive text
                no_yellow_action = menu.addAction(self.tr("No Yellow Cards"))
                no_yellow_action.setData((row, column, "0"))
                no_yellow_action.triggered.connect(self._set_stat_value)

                one_yellow_action = menu.addAction(self.tr("1 Yellow Card"))
                one_yellow_action.setData((row, column, "1"))
                one_yellow_action.triggered.connect(self._set_stat_value)

                two_yellow_action = menu.addAction(self.tr("2 Yellow Cards"))
                two_yellow_action.setData((row, column, "2"))
                two_yellow_action.triggered.connect(self._set_stat_value)

            # If clicked on Red Cards column, show special menu
            elif column == 11:  # Red Cards column
                # Add value options with descriptive text
                no_red_action = menu.addAction(self.tr("No Red Card"))
                no_red_action.setData((row, column, "0"))
                no_red_action.triggered.connect(self._set_stat_value)

                red_action = menu.addAction(self.tr("Red Card"))
                red_action.setData((row, column, "1"))
                red_action.triggered.connect(self._set_stat_value)

            # If clicked on Injury column, show special menu
            elif column == 12:  # Injury column
                # Get the current injury minute
                injury_minute = item.text().strip()

                # If there's an injury minute, add option to edit description
                if injury_minute:
                    edit_desc_action = menu.addAction(self.tr("Edit Injury Description"))
                    edit_desc_action.triggered.connect(lambda: self._show_injury_description_dialog(row))

                    menu.addSeparator()

                    clear_action = menu.addAction(self.tr("Clear Injury"))
                    clear_action.triggered.connect(lambda: self._clear_injury(row))
                else:
                    # If no injury minute, show message
                    menu.addAction(self.tr("Enter injury minute first")).setEnabled(False)

            # Show menu at cursor position
            menu.exec(self.player_stats_table.viewport().mapToGlobal(pos))
        else:
            # No item at position - might be header or empty area
            # We don't show any menu for empty areas
            pass

    def _show_header_context_menu(self, pos):
        """Show context menu for player stats table header."""
        # Create context menu
        menu = QMenu(self)

        # Add column visibility options directly to the menu
        menu.addSection(self.tr("Show/Hide Columns"))

        # Add group for basic stats
        basic_stats_menu = menu.addMenu(self.tr("Basic Stats"))
        basic_stats_columns = {
            "minutes_played", "events"
        }

        # Add group for attacking stats
        attacking_stats_menu = menu.addMenu(self.tr("Attacking Stats"))
        attacking_stats_columns = {
            "attempts", "on_target", "xgoals", "goal_creating_actions",
            "take_ons_attempted", "take_ons_successful", "crosses"
        }

        # Add group for passing stats
        passing_stats_menu = menu.addMenu(self.tr("Passing Stats"))
        passing_stats_columns = {
            "passes_attempted", "passes_completed", "passes_progressive",
            "carries_progressive"
        }

        # Add group for defensive stats
        defensive_stats_menu = menu.addMenu(self.tr("Defensive Stats"))
        defensive_stats_columns = {
            "tackles", "tackles_won", "interceptions", "shot_blocks",
            "pass_blocks", "clearances", "error_opp_shot"
        }

        # Add group for physical stats
        physical_stats_menu = menu.addMenu(self.tr("Physical Stats"))
        physical_stats_columns = {
            "touches", "aerial_won", "aerial_lost", "fouls_committed",
            "fouls_suffered", "offsides", "distance"
        }

        # Add group for performance stats
        performance_menu = menu.addMenu(self.tr("Performance"))
        performance_columns = {
            "work_rate",
            "focus_discipline",
            "coachability",
            "positioning",
            "tactical_discipline",
            "decision_making",
            "passing_quality",
            "ball_control",
            "dribbling",
            "shooting_finishing",
            "crossing_distribution",
            "marking_tracking",
            "tackling_eval",
            "pressure_pressing",
            "recovery_runs",
            "composure_pressure",
            "leadership_communication",
            "confidence",
            "speed_agility",
            "strength_eval",
            "stamina_fitness",
            "role_understanding",
            "overall_performance"
        }

        # Add columns to their respective menus
        for i, col in enumerate(self.player_stats_columns):
            if col.get("hideable", True):
                action = QAction(col["name"], self)
                action.setCheckable(True)
                action.setChecked(not self.player_stats_table.isColumnHidden(i))
                action.setData(i)
                action.triggered.connect(self._toggle_player_stats_column)

                # Add to appropriate submenu
                if col["key"] in basic_stats_columns:
                    basic_stats_menu.addAction(action)
                elif col["key"] in attacking_stats_columns:
                    attacking_stats_menu.addAction(action)
                elif col["key"] in passing_stats_columns:
                    passing_stats_menu.addAction(action)
                elif col["key"] in defensive_stats_columns:
                    defensive_stats_menu.addAction(action)
                elif col["key"] in physical_stats_columns:
                    physical_stats_menu.addAction(action)
                elif col["key"] in performance_columns:
                    performance_menu.addAction(action)

        # --- Add group toggle actions ---
        def group_all_visible(group_keys):
            for i, col in enumerate(self.player_stats_columns):
                if col["key"] in group_keys and col.get("hideable", True):
                    if self.player_stats_table.isColumnHidden(i):
                        return False
            return True

        def toggle_group_columns(group_keys, show):
            header = self.player_stats_table.horizontalHeader()
            header.blockSignals(True)
            try:
                for i, col in enumerate(self.player_stats_columns):
                    if col["key"] in group_keys and col.get("hideable", True):
                        self.player_stats_table.setColumnHidden(i, not show)
            finally:
                header.blockSignals(False)
                self._save_player_stats_header_state()

        menu.addSeparator()
        # Basic
        basic_visible = group_all_visible(basic_stats_columns)
        basic_label = self.tr("Hide all basic stats") if basic_visible else self.tr("Show all basic stats")
        show_basic_action = QAction(basic_label, self)
        show_basic_action.triggered.connect(lambda: toggle_group_columns(basic_stats_columns, not basic_visible))
        menu.addAction(show_basic_action)
        # Attacking
        attacking_visible = group_all_visible(attacking_stats_columns)
        attacking_label = self.tr("Hide all attacking stats") if attacking_visible else self.tr("Show all attacking stats")
        show_attacking_action = QAction(attacking_label, self)
        show_attacking_action.triggered.connect(lambda: toggle_group_columns(attacking_stats_columns, not attacking_visible))
        menu.addAction(show_attacking_action)
        # Defensive
        defensive_visible = group_all_visible(defensive_stats_columns)
        defensive_label = self.tr("Hide all defensive stats") if defensive_visible else self.tr("Show all defensive stats")
        show_defensive_action = QAction(defensive_label, self)
        show_defensive_action.triggered.connect(lambda: toggle_group_columns(defensive_stats_columns, not defensive_visible))
        menu.addAction(show_defensive_action)
        # Physical
        physical_visible = group_all_visible(physical_stats_columns)
        physical_label = self.tr("Hide all physical stats") if physical_visible else self.tr("Show all physical stats")
        show_physical_action = QAction(physical_label, self)
        show_physical_action.triggered.connect(lambda: toggle_group_columns(physical_stats_columns, not physical_visible))
        menu.addAction(show_physical_action)
        # Performance
        performance_visible = group_all_visible(performance_columns)
        performance_label = self.tr("Hide all performance stats") if performance_visible else self.tr("Show all performance stats")
        show_performance_action = QAction(performance_label, self)
        show_performance_action.triggered.connect(lambda: toggle_group_columns(performance_columns, not performance_visible))
        menu.addAction(show_performance_action)
        # --- End group toggle actions ---

        # Add separator and group actions
        menu.addSeparator()

        # Add action to show/hide all stats columns
        all_stats_visible = True
        for i in range(14, len(self.player_stats_columns)):  # Stats columns start at index 14
            if self.player_stats_table.isColumnHidden(i):
                all_stats_visible = False
                break

        show_all_stats_action = QAction(self.tr("Show All Stats Columns"), self, checkable=True)
        show_all_stats_action.setChecked(all_stats_visible)
        show_all_stats_action.toggled.connect(self._toggle_all_stats_columns)
        menu.addAction(show_all_stats_action)

        # Show menu at cursor position
        header = self.player_stats_table.horizontalHeader()
        menu.exec(header.mapToGlobal(pos))

    def _validate_on_target(self, item):
        """Validate that on-target shots are not greater than total attempts."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            row = item.row()

            # Check if the player has played
            has_played = self._validate_player_has_played(row)

            # Get the current on-target value
            on_target_value = 0
            try:
                on_target_value = int(item.text().strip() or "0")
            except ValueError:
                on_target_value = 0
                item.setText("0")

            # If player hasn't played but trying to set a value > 0
            if on_target_value > 0 and not has_played:
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("On Target shots can only be set for players who have played in the match (minutes > 0).")
                )
                item.setText("0")
                return

            # Get the attempts value
            attempts_item = self.player_stats_table.item(row, 14)
            attempts_value = 0
            if attempts_item:
                try:
                    attempts_value = int(attempts_item.text().strip() or "0")
                except ValueError:
                    attempts_value = 0

            # Validate: on-target cannot be greater than attempts
            if on_target_value > attempts_value:
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("On-target shots cannot be greater than total attempts.")
                )
                # Set on-target equal to attempts
                item.setText(str(attempts_value))
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _validate_player_has_played(self, row):
        """Check if a player has played in the match (minutes > 0)."""
        minutes_item = self.player_stats_table.item(row, 7)  # Minutes played column
        if minutes_item:
            try:
                minutes = int(minutes_item.text().strip() or "0")
                return minutes > 0
            except ValueError:
                return False
        return False

    def _validate_passes_completed(self, item):
        """Validate that completed passes are not greater than total passes attempted."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            row = item.row()

            # Check if the player has played
            has_played = self._validate_player_has_played(row)

            # Get the current completed passes value
            completed_value = 0
            try:
                completed_value = int(item.text().strip() or "0")
            except ValueError:
                completed_value = 0
                item.setText("0")

            # If player hasn't played but trying to set a value > 0
            if completed_value > 0 and not has_played:
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("Passes Completed can only be set for players who have played in the match (minutes > 0).")
                )
                item.setText("0")
                return

            # Get the passes attempted value
            attempted_item = self.player_stats_table.item(row, 26)
            attempted_value = 0
            if attempted_item:
                try:
                    attempted_value = int(attempted_item.text().strip() or "0")
                except ValueError:
                    attempted_value = 0

            # Validate: completed passes cannot be greater than attempted passes
            if completed_value > attempted_value:
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("Completed passes cannot be greater than total passes attempted.")
                )
                # Set completed passes equal to attempted passes
                item.setText(str(attempted_value))
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _toggle_all_stats_columns(self, show):
        """Show or hide all stats columns."""
        # Stats columns start at index 14
        for i in range(14, len(self.player_stats_columns)):
            self.player_stats_table.setColumnHidden(i, not show)
        self._save_player_stats_header_state()

    def _toggle_player_stats_column(self):
        """Toggle visibility of a player stats column."""
        action = self.sender()
        if action:
            column = action.data()
            is_visible = action.isChecked()
            self.player_stats_table.setColumnHidden(column, not is_visible)
            self._save_player_stats_header_state()

    def _sort_player_stats_table(self):
        """Sort the player stats table to keep lineup players at the top, then by position (GK, DF, MF, AT)."""
        # Save current selection
        selected_player_ids = set()
        for item in self.player_stats_table.selectedItems():
            row = item.row()
            lineup_item = self.player_stats_table.item(row, 0)
            if lineup_item:
                player_id = lineup_item.data(Qt.ItemDataRole.UserRole)
                if player_id:
                    selected_player_ids.add(player_id)

        # Collect all player data
        players_data = []

        for row in range(self.player_stats_table.rowCount()):
            # Get lineup item (column 0)
            lineup_item = self.player_stats_table.item(row, 0)
            if not lineup_item:
                continue

            # Get player ID
            player_id = lineup_item.data(Qt.ItemDataRole.UserRole)
            if not player_id:
                continue

            # Check if player is in lineup
            is_in_lineup = lineup_item.checkState() == Qt.CheckState.Checked

            # Get player name (column 2)
            name_item = self.player_stats_table.item(row, 2)
            player_name = name_item.text() if name_item else ""

            # Get player position (column 3)
            pos_item = self.player_stats_table.item(row, 3)
            position = pos_item.text() if pos_item else ""

            # Define position order for sorting
            position_order = {"GK": 0, "DF": 1, "MF": 2, "AT": 3}
            pos_sort_value = position_order.get(position, 4)  # Default to 4 for unknown positions

            # Get other data
            player_data = {
                "player_id": player_id,
                "lineup": is_in_lineup,
                "name": player_name,
                "position": position,
                "position_order": pos_sort_value,
                "row": row
            }

            # Get data for all columns
            for col in range(self.player_stats_table.columnCount()):
                item = self.player_stats_table.item(row, col)
                if item:
                    player_data[f"col_{col}_text"] = item.text()
                    player_data[f"col_{col}_flags"] = item.flags()
                    if col == 0 or col == 4:  # For lineup and captain columns, store checkbox state
                        player_data[f"col_{col}_checked"] = (item.checkState() == Qt.CheckState.Checked)

            players_data.append(player_data)

        # Sort players: lineup players first, then by position order, then by name
        players_data.sort(key=lambda x: (
            not x.get("lineup", False),  # First by lineup (True values first)
            x.get("position_order", 4),  # Then by position order (GK, DF, MF, AT, other)
            x.get("name", "")            # Then by name
        ))

        # Block signals during table update
        self.player_stats_table.blockSignals(True)

        # Clear and rebuild table
        self.player_stats_table.setRowCount(0)

        # Add sorted players back to table
        for player_data in players_data:
            row = self.player_stats_table.rowCount()
            self.player_stats_table.insertRow(row)

            # Add data for each column
            for col in range(self.player_stats_table.columnCount()):
                if col == self.player_stats_table.columnCount() - 1:  # Events column (last column)
                    # Create Events widget
                    from app.widgets.player_events_widget import PlayerEventsLabel
                    events_widget = PlayerEventsLabel()

                    # Get player data for events from the stored data
                    goals = int(player_data.get(f"col_8_text", "0")) if player_data.get(f"col_8_text") else 0
                    assists = int(player_data.get(f"col_9_text", "0")) if player_data.get(f"col_9_text") else 0
                    sub_in_text = player_data.get(f"col_5_text", "").strip()
                    sub_out_text = player_data.get(f"col_6_text", "").strip()
                    yellow_cards = int(player_data.get(f"col_10_text", "0")) if player_data.get(f"col_10_text") else 0
                    red_cards = int(player_data.get(f"col_11_text", "0")) if player_data.get(f"col_11_text") else 0
                    injury_text = player_data.get(f"col_12_text", "").strip()

                    # Convert text to appropriate values
                    sub_in = sub_in_text if sub_in_text else None
                    sub_out = sub_out_text if sub_out_text else None
                    injury_minute = injury_text if injury_text else None

                    # Set events data
                    events_widget.set_events(goals, assists, sub_in, sub_out, yellow_cards, red_cards, injury_minute)
                    self.player_stats_table.setCellWidget(row, col, events_widget)
                elif col == 0:  # Lineup column (checkbox)
                    item = QTableWidgetItem()
                    item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
                    item.setCheckState(Qt.CheckState.Checked if player_data.get("lineup", False) else Qt.CheckState.Unchecked)
                    item.setData(Qt.ItemDataRole.UserRole, player_data.get("player_id"))
                    self.player_stats_table.setItem(row, col, item)
                elif col == 4:  # Captain column (checkbox)
                    item = QTableWidgetItem()
                    item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
                    item.setCheckState(Qt.CheckState.Checked if player_data.get(f"col_{col}_checked", False) else Qt.CheckState.Unchecked)
                    # Set text to empty string to ensure checkbox is displayed
                    item.setText("")
                    self.player_stats_table.setItem(row, col, item)
                else:
                    item = QTableWidgetItem(player_data.get(f"col_{col}_text", ""))
                    if f"col_{col}_flags" in player_data:
                        item.setFlags(player_data.get(f"col_{col}_flags"))

                    # Center align all columns except player name (column 2)
                    if col != 2:
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                    self.player_stats_table.setItem(row, col, item)

        # Restore selection
        if selected_player_ids:
            self.player_stats_table.clearSelection()
            for row in range(self.player_stats_table.rowCount()):
                item = self.player_stats_table.item(row, 0)
                if item and item.data(Qt.ItemDataRole.UserRole) in selected_player_ids:
                    # Select the first cell in the row instead of the entire row
                    self.player_stats_table.setCurrentItem(item)

        # Re-enable signals
        self.player_stats_table.blockSignals(False)

        # Force a visual update
        self.player_stats_table.viewport().update()

    def _on_player_stats_item_changed(self, item):
        """Handle changes to player stats table items."""
        # Get the column key
        col_key = self.player_stats_columns[item.column()]["key"]

        # Check if the column is a performance evaluation column
        performance_columns = {
            "work_rate",
            "focus_discipline",
            "coachability",
            "positioning",
            "tactical_discipline",
            "decision_making",
            "passing_quality",
            "ball_control",
            "dribbling",
            "shooting_finishing",
            "crossing_distribution",
            "marking_tracking",
            "tackling_eval",
            "pressure_pressing",
            "recovery_runs",
            "composure_pressure",
            "leadership_communication",
            "confidence",
            "speed_agility",
            "strength_eval",
            "stamina_fitness",
            "role_understanding",
            "overall_performance"
        }

        if col_key in performance_columns:
            self._validate_performance_field_change(item, self.player_stats_columns[item.column()]["name"])
            return

        # Handle other column changes as before
        if col_key == "on_target":
            self._validate_on_target(item)
        elif col_key == "passes_completed":
            self._validate_passes_completed(item)
        elif col_key == "yellow_cards":
            self._handle_yellow_card_change(item)
        elif col_key == "red_cards":
            self._handle_red_card_change(item)
        elif col_key == "captain":
            self._handle_captain_checkbox_change(item)
        elif col_key == "lineup":
            self._handle_lineup_checkbox_change(item)
        elif col_key == "sub_in":
            self._handle_sub_in_change(item)
        elif col_key == "sub_out":
            self._handle_sub_out_change(item)
        elif col_key == "injury_minute":
            self._handle_injury_minute_change(item)
        elif col_key in ["minutes_played", "goals", "assists", "attempts", "touches", "tackles", "tackles_won", "interceptions", "shot_blocks", "pass_blocks", "clearances", "error_opp_shot", "xgoals", "goal_creating_actions", "passes_attempted", "passes_progressive", "carries_progressive", "take_ons_attempted", "take_ons_successful", "crosses", "aerial_won", "aerial_lost", "fouls_committed", "fouls_suffered", "offsides", "distance"]:
            self._handle_integer_field_change(item, col_key)

    def _handle_yellow_card_change(self, item):
        """Handle changes to the yellow cards field."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        row = item.row()

        try:
            # Get the current value
            value = item.text().strip()
            if not value:
                value = "0"

            # Ensure it's a valid integer (0, 1, or 2)
            try:
                int_value = int(value)
                if int_value < 0:
                    int_value = 0
                elif int_value > 2:
                    int_value = 2
                item.setText(str(int_value))
            except ValueError:
                item.setText("0")
                int_value = 0

            # If yellow cards is 2, set red card to 1 (if not already set manually)
            if int_value == 2:
                red_card_item = self.player_stats_table.item(row, 11)
                if red_card_item:
                    # Check if red card was set manually or by yellow cards
                    if not hasattr(red_card_item, 'manual_red_card') or not red_card_item.manual_red_card:
                        red_card_item.setText("1")
                        # Mark that this red card was set by yellow cards
                        red_card_item.yellow_card_red = True
            else:
                # If yellow cards is less than 2, check if red card was set by yellow cards
                red_card_item = self.player_stats_table.item(row, 11)
                if red_card_item and hasattr(red_card_item, 'yellow_card_red') and red_card_item.yellow_card_red:
                    # Clear the red card that was set by yellow cards
                    red_card_item.setText("0")
                    red_card_item.yellow_card_red = False
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _clear_injury(self, row):
        """Clear injury minute and description for a player."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            # Clear injury minute
            injury_item = self.player_stats_table.item(row, 12)
            if injury_item:
                injury_item.setText("")

            # Clear injury description
            injury_desc_item = self.player_stats_table.item(row, 13)
            if injury_desc_item:
                injury_desc_item.setText("")
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _show_injury_description_dialog(self, row):
        """Show a dialog to enter injury description."""
        # Get the player name
        player_item = self.player_stats_table.item(row, 2)
        player_name = player_item.text() if player_item else "Player"

        # Get the current injury description
        injury_desc_item = self.player_stats_table.item(row, 13)
        current_description = injury_desc_item.text() if injury_desc_item else ""

        # Create a dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Injury Description"))
        dialog.setMinimumWidth(400)

        # Create layout
        layout = QVBoxLayout(dialog)

        # Add a label
        label = QLabel(self.tr("Enter injury description for {}:").format(player_name))
        layout.addWidget(label)

        # Add a text field
        text_edit = QLineEdit(dialog)
        text_edit.setText(current_description)
        text_edit.setPlaceholderText(self.tr("e.g., Broken knee, Ankle sprain, etc."))
        layout.addWidget(text_edit)

        # Add buttons
        button_layout = QHBoxLayout()
        ok_button = QPushButton(self.tr("OK"), dialog)
        cancel_button = QPushButton(self.tr("Cancel"), dialog)
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        # Connect buttons
        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)

        # Show the dialog
        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            # Update the injury description
            description = text_edit.text().strip()
            if injury_desc_item:
                injury_desc_item.setText(description)
            else:
                injury_desc_item = QTableWidgetItem(description)
                self.player_stats_table.setItem(row, 13, injury_desc_item)

    def _handle_injury_minute_change(self, item):
        """Handle changes to the injury minute field."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        row = item.row()
        text = item.text().strip()

        # Get match duration for validation
        match_duration = self._get_total_match_duration()

        # We don't need minutes played for validation, so we'll skip getting it

        # Get lineup status
        lineup_item = self.player_stats_table.item(row, 0)
        is_in_lineup = lineup_item and lineup_item.checkState() == Qt.CheckState.Checked

        # Get Sub In value
        sub_in_value = 0
        sub_in_item = self.player_stats_table.item(row, 5)
        if sub_in_item and sub_in_item.text().strip():
            try:
                sub_in_value = int(sub_in_item.text())
            except ValueError:
                sub_in_value = 0

        # Get Sub Out value
        sub_out_value = match_duration
        sub_out_item = self.player_stats_table.item(row, 6)
        if sub_out_item and sub_out_item.text().strip():
            try:
                sub_out_value = int(sub_out_item.text())
            except ValueError:
                sub_out_value = match_duration

        if text:
            try:
                # Try to convert to integer
                value = int(text)

                # Special case: 0 can be used to indicate pre-match injury (warm-up)
                if value == 0:
                    # This is valid - injury during warm-up
                    # Re-enable signals before showing dialog
                    self.player_stats_table.blockSignals(False)
                    # Show dialog to enter injury description
                    self._show_injury_description_dialog(row)
                    # Block signals again to continue processing
                    self.player_stats_table.blockSignals(True)
                # For players who played (either started or subbed in)
                elif is_in_lineup or sub_in_value > 0:
                    # Injury minute must be within the time the player was on the field
                    start_minute = 0 if is_in_lineup else sub_in_value
                    end_minute = sub_out_value if sub_out_item and sub_out_item.text().strip() else match_duration

                    if value < start_minute or value > end_minute:
                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Injury Time"),
                            self.tr("The injury minute must be within the time the player was on the field ({0} to {1}).").format(start_minute, end_minute)
                        )
                        item.setText("")
                    elif value > match_duration:
                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Injury Time"),
                            self.tr("The injury minute cannot exceed the match duration ({0} minutes).").format(match_duration)
                        )
                        item.setText("")
                    else:
                        # Valid injury time - show dialog to enter description
                        # Re-enable signals before showing dialog
                        self.player_stats_table.blockSignals(False)
                        # Show dialog to enter injury description
                        self._show_injury_description_dialog(row)
                        # Block signals again to continue processing
                        self.player_stats_table.blockSignals(True)
                # For players who didn't play
                else:
                    # For players who didn't play, only allow 0 (warm-up injury) or empty
                    if value != 0:
                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Injury Time"),
                            self.tr("For players who didn't play, only 0 (warm-up injury) is allowed.")
                        )
                        item.setText("")
            except ValueError:
                # Not a valid integer
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("Injury minute must be a number.")
                )
                item.setText("")

        # Re-enable signals
        self.player_stats_table.blockSignals(False)

    def _handle_red_card_change(self, item):
        """Handle changes to the red cards field."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        row = item.row()

        try:
            # Check if yellow cards is 2 (which forces red card to be 1)
            yellow_card_item = self.player_stats_table.item(row, 10)
            yellow_card_value = 0
            if yellow_card_item and yellow_card_item.text().strip():
                try:
                    yellow_card_value = int(yellow_card_item.text())
                except ValueError:
                    yellow_card_value = 0

            # Get the current value
            value = item.text().strip()
            if not value:
                value = "0"

            # Ensure it's a valid integer (0 or 1)
            try:
                int_value = int(value)

                # If yellow cards is 2, force red card to be 1
                if yellow_card_value == 2 and int_value == 0:
                    int_value = 1
                    # Show a message to the user
                    QMessageBox.information(
                        self,
                        self.tr("Red Card Required"),
                        self.tr("A player with 2 yellow cards must have a red card. The red card has been kept.")
                    )

                if int_value < 0:
                    int_value = 0
                elif int_value > 1:
                    int_value = 1

                item.setText(str(int_value))

                # If set to 1, mark as manually set (unless it's due to yellow cards)
                if int_value == 1:
                    if yellow_card_value == 2:
                        item.yellow_card_red = True
                        if hasattr(item, 'manual_red_card'):
                            delattr(item, 'manual_red_card')
                    else:
                        item.manual_red_card = True
                        if hasattr(item, 'yellow_card_red'):
                            delattr(item, 'yellow_card_red')
                else:
                    # If cleared, remove the manual flag
                    if hasattr(item, 'manual_red_card'):
                        delattr(item, 'manual_red_card')
                    if hasattr(item, 'yellow_card_red'):
                        delattr(item, 'yellow_card_red')
            except ValueError:
                item.setText("0")
                if hasattr(item, 'manual_red_card'):
                    delattr(item, 'manual_red_card')
                if hasattr(item, 'yellow_card_red'):
                    delattr(item, 'yellow_card_red')
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _handle_captain_checkbox_change(self, item):
        """Handle changes to the captain checkbox."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        row = item.row()
        is_checked = item.checkState() == Qt.CheckState.Checked

        # If this player is being set as captain, uncheck any other captain
        if is_checked:
            for i in range(self.player_stats_table.rowCount()):
                if i != row:  # Skip the current row
                    captain_item = self.player_stats_table.item(i, 4)
                    if captain_item and captain_item.checkState() == Qt.CheckState.Checked:
                        captain_item.setCheckState(Qt.CheckState.Unchecked)

        # Re-enable signals
        self.player_stats_table.blockSignals(False)

    def _handle_lineup_checkbox_change(self, item):
        """Handle changes to the lineup checkbox."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        row = item.row()
        is_checked = item.checkState() == Qt.CheckState.Checked

        # Store the previous state for potential revert
        previous_state = {
            'lineup': not is_checked,  # The previous state is the opposite of current
            'sub_in': '',
            'sub_out': '',
            'minutes': 0
        }

        # Get current minutes before updating
        current_minutes = 0
        minutes_item = self.player_stats_table.item(row, 7)
        if minutes_item and minutes_item.text().strip():
            try:
                current_minutes = int(minutes_item.text().strip())
                previous_state['minutes'] = current_minutes
            except ValueError:
                current_minutes = 0

        # Get Sub In/Out values
        sub_in_item = self.player_stats_table.item(row, 5)
        sub_out_item = self.player_stats_table.item(row, 6)

        if sub_in_item and sub_in_item.text().strip():
            previous_state['sub_in'] = sub_in_item.text().strip()

        if sub_out_item and sub_out_item.text().strip():
            previous_state['sub_out'] = sub_out_item.text().strip()

        # Store the previous state in the row's data
        name_item = self.player_stats_table.item(row, 2)
        if name_item:
            name_item.setData(Qt.ItemDataRole.UserRole, previous_state)

        # Remember player ID to reselect after sorting
        player_id = None
        player_id_item = self.player_stats_table.item(row, 0)
        if player_id_item:
            player_id = player_id_item.data(Qt.ItemDataRole.UserRole)

        # Get current lineup count
        lineup_count = 0
        for i in range(self.player_stats_table.rowCount()):
            if i == row:
                continue  # Skip the current row

            lineup_item = self.player_stats_table.item(i, 0)
            if lineup_item and lineup_item.checkState() == Qt.CheckState.Checked:
                lineup_count += 1

        # Get max players for this competition
        max_players = self._get_competition_x_a_side()

        # If trying to check and already at max, prevent it
        if is_checked and lineup_count >= max_players:
            # Show message
            QMessageBox.warning(
                self,
                self.tr("Lineup Full"),
                self.tr("You already have {0} players in the lineup. "
                        "To add this player, first remove another player from the lineup.")
                .format(max_players)
            )

            # Uncheck the box
            item.setCheckState(Qt.CheckState.Unchecked)
            is_checked = False  # Update the local variable to match the new state

        # Update Sub In/Out fields based on lineup status
        sub_in_item = self.player_stats_table.item(row, 5)
        sub_out_item = self.player_stats_table.item(row, 6)

        if is_checked:
            # Player is in lineup - disable Sub In, enable Sub Out
            if sub_in_item:
                sub_in_item.setText("")
                sub_in_item.setFlags(sub_in_item.flags() & ~Qt.ItemFlag.ItemIsEnabled & ~Qt.ItemFlag.ItemIsEditable)

            if sub_out_item:
                sub_out_item.setFlags(sub_out_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)
        else:
            # Player is not in lineup - enable Sub In, disable Sub Out unless Sub In has a value
            if sub_in_item:
                sub_in_item.setFlags(sub_in_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)

            if sub_out_item:
                has_sub_in = sub_in_item and sub_in_item.text().strip()
                if has_sub_in:
                    sub_out_item.setFlags(sub_out_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)
                else:
                    sub_out_item.setText("")
                    sub_out_item.setFlags(sub_out_item.flags() & ~Qt.ItemFlag.ItemIsEnabled & ~Qt.ItemFlag.ItemIsEditable)

        # Calculate new minutes played
        new_minutes = self._calculate_minutes_played(row)

        # Check if minutes are changing from > 0 to 0
        if current_minutes > 0 and new_minutes == 0:
            # Player had minutes before but now has 0 minutes
            self._handle_minutes_reduced_to_zero(row, current_minutes)

        # Update minutes played for this player
        self._update_minutes_played(row)

        # Re-enable signals
        self.player_stats_table.blockSignals(False)

        # Sort table to keep lineup players at the top
        self._sort_player_stats_table()

        # After sorting, we need to update the Sub In/Out fields again
        # because the sort operation rebuilds the table and loses the flag settings
        self._update_player_stats_fields()

        # Reselect the player after sorting
        if player_id:
            for i in range(self.player_stats_table.rowCount()):
                item = self.player_stats_table.item(i, 0)
                if item and item.data(Qt.ItemDataRole.UserRole) == player_id:
                    # Select the first cell in the row instead of the entire row
                    self.player_stats_table.setCurrentItem(item)
                    break

    def _count_players_on_field_at_minute(self, minute, exclude_row=None):
        """Count the number of players on the field at a specific minute.

        Args:
            minute (int): The minute to check
            exclude_row (int, optional): Row to exclude from the count (for validation)

        Returns:
            int: Number of players on the field at the specified minute
        """
        count = 0

        for row in range(self.player_stats_table.rowCount()):
            if exclude_row is not None and row == exclude_row:
                continue

            # Get lineup status
            lineup_item = self.player_stats_table.item(row, 0)
            if not lineup_item:
                continue

            is_in_lineup = lineup_item.checkState() == Qt.CheckState.Checked

            # Get Sub In and Sub Out values
            sub_in_item = self.player_stats_table.item(row, 5)
            sub_out_item = self.player_stats_table.item(row, 6)

            sub_in_value = None
            if sub_in_item and sub_in_item.text().strip():
                try:
                    sub_in_value = int(sub_in_item.text())
                except ValueError:
                    sub_in_value = None

            sub_out_value = None
            if sub_out_item and sub_out_item.text().strip():
                try:
                    sub_out_value = int(sub_out_item.text())
                except ValueError:
                    sub_out_value = None

            # Check if player is on the field at the specified minute
            if is_in_lineup:
                # Player started in the lineup
                if sub_out_value is None:
                    # Player never subbed out, on field for the whole match
                    is_on_field = True
                else:
                    # Player subbed out at some point
                    is_on_field = minute < sub_out_value
            else:
                # Player started on the bench
                if sub_in_value is None:
                    # Player never subbed in
                    is_on_field = False
                else:
                    # Player subbed in at some point
                    is_on_field = minute >= sub_in_value

                    # Check if player was later subbed out
                    if sub_out_value is not None and minute >= sub_out_value:
                        is_on_field = False

            if is_on_field:
                count += 1

        return count

    def _validate_substitution_rules(self, row, sub_in_value=None, sub_out_value=None):
        """Validate substitution rules.

        Args:
            row (int): The row being edited
            sub_in_value (int, optional): The Sub In value being set
            sub_out_value (int, optional): The Sub Out value being set

        Returns:
            tuple: (is_valid, error_message)
        """
        # Get max players for this competition
        max_players = self._get_competition_x_a_side()

        # Get lineup status
        lineup_item = self.player_stats_table.item(row, 0)
        if not lineup_item:
            return False, self.tr("Invalid player data.")

        is_in_lineup = lineup_item.checkState() == Qt.CheckState.Checked

        # Get current Sub In and Sub Out values if not provided
        if sub_in_value is None:
            sub_in_item = self.player_stats_table.item(row, 5)
            if sub_in_item and sub_in_item.text().strip():
                try:
                    sub_in_value = int(sub_in_item.text())
                except ValueError:
                    sub_in_value = None

        if sub_out_value is None:
            sub_out_item = self.player_stats_table.item(row, 6)
            if sub_out_item and sub_out_item.text().strip():
                try:
                    sub_out_value = int(sub_out_item.text())
                except ValueError:
                    sub_out_value = None

        # If we're validating a Sub In value
        if sub_in_value is not None and not is_in_lineup:
            # Count players on field at the Sub In minute
            players_on_field = self._count_players_on_field_at_minute(sub_in_value, row)

            # Count players being subbed out at this exact minute
            players_subbed_out_at_minute = 0
            for r in range(self.player_stats_table.rowCount()):
                if r == row:
                    continue

                sub_out_item = self.player_stats_table.item(r, 6)
                if sub_out_item and sub_out_item.text().strip():
                    try:
                        other_sub_out = int(sub_out_item.text())
                        if other_sub_out == sub_in_value:
                            players_subbed_out_at_minute += 1
                    except ValueError:
                        pass

            # If adding this player would exceed the maximum
            if players_on_field - players_subbed_out_at_minute >= max_players:
                return False, self.tr("Cannot substitute in at minute {0}. The maximum number of players ({1}) would be exceeded. A player must be substituted out first.").format(sub_in_value, max_players)

            # Check if we have enough players being subbed out to allow this sub in
            # Count total subs in and subs out up to this minute
            total_subs_in = 0
            total_subs_out = 0

            for r in range(self.player_stats_table.rowCount()):
                if r == row:  # Skip the current row
                    continue

                # Count subs in
                sub_in_item = self.player_stats_table.item(r, 5)
                if sub_in_item and sub_in_item.text().strip():
                    try:
                        other_sub_in = int(sub_in_item.text())
                        if other_sub_in <= sub_in_value:  # Count only subs that happened before or at the same time
                            total_subs_in += 1
                    except ValueError:
                        pass

                # Count subs out
                sub_out_item = self.player_stats_table.item(r, 6)
                if sub_out_item and sub_out_item.text().strip():
                    try:
                        other_sub_out = int(sub_out_item.text())
                        if other_sub_out <= sub_in_value:  # Count only subs that happened before or at the same time
                            total_subs_out += 1
                    except ValueError:
                        pass

            # If we're trying to sub in more players than have been subbed out
            # Note: We add 1 to total_subs_in to account for the current sub in we're validating
            if total_subs_in + 1 > total_subs_out:
                return False, self.tr("Cannot substitute in at minute {0}. The number of players subbed in ({1}) would exceed the number of players subbed out ({2}). A player must be substituted out first.").format(sub_in_value, total_subs_in + 1, total_subs_out)

        return True, ""

    def _handle_sub_in_change(self, item):
        """Handle changes to the Sub In field."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            row = item.row()
            text = item.text().strip()

            # Store the previous state for potential revert
            previous_state = {
                'lineup': False,
                'sub_in': '',
                'sub_out': '',
                'minutes': 0
            }

            # Get lineup status
            lineup_item = self.player_stats_table.item(row, 0)
            if lineup_item:
                previous_state['lineup'] = lineup_item.checkState() == Qt.CheckState.Checked

            # Get current Sub In value (before change)
            previous_sub_in = item.data(Qt.ItemDataRole.UserRole)
            if previous_sub_in:
                previous_state['sub_in'] = previous_sub_in

            # Get current Sub Out value
            sub_out_item = self.player_stats_table.item(row, 6)
            if sub_out_item and sub_out_item.text().strip():
                previous_state['sub_out'] = sub_out_item.text().strip()

            # Get current minutes
            minutes_item = self.player_stats_table.item(row, 7)
            if minutes_item and minutes_item.text().strip():
                try:
                    previous_state['minutes'] = int(minutes_item.text().strip())
                except ValueError:
                    pass

            # Store the previous state in the row's data
            name_item = self.player_stats_table.item(row, 2)
            if name_item:
                name_item.setData(Qt.ItemDataRole.UserRole, previous_state)

            # Store the original value in the item's data for future reference
            item.setData(Qt.ItemDataRole.UserRole, text)

            # Validate input is a number between 1 and match duration
            match_duration = self._get_total_match_duration()

            if text:
                try:
                    value = int(text)
                    if value == 0 or value < 1 or value > match_duration:
                        # Invalid value, reset and show message
                        if value == 0:
                            message = self.tr("Sub In time cannot be zero. It must be at least 1 minute.")
                        else:
                            message = self.tr("Sub In time must be between 1 and {0} minutes.").format(match_duration)

                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Value"),
                            message
                        )
                        item.setText("")
                    else:
                        # Validate substitution rules
                        is_valid, error_message = self._validate_substitution_rules(row, sub_in_value=value)
                        if not is_valid:
                            QMessageBox.warning(
                                self,
                                self.tr("Substitution Rule Violation"),
                                error_message
                            )
                            item.setText("")
                        else:
                            # Valid value, update Sub Out field
                            sub_out_item = self.player_stats_table.item(row, 6)  # Column 6 is Sub Out
                            if sub_out_item:
                                sub_out_item.setFlags(sub_out_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)

                                # If Sub Out has a value, make sure it's strictly greater than Sub In
                                sub_out_text = sub_out_item.text().strip()
                                if sub_out_text:
                                    try:
                                        sub_out_value = int(sub_out_text)
                                        if sub_out_value <= value:  # Changed from <= to < to enforce strictly greater
                                            sub_out_item.setText("")
                                    except ValueError:
                                        sub_out_item.setText("")
                except ValueError:
                    # Not a number, reset
                    item.setText("")
            else:
                # Empty value, disable Sub Out if not in lineup
                lineup_item = self.player_stats_table.item(row, 0)
                is_in_lineup = lineup_item and lineup_item.checkState() == Qt.CheckState.Checked

                sub_out_item = self.player_stats_table.item(row, 6)  # Column 6 is Sub Out
                if sub_out_item and not is_in_lineup:
                    sub_out_item.setText("")
                    sub_out_item.setFlags(sub_out_item.flags() & ~Qt.ItemFlag.ItemIsEnabled & ~Qt.ItemFlag.ItemIsEditable)

            # Get current minutes before updating
            current_minutes = 0
            minutes_item = self.player_stats_table.item(row, 7)
            if minutes_item and minutes_item.text().strip():
                try:
                    current_minutes = int(minutes_item.text().strip())
                except ValueError:
                    current_minutes = 0

            # Calculate new minutes played
            new_minutes = self._calculate_minutes_played(row)

            # Check if minutes are changing from > 0 to 0
            if current_minutes > 0 and new_minutes == 0:
                # Player had minutes before but now has 0 minutes
                self._handle_minutes_reduced_to_zero(row, current_minutes)

            # Update minutes played for this player
            self._update_minutes_played(row)
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _handle_sub_out_change(self, item):
        """Handle changes to the Sub Out field."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            row = item.row()
            text = item.text().strip()

            # Store the previous state for potential revert
            previous_state = {
                'lineup': False,
                'sub_in': '',
                'sub_out': '',
                'minutes': 0
            }

            # Get lineup status
            lineup_item = self.player_stats_table.item(row, 0)
            if lineup_item:
                previous_state['lineup'] = lineup_item.checkState() == Qt.CheckState.Checked

            # Get current Sub In value
            sub_in_item = self.player_stats_table.item(row, 5)
            if sub_in_item and sub_in_item.text().strip():
                previous_state['sub_in'] = sub_in_item.text().strip()

            # Get current Sub Out value (before change)
            previous_sub_out = item.data(Qt.ItemDataRole.UserRole)
            if previous_sub_out:
                previous_state['sub_out'] = previous_sub_out

            # Get current minutes
            minutes_item = self.player_stats_table.item(row, 7)
            if minutes_item and minutes_item.text().strip():
                try:
                    previous_state['minutes'] = int(minutes_item.text().strip())
                except ValueError:
                    pass

            # Store the previous state in the row's data
            name_item = self.player_stats_table.item(row, 2)
            if name_item:
                name_item.setData(Qt.ItemDataRole.UserRole, previous_state)

            # Store the original value in the item's data for future reference
            item.setData(Qt.ItemDataRole.UserRole, text)

            # Get Sub In value or 1 if player is in lineup
            sub_in_item = self.player_stats_table.item(row, 5)  # Column 5 is Sub In
            lineup_item = self.player_stats_table.item(row, 0)

            is_in_lineup = lineup_item and lineup_item.checkState() == Qt.CheckState.Checked

            min_value = 1
            if sub_in_item and sub_in_item.text().strip():
                try:
                    min_value = int(sub_in_item.text())
                except ValueError:
                    pass

            # Validate input is a number between min_value and match duration
            match_duration = self._get_total_match_duration()

            if text:
                try:
                    value = int(text)
                    if value == 0 or (not is_in_lineup and value <= min_value) or value > match_duration:
                        # Invalid value, reset and show message
                        if value == 0:
                            message = self.tr("Sub Out time cannot be zero. It must be at least 1 minute.")
                        elif is_in_lineup:
                            message = self.tr("Sub Out time must be between 1 and {0} minutes.").format(match_duration)
                        else:
                            message = self.tr("Sub Out time must be greater than {0} and not exceed {1} minutes.").format(min_value, match_duration)

                        QMessageBox.warning(
                            self,
                            self.tr("Invalid Value"),
                            message
                        )
                        item.setText("")
                    else:
                        # Validate substitution rules
                        is_valid, error_message = self._validate_substitution_rules(row, sub_out_value=value)
                        if not is_valid:
                            QMessageBox.warning(
                                self,
                                self.tr("Substitution Rule Violation"),
                                error_message
                            )
                            item.setText("")
                except ValueError:
                    # Not a number, reset
                    item.setText("")

            # Get current minutes before updating
            current_minutes = 0
            minutes_item = self.player_stats_table.item(row, 7)
            if minutes_item and minutes_item.text().strip():
                try:
                    current_minutes = int(minutes_item.text().strip())
                except ValueError:
                    current_minutes = 0

            # Calculate new minutes played
            new_minutes = self._calculate_minutes_played(row)

            # Check if minutes are changing from > 0 to 0
            if current_minutes > 0 and new_minutes == 0:
                # Player had minutes before but now has 0 minutes
                self._handle_minutes_reduced_to_zero(row, current_minutes)

            # Update minutes played for this player
            self._update_minutes_played(row)
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _validate_stats_field_change(self, item, field_name):
        """Validate changes to stats fields that require the player to have played."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        row = item.row()
        text = item.text().strip()

        # Check if the player has played
        has_played = self._validate_player_has_played(row)

        # If empty, set to 0
        if not text:
            item.setText("0")
        else:
            try:
                # Try to convert to integer
                value = int(text)

                # Ensure value is non-negative
                if value < 0:
                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr("{0} must be a non-negative integer.").format(field_name)
                    )
                    item.setText("0")
                # If player hasn't played but trying to set a value > 0
                elif value > 0 and not has_played:
                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr("{0} can only be set for players who have played in the match (minutes > 0).").format(field_name)
                    )
                    item.setText("0")
            except ValueError:
                # Not a valid integer, reset to 0
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("{0} must be a non-negative integer.").format(field_name)
                )
                item.setText("0")

        # Re-enable signals
        self.player_stats_table.blockSignals(False)

    def _handle_integer_field_change(self, item, field_name):
        """Handle changes to integer fields like Goals and Assists."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        text = item.text().strip()

        # If empty, set to 0
        if not text:
            item.setText("0")
        else:
            try:
                # Try to convert to integer
                value = int(text)

                # Ensure value is non-negative
                if value < 0:
                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr("{0} must be a non-negative integer.").format(field_name)
                    )
                    item.setText("0")
            except ValueError:
                # Not a valid integer, reset to 0
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr("{0} must be a non-negative integer.").format(field_name)
                )
                item.setText("0")

        # Re-enable signals
        self.player_stats_table.blockSignals(False)

    def _set_stat_value(self):
        """Set the value for a stat field from the context menu."""
        action = self.sender()
        if not action:
            return

        # Get data from action
        data = action.data()
        if not data or len(data) != 3:
            return

        row, column, value = data

        # Get the current item
        item = self.player_stats_table.item(row, column)
        if not item:
            return

        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            # For stats columns that require playing time, validate first
            if column >= 8 and column != 10 and column != 11 and column != 12 and column != 13:
                # Check if the player has played
                has_played = self._validate_player_has_played(row)

                # If player hasn't played but trying to set a value > 0
                if int(value) > 0 and not has_played:
                    # Get the column name for the error message
                    header_item = self.player_stats_table.horizontalHeaderItem(column)
                    field_name = header_item.text() if header_item else f"Column {column}"

                    QMessageBox.warning(
                        self,
                        self.tr("Invalid Value"),
                        self.tr("{0} can only be set for players who have played in the match (minutes > 0).").format(field_name)
                    )
                    return

            # Handle special values
            if value == "+":
                # Increment
                current_value = int(item.text() or "0")
                # Apply appropriate limits based on column
                if column == 10:  # Yellow cards
                    new_value = min(2, current_value + 1)
                elif column == 11:  # Red cards
                    new_value = min(1, current_value + 1)
                else:  # Goals or assists
                    new_value = min(5, current_value + 1)
                item.setText(str(new_value))
            elif value == "-":
                # Decrement, but not below 0
                current_value = int(item.text() or "0")
                item.setText(str(max(0, current_value - 1)))
            else:
                # Set to specific value
                item.setText(value)

            # For yellow and red cards, we need to handle the special logic
            if column == 10:  # Yellow cards
                # Trigger the yellow card change handler
                self._handle_yellow_card_change(item)
            elif column == 11:  # Red cards
                # Trigger the red card change handler
                self._handle_red_card_change(item)

            # Update the Events widget for this player
            self._update_player_events_widget(row)

        except ValueError:
            # If any error, set to 0
            item.setText("0")
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _on_player_stats_item_clicked(self, index):
        """Handle single click on player stats table items."""
        # Get the item at the clicked index
        item = self.player_stats_table.item(index.row(), index.column())
        if not item:
            return

        column = index.column()

        # For Sub In, Sub Out, Injury, and all stats columns, start editing directly
        if column == 5 or column == 6 or column == 12 or column >= 14:  # Sub In, Sub Out, Injury, or any stats column
            if (item.flags() & Qt.ItemFlag.ItemIsEnabled) and (item.flags() & Qt.ItemFlag.ItemIsEditable):
                # Start editing immediately
                self.player_stats_table.editItem(item)

    def _on_player_stats_item_double_clicked(self, item):
        """Handle double-click on player stats table items."""
        column = item.column()

        # Handle Goals and Assists columns (NOT Min column)
        if column == 8 or column == 9:  # Goals or Assists columns only
            # Block signals to prevent recursion
            self.player_stats_table.blockSignals(True)

            try:
                # Get current value
                current_value = int(item.text() or "0")

                # Toggle through values: 0->1->2->3->4->5->0
                next_values = [1, 2, 3, 4, 5, 0]

                # Find the next value in the sequence
                next_value = 0
                for val in next_values:
                    if current_value < val:
                        next_value = val
                        break

                # Set the new value
                item.setText(str(next_value))

                # Update the Events widget for this player
                self._update_player_events_widget(item.row())

                # Prevent the editor from opening on double-click for these columns
                self.player_stats_table.setCurrentItem(None)
                self.player_stats_table.setCurrentItem(item)
            except ValueError:
                # If any error, set to 0
                item.setText("0")
            finally:
                # Re-enable signals
                self.player_stats_table.blockSignals(False)

        # Handle Yellow Cards column
        elif column == 10:  # Yellow Cards column
            # Block signals to prevent recursion
            self.player_stats_table.blockSignals(True)

            try:
                # Get current value
                current_value = int(item.text() or "0")

                # Toggle through values: 0->1->2->0
                next_values = [1, 2, 0]

                # Find the next value in the sequence
                next_value = 0
                for val in next_values:
                    if current_value < val:
                        next_value = val
                        break

                # Set the new value
                item.setText(str(next_value))

                # Handle the yellow card change
                self._handle_yellow_card_change(item)

                # Update the Events widget for this player
                self._update_player_events_widget(item.row())

                # Prevent the editor from opening on double-click
                self.player_stats_table.setCurrentItem(None)
                self.player_stats_table.setCurrentItem(item)
            except ValueError:
                # If any error, set to 0
                item.setText("0")
            finally:
                # Re-enable signals
                self.player_stats_table.blockSignals(False)

        # Handle Red Cards column
        elif column == 11:  # Red Cards column
            # Block signals to prevent recursion
            self.player_stats_table.blockSignals(True)

            try:
                # Get current value
                current_value = int(item.text() or "0")

                # Check if yellow cards is 2 (which forces red card to be 1)
                row = item.row()
                yellow_card_item = self.player_stats_table.item(row, 10)
                yellow_card_value = 0
                if yellow_card_item and yellow_card_item.text().strip():
                    try:
                        yellow_card_value = int(yellow_card_item.text())
                    except ValueError:
                        yellow_card_value = 0

                # If yellow cards is 2, we can't toggle to 0
                if yellow_card_value == 2 and current_value == 1:
                    # Show a message to the user
                    QMessageBox.information(
                        self,
                        self.tr("Red Card Required"),
                        self.tr("A player with 2 yellow cards must have a red card.")
                    )
                    next_value = 1  # Keep it at 1
                else:
                    # Toggle between 0 and 1
                    next_value = 1 if current_value == 0 else 0

                # Set the new value
                item.setText(str(next_value))

                # Handle the red card change
                self._handle_red_card_change(item)

                # Update the Events widget for this player
                self._update_player_events_widget(item.row())

                # Prevent the editor from opening on double-click
                self.player_stats_table.setCurrentItem(None)
                self.player_stats_table.setCurrentItem(item)
            except ValueError:
                # If any error, set to 0
                item.setText("0")
                # Handle the red card change to ensure proper validation
                self._handle_red_card_change(item)
            finally:
                # Re-enable signals
                self.player_stats_table.blockSignals(False)

    def eventFilter(self, obj, event):
        """Handle keyboard events for the player stats table."""
        if obj == self.player_stats_table and event.type() == QEvent.Type.KeyPress:
            key_event = event

            # Check if we have a selected item
            current_item = self.player_stats_table.currentItem()
            if current_item:
                column = current_item.column()

                # Handle Sub In and Sub Out columns (direct editing)
                if column == 5 or column == 6:  # Sub In or Sub Out columns
                    # Check if the item is enabled and editable
                    if current_item.flags() & Qt.ItemFlag.ItemIsEnabled and current_item.flags() & Qt.ItemFlag.ItemIsEditable:
                        # For numeric keys, start editing the cell
                        if Qt.Key.Key_0 <= key_event.key() <= Qt.Key.Key_9:
                            # Start editing the cell
                            self.player_stats_table.editItem(current_item)
                            return False  # Let the editor handle the key press

                # Handle Goals, Assists, Yellow and Red cards columns
                elif column == 8 or column == 9 or column == 10 or column == 11:  # Goals, Assists, Yellow or Red cards columns
                    # Handle + key to increment
                    if key_event.key() == Qt.Key.Key_Plus or key_event.key() == Qt.Key.Key_Equal:
                        # Block signals to prevent recursion
                        self.player_stats_table.blockSignals(True)

                        try:
                            # Get current value and increment with appropriate limits
                            current_value = int(current_item.text() or "0")
                            if column == 10:  # Yellow cards
                                new_value = min(2, current_value + 1)
                                current_item.setText(str(new_value))
                                # Handle yellow card change
                                self._handle_yellow_card_change(current_item)
                            elif column == 11:  # Red cards
                                new_value = min(1, current_value + 1)
                                current_item.setText(str(new_value))
                                # Handle red card change
                                self._handle_red_card_change(current_item)
                            else:  # Goals or assists
                                new_value = min(5, current_value + 1)
                                current_item.setText(str(new_value))

                            # Update the Events widget for this player
                            self._update_player_events_widget(current_item.row())
                        except ValueError:
                            # If any error, set to 1
                            current_item.setText("1")
                        finally:
                            # Re-enable signals
                            self.player_stats_table.blockSignals(False)

                        return True  # Event handled

                    # Handle - key to decrement
                    elif key_event.key() == Qt.Key.Key_Minus:
                        # Block signals to prevent recursion
                        self.player_stats_table.blockSignals(True)

                        try:
                            # Get current value and decrement, but not below 0
                            current_value = int(current_item.text() or "0")
                            new_value = max(0, current_value - 1)
                            current_item.setText(str(new_value))

                            # Handle special logic for yellow and red cards
                            if column == 10:  # Yellow cards
                                self._handle_yellow_card_change(current_item)
                            elif column == 11:  # Red cards
                                self._handle_red_card_change(current_item)

                            # Update the Events widget for this player
                            self._update_player_events_widget(current_item.row())
                        except ValueError:
                            # If any error, set to 0
                            current_item.setText("0")
                        finally:
                            # Re-enable signals
                            self.player_stats_table.blockSignals(False)

                        return True  # Event handled

                    # Handle direct key input for setting values
                    elif Qt.Key.Key_0 <= key_event.key() <= Qt.Key.Key_9:
                        # Block signals to prevent recursion
                        self.player_stats_table.blockSignals(True)

                        # Convert key code to number
                        value = key_event.key() - Qt.Key.Key_0

                        # Apply appropriate limits based on column
                        if column == 10:  # Yellow cards
                            if value <= 2:  # Only allow 0, 1, 2 for yellow cards
                                current_item.setText(str(value))
                                # Handle yellow card change
                                self._handle_yellow_card_change(current_item)
                            else:
                                # Ignore invalid values for yellow cards
                                pass
                        elif column == 11:  # Red cards
                            # Check if yellow cards is 2 (which forces red card to be 1)
                            row = current_item.row()
                            yellow_card_item = self.player_stats_table.item(row, 10)
                            yellow_card_value = 0
                            if yellow_card_item and yellow_card_item.text().strip():
                                try:
                                    yellow_card_value = int(yellow_card_item.text())
                                except ValueError:
                                    yellow_card_value = 0

                            # If yellow cards is 2 and trying to set red to 0, show message
                            if yellow_card_value == 2 and value == 0:
                                QMessageBox.information(
                                    self,
                                    self.tr("Red Card Required"),
                                    self.tr("A player with 2 yellow cards must have a red card.")
                                )
                                # Force red card to 1
                                current_item.setText("1")
                            elif value <= 1:  # Only allow 0, 1 for red cards
                                current_item.setText(str(value))
                                # Handle red card change
                                self._handle_red_card_change(current_item)
                            else:
                                # Ignore invalid values for red cards
                                pass
                        else:  # Goals or assists
                            if value <= 5:  # Only allow 0-5 for goals/assists
                                current_item.setText(str(value))
                            else:
                                # Ignore invalid values for goals/assists
                                pass

                        # Update the Events widget for this player
                        self._update_player_events_widget(current_item.row())

                        # Re-enable signals
                        self.player_stats_table.blockSignals(False)

                        return True  # Event handled

        # Pass event to parent class
        return super().eventFilter(obj, event)

    def _get_total_match_duration(self):
        """Get the total match duration including extra time and stoppage time."""
        # Get base match duration
        match_duration = int(self.match_duration_value.text())

        # Add stoppage time
        stoppage_time = self.stoppage_time_spinner.value()

        # Add extra time if played
        extra_time = 0
        if self.extra_time_check.isChecked():
            extra_time = self.et_duration_spinner.value()

        return match_duration + stoppage_time + extra_time

    def _get_competition_x_a_side(self):
        """Get the X-a-side value for the current competition."""
        # Get the selected competition ID
        comp_id = self.competition_field.currentData()

        # Find the competition in the list
        competitions = self.matches_manager.get_competitions()
        x_a_side = 11  # Default value

        for comp in competitions:
            if comp.get('id') == comp_id:
                x_a_side = comp.get('x_a_side', 11)
                break

        return x_a_side

    def _calculate_minutes_played(self, row):
        """Calculate minutes played for a player based on lineup status, sub in, and sub out times."""
        # Get total match duration
        match_duration = self._get_total_match_duration()

        # Get lineup status
        lineup_item = self.player_stats_table.item(row, 0)
        if not lineup_item:
            return 0

        is_in_lineup = lineup_item.checkState() == Qt.CheckState.Checked

        # Get Sub In and Sub Out values
        sub_in_item = self.player_stats_table.item(row, 5)
        sub_out_item = self.player_stats_table.item(row, 6)

        sub_in_value = 0
        sub_out_value = match_duration

        # Parse Sub In value if available
        if sub_in_item and sub_in_item.text().strip():
            try:
                sub_in_value = int(sub_in_item.text())
            except ValueError:
                sub_in_value = 0

        # Parse Sub Out value if available
        if sub_out_item and sub_out_item.text().strip():
            try:
                sub_out_value = int(sub_out_item.text())
            except ValueError:
                sub_out_value = match_duration

        # Calculate minutes played
        if is_in_lineup:
            # Player started the match
            if sub_out_item and sub_out_item.text().strip():
                # Player was substituted out
                return sub_out_value
            else:
                # Player played the full match
                return match_duration
        else:
            # Player started on the bench
            if sub_in_item and sub_in_item.text().strip():
                # Player was substituted in
                if sub_out_item and sub_out_item.text().strip() and sub_out_value > sub_in_value:
                    # Player was later substituted out
                    return sub_out_value - sub_in_value
                else:
                    # Player stayed until the end
                    return match_duration - sub_in_value
            else:
                # Player didn't play
                return 0

    def _update_minutes_played(self, row):
        """Update the minutes played display for a specific player row."""
        # Get the current minutes value before updating
        current_minutes = 0
        minutes_item = self.player_stats_table.item(row, 7)
        if minutes_item and minutes_item.text().strip():
            try:
                current_minutes = int(minutes_item.text().strip())
            except ValueError:
                current_minutes = 0

        # Calculate new minutes played
        minutes_played = self._calculate_minutes_played(row)

        # Check if minutes are changing from > 0 to 0
        if current_minutes > 0 and minutes_played == 0:
            # Player had minutes before but now has 0 minutes
            self._handle_minutes_reduced_to_zero(row, current_minutes)

        # Update the minutes played cell
        if minutes_item:
            minutes_item.setText(str(minutes_played))

    def _handle_minutes_reduced_to_zero(self, row, previous_minutes):
        """Handle the case when a player's minutes are reduced from > 0 to 0.

        Args:
            row: The row index of the player
            previous_minutes: The previous minutes value before reduction
        """
        # Get player name for the message
        player_name = ""
        name_item = self.player_stats_table.item(row, 2)
        if name_item:
            player_name = name_item.text()

        # Check if the player has any stats that require playing time
        has_stats = False

        # Check specific columns that require playing time (Goals, Assists, etc.)
        # These are the columns that should trigger the warning
        playing_time_columns = [8, 9]  # Goals, Assists

        # Add any additional columns that require playing time (after column 13)
        for col in range(14, self.player_stats_table.columnCount()):
            playing_time_columns.append(col)

        # Check each column that requires playing time
        for col in playing_time_columns:
            item = self.player_stats_table.item(row, col)
            # Check if the item has a non-zero, non-empty value
            if item and item.text().strip() and item.text().strip() != "0" and item.text().strip() != "0.0":
                has_stats = True
                break

        # If player has stats, show warning and options
        if has_stats:
            # Create message box with options
            message = self.tr("Player '{0}' now has 0 minutes played, but has stats that require playing time.\n\n"
                             "What would you like to do?").format(player_name)

            # Create custom message box
            msgBox = QMessageBox(self)
            msgBox.setWindowTitle(self.tr("Player Minutes Changed"))
            msgBox.setText(message)
            msgBox.setIcon(QMessageBox.Icon.Warning)

            # Add buttons
            revert_button = msgBox.addButton(self.tr("Revert Change"), QMessageBox.ButtonRole.AcceptRole)
            clear_button = msgBox.addButton(self.tr("Clear Stats"), QMessageBox.ButtonRole.DestructiveRole)
            msgBox.addButton(self.tr("Cancel"), QMessageBox.ButtonRole.RejectRole)

            # Show the dialog
            msgBox.exec()

            clicked_button = msgBox.clickedButton()

            if clicked_button == revert_button:
                # Revert the change that caused minutes to go to 0
                self._revert_minutes_change(row, previous_minutes)
            elif clicked_button == clear_button:
                # Clear all stats that require playing time
                self._clear_player_stats(row)
            # If cancel, do nothing and keep the inconsistent state

    def _revert_minutes_change(self, row, previous_minutes):
        """Revert the change that caused minutes to go to 0."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            # Try to get the previous state from the name item
            name_item = self.player_stats_table.item(row, 2)
            previous_state = None
            if name_item:
                previous_state = name_item.data(Qt.ItemDataRole.UserRole)

            # If we have a stored previous state, use it
            if previous_state and isinstance(previous_state, dict):
                # Get the lineup item
                lineup_item = self.player_stats_table.item(row, 0)
                if lineup_item:
                    # Restore the previous lineup state
                    if previous_state.get('lineup', False):
                        lineup_item.setCheckState(Qt.CheckState.Checked)
                    else:
                        lineup_item.setCheckState(Qt.CheckState.Unchecked)

                # Get Sub In and Sub Out items
                sub_in_item = self.player_stats_table.item(row, 5)
                sub_out_item = self.player_stats_table.item(row, 6)

                # Restore Sub In value
                if sub_in_item and 'sub_in' in previous_state:
                    sub_in_item.setText(previous_state['sub_in'])
                    # Make sure it's enabled if not in lineup
                    if not previous_state.get('lineup', False):
                        sub_in_item.setFlags(sub_in_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)
                    else:
                        # Disable if in lineup
                        sub_in_item.setFlags(sub_in_item.flags() & ~Qt.ItemFlag.ItemIsEnabled & ~Qt.ItemFlag.ItemIsEditable)

                # Restore Sub Out value
                if sub_out_item and 'sub_out' in previous_state:
                    sub_out_item.setText(previous_state['sub_out'])
                    # Always enable Sub Out for players with minutes
                    sub_out_item.setFlags(sub_out_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)
            else:
                # Fallback to the old method if no previous state is available
                # Get lineup status
                lineup_item = self.player_stats_table.item(row, 0)
                if not lineup_item:
                    return

                is_in_lineup = lineup_item.checkState() == Qt.CheckState.Checked

                if is_in_lineup:
                    # Player is in lineup, so the Sub Out field was probably changed
                    # Clear the Sub Out field to restore minutes
                    sub_out_item = self.player_stats_table.item(row, 6)
                    if sub_out_item:
                        sub_out_item.setText("")
                else:
                    # Player is not in lineup, so either:
                    # 1. The Sub In field was cleared
                    # 2. The Sub Out field was set equal to Sub In

                    # Get current Sub In and Sub Out values
                    sub_in_item = self.player_stats_table.item(row, 5)
                    sub_out_item = self.player_stats_table.item(row, 6)

                    sub_in_value = ""
                    sub_out_value = ""

                    if sub_in_item:
                        sub_in_value = sub_in_item.text().strip()
                    if sub_out_item:
                        sub_out_value = sub_out_item.text().strip()

                    # For players not in lineup, we need to restore their minutes
                    # If they had minutes before, we need to restore their Sub In value
                    if not sub_in_value:
                        # Calculate a reasonable Sub In value based on match duration
                        match_duration = self._get_total_match_duration()
                        sub_in_value = str(max(1, match_duration - previous_minutes))
                        if sub_in_item:
                            sub_in_item.setText(sub_in_value)

                    # If Sub Out is equal to Sub In, clear it
                    if sub_in_value and sub_out_value and sub_in_value == sub_out_value:
                        if sub_out_item:
                            sub_out_item.setText("")
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

        # Update minutes played to reflect the changes
        self._update_minutes_played(row)

    def _clear_player_stats(self, row):
        """Clear all stats that require playing time for a player."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            # Define columns that require playing time
            playing_time_columns = [8, 9]  # Goals, Assists

            # Add any additional columns that require playing time (after column 13)
            for col in range(14, self.player_stats_table.columnCount()):
                playing_time_columns.append(col)

            # Clear all stats columns that require playing time
            for col in playing_time_columns:
                item = self.player_stats_table.item(row, col)
                if item:
                    item.setText("0")
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

    def _update_all_minutes_played(self):
        """Update minutes played for all players in the table."""
        for row in range(self.player_stats_table.rowCount()):
            self._update_minutes_played(row)

    def _get_player_stats_data(self):
        """Get the player stats data from the table."""
        data = []

        for row in range(self.player_stats_table.rowCount()):
            player_data = {}

            # Get player ID
            lineup_item = self.player_stats_table.item(row, 0)
            if not lineup_item:
                continue

            player_id = lineup_item.data(Qt.ItemDataRole.UserRole)
            if not player_id:
                continue

            player_data["player_id"] = player_id
            player_data["lineup"] = lineup_item.checkState() == Qt.CheckState.Checked

            # Get shirt number
            shirt_item = self.player_stats_table.item(row, 1)
            player_data["shirt_number"] = shirt_item.text() if shirt_item else ""

            # Get player name
            name_item = self.player_stats_table.item(row, 2)
            player_data["player_name"] = name_item.text() if name_item else ""

            # Get position
            pos_item = self.player_stats_table.item(row, 3)
            player_data["position"] = pos_item.text() if pos_item else ""

            # Get captain status
            captain_item = self.player_stats_table.item(row, 4)
            player_data["captain"] = captain_item.checkState() == Qt.CheckState.Checked if captain_item else False

            # Get sub in
            sub_in_item = self.player_stats_table.item(row, 5)
            player_data["sub_in"] = sub_in_item.text() if sub_in_item else ""

            # Get sub out
            sub_out_item = self.player_stats_table.item(row, 6)
            player_data["sub_out"] = sub_out_item.text() if sub_out_item else ""

            # Get minutes played
            minutes_item = self.player_stats_table.item(row, 7)
            player_data["minutes_played"] = minutes_item.text() if minutes_item else "0"

            # Get goals
            goals_item = self.player_stats_table.item(row, 8)
            player_data["goals"] = goals_item.text() if goals_item else "0"

            # Get assists
            assists_item = self.player_stats_table.item(row, 9)
            player_data["assists"] = assists_item.text() if assists_item else "0"

            # Get yellow cards
            yellow_cards_item = self.player_stats_table.item(row, 10)
            player_data["yellow_cards"] = yellow_cards_item.text() if yellow_cards_item else "0"

            # Get red cards
            red_cards_item = self.player_stats_table.item(row, 11)
            player_data["red_cards"] = red_cards_item.text() if red_cards_item else "0"

            # Get injury minute
            injury_item = self.player_stats_table.item(row, 12)
            player_data["injury_minute"] = injury_item.text() if injury_item else ""

            # Get injury description
            injury_desc_item = self.player_stats_table.item(row, 13)
            player_data["injury_description"] = injury_desc_item.text() if injury_desc_item else ""

            # Get all additional stats
            # Attempts (column 14)
            attempts_item = self.player_stats_table.item(row, 14)
            player_data["attempts"] = attempts_item.text() if attempts_item else "0"

            # On Target (column 15)
            on_target_item = self.player_stats_table.item(row, 15)
            player_data["on_target"] = on_target_item.text() if on_target_item else "0"

            # Touches (column 16)
            touches_item = self.player_stats_table.item(row, 16)
            player_data["touches"] = touches_item.text() if touches_item else "0"

            # Tackles (column 17)
            tackles_item = self.player_stats_table.item(row, 17)
            player_data["tackles"] = tackles_item.text() if tackles_item else "0"

            # Tackles Won (column 18)
            tackles_won_item = self.player_stats_table.item(row, 18)
            player_data["tackles_won"] = tackles_won_item.text() if tackles_won_item else "0"

            # Interceptions (column 19)
            interceptions_item = self.player_stats_table.item(row, 19)
            player_data["interceptions"] = interceptions_item.text() if interceptions_item else "0"

            # Shot Blocks (column 20)
            shot_blocks_item = self.player_stats_table.item(row, 20)
            player_data["shot_blocks"] = shot_blocks_item.text() if shot_blocks_item else "0"

            # Pass Blocks (column 21)
            pass_blocks_item = self.player_stats_table.item(row, 21)
            player_data["pass_blocks"] = pass_blocks_item.text() if pass_blocks_item else "0"

            # Clearances (column 22)
            clearances_item = self.player_stats_table.item(row, 22)
            player_data["clearances"] = clearances_item.text() if clearances_item else "0"

            # Error → Shot (column 23)
            error_opp_shot_item = self.player_stats_table.item(row, 23)
            player_data["error_opp_shot"] = error_opp_shot_item.text() if error_opp_shot_item else "0"

            # xG (column 24)
            xgoals_item = self.player_stats_table.item(row, 24)
            player_data["xgoals"] = xgoals_item.text() if xgoals_item else "0.0"

            # GCA (column 25)
            gca_item = self.player_stats_table.item(row, 25)
            player_data["goal_creating_actions"] = gca_item.text() if gca_item else "0"

            # Passes Attempted (column 26)
            passes_att_item = self.player_stats_table.item(row, 26)
            player_data["passes_attempted"] = passes_att_item.text() if passes_att_item else "0"

            # Passes Completed (column 27)
            passes_comp_item = self.player_stats_table.item(row, 27)
            player_data["passes_completed"] = passes_comp_item.text() if passes_comp_item else "0"

            # Passes Progressive (column 28)
            passes_prog_item = self.player_stats_table.item(row, 28)
            player_data["passes_progressive"] = passes_prog_item.text() if passes_prog_item else "0"

            # Carries Progressive (column 29)
            carries_prog_item = self.player_stats_table.item(row, 29)
            player_data["carries_progressive"] = carries_prog_item.text() if carries_prog_item else "0"

            # Take-ons Attempted (column 30)
            take_ons_att_item = self.player_stats_table.item(row, 30)
            player_data["take_ons_attempted"] = take_ons_att_item.text() if take_ons_att_item else "0"

            # Take-ons Successful (column 31)
            take_ons_succ_item = self.player_stats_table.item(row, 31)
            player_data["take_ons_successful"] = take_ons_succ_item.text() if take_ons_succ_item else "0"

            # Crosses (column 32)
            crosses_item = self.player_stats_table.item(row, 32)
            player_data["crosses"] = crosses_item.text() if crosses_item else "0"

            # Aerial Won (column 33)
            aerial_won_item = self.player_stats_table.item(row, 33)
            player_data["aerial_won"] = aerial_won_item.text() if aerial_won_item else "0"

            # Aerial Lost (column 34)
            aerial_lost_item = self.player_stats_table.item(row, 34)
            player_data["aerial_lost"] = aerial_lost_item.text() if aerial_lost_item else "0"

            # Fouls Committed (column 35)
            fouls_com_item = self.player_stats_table.item(row, 35)
            player_data["fouls_committed"] = fouls_com_item.text() if fouls_com_item else "0"

            # Fouls Suffered (column 36)
            fouls_suf_item = self.player_stats_table.item(row, 36)
            player_data["fouls_suffered"] = fouls_suf_item.text() if fouls_suf_item else "0"

            # Offsides (column 37)
            offsides_item = self.player_stats_table.item(row, 37)
            player_data["offsides"] = offsides_item.text() if offsides_item else "0"

            # Distance (column 38)
            distance_item = self.player_stats_table.item(row, 38)
            player_data["distance"] = distance_item.text() if distance_item else "0"

            data.append(player_data)

        return data

    def _load_player_stats_data(self, data):
        """Load player stats data into the table."""
        # Clear existing data
        self.player_stats_table.setRowCount(0)

        # Add each player
        for player_data in data:
            player_id = player_data.get("player_id")
            if not player_id:
                continue

            # Get player from roster manager
            player = self.roster_manager.get_player(player_id)
            if not player:
                continue

            # Add player to table
            row = self.player_stats_table.rowCount()
            self.player_stats_table.insertRow(row)

            # Lineup checkbox (column 0)
            lineup_item = QTableWidgetItem()
            lineup_item.setFlags(lineup_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
            lineup_item.setCheckState(Qt.CheckState.Checked if player_data.get("lineup", False) else Qt.CheckState.Unchecked)
            lineup_item.setData(Qt.ItemDataRole.UserRole, player_id)
            self.player_stats_table.setItem(row, 0, lineup_item)

            # Shirt number (column 1)
            shirt_number = player.get("shirt_number", "")
            shirt_item = QTableWidgetItem(str(shirt_number) if shirt_number else "")
            shirt_item.setFlags(shirt_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            shirt_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 1, shirt_item)

            # Player name (column 2)
            player_name = f"{player.get('last_name', '')} {player.get('first_name', '')}"
            name_item = QTableWidgetItem(player_name.strip())
            name_item.setFlags(name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.player_stats_table.setItem(row, 2, name_item)

            # Position (column 3)
            position = player.get("position", "")
            pos_item = QTableWidgetItem(position)
            pos_item.setFlags(pos_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            pos_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 3, pos_item)

            # Captain (column 4)
            captain_item = QTableWidgetItem()
            captain_item.setFlags(captain_item.flags() | Qt.ItemFlag.ItemIsUserCheckable | Qt.ItemFlag.ItemIsEnabled)
            captain_item.setCheckState(Qt.CheckState.Checked if player_data.get("captain", False) else Qt.CheckState.Unchecked)
            # Set text to empty string to ensure checkbox is displayed
            captain_item.setText("")
            self.player_stats_table.setItem(row, 4, captain_item)

            # Sub in (column 5)
            sub_in = player_data.get("sub_in", "")
            sub_in_item = QTableWidgetItem(sub_in)
            # Make sure it's editable (will be properly enabled/disabled in _update_player_stats_fields)
            sub_in_item.setFlags(sub_in_item.flags() | Qt.ItemFlag.ItemIsEditable)
            sub_in_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 5, sub_in_item)

            # Sub out (column 6)
            sub_out = player_data.get("sub_out", "")
            sub_out_item = QTableWidgetItem(sub_out)
            # Make sure it's editable (will be properly enabled/disabled in _update_player_stats_fields)
            sub_out_item.setFlags(sub_out_item.flags() | Qt.ItemFlag.ItemIsEditable)
            sub_out_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 6, sub_out_item)

            # Minutes played (column 7) - calculated field
            # Get minutes played from data or calculate it
            minutes_played = player_data.get("minutes_played", "")
            if not minutes_played:
                # If no value in data, calculate it based on lineup status and sub in/out
                is_in_lineup = lineup_item.checkState() == Qt.CheckState.Checked
                match_duration = self._get_total_match_duration()

                if is_in_lineup:
                    if sub_out:
                        minutes_played = sub_out
                    else:
                        minutes_played = str(match_duration)
                else:
                    if sub_in:
                        if sub_out:
                            try:
                                minutes_played = str(int(sub_out) - int(sub_in))
                            except ValueError:
                                minutes_played = "0"
                        else:
                            try:
                                minutes_played = str(match_duration - int(sub_in))
                            except ValueError:
                                minutes_played = "0"
                    else:
                        minutes_played = "0"

            minutes_played_item = QTableWidgetItem(str(minutes_played))
            # Make it read-only
            minutes_played_item.setFlags(minutes_played_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            minutes_played_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 7, minutes_played_item)

            # Goals (column 8)
            goals = player_data.get("goals", "0")
            goals_item = QTableWidgetItem(str(goals))
            goals_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 8, goals_item)

            # Assists (column 9)
            assists = player_data.get("assists", "0")
            assists_item = QTableWidgetItem(str(assists))
            assists_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 9, assists_item)

            # Yellow cards (column 10)
            yellow_cards = player_data.get("yellow_cards", "0")
            yellow_cards_item = QTableWidgetItem(str(yellow_cards))
            yellow_cards_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 10, yellow_cards_item)

            # Red cards (column 11)
            red_cards = player_data.get("red_cards", "0")
            red_cards_item = QTableWidgetItem(str(red_cards))
            red_cards_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 11, red_cards_item)

            # Injury minute (column 12)
            injury_minute = player_data.get("injury_minute", "")
            injury_item = QTableWidgetItem(str(injury_minute))
            injury_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 12, injury_item)

            # Injury description (column 13) - hidden column
            injury_description = player_data.get("injury_description", "")
            injury_desc_item = QTableWidgetItem(str(injury_description))
            injury_desc_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.player_stats_table.setItem(row, 13, injury_desc_item)

            # Load all additional stats columns (columns 14-38)
            # Define the stats columns data
            stats_columns = [
                {"col": 14, "key": "attempts", "default": "0"},
                {"col": 15, "key": "on_target", "default": "0"},
                {"col": 16, "key": "touches", "default": "0"},
                {"col": 17, "key": "tackles", "default": "0"},
                {"col": 18, "key": "tackles_won", "default": "0"},
                {"col": 19, "key": "interceptions", "default": "0"},
                {"col": 20, "key": "shot_blocks", "default": "0"},
                {"col": 21, "key": "pass_blocks", "default": "0"},
                {"col": 22, "key": "clearances", "default": "0"},
                {"col": 23, "key": "error_opp_shot", "default": "0"},
                {"col": 24, "key": "xgoals", "default": "0.0"},  # Special case for xG with decimal
                {"col": 25, "key": "goal_creating_actions", "default": "0"},
                {"col": 26, "key": "passes_attempted", "default": "0"},
                {"col": 27, "key": "passes_completed", "default": "0"},
                {"col": 28, "key": "passes_progressive", "default": "0"},
                {"col": 29, "key": "carries_progressive", "default": "0"},
                {"col": 30, "key": "take_ons_attempted", "default": "0"},
                {"col": 31, "key": "take_ons_successful", "default": "0"},
                {"col": 32, "key": "crosses", "default": "0"},
                {"col": 33, "key": "aerial_won", "default": "0"},
                {"col": 34, "key": "aerial_lost", "default": "0"},
                {"col": 35, "key": "fouls_committed", "default": "0"},
                {"col": 36, "key": "fouls_suffered", "default": "0"},
                {"col": 37, "key": "offsides", "default": "0"},
                {"col": 38, "key": "distance", "default": "0"}
            ]

            # Set all stats columns
            for stat in stats_columns:
                value = player_data.get(stat["key"], stat["default"])
                item = QTableWidgetItem(str(value))
                # Make sure all stats columns are editable
                item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)
                # Center align all stats columns
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.player_stats_table.setItem(row, stat["col"], item)

        # Update the enabled/disabled state of fields
        self._update_player_stats_fields()

        # Calculate and update minutes played for all players
        self._update_all_minutes_played()

        # Sort table to keep lineup players at the top
        self._sort_player_stats_table()

    def _update_player_stats_fields(self):
        """Update the enabled/disabled state of player stats fields."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            for row in range(self.player_stats_table.rowCount()):
                # Get lineup status
                lineup_item = self.player_stats_table.item(row, 0)
                if not lineup_item:
                    continue

                is_in_lineup = lineup_item.checkState() == Qt.CheckState.Checked

                # Get Sub In/Out items
                sub_in_item = self.player_stats_table.item(row, 5)
                sub_out_item = self.player_stats_table.item(row, 6)

                if is_in_lineup:
                    # Player is in lineup - disable Sub In, enable Sub Out
                    if sub_in_item:
                        # Clear any existing Sub In value
                        sub_in_item.setText("")
                        # Disable the Sub In field
                        sub_in_item.setFlags(sub_in_item.flags() & ~Qt.ItemFlag.ItemIsEnabled & ~Qt.ItemFlag.ItemIsEditable)

                    if sub_out_item:
                        # Enable the Sub Out field
                        sub_out_item.setFlags(sub_out_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)
                else:
                    # Player is not in lineup - enable Sub In
                    if sub_in_item:
                        # Make sure Sub In is enabled
                        sub_in_item.setFlags(sub_in_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)

                    # Enable Sub Out only if Sub In has a value
                    if sub_out_item:
                        has_sub_in = sub_in_item and sub_in_item.text().strip()
                        if has_sub_in:
                            # If there's a Sub In value, enable Sub Out
                            sub_out_item.setFlags(sub_out_item.flags() | Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsEditable)
                        else:
                            # If no Sub In value, clear and disable Sub Out
                            sub_out_item.setText("")
                            sub_out_item.setFlags(sub_out_item.flags() & ~Qt.ItemFlag.ItemIsEnabled & ~Qt.ItemFlag.ItemIsEditable)
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

        # Force a visual update
        self.player_stats_table.viewport().update()

    def _add_placeholder_content(self, tab, tab_name):
        """Add placeholder content to a tab."""
        layout = QVBoxLayout(tab)

        placeholder = QLabel(self.tr("Content for the {} tab will be added later").format(tab_name))
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Use a larger font for the placeholder
        font = placeholder.font()
        font.setPointSize(font.pointSize() + 2)
        placeholder.setFont(font)

        layout.addWidget(placeholder)

    # Event handlers
    def _on_mode_toggled(self, _):
        """Handle mode toggle between browse and edit."""
        if self.browse_radio.isChecked():
            # Switch to browse mode
            self.main_stack.setCurrentIndex(0)
            self.is_edit_mode = False

            # Show filter controls and browse action buttons, hide edit action buttons
            self.filter_widget.setVisible(True)
            self.browse_actions_widget.setVisible(True)
            self.action_widget.setVisible(False)
        else:
            # Switch to edit mode
            self.main_stack.setCurrentIndex(1)
            self.is_edit_mode = True

            # Hide filter controls and browse action buttons, show edit action buttons
            self.filter_widget.setVisible(False)
            self.browse_actions_widget.setVisible(False)
            self.action_widget.setVisible(True)

    def _on_add_match_clicked(self):
        """Handle add new match button click."""
        self.edit_radio.setChecked(True)
        self.current_match_id = None
        self._clear_match_form()
        self._update_result_label()

        # Explicitly populate venue fields since Home is selected by default
        # This ensures venue is populated even if the radio button's toggled signal isn't fired
        self._populate_venue_from_club_data()

        # Update team names in statistics section
        self._update_team_names_in_stats()

        # Update final score in statistics section
        self._update_final_score()

    def _on_header_clicked(self, column):
        """Handle click on table header for sorting."""
        if column == self.sort_column:
            # Toggle sort order if clicking the same column
            self.sort_order = Qt.SortOrder.AscendingOrder if self.sort_order == Qt.SortOrder.DescendingOrder else Qt.SortOrder.DescendingOrder
        else:
            # Set new sort column and default to ascending
            self.sort_column = column
            self.sort_order = Qt.SortOrder.AscendingOrder

        # Set the sort indicator on the header
        self.match_table.horizontalHeader().setSortIndicator(column, self.sort_order)

        # Sort the table
        self.match_table.sortItems(column, self.sort_order)

        # Save the sort state
        self._save_header_state()

    def _save_header_state(self):
        """Save the header state (column order, width, sort) to settings."""
        settings = QSettings()
        settings.beginGroup("matches_page")

        # Save header visual state (column widths and order)
        header_state = self.match_table.horizontalHeader().saveState()
        settings.setValue("header_state", header_state)

        # Save sort column and order
        settings.setValue("sort_column", self.sort_column)

        # Convert sort order to integer (0 for ascending, 1 for descending)
        sort_order_value = 0 if self.sort_order == Qt.SortOrder.AscendingOrder else 1
        settings.setValue("sort_order", sort_order_value)

        settings.endGroup()

    def _restore_header_state(self):
        """Restore the header state from settings."""
        settings = QSettings()
        settings.beginGroup("matches_page")

        # Restore header visual state
        header_state = settings.value("header_state")
        if header_state:
            self.match_table.horizontalHeader().restoreState(header_state)

        # Restore sort column and order
        self.sort_column = settings.value("sort_column", 0, int)

        # Convert integer value back to Qt.SortOrder
        sort_order_value = settings.value("sort_order", 0, int)
        self.sort_order = Qt.SortOrder.DescendingOrder if sort_order_value == 1 else Qt.SortOrder.AscendingOrder

        # Apply the sort
        if self.match_table.rowCount() > 0:
            self.match_table.horizontalHeader().setSortIndicator(self.sort_column, self.sort_order)
            self.match_table.sortItems(self.sort_column, self.sort_order)

        settings.endGroup()

    def _save_player_stats_header_state(self):
        """Save the player stats table header state (column visibility) to settings."""
        settings = QSettings()
        settings.beginGroup("matches_page_player_stats")

        # Save column visibility state
        for i, col in enumerate(self.player_stats_columns):
            if col.get("hideable", True):
                is_visible = not self.player_stats_table.isColumnHidden(i)
                settings.setValue(f"column_{col['key']}_visible", is_visible)

        settings.endGroup()

    def _restore_player_stats_header_state(self):
        """Restore the player stats table header state from settings."""
        settings = QSettings()
        settings.beginGroup("matches_page_player_stats")

        # Restore column visibility state
        for i, col in enumerate(self.player_stats_columns):
            if col.get("hideable", True):
                # Default visibility: attempts and on_target visible, others hidden
                default_visible = col["key"] in ["attempts", "on_target"]
                is_visible = settings.value(f"column_{col['key']}_visible", default_visible, bool)
                self.player_stats_table.setColumnHidden(i, not is_visible)

        settings.endGroup()

    def _on_save_match(self):
        """Handle save match button click."""
        # Validate form data
        if not self._validate_match_data():
            return

        # Get form data
        match_data = {
            'date': self.match_date.date().toString("yyyy-MM-dd"),
            'time': self.match_time.time().toString("HH:mm"),
            'competition_id': self.competition_field.currentData(),  # Get competition ID from combo data
            'season': self.season_field.currentText(),
            'matchday': self.matchday_field.text(),
            'is_home': self.home_radio.isChecked(),
            'opponent': self.opponent_field.text(),
            'team_score': self.team_score.value(),
            'opponent_score': self.opponent_score.value(),
            'ht_team_score': self.ht_team_score.value(),
            'ht_opponent_score': self.ht_opponent_score.value(),
            'et_played': self.extra_time_check.isChecked(),
            'et_team_score': self.et_team_score.value() if self.extra_time_check.isChecked() else 0,
            'et_opponent_score': self.et_opponent_score.value() if self.extra_time_check.isChecked() else 0,
            'et_duration': self.et_duration_spinner.value() if self.extra_time_check.isChecked() else 0,
            'penalties_played': self.penalties_check.isChecked(),
            'penalties_team_score': self.penalties_team_score.value() if self.penalties_check.isChecked() else 0,
            'penalties_opponent_score': self.penalties_opponent_score.value() if self.penalties_check.isChecked() else 0,
            'match_duration': int(self.match_duration_value.text()),
            'stoppage_time': self.stoppage_time_spinner.value(),
            'venue': self.venue_field.text(),
            'city': self.city_field.text(),
            'country': self.country_field.currentText(),
            'weather': self.weather_combo.currentText(),
            'attendance': self.attendance_field.value(),
            'broadcast': self.broadcast_field.text(),
            'officials': [
                {'name': self.referee_fields[0].text() if len(self.referee_fields) > 0 else '', 'role': 'Referee'},
                {'name': self.referee_fields[1].text() if len(self.referee_fields) > 1 else '', 'role': 'Assistant Referee 1'},
                {'name': self.referee_fields[2].text() if len(self.referee_fields) > 2 else '', 'role': 'Assistant Referee 2'},
                {'name': self.referee_fields[3].text() if len(self.referee_fields) > 3 else '', 'role': 'Fourth Official'},
                {'name': self.referee_fields[4].text() if len(self.referee_fields) > 4 else '', 'role': 'Video Assistant Referee'},
            ],
            'officials_rating': self.referee_rating.value(),
            'officials_comments': self.referee_comments.toPlainText(),
            'team_staff': [],  # TODO: Implement team staff
            'match_stats': {
                'team': {
                    # Basic statistics
                    **{
                        stat_key: (
                            # Handle QDoubleSpinBox for xgoals
                            float(spinbox.value()) if stat_key == "xgoals" and hasattr(spinbox, 'value')
                            # Handle QLabel for final_score
                            else spinbox.text() if stat_key == "final_score" and hasattr(spinbox, 'text')
                            # Handle regular spinboxes
                            else spinbox.value() if hasattr(spinbox, 'value')
                            # Default fallback
                            else 0
                        ) for stat_key, spinbox in self.team_stats.items()
                        if stat_key != "final_score"  # Skip final_score as it's calculated
                    },
                    # Advanced statistics
                    **{
                        stat_key: spinbox.value() if hasattr(spinbox, 'value') else 0
                        for stat_key, spinbox in self.team_adv_stats.items()
                    }
                },
                'opponent': {
                    # Basic statistics
                    **{
                        stat_key: (
                            # Handle QDoubleSpinBox for xgoals
                            float(spinbox.value()) if stat_key == "xgoals" and hasattr(spinbox, 'value')
                            # Handle QLabel for final_score
                            else spinbox.text() if stat_key == "final_score" and hasattr(spinbox, 'text')
                            # Handle regular spinboxes
                            else spinbox.value() if hasattr(spinbox, 'value')
                            # Default fallback
                            else 0
                        ) for stat_key, spinbox in self.opponent_stats.items()
                        if stat_key != "final_score"  # Skip final_score as it's calculated
                    },
                    # Advanced statistics
                    **{
                        stat_key: spinbox.value() if hasattr(spinbox, 'value') else 0
                        for stat_key, spinbox in self.opponent_adv_stats.items()
                    }
                }
            },
            'player_stats': self._get_player_stats_data()
        }

        # Log match data for debugging
        self.logger.info("Preparing to save match data:")
        for key, value in match_data.items():
            if key != 'match_stats':  # Skip match_stats for brevity
                self.logger.info(f"  {key}: {value}")
            else:
                self.logger.info(f"  {key}: <match statistics data>")

        # Save match data to database
        if self.current_match_id:
            # Update existing match
            self.logger.info(f"Updating existing match with ID: {self.current_match_id}")
            match_data['match_id'] = self.current_match_id
            success = self.matches_manager.update_match(match_data)
            if success:
                self.logger.info("Match updated successfully")
                QMessageBox.information(self, self.tr("Success"), self.tr("Match updated successfully."))
            else:
                self.logger.error("Failed to update match")
                QMessageBox.warning(self, self.tr("Error"), self.tr("Failed to update match."))
                return
        else:
            # Add new match
            self.logger.info("Adding new match")
            match_id = self.matches_manager.add_match(match_data)
            if match_id:
                self.logger.info(f"New match added with ID: {match_id}")
                self.current_match_id = match_id
                QMessageBox.information(self, self.tr("Success"), self.tr("Match added successfully."))
            else:
                self.logger.error("Failed to add new match")
                QMessageBox.warning(self, self.tr("Error"), self.tr("Failed to add match."))
                return

        # Switch back to browse mode
        self.browse_radio.setChecked(True)

        # Refresh match list
        self._load_matches()

    def _on_cancel_edit(self):
        """Handle cancel edit button click."""
        # Switch back to browse mode
        self.browse_radio.setChecked(True)

    def _on_table_double_clicked(self, index):
        """Handle double-click on table to edit the selected match."""
        # Get the match ID from the clicked item
        item = self.match_table.item(index.row(), index.column())
        if not item:
            return

        match_id = item.data(Qt.ItemDataRole.UserRole)
        if not match_id:
            return

        self._edit_match(match_id)

    def _show_context_menu(self, position):
        """Show context menu for the table."""
        # Get the selected row
        indexes = self.match_table.selectedIndexes()
        if not indexes:
            return

        # Get the match ID from the selected row
        item = self.match_table.item(indexes[0].row(), indexes[0].column())
        if not item:
            return

        match_id = item.data(Qt.ItemDataRole.UserRole)
        if not match_id:
            return

        # Create context menu
        menu = QMenu(self)

        # Add actions
        edit_action = menu.addAction(self.tr("Edit Match"))
        delete_action = menu.addAction(self.tr("Delete Match"))
        duplicate_action = menu.addAction(self.tr("Duplicate Match"))

        # Show the menu and get the selected action
        action = menu.exec(self.match_table.viewport().mapToGlobal(position))

        # Handle the selected action
        if action == edit_action:
            self._edit_match(match_id)
        elif action == delete_action:
            self._delete_match(match_id)
        elif action == duplicate_action:
            self._duplicate_match(match_id)

    def _edit_match(self, match_id):
        """Edit a match with the given ID."""
        # Get match data from database
        match_data = self.matches_manager.get_match(match_id)
        if not match_data:
            QMessageBox.warning(self, self.tr("Error"), self.tr("Failed to load match data."))
            return

        # Switch to edit mode
        self.edit_radio.setChecked(True)

        # Set current match ID
        self.current_match_id = match_id

        # Populate form with match data
        self._populate_match_form(match_data)

    def _delete_match(self, match_id):
        """Delete a match with the given ID."""
        # Confirm deletion
        result = QMessageBox.question(
            self,
            self.tr("Confirm Deletion"),
            self.tr("Are you sure you want to delete this match?"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if result == QMessageBox.StandardButton.Yes:
            # Delete match from database
            success = self.matches_manager.delete_match(match_id)

            if success:
                QMessageBox.information(self, self.tr("Success"), self.tr("Match deleted successfully."))
                # Refresh match list
                self._load_matches()
            else:
                QMessageBox.warning(self, self.tr("Error"), self.tr("Failed to delete match."))

    def _duplicate_match(self, match_id):
        """Duplicate a match with the given ID."""
        # Get match data from database
        match_data = self.matches_manager.get_match(match_id)
        if not match_data:
            QMessageBox.warning(self, self.tr("Error"), self.tr("Failed to load match data."))
            return

        # Remove the match_id to create a new match
        if 'match_id' in match_data:
            del match_data['match_id']

        # Switch to edit mode
        self.edit_radio.setChecked(True)

        # Clear current match ID to create a new match
        self.current_match_id = None

        # Populate form with match data
        self._populate_match_form(match_data)

        # Show a message to the user
        QMessageBox.information(
            self,
            self.tr("Duplicate Match"),
            self.tr("This is a duplicate of the selected match. Make any necessary changes and click Save to create a new match.")
        )

    def _on_table_selection_changed(self):
        """Handle table selection change to enable/disable buttons."""
        # Check if any row is selected
        has_selection = len(self.match_table.selectedItems()) > 0

        # Enable/disable buttons based on selection
        self.edit_btn_top.setEnabled(has_selection)
        self.delete_btn_browse.setEnabled(has_selection)
        self.duplicate_btn.setEnabled(has_selection)

    def _on_edit_btn_clicked(self):
        """Handle edit button click from the top controls."""
        # Get the selected row
        indexes = self.match_table.selectedIndexes()
        if not indexes:
            return

        # Get the match ID from the selected row
        item = self.match_table.item(indexes[0].row(), indexes[0].column())
        if not item:
            return

        match_id = item.data(Qt.ItemDataRole.UserRole)
        if not match_id:
            return

        # Edit the match
        self._edit_match(match_id)

    def _on_delete_btn_clicked(self):
        """Handle delete button click from the top controls."""
        # Get the selected row
        indexes = self.match_table.selectedIndexes()
        if not indexes:
            return

        # Get the match ID from the selected row
        item = self.match_table.item(indexes[0].row(), indexes[0].column())
        if not item:
            return

        match_id = item.data(Qt.ItemDataRole.UserRole)
        if not match_id:
            return

        # Delete the match
        self._delete_match(match_id)

    def _on_duplicate_btn_clicked(self):
        """Handle duplicate button click from the top controls."""
        # Get the selected row
        indexes = self.match_table.selectedIndexes()
        if not indexes:
            return

        # Get the match ID from the selected row
        item = self.match_table.item(indexes[0].row(), indexes[0].column())
        if not item:
            return

        match_id = item.data(Qt.ItemDataRole.UserRole)
        if not match_id:
            return

        # Duplicate the match
        self._duplicate_match(match_id)

    def _populate_match_form(self, match_data):
        """Populate the match form with data from the database."""
        # Set match ID
        self.match_id_field.setText(str(match_data.get('match_id', '')))

        # Set date and time
        date_str = match_data.get('date', '')
        if date_str:
            self.match_date.setDate(QDate.fromString(date_str, "yyyy-MM-dd"))

        time_str = match_data.get('time', '')
        if time_str:
            self.match_time.setTime(QTime.fromString(time_str, "HH:mm"))

        # Set competition
        comp_id = match_data.get('competition_id', '')
        if comp_id:
            index = self.competition_field.findData(comp_id)
            if index >= 0:
                self.competition_field.setCurrentIndex(index)

        # Set season
        season = match_data.get('season', '')
        if season:
            index = self.season_field.findText(season)
            if index >= 0:
                self.season_field.setCurrentIndex(index)

        # Set matchday
        self.matchday_field.setText(match_data.get('matchday', ''))

        # Set home/away
        is_home = match_data.get('is_home', False)
        if is_home:
            self.home_radio.setChecked(True)
        else:
            self.away_radio.setChecked(True)

        # Update team names in statistics sections
        self._update_team_names_in_stats()

        # Set opponent
        self.opponent_field.setText(match_data.get('opponent', ''))

        # Set scores
        self.team_score.setValue(match_data.get('team_score', 0))
        self.opponent_score.setValue(match_data.get('opponent_score', 0))

        # Set half-time scores
        self.ht_team_score.setValue(match_data.get('ht_team_score', 0))
        self.ht_opponent_score.setValue(match_data.get('ht_opponent_score', 0))

        # Set extra time
        et_played = match_data.get('et_played', False)
        self.extra_time_check.setChecked(et_played)
        if et_played:
            self.et_team_score.setValue(match_data.get('et_team_score', 0))
            self.et_opponent_score.setValue(match_data.get('et_opponent_score', 0))
            self.et_duration_spinner.setValue(match_data.get('et_duration', 30))

        # Set penalties
        penalties_played = match_data.get('penalties_played', False)
        self.penalties_check.setChecked(penalties_played)
        if penalties_played:
            self.penalties_team_score.setValue(match_data.get('penalties_team_score', 0))
            self.penalties_opponent_score.setValue(match_data.get('penalties_opponent_score', 0))

        # Set match duration
        self.match_duration_value.setText(str(match_data.get('match_duration', 90)))
        self.stoppage_time_spinner.setValue(match_data.get('stoppage_time', 0))

        # Set venue information
        self.venue_field.setText(match_data.get('venue', ''))
        self.city_field.setText(match_data.get('city', ''))

        # Set country
        country = match_data.get('country', '')
        if country:
            index = self.country_field.findText(country)
            if index >= 0:
                self.country_field.setCurrentIndex(index)

        # Set weather
        weather = match_data.get('weather', '')
        if weather:
            index = self.weather_combo.findText(weather)
            if index >= 0:
                self.weather_combo.setCurrentIndex(index)

        # Set attendance
        self.attendance_field.setValue(match_data.get('attendance', 0))

        # Set broadcast
        self.broadcast_field.setText(match_data.get('broadcast', ''))

        # Set officials
        officials = match_data.get('officials', [])
        # Clear all referee fields first
        for field in self.referee_fields:
            field.clear()

        # Populate referee fields from officials data
        for i, official in enumerate(officials):
            if i < len(self.referee_fields):
                self.referee_fields[i].setText(official.get('name', ''))

        # Set officials rating and comments
        officials_rating = match_data.get('officials_rating', {})
        if officials_rating:
            # Check if officials_rating is a dict or a direct value
            if isinstance(officials_rating, dict):
                self.referee_rating.setValue(officials_rating.get('rating', 5))
                self.referee_comments.setPlainText(officials_rating.get('comments', ''))
            else:
                # If it's a direct value (e.g., integer), use it for the rating
                self.referee_rating.setValue(officials_rating)
                # Get comments from a separate field if available
                self.referee_comments.setPlainText(match_data.get('officials_comments', ''))

        # Load match statistics if available
        match_stats = match_data.get('match_stats', {})
        if match_stats:
            self._populate_match_statistics(match_stats)

        # Load player stats if available
        player_stats = match_data.get('player_stats', [])
        if player_stats:
            self._load_player_stats_data(player_stats)

        # Update result label
        self._update_result_label()

        # Update final score in statistics
        self._update_final_score()

        # Update match duration calculation
        self._update_match_duration_calculation()

    def _on_delete_match(self):
        """Handle delete match button click."""
        if not self.current_match_id:
            return

        # Confirm deletion
        result = QMessageBox.question(
            self,
            self.tr("Confirm Deletion"),
            self.tr("Are you sure you want to delete this match?"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if result == QMessageBox.StandardButton.Yes:
            # Delete match from database
            success = self.matches_manager.delete_match(self.current_match_id)

            if success:
                QMessageBox.information(self, self.tr("Success"), self.tr("Match deleted successfully."))
            else:
                QMessageBox.warning(self, self.tr("Error"), self.tr("Failed to delete match."))
                return

            # Switch back to browse mode
            self.browse_radio.setChecked(True)

            # Refresh match list
            self._load_matches()

    def _validate_match_data(self):
        """Validate match form data."""
        # Check required fields
        if not self.opponent_field.text().strip():
            QMessageBox.warning(self, self.tr("Validation Error"), self.tr("Opponent name is required."))
            return False

        # Validate competition is selected
        if self.competition_field.currentText() == "":
            QMessageBox.warning(self, self.tr("Validation Error"), self.tr("Competition must be selected."))
            return False

        # Validate season is selected
        if self.season_field.currentText() == "":
            QMessageBox.warning(self, self.tr("Validation Error"), self.tr("Season must be selected."))
            return False

        # Validate date is within season
        match_date = self.match_date.date()
        season_start, season_end = self._get_season_dates()

        if match_date < season_start or match_date > season_end:
            QMessageBox.warning(
                self,
                self.tr("Validation Error"),
                self.tr("Match date must be within the season date range ({} to {}).").format(
                    season_start.toString("yyyy-MM-dd"),
                    season_end.toString("yyyy-MM-dd")
                )
            )
            return False

        # Validate season format
        selected_season = self.season_field.currentText()
        season_parts = selected_season.split('/')
        if len(season_parts) != 2:
            QMessageBox.warning(
                self,
                self.tr("Validation Error"),
                self.tr("Season must be in the format 'YYYY/YYYY'.")
            )
            return False

        try:
            start_year = int(season_parts[0])
            end_year = int(season_parts[1])

            # Check if the season years are valid based on season dates
            season_start_year = season_start.year() - 1  # One year before season start
            season_end_year = season_end.year() + 1      # One year after season end

            if start_year < season_start_year or end_year > season_end_year:
                QMessageBox.warning(
                    self,
                    self.tr("Validation Error"),
                    self.tr("Season years must be within the range of {} to {}.").format(
                        season_start_year, season_end_year
                    )
                )
                return False

            # Check if end year is start year + 1
            if end_year != start_year + 1:
                QMessageBox.warning(
                    self,
                    self.tr("Validation Error"),
                    self.tr("Season end year must be one year after the start year.")
                )
                return False
        except ValueError:
            QMessageBox.warning(
                self,
                self.tr("Validation Error"),
                self.tr("Season years must be valid numbers.")
            )
            return False

        # No validation needed for penalties without extra time
        # We now allow penalty shootouts without extra time for certain competitions

        return True

    def _clear_match_form(self):
        """Clear all form fields."""
        self.match_id_field.clear()

        # Set date to today if within season range, otherwise to season start
        season_start, season_end = self._get_season_dates()
        today = QDate.currentDate()
        if today >= season_start and today <= season_end:
            self.match_date.setDate(today)
        else:
            self.match_date.setDate(season_start)

        self.match_time.setTime(QTime(18, 0))
        self.competition_field.setCurrentIndex(0)
        # Don't reset the season field to keep the default season
        # self.season_field.setCurrentIndex(0)
        self.matchday_field.clear()

        self.home_radio.setChecked(True)
        self.opponent_field.clear()

        # Auto-populate venue and city since Home is selected
        self._populate_venue_from_club_data()

        self.team_score.setValue(0)
        self.opponent_score.setValue(0)

        # Set half-time to checked by default and clear scores
        self.halftime_check.setChecked(True)
        self.ht_team_score.setValue(0)
        self.ht_opponent_score.setValue(0)
        self.ht_team_score.setEnabled(True)
        self.ht_opponent_score.setEnabled(True)
        # Apply visual styling
        self._apply_disabled_style(self.ht_team_score, False)
        self._apply_disabled_style(self.ht_opponent_score, False)
        # Force update the enabled state
        self.ht_team_score.repaint()
        self.ht_opponent_score.repaint()

        # Set extra time to unchecked and disable score fields
        self.extra_time_check.setChecked(False)
        self.et_team_score.setValue(0)
        self.et_opponent_score.setValue(0)
        self.et_team_score.setEnabled(False)
        self.et_opponent_score.setEnabled(False)
        # Apply visual styling
        self._apply_disabled_style(self.et_team_score, True)
        self._apply_disabled_style(self.et_opponent_score, True)
        # Force update the enabled state
        self.et_team_score.repaint()
        self.et_opponent_score.repaint()

        # Set penalties to unchecked and disable score fields
        self.penalties_check.setChecked(False)
        self.penalties_team_score.setValue(0)
        self.penalties_opponent_score.setValue(0)
        self.penalties_team_score.setEnabled(False)
        self.penalties_opponent_score.setEnabled(False)
        # Apply visual styling
        self._apply_disabled_style(self.penalties_team_score, True)
        self._apply_disabled_style(self.penalties_opponent_score, True)
        # Force update the enabled state
        self.penalties_team_score.repaint()
        self.penalties_opponent_score.repaint()

        # We don't clear venue and city fields here anymore
        # They will be populated by _populate_venue_from_club_data() based on Home/Away selection

        # Set country to club country if available, otherwise clear it
        if self.club_data_manager:
            club_country = self.club_data_manager.get_data("country")
            self.logger.info(f"Setting match country field from club data: '{club_country}'")
            if club_country:
                # Find the index of the club country in the combo box
                index = self.country_field.findText(club_country)
                if index >= 0:
                    self.logger.info(f"Found club country '{club_country}' at index {index}")
                    self.country_field.setCurrentIndex(index)
                else:
                    # If not found in the list, set it as custom text if the combo is editable
                    self.logger.info(f"Club country '{club_country}' not found in list, using as custom text")
                    if self.country_field.isEditable():
                        self.country_field.setEditText(club_country)
                    else:
                        self.country_field.setCurrentIndex(0)
            else:
                self.logger.info("No club country found, clearing country field")
                self.country_field.setCurrentIndex(0)
        else:
            self.logger.info("No club data manager available, clearing country field")
            self.country_field.setCurrentIndex(0)

        self.weather_combo.setCurrentIndex(0)
        self.attendance_field.setValue(0)
        self.broadcast_field.clear()

        for field in self.referee_fields:
            field.clear()

        self.referee_rating.setValue(5)
        self.referee_comments.clear()

        # Reset match duration fields
        self.stoppage_time_spinner.setValue(0)
        self.stoppage_time_spinner.setStyleSheet("")
        self.stoppage_time_spinner.setToolTip("")

        # Reset extra time duration
        if hasattr(self, 'et_duration_spinner'):
            self.et_duration_spinner.setValue(30)

        # Reset match statistics
        self._reset_match_statistics()

        # Clear player stats table
        self.player_stats_table.setRowCount(0)

        # Update match duration from competition
        self._on_competition_changed(0)

        # Update full match calculation
        self._update_match_duration_calculation()

    def _on_halftime_toggled(self, checked):
        """Handle half-time checkbox toggle."""
        # Check if the attributes exist before trying to use them
        if hasattr(self, 'ht_team_score') and hasattr(self, 'ht_opponent_score'):
            # Enable/disable half-time score fields
            self.ht_team_score.setEnabled(checked)
            self.ht_opponent_score.setEnabled(checked)

            # Apply visual styling to make disabled state more obvious
            self._apply_disabled_style(self.ht_team_score, not checked)
            self._apply_disabled_style(self.ht_opponent_score, not checked)

            # If unchecked, clear the values
            if not checked:
                self.ht_team_score.setValue(0)
                self.ht_opponent_score.setValue(0)

            # Force update the enabled state to ensure it takes effect
            self.ht_team_score.repaint()
            self.ht_opponent_score.repaint()

    def _on_extra_time_toggled(self, checked):
        """Handle extra time checkbox toggle."""
        # Check if the attributes exist before trying to use them
        if hasattr(self, 'et_team_score') and hasattr(self, 'et_opponent_score'):
            # Enable/disable extra time score fields
            self.et_team_score.setEnabled(checked)
            self.et_opponent_score.setEnabled(checked)

            # Apply visual styling to make disabled state more obvious
            self._apply_disabled_style(self.et_team_score, not checked)
            self._apply_disabled_style(self.et_opponent_score, not checked)

            # If unchecked, clear the values
            if not checked:
                self.et_team_score.setValue(0)
                self.et_opponent_score.setValue(0)

                # If extra time is unchecked, penalties must also be unchecked
                if hasattr(self, 'penalties_check') and self.penalties_check.isChecked():
                    self.penalties_check.setChecked(False)

            # Show/hide the extra time duration field
            if hasattr(self, 'et_duration_widget'):
                self.et_duration_widget.setVisible(checked)

            # Force update the enabled state to ensure it takes effect
            self.et_team_score.repaint()
            self.et_opponent_score.repaint()

            # Update result label
            if hasattr(self, '_update_result_label'):
                self._update_result_label()

            # Update match duration calculation
            if hasattr(self, '_update_match_duration_calculation'):
                self._update_match_duration_calculation()

    def _on_penalties_toggled(self, checked):
        """Handle penalties checkbox toggle."""
        # Check if the attributes exist before trying to use them
        if hasattr(self, 'penalties_team_score') and hasattr(self, 'penalties_opponent_score'):
            # Enable/disable penalty score fields
            self.penalties_team_score.setEnabled(checked)
            self.penalties_opponent_score.setEnabled(checked)

            # Apply visual styling to make disabled state more obvious
            self._apply_disabled_style(self.penalties_team_score, not checked)
            self._apply_disabled_style(self.penalties_opponent_score, not checked)

            # If unchecked, clear the values
            if not checked:
                self.penalties_team_score.setValue(0)
                self.penalties_opponent_score.setValue(0)

            # If checked and extra time is not checked, ask user if they want to enable extra time
            if checked and hasattr(self, 'extra_time_check') and not self.extra_time_check.isChecked():
                # Show confirmation dialog
                response = QMessageBox.question(
                    self,
                    self.tr("Enable Extra Time?"),
                    self.tr("Normally, penalty shootouts occur after extra time.\nDo you want to enable extra time as well?"),
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                # If user clicks Yes, enable extra time
                if response == QMessageBox.StandardButton.Yes:
                    self.extra_time_check.setChecked(True)

            # Force update the enabled state to ensure it takes effect
            self.penalties_team_score.repaint()
            self.penalties_opponent_score.repaint()

            # Update result label
            if hasattr(self, '_update_result_label'):
                self._update_result_label()

    def _populate_venue_from_club_data(self):
        """Populate venue and city fields from club data."""
        # Check if we have access to club data manager
        if hasattr(self, 'club_data_manager') and self.club_data_manager:
            # Get stadium name and city from club data
            stadium_name = self.club_data_manager.get_data("stadium_name", "")
            city = self.club_data_manager.get_data("city", "")

            # Set the venue and city fields
            if stadium_name:
                self.venue_field.setText(stadium_name)
            if city:
                self.city_field.setText(city)

            # Log the action
            if hasattr(self, 'logger'):
                self.logger.info(f"Auto-populated venue '{stadium_name}' and city '{city}' from club data")

    def _on_home_away_toggled(self, checked):
        """Handle home/away radio button toggle."""
        # Only proceed if the home radio button is checked (not when unchecked)
        if checked and self.home_radio.isChecked():
            # Use the common method to populate venue and city
            self._populate_venue_from_club_data()

            # Update team names in statistics section
            self._update_team_names_in_stats()
        elif not checked and self.away_radio.isChecked():
            # Clear venue and city fields when switching to Away
            self.venue_field.clear()
            self.city_field.clear()

            # Update team names in statistics section
            self._update_team_names_in_stats()

            # Log the action
            if hasattr(self, 'logger'):
                self.logger.info("Cleared venue and city fields when switching to Away")

    def _update_team_names_in_stats(self):
        """Update the team name labels in the statistics section based on Home/Away selection."""
        # Get club name from club data manager
        club_name = ""
        if hasattr(self, 'club_data_manager') and self.club_data_manager:
            club_name = self.club_data_manager.get_data("club_name", "")
            if not club_name:
                club_name = self.tr("My Team")
        else:
            club_name = self.tr("My Team")

        # Get opponent name from the opponent field
        opponent_name = self.opponent_field.text().strip()
        if not opponent_name:
            opponent_name = self.tr("Opponent")

        # Set the labels based on Home/Away selection
        if self.home_radio.isChecked():
            # Home: Club name on left, opponent on right
            self.team_name_label.setText(club_name)
            self.opponent_name_label.setText(opponent_name)

            # Also update advanced statistics labels
            if hasattr(self, 'adv_team_name_label') and hasattr(self, 'adv_opponent_name_label'):
                self.adv_team_name_label.setText(club_name)
                self.adv_opponent_name_label.setText(opponent_name)
        else:
            # Away: Opponent on left, club name on right
            self.team_name_label.setText(opponent_name)
            self.opponent_name_label.setText(club_name)

            # Also update advanced statistics labels
            if hasattr(self, 'adv_team_name_label') and hasattr(self, 'adv_opponent_name_label'):
                self.adv_team_name_label.setText(opponent_name)
                self.adv_opponent_name_label.setText(club_name)

    def _validate_match_date(self):
        """Ensure the date picker has the correct min/max constraints."""
        # Update min/max dates based on current season settings
        season_start, season_end = self._get_season_dates()

        # Temporarily disconnect the signal to prevent recursive calls
        self.match_date.dateChanged.disconnect(self._validate_match_date)

        # Update the min/max date constraints
        self.match_date.setMinimumDate(season_start)
        self.match_date.setMaximumDate(season_end)

        # Check if current date is outside the range and adjust if needed
        current_date = self.match_date.date()
        if current_date < season_start:
            self.match_date.setDate(season_start)
        elif current_date > season_end:
            self.match_date.setDate(season_end)

        # Reconnect the signal
        self.match_date.dateChanged.connect(self._validate_match_date)

    def _update_result_label(self):
        """Update the result label based on scores and home/away status.

        Priority order for determining result:
        1. Penalty shootout (if played)
        2. Extra time score (if played)
        3. Full time score

        Result is always from the perspective of the club (not the team selection in UI).
        When "Away" is selected, the team and opponent scores are interpreted in reverse.
        """
        # Get scores
        team_score = self.team_score.value()
        opponent_score = self.opponent_score.value()

        # Get home/away status
        is_home = self.home_radio.isChecked()

        # When "Away" is selected, we need to swap the interpretation of scores
        # because the UI shows "team" on the left and "opponent" on the right,
        # but when we're away, "team" is actually our club and "opponent" is the home team
        if not is_home:
            # Swap the scores for result calculation
            my_score = opponent_score
            their_score = team_score
        else:
            # Home - use scores as they are
            my_score = team_score
            their_score = opponent_score

        # Determine which scores to compare based on priority
        # 1. Penalty shootout (highest priority)
        if self.penalties_check.isChecked():
            team_pens = self.penalties_team_score.value()
            opp_pens = self.penalties_opponent_score.value()

            # Swap penalty scores too if away
            if not is_home:
                my_pens = opp_pens
                their_pens = team_pens
            else:
                my_pens = team_pens
                their_pens = opp_pens

            # Check if penalty scores are equal
            if my_pens == their_pens:
                # Invalid state - penalties must have a winner
                result = self.tr("Invalid Result")
                color = QColor(255, 165, 0)  # Orange for warning
                self.result_label.setToolTip(self.tr("Penalty shootout must have a winner. Please set different penalty scores."))
                self.result_label.setText(f"<b style='color:{color.name()};'>{result}</b>")
                return

            # Determine result based on penalty scores
            if my_pens > their_pens:
                # Team won on penalties
                result = self.tr("Win (Penalties)")
                color = get_color("match_win")
            else:
                # Team lost on penalties
                result = self.tr("Loss (Penalties)")
                color = get_color("match_loss")

        # 2. Extra time score (second priority)
        elif self.extra_time_check.isChecked():
            et_team_score = self.et_team_score.value()
            et_opp_score = self.et_opponent_score.value()

            # Swap extra time scores too if away
            if not is_home:
                my_et_score = et_opp_score
                their_et_score = et_team_score
            else:
                my_et_score = et_team_score
                their_et_score = et_opp_score

            if my_et_score > their_et_score:
                # Team won in extra time
                result = self.tr("Win (ET)")
                color = get_color("match_win")
            elif my_et_score < their_et_score:
                # Team lost in extra time
                result = self.tr("Loss (ET)")
                color = get_color("match_loss")
            else:
                # Draw after extra time (should not happen in most competitions)
                result = self.tr("Draw (ET)")
                color = get_color("match_draw")

        # 3. Full time score (lowest priority)
        else:
            if my_score > their_score:
                # Team won in regular time
                result = self.tr("Win")
                color = get_color("match_win")
            elif my_score < their_score:
                # Team lost in regular time
                result = self.tr("Loss")
                color = get_color("match_loss")
            else:
                # Draw in regular time
                result = self.tr("Draw")
                color = get_color("match_draw")

        # Clear any previous tooltip
        self.result_label.setToolTip("")

        # Set the result text with color
        self.result_label.setText(f"<b style='color:{color.name()};'>{result}</b>")

    def _get_season_dates(self, _=None):
        """Get start and end dates for the selected season from settings."""
        settings = QSettings()

        # Get start date from settings
        settings.beginGroup("season_dates")
        start_date_str = settings.value("start_date", "")
        end_date_str = settings.value("end_date", "")
        settings.endGroup()

        # Parse dates
        start_date = QDate.fromString(start_date_str, "yyyy-MM-dd") if start_date_str else QDate.currentDate()
        end_date = QDate.fromString(end_date_str, "yyyy-MM-dd") if end_date_str else QDate.currentDate().addYears(1)

        self.logger.info(f"Season dates: {start_date.toString('yyyy-MM-dd')} to {end_date.toString('yyyy-MM-dd')}")
        return start_date, end_date

    def _on_filter_changed(self, _=None):
        """Handle filter changes and reload matches."""
        # Add a small delay to avoid multiple reloads when multiple filters change
        # This is especially useful for date filters
        if hasattr(self, '_filter_timer'):
            self._filter_timer.stop()
        else:
            self._filter_timer = QTimer()
            self._filter_timer.setSingleShot(True)
            self._filter_timer.timeout.connect(self._load_matches)

        # Start the timer with a short delay (200ms)
        self._filter_timer.start(200)

    def _on_search_text_changed(self, _):
        """Handle search text changes with a delay to avoid reloading on every keystroke."""
        # Add a longer delay for search to avoid reloading while typing
        if hasattr(self, '_search_timer'):
            self._search_timer.stop()
        else:
            self._search_timer = QTimer()
            self._search_timer.setSingleShot(True)
            self._search_timer.timeout.connect(self._load_matches)

        # Start the timer with a longer delay (500ms)
        self._search_timer.start(500)

    def _clear_date_filters(self):
        """Clear the date range filters."""
        # Reset from date to 3 months ago
        self.from_date.setDate(QDate.currentDate().addMonths(-3))

        # Reset to date to today
        self.to_date.setDate(QDate.currentDate())

        # Reload matches
        self._load_matches()

    def _clear_all_filters(self):
        """Clear all filters and reload matches."""
        # Reset season filter
        self.season_combo.setCurrentIndex(0)  # "All Seasons"

        # Reset competition filter
        self.competition_combo.setCurrentIndex(0)  # "All Competitions"

        # Reset result filter
        self.result_combo.setCurrentIndex(0)  # "All Results"

        # Reset home/away filter
        self.home_away_combo.setCurrentIndex(0)  # "All Matches"

        # Reset match finish filter
        self.finish_combo.setCurrentIndex(0)  # "All Types"

        # Reset date filters
        self._clear_date_filters()

        # Clear search box
        self.search_box.clear()

        # Reload matches
        self._load_matches()

    def _load_matches(self):
        """Load matches into the table."""
        # Clear the table
        self.match_table.setRowCount(0)

        # Get filters from UI
        filters = {}

        # Apply competition filter if selected
        if self.competition_combo.currentData() != "all":
            filters["competition_id"] = self.competition_combo.currentData()

        # Apply season filter if selected
        if self.season_combo.currentData() != "all":
            filters["season"] = self.season_combo.currentData()

        # Apply search filter if entered
        if self.search_box.text().strip():
            filters["search"] = self.search_box.text().strip()

        # Apply date range filter
        from_date = self.from_date.date().toString("yyyy-MM-dd")
        to_date = self.to_date.date().toString("yyyy-MM-dd")
        filters["date_from"] = from_date
        filters["date_to"] = to_date

        # Get matches from database
        matches = self.matches_manager.get_matches(filters=filters)

        # Apply result filter (client-side since it's based on calculated values)
        result_filter = self.result_combo.currentData()
        if result_filter != "all":
            filtered_matches = []
            for match in matches:
                # Determine match result
                team_score = match.get('team_score', 0)
                opponent_score = match.get('opponent_score', 0)

                # Check for penalties or extra time
                if match.get('penalties_played', False):
                    penalties_team = match.get('penalties_team_score', 0)
                    penalties_opponent = match.get('penalties_opponent_score', 0)
                    if (result_filter == "win" and penalties_team > penalties_opponent) or \
                       (result_filter == "loss" and penalties_team < penalties_opponent):
                        filtered_matches.append(match)
                elif match.get('et_played', False):
                    et_team = match.get('et_team_score', 0)
                    et_opponent = match.get('et_opponent_score', 0)
                    if (result_filter == "win" and et_team > et_opponent) or \
                       (result_filter == "loss" and et_team < et_opponent) or \
                       (result_filter == "draw" and et_team == et_opponent):
                        filtered_matches.append(match)
                else:
                    # Regular time result
                    if (result_filter == "win" and team_score > opponent_score) or \
                       (result_filter == "loss" and team_score < opponent_score) or \
                       (result_filter == "draw" and team_score == opponent_score):
                        filtered_matches.append(match)

            # Replace matches with filtered matches
            matches = filtered_matches

        # Apply Home/Away filter
        home_away_filter = self.home_away_combo.currentData()
        if home_away_filter != "all":
            filtered_matches = []
            for match in matches:
                is_home = match.get('is_home', False)
                if (home_away_filter == "home" and is_home) or (home_away_filter == "away" and not is_home):
                    filtered_matches.append(match)
            matches = filtered_matches

        # Apply Match Finish filter
        finish_filter = self.finish_combo.currentData()
        if finish_filter != "all":
            filtered_matches = []
            for match in matches:
                if finish_filter == "pen" and match.get('penalties_played', False):
                    filtered_matches.append(match)
                elif finish_filter == "et" and match.get('et_played', False) and not match.get('penalties_played', False):
                    filtered_matches.append(match)
                elif finish_filter == "ft" and not match.get('et_played', False) and not match.get('penalties_played', False):
                    filtered_matches.append(match)
            matches = filtered_matches

        if not matches:
            self.logger.info("No matches found with the current filters")
            return

        # Add matches to the table
        for row, match in enumerate(matches):
            self.match_table.insertRow(row)

            # Date column
            date_str = match.get('date', '')
            if date_str:
                date = QDate.fromString(date_str, "yyyy-MM-dd")
                date_item = QTableWidgetItem(date.toString("MM/dd/yyyy"))
            else:
                date_item = QTableWidgetItem("")
            self.match_table.setItem(row, 0, date_item)

            # Competition column
            comp_id = match.get('competition_id', '')
            comp_name = ""
            if comp_id:
                # Find competition name
                competitions = self.matches_manager.get_competitions()
                for comp in competitions:
                    if comp.get('id') == comp_id:
                        comp_name = comp.get('name', '')
                        break
            comp_item = QTableWidgetItem(comp_name)
            self.match_table.setItem(row, 1, comp_item)

            # Opponent column
            opponent = match.get('opponent', '')
            opponent_item = QTableWidgetItem(opponent)
            self.match_table.setItem(row, 2, opponent_item)

            # Home/Away column
            is_home = match.get('is_home', False)
            home_away_text = self.tr("Home") if is_home else self.tr("Away")
            home_away_item = QTableWidgetItem(home_away_text)
            self.match_table.setItem(row, 3, home_away_item)

            # Score column
            team_score = match.get('team_score', 0)
            opponent_score = match.get('opponent_score', 0)

            # Format score based on home/away
            if is_home:
                score_text = f"{team_score} - {opponent_score}"
            else:
                score_text = f"{opponent_score} - {team_score}"

            # Add extra time and penalties if applicable
            if match.get('penalties_played', False):
                penalties_team = match.get('penalties_team_score', 0)
                penalties_opponent = match.get('penalties_opponent_score', 0)
                if is_home:
                    score_text += f" (Pen: {penalties_team}-{penalties_opponent})"
                else:
                    score_text += f" (Pen: {penalties_opponent}-{penalties_team})"
            elif match.get('et_played', False):
                et_team = match.get('et_team_score', 0)
                et_opponent = match.get('et_opponent_score', 0)
                if is_home:
                    score_text += f" (ET: {et_team}-{et_opponent})"
                else:
                    score_text += f" (ET: {et_opponent}-{et_team})"

            score_item = QTableWidgetItem(score_text)
            self.match_table.setItem(row, 4, score_item)

            # Result column
            result_text = ""
            result_color = None

            # Determine result based on scores and home/away
            if match.get('penalties_played', False):
                # Penalty shootout result
                penalties_team = match.get('penalties_team_score', 0)
                penalties_opponent = match.get('penalties_opponent_score', 0)
                if penalties_team > penalties_opponent:
                    result_text = self.tr("Win (Penalties)")
                    result_color = get_color("match_win")
                else:
                    result_text = self.tr("Loss (Penalties)")
                    result_color = get_color("match_loss")
            elif match.get('et_played', False):
                # Extra time result
                et_team = match.get('et_team_score', 0)
                et_opponent = match.get('et_opponent_score', 0)
                if et_team > et_opponent:
                    result_text = self.tr("Win (ET)")
                    result_color = get_color("match_win")
                elif et_team < et_opponent:
                    result_text = self.tr("Loss (ET)")
                    result_color = get_color("match_loss")
                else:
                    result_text = self.tr("Draw (ET)")
                    result_color = get_color("match_draw")
            else:
                # Regular time result
                if team_score > opponent_score:
                    result_text = self.tr("Win")
                    result_color = get_color("match_win")
                elif team_score < opponent_score:
                    result_text = self.tr("Loss")
                    result_color = get_color("match_loss")
                else:
                    result_text = self.tr("Draw")
                    result_color = get_color("match_draw")

            result_item = QTableWidgetItem(result_text)
            if result_color:
                result_item.setForeground(QBrush(result_color))
            self.match_table.setItem(row, 5, result_item)

            # Match Finish column
            finish_text = ""
            if match.get('penalties_played', False):
                finish_text = self.tr("Penalties")
            elif match.get('et_played', False):
                finish_text = self.tr("Extra Time")
            else:
                finish_text = self.tr("Full Time")

            finish_item = QTableWidgetItem(finish_text)
            self.match_table.setItem(row, 6, finish_item)

            # Store match_id as data in each item for the row
            match_id = match.get('match_id')
            for col in range(7):  # Updated to include all 7 columns
                item = self.match_table.item(row, col)
                if item:
                    item.setData(Qt.ItemDataRole.UserRole, match_id)

        # Resize columns to content
        self.match_table.resizeColumnsToContents()

        # Apply current sort
        if self.match_table.rowCount() > 0:
            self.match_table.horizontalHeader().setSortIndicator(self.sort_column, self.sort_order)
            self.match_table.sortItems(self.sort_column, self.sort_order)

        # Log the number of matches loaded
        self.logger.info(f"Loaded {len(matches)} matches")

    def _load_competitions(self):
        """Load competitions into the combo boxes from settings."""
        self.competition_field.clear()
        self.competition_combo.clear()

        # Add empty item for competition field
        self.competition_field.addItem("")

        # Add "All Competitions" for filter combo
        self.competition_combo.addItem(self.tr("All Competitions"), "all")

        # Get competitions from manager
        competitions = self.matches_manager.get_competitions()

        # Add competitions to combo boxes
        for comp in competitions:
            name = comp.get('name', '')
            if name:
                self.competition_field.addItem(name, comp.get('id', ''))
                self.competition_combo.addItem(name, comp.get('id', ''))

    def _load_seasons(self):
        """Load seasons into the combo boxes based on season dates."""
        self.season_field.clear()
        self.season_combo.clear()

        # Add empty item for season field
        self.season_field.addItem("")

        # Add "All Seasons" for filter combo
        self.season_combo.addItem(self.tr("All Seasons"), "all")

        # Get season dates
        season_start, season_end = self._get_season_dates()

        # Calculate valid seasons based on season dates
        # For example, if season is from May 2024 to May 2025,
        # valid seasons are 2023-2024, 2024-2025, 2025-2026
        start_year = season_start.year() - 1  # One year before season start
        end_year = season_end.year() + 1      # One year after season end

        # Determine the default season that includes both years from the season dates
        # For example, if season is from May 2024 to May 2025, default season is 2024/2025
        default_season_year = season_start.year()
        if season_start.month() <= 6 and season_end.year() > season_start.year():
            # If season starts in first half of the year, use previous year as start
            default_season_year = season_start.year() - 1
        default_season = f"{default_season_year}/{default_season_year+1}"

        # Track the index of the default season
        default_season_index = 0

        # Add valid seasons
        for year in range(start_year, end_year):
            season = f"{year}/{year+1}"
            self.season_field.addItem(season)
            self.season_combo.addItem(season, season)

            # Check if this is the default season
            if season == default_season:
                # +1 because we added an empty item at index 0
                default_season_index = self.season_field.count() - 1

        # Set the default season
        if default_season_index > 0:
            self.season_field.setCurrentIndex(default_season_index)
            self.logger.info(f"Set default season to {default_season}")

        # Get existing seasons from database for filter combo
        db_seasons = self.matches_manager.get_seasons()
        for season in db_seasons:
            if season and self.season_combo.findText(season) == -1:
                self.season_combo.addItem(season, season)

    def _load_weather_conditions(self):
        """Load weather conditions into the combo box."""
        self.weather_combo.clear()

        weather_conditions = [
            "",
            self.tr("Sunny"),
            self.tr("Partly Cloudy"),
            self.tr("Cloudy"),
            self.tr("Light Rain"),
            self.tr("Heavy Rain"),
            self.tr("Thunderstorm"),
            self.tr("Snowy"),
            self.tr("Foggy"),
            self.tr("Windy"),
            self.tr("Hot"),
            self.tr("Cold"),
            self.tr("Humid")
        ]

        for condition in weather_conditions:
            self.weather_combo.addItem(condition)

    def retranslateUi(self):
        """Update all translatable strings."""
        self.setWindowTitle(self.tr("Matches"))

        # Update UI elements with translated strings
        # TODO: Implement full translation updates

    def _apply_disabled_style(self, widget, is_disabled):
        """Apply a visual style to make disabled state more obvious."""
        if is_disabled:
            widget.setStyleSheet("background-color: #e0e0e0; color: #a0a0a0;")
        else:
            widget.setStyleSheet("")

    def _on_stoppage_time_changed(self, value):
        """Handle stoppage time value changes."""
        # Show warning if stoppage time is unusually high
        if value > 20:
            self.stoppage_time_spinner.setStyleSheet("background-color: #ffeeee;")
            self.stoppage_time_spinner.setToolTip(self.tr("Warning: Stoppage time is unusually high"))
        else:
            self.stoppage_time_spinner.setStyleSheet("")
            self.stoppage_time_spinner.setToolTip("")

        # Update the full match duration calculation
        self._update_match_duration_calculation()

    def _update_match_duration_calculation(self):
        """Update the full match duration calculation."""
        # Get the base match duration
        match_duration = int(self.match_duration_value.text())

        # Add stoppage time
        stoppage_time = self.stoppage_time_spinner.value()

        # Add extra time if enabled
        extra_time = 0
        if self.extra_time_check.isChecked() and hasattr(self, 'et_duration_spinner'):
            extra_time = self.et_duration_spinner.value()

        # Calculate total
        total_duration = match_duration + stoppage_time + extra_time

        # Update the display
        self.full_match_value.setText(str(total_duration))

        # Update minutes played for all players when match duration changes
        if hasattr(self, 'player_stats_table') and self.player_stats_table.rowCount() > 0:
            self._update_all_minutes_played()

    def _populate_match_statistics(self, match_stats):
        """Populate the match statistics fields with data from the database.

        Args:
            match_stats (dict): Dictionary with team and opponent statistics
        """
        self.logger.info(f"Populating match statistics: {match_stats}")

        # Get team and opponent stats
        team_stats = match_stats.get('team', {})
        opponent_stats = match_stats.get('opponent', {})

        # Populate basic team stats
        for stat_key, stat_value in team_stats.items():
            # Check if this is a basic statistic
            if stat_key in self.team_stats and stat_key != 'final_score':
                spinbox = self.team_stats[stat_key]

                # Handle different types of spinboxes
                if hasattr(spinbox, 'setValue'):
                    if stat_key == 'xgoals' and isinstance(spinbox, QDoubleSpinBox):
                        # Handle float values for xgoals
                        spinbox.setValue(float(stat_value))
                    else:
                        # Handle integer values for other stats
                        spinbox.setValue(int(stat_value))

            # Check if this is an advanced statistic
            elif stat_key in self.team_adv_stats:
                spinbox = self.team_adv_stats[stat_key]

                # Advanced stats are all integers
                if hasattr(spinbox, 'setValue'):
                    spinbox.setValue(int(stat_value))

        # Populate basic opponent stats
        for stat_key, stat_value in opponent_stats.items():
            # Check if this is a basic statistic
            if stat_key in self.opponent_stats and stat_key != 'final_score':
                spinbox = self.opponent_stats[stat_key]

                # Handle different types of spinboxes
                if hasattr(spinbox, 'setValue'):
                    if stat_key == 'xgoals' and isinstance(spinbox, QDoubleSpinBox):
                        # Handle float values for xgoals
                        spinbox.setValue(float(stat_value))
                    else:
                        # Handle integer values for other stats
                        spinbox.setValue(int(stat_value))

            # Check if this is an advanced statistic
            elif stat_key in self.opponent_adv_stats:
                spinbox = self.opponent_adv_stats[stat_key]

                # Advanced stats are all integers
                if hasattr(spinbox, 'setValue'):
                    spinbox.setValue(int(stat_value))

        self.logger.info("Match statistics populated successfully")

    def _reset_match_statistics(self):
        """Reset all match statistics fields to their default values."""
        self.logger.info("Resetting match statistics")

        # Reset basic team stats
        for stat_key, spinbox in self.team_stats.items():
            if stat_key != 'final_score':  # Skip final_score as it's a label
                if hasattr(spinbox, 'setValue'):
                    if stat_key == 'xgoals' and isinstance(spinbox, QDoubleSpinBox):
                        # Reset xgoals to 0.0
                        spinbox.setValue(0.0)
                    elif stat_key == 'possession':
                        # Reset possession to 50%
                        spinbox.setValue(50)
                    else:
                        # Reset other stats to 0
                        spinbox.setValue(0)

        # Reset basic opponent stats
        for stat_key, spinbox in self.opponent_stats.items():
            if stat_key != 'final_score':  # Skip final_score as it's a label
                if hasattr(spinbox, 'setValue'):
                    if stat_key == 'xgoals' and isinstance(spinbox, QDoubleSpinBox):
                        # Reset xgoals to 0.0
                        spinbox.setValue(0.0)
                    elif stat_key == 'possession':
                        # Reset possession to 50%
                        spinbox.setValue(50)
                    else:
                        # Reset other stats to 0
                        spinbox.setValue(0)

        # Reset advanced team stats
        for stat_key, spinbox in self.team_adv_stats.items():
            if hasattr(spinbox, 'setValue'):
                # All advanced stats are integers and reset to 0
                spinbox.setValue(0)

        # Reset advanced opponent stats
        for stat_key, spinbox in self.opponent_adv_stats.items():
            if hasattr(spinbox, 'setValue'):
                # All advanced stats are integers and reset to 0
                spinbox.setValue(0)

        self.logger.info("Match statistics reset successfully")

    def _update_final_score(self):
        """Update the final score in the Match Statistics section.

        Final score calculation rules:
        1. If penalties and extra time: show extra time + penalties
        2. If penalties without extra time: show full time + penalties
        3. If extra time: show extra time score
        4. Otherwise: show full time score
        """
        # Check if the final score labels exist
        if "final_score" not in self.team_stats or "final_score" not in self.opponent_stats:
            return

        # Get the home/away status
        is_home = self.home_radio.isChecked()

        # Get all scores
        team_score = self.team_score.value()
        opponent_score = self.opponent_score.value()
        et_team_score = self.et_team_score.value() if self.extra_time_check.isChecked() else 0
        et_opponent_score = self.et_opponent_score.value() if self.extra_time_check.isChecked() else 0
        penalties_team_score = self.penalties_team_score.value() if self.penalties_check.isChecked() else 0
        penalties_opponent_score = self.penalties_opponent_score.value() if self.penalties_check.isChecked() else 0

        # Calculate final scores based on the rules
        if self.penalties_check.isChecked() and self.extra_time_check.isChecked():
            # Rule 1: Penalties and extra time - show extra time + penalties
            team_final = f"{et_team_score} ({penalties_team_score})"
            opponent_final = f"{et_opponent_score} ({penalties_opponent_score})"
        elif self.penalties_check.isChecked():
            # Rule 2: Penalties without extra time - show full time + penalties
            team_final = f"{team_score} ({penalties_team_score})"
            opponent_final = f"{opponent_score} ({penalties_opponent_score})"
        elif self.extra_time_check.isChecked():
            # Rule 3: Extra time - show extra time score
            team_final = str(et_team_score)
            opponent_final = str(et_opponent_score)
        else:
            # Rule 4: Otherwise - show full time score
            team_final = str(team_score)
            opponent_final = str(opponent_score)

        # When "Away" is selected, we need to swap the display
        if not is_home:
            # Swap the scores for display
            my_final = opponent_final
            their_final = team_final
        else:
            # Home - use scores as they are
            my_final = team_final
            their_final = opponent_final

        # Update the labels
        self.team_stats["final_score"].setText(my_final)
        self.opponent_stats["final_score"].setText(their_final)

    def _on_competition_changed(self, _=None):
        """Handle competition selection changes."""
        # Get the selected competition
        comp_id = self.competition_field.currentData()

        # Find the competition in the list
        competitions = self.matches_manager.get_competitions()
        match_duration = 90  # Default value

        for comp in competitions:
            if comp.get('id') == comp_id:
                match_duration = comp.get('match_duration', 90)
                break

        # Update the match duration display
        self.match_duration_value.setText(str(match_duration))

        # Update the full match calculation
        self._update_match_duration_calculation()

    def _save_section_state(self, section_name, collapsed):
        """Save the collapsed state of a section to settings."""
        settings = QSettings()
        settings.beginGroup("matches_page")
        settings.setValue(f"section_{section_name}_collapsed", collapsed)
        settings.endGroup()

    def _load_section_states(self):
        """Load the collapsed state of all sections from settings."""
        settings = QSettings()
        settings.beginGroup("matches_page")

        # Default states - venue and conditions expanded, officials collapsed
        default_states = {
            "venue": False,
            "conditions": False,
            "officials": True
        }

        # Load states for each section
        for section_name, section in self.collapsible_sections.items():
            collapsed = settings.value(f"section_{section_name}_collapsed",
                                      default_states.get(section_name, False),
                                      bool)
            section.set_collapsed(collapsed)

        settings.endGroup()

    def closeEvent(self, event):
        """Handle window close event."""
        # Save section states before closing
        for section_name, section in self.collapsible_sections.items():
            self._save_section_state(section_name, section.is_collapsed())

        # Save header state before closing
        self._save_header_state()

        # Save player stats header state before closing
        self._save_player_stats_header_state()

        event.accept()

    def _toggle_basic_view(self, checked):
        """Toggle between basic and full view of player stats columns."""
        # Define basic columns that should be visible
        basic_columns = {
            "minutes_played", "events"
        }

        # Get header
        header = self.player_stats_table.horizontalHeader()
        header.blockSignals(True)

        try:
            # Toggle visibility of all columns
            for i, col in enumerate(self.player_stats_columns):
                if col["key"] in basic_columns:
                    # Show basic columns if checked, hide if unchecked
                    self.player_stats_table.setColumnHidden(i, not checked)
                elif col["hideable"]:  # Only toggle hideable columns
                    # Hide non-basic columns if checked, show if unchecked
                    self.player_stats_table.setColumnHidden(i, checked)

        finally:
            header.blockSignals(False)
            self._save_player_stats_header_state()  # Save the new state

    def _validate_performance_field_change(self, item, field_name):
        """Validate that performance evaluation fields are between 0 and 10."""
        # Block signals to prevent recursion
        self.player_stats_table.blockSignals(True)

        try:
            # Get the current value
            value = 0
            try:
                value = int(item.text().strip() or "0")
            except ValueError:
                value = 0
                item.setText("0")

            # Validate: value must be between 0 and 10
            if value < 0 or value > 10:
                QMessageBox.warning(
                    self,
                    self.tr("Invalid Value"),
                    self.tr(f"{field_name} must be between 0 and 10.")
                )
                # Revert to previous valid value or 0
                item.setText("0")
        finally:
            # Re-enable signals
            self.player_stats_table.blockSignals(False)

# Example usage (for testing)
if __name__ == '__main__':
    app = QApplication(sys.argv)

    # Set application info for QSettings
    QCoreApplication.setOrganizationName("FootData")
    QCoreApplication.setApplicationName("FootData")

    # Create test managers
    roster_manager = RosterManager()
    matches_manager = MatchesManager()

    # Create and show the matches page
    window = MatchesPage(roster_manager, matches_manager)
    window.resize(1200, 800)
    window.show()

    # Clean up on exit
    app.aboutToQuit.connect(roster_manager.close_db)
    app.aboutToQuit.connect(matches_manager.close_db)

    sys.exit(app.exec())
