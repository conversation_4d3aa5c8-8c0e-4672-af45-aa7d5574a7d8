from PySide6.QtWidgets import (
    QDialog, QVBox<PERSON>ayout, QLabel, QPushButton, QGroupBox, QGridLayout,
    QComboBox, QLineEdit, QCheckBox, QTabWidget, QWidget, QMessageBox,
    QRadioButton, QButtonGroup, QHBoxLayout, QFrame, QDialogButtonBox,
    QToolButton, QApplication, QScrollArea
)
from app.widgets.collapsible_group_box import CollapsibleGroupBox
from PySide6.QtCore import Qt, QSettings
from PySide6.QtGui import QIcon, QPixmap, QAction, QKeySequence, QShortcut
import logging

# Import the permission manager
from app.utils.permission_manager import get_permission_manager

# Define the loggers we want to control
LOGGERS_TO_CONTROL = {
    "Root Logger": None,
    "App": "app",
    "Database": "app.database",
    "UI": "app.ui",
    "Utils": "app.utils",
    "Models": "app.models",
    "Pages": "app.pages",
    "Windows": "app.windows",
}

# Define the log levels we want to show
LOG_LEVELS = {
    "NOTSET": logging.NOTSET,
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL
}

# Create a reverse mapping for level numbers to names
REVERSE_LOG_LEVELS = {v: k for k, v in LOG_LEVELS.items()}

class DeveloperSettingsWindow(QDialog):
    """
    A dialog for developer settings.

    This dialog allows developers to:
    1. Set the application version level
    2. Enable/disable developer mode
    3. Configure logging levels
    """

    def __init__(self, parent=None):
        super().__init__(parent)

        # Set up the dialog
        self.setWindowTitle(self.tr("Developer Settings"))
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)

        # Get the permission manager
        self.permission_manager = get_permission_manager()

        # Set up logger
        self.logger = logging.getLogger(__name__)

        # Create main layout
        self.layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget(self)

        # Create tabs
        self.dev_mode_tab = QWidget()
        self.version_level_tab = QWidget()
        self.log_levels_tab = QWidget()
        self.debug_options_tab = QWidget()
        self.reset_data_tab = QWidget()

        # Set up tabs
        self._setup_dev_mode_tab()
        self._setup_version_level_tab()
        self._setup_log_levels_tab()
        self._setup_debug_options_tab()
        self._setup_reset_data_tab()

        # Add tabs to tab widget in the requested order
        self.tab_widget.addTab(self.dev_mode_tab, self.tr("Developer Mode"))
        self.version_level_tab_index = self.tab_widget.addTab(self.version_level_tab, self.tr("Application Version Level"))
        self.log_levels_tab_index = self.tab_widget.addTab(self.log_levels_tab, self.tr("Log Levels"))
        self.debug_options_tab_index = self.tab_widget.addTab(self.debug_options_tab, self.tr("Debug Options"))
        self.reset_data_tab_index = self.tab_widget.addTab(self.reset_data_tab, self.tr("Reset Data"))

        # Hide restricted tabs if developer mode is not enabled
        self._update_tab_visibility()

        # Add tab widget to main layout
        self.layout.addWidget(self.tab_widget)

        # Restore window geometry and section states
        self._restore_window_state()

        # Add close button
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        self.layout.addWidget(button_box)
        self.close_button = button_box.button(QDialogButtonBox.StandardButton.Close)

        # Add a tip about password reset
        tip_label = QLabel(self.tr("Tip: Press Ctrl+Alt+R to reset password to default if forgotten"))
        tip_label.setStyleSheet("font-style: italic; color: #666;")
        self.layout.addWidget(tip_label)

        # Set up keyboard shortcut for password reset
        self.reset_shortcut = QShortcut(QKeySequence("Ctrl+Alt+R"), self)
        self.reset_shortcut.activated.connect(self._reset_password_to_default)

        # Translate UI
        self.retranslateUi()

    def _setup_dev_mode_tab(self):
        """Set up the Developer Mode tab with password controls."""
        dev_mode_layout = QVBoxLayout(self.dev_mode_tab)

        # Developer Mode Section
        dev_mode_groupbox = QGroupBox(self.tr("Developer Mode"))
        dev_mode_inner_layout = QVBoxLayout(dev_mode_groupbox)

        # Developer mode checkbox
        self.dev_mode_checkbox = QCheckBox(self.tr("Enable Developer Mode"))
        self.dev_mode_checkbox.setChecked(self.permission_manager.developer_mode)
        self.dev_mode_checkbox.stateChanged.connect(self._on_dev_mode_changed)
        dev_mode_inner_layout.addWidget(self.dev_mode_checkbox)

        # Password security warning
        self.password_security_warning = QLabel()
        self.password_security_warning.setWordWrap(True)
        if self.permission_manager.is_using_default_password():
            self.password_security_warning.setText(
                self.tr("Warning: Using default password. For security, please change the password.")
            )
            self.password_security_warning.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.password_security_warning.setText(
                self.tr("Using custom password. Good security practice!")
            )
            self.password_security_warning.setStyleSheet("color: green; font-weight: bold;")
        dev_mode_inner_layout.addWidget(self.password_security_warning)

        # Password section
        password_frame = QFrame()
        password_layout = QGridLayout(password_frame)

        # Create a helper method to create password fields with show/hide buttons
        def create_password_field():
            container = QWidget()
            layout = QHBoxLayout(container)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(2)

            # Create password field
            password_field = QLineEdit()
            password_field.setEchoMode(QLineEdit.EchoMode.Password)
            layout.addWidget(password_field)

            # Create show/hide button with text instead of icon
            show_button = QPushButton("Show")
            show_button.setFixedWidth(50)  # Make it wide enough for the text
            show_button.setCheckable(True)
            show_button.setToolTip(self.tr("Show/hide password"))

            # Connect show/hide functionality
            def toggle_password_visibility(checked):
                password_field.setEchoMode(
                    QLineEdit.EchoMode.Normal if checked else QLineEdit.EchoMode.Password
                )
                # Update button text based on state
                show_button.setText("Hide" if checked else "Show")

            show_button.toggled.connect(toggle_password_visibility)

            layout.addWidget(show_button)
            return container, password_field, show_button

        # Current password
        password_layout.addWidget(QLabel(self.tr("Current Password:")), 0, 0)
        current_container, self.current_password, self.current_show_button = create_password_field()
        password_layout.addWidget(current_container, 0, 1)

        # New password
        password_layout.addWidget(QLabel(self.tr("New Password:")), 1, 0)
        new_container, self.new_password, self.new_show_button = create_password_field()
        password_layout.addWidget(new_container, 1, 1)

        # Confirm new password
        password_layout.addWidget(QLabel(self.tr("Confirm Password:")), 2, 0)
        confirm_container, self.confirm_password, self.confirm_show_button = create_password_field()
        password_layout.addWidget(confirm_container, 2, 1)

        # Password hint field
        password_layout.addWidget(QLabel(self.tr("Password Hint:")), 3, 0)
        self.password_hint_field = QLineEdit()
        self.password_hint_field.setPlaceholderText(self.tr("Enter a hint to help remember your password"))
        self.password_hint_field.setText(self.permission_manager.get_password_hint())
        password_layout.addWidget(self.password_hint_field, 3, 1)

        # Password buttons and hint
        password_buttons_layout = QHBoxLayout()

        self.change_password_button = QPushButton(self.tr("Change Password"))
        self.change_password_button.clicked.connect(self._on_change_password)
        password_buttons_layout.addWidget(self.change_password_button)

        # Password hint button
        self.password_hint_button = QPushButton(self.tr("Show Password Hint"))
        self.password_hint_button.clicked.connect(self._show_password_hint)
        password_buttons_layout.addWidget(self.password_hint_button)

        password_layout.addLayout(password_buttons_layout, 4, 0, 1, 2)

        # Add a security note
        security_note = QLabel(self.tr("For security, please use a strong password different from the default."))
        security_note.setStyleSheet("font-style: italic; color: #666;")
        security_note.setWordWrap(True)
        password_layout.addWidget(security_note, 5, 0, 1, 2)

        dev_mode_inner_layout.addWidget(password_frame)
        dev_mode_inner_layout.addStretch()

        dev_mode_layout.addWidget(dev_mode_groupbox)
        dev_mode_layout.addStretch()

    def _setup_version_level_tab(self):
        """Set up the Application Version Level tab."""
        version_layout = QVBoxLayout(self.version_level_tab)

        # Version Level Section
        version_groupbox = QGroupBox(self.tr("Application Version Level"))
        version_inner_layout = QVBoxLayout(version_groupbox)

        # Add a message about developer mode requirement
        self.version_level_message = QLabel(self.tr("Version level can only be changed in Developer Mode"))
        self.version_level_message.setStyleSheet("color: #666; font-style: italic;")
        self.version_level_message.setVisible(not self.permission_manager.developer_mode)
        version_inner_layout.addWidget(self.version_level_message)

        # Version level radio buttons
        self.version_button_group = QButtonGroup(self)

        # Get all version levels from permission manager
        version_levels = self.permission_manager.get_all_version_levels()
        current_version = self.permission_manager.version_level

        # Create a container for radio buttons to apply a single enabled/disabled state
        self.version_radio_container = QWidget()
        radio_container_layout = QVBoxLayout(self.version_radio_container)
        radio_container_layout.setContentsMargins(0, 0, 0, 0)

        for level in version_levels:
            radio = QRadioButton(level["name"])
            radio.setProperty("version_id", level["id"])
            self.version_button_group.addButton(radio)
            radio_container_layout.addWidget(radio)

            # Check the current version level
            if level["id"] == current_version:
                radio.setChecked(True)

        # Add the radio button container to the layout
        version_inner_layout.addWidget(self.version_radio_container)

        # Set initial enabled state based on developer mode
        self.version_radio_container.setEnabled(self.permission_manager.developer_mode)

        # Connect version level change
        self.version_button_group.buttonClicked.connect(self._on_version_level_changed)

        version_inner_layout.addStretch()
        version_layout.addWidget(version_groupbox)
        version_layout.addStretch()

    def _setup_log_levels_tab(self):
        """Set up the log levels tab with logging controls."""
        log_layout = QVBoxLayout(self.log_levels_tab)

        # Debug Log Levels Section
        self.debug_groupbox = QGroupBox(self.tr("Component Log Levels"))
        self.debug_layout = QGridLayout(self.debug_groupbox)

        self.log_level_combos = {}
        settings = QSettings()

        for i, (display_name, logger_name) in enumerate(LOGGERS_TO_CONTROL.items()):
            label = QLabel(self.tr(display_name) + ":", self)
            combo = QComboBox(self)
            combo.addItems(LOG_LEVELS.keys())

            # Load saved setting OR current level
            setting_key = f"logging_levels/{logger_name if logger_name else '__root__'}"
            saved_level_str = settings.value(setting_key, None)

            initial_level_str = "NOTSET"  # Default fallback
            if saved_level_str in LOG_LEVELS:
                initial_level_str = saved_level_str
            else:
                # If not saved, determine current effective level
                try:
                    current_level_num = logging.getLogger(logger_name if logger_name else None).getEffectiveLevel()
                    if current_level_num in REVERSE_LOG_LEVELS:
                        initial_level_str = REVERSE_LOG_LEVELS[current_level_num]
                except Exception as e:
                    self.logger.error(f"Error getting effective level for '{logger_name}': {e}")

            combo.setCurrentText(initial_level_str)

            # Store logger_name with the combo for the slot
            combo.setProperty("loggerName", logger_name)
            combo.currentTextChanged.connect(self.on_log_level_changed)

            self.debug_layout.addWidget(label, i, 0)
            self.debug_layout.addWidget(combo, i, 1)
            self.log_level_combos[logger_name] = combo

        log_layout.addWidget(self.debug_groupbox)
        log_layout.addStretch()

    def on_log_level_changed(self, text_level):
        """Handle log level combo box changes."""
        combo = self.sender()
        if not combo:
            return

        logger_name = combo.property("loggerName")

        # Get the numeric level
        if text_level in LOG_LEVELS:
            level = LOG_LEVELS[text_level]

            # Set the logger level
            logger = logging.getLogger(logger_name if logger_name else None)
            logger.setLevel(level)

            # Save the setting
            settings = QSettings()
            setting_key = f"logging_levels/{logger_name if logger_name else '__root__'}"
            settings.setValue(setting_key, text_level)

            self.logger.info(f"Set log level for '{logger_name if logger_name else 'root'}' to {text_level}")
        else:
            # Log the error (using print for now in this dev tool)
            self.logger.warning(f"Unknown log level selected: {text_level} for logger '{logger_name}'")

    def _setup_debug_options_tab(self):
        """Set up the debug options tab with debug controls."""
        debug_layout = QVBoxLayout(self.debug_options_tab)

        # Debug Output Section
        debug_output_groupbox = QGroupBox(self.tr("Debug Output"))
        debug_output_layout = QVBoxLayout(debug_output_groupbox)

        # Window initialization debug
        self.window_init_debug_checkbox = QCheckBox(self.tr("Enable Window Initialization Debug"))
        self.window_init_debug_checkbox.setChecked(self._get_debug_setting("window_initialization", False))
        self.window_init_debug_checkbox.stateChanged.connect(self._on_window_init_debug_changed)
        debug_output_layout.addWidget(self.window_init_debug_checkbox)

        # Match page debug
        self.match_page_debug_checkbox = QCheckBox(self.tr("Enable Match Page Debug"))
        self.match_page_debug_checkbox.setChecked(self._get_debug_setting("match_page", False))
        self.match_page_debug_checkbox.stateChanged.connect(self._on_match_page_debug_changed)
        debug_output_layout.addWidget(self.match_page_debug_checkbox)

        # Progress validation debug
        self.progress_validation_debug_checkbox = QCheckBox(self.tr("Enable Progress Validation Debug"))
        self.progress_validation_debug_checkbox.setChecked(self._get_debug_setting("progress_validation", False))
        self.progress_validation_debug_checkbox.stateChanged.connect(self._on_progress_validation_debug_changed)
        debug_output_layout.addWidget(self.progress_validation_debug_checkbox)

        debug_layout.addWidget(debug_output_groupbox)

        # Add stretch to push content to top
        debug_layout.addStretch()

    def _get_debug_setting(self, setting_name, default_value):
        """Get a debug setting from QSettings."""
        settings = QSettings()
        return settings.value(f"debug/{setting_name}", default_value, type=bool)

    def _set_debug_setting(self, setting_name, value):
        """Set a debug setting in QSettings."""
        settings = QSettings()
        settings.setValue(f"debug/{setting_name}", value)

    def _on_window_init_debug_changed(self, state):
        """Handle window initialization debug checkbox changes."""
        self._set_debug_setting("window_initialization", bool(state))
        self.logger.info(f"Window initialization debug {'enabled' if state else 'disabled'}")

    def _on_match_page_debug_changed(self, state):
        """Handle match page debug checkbox changes."""
        self._set_debug_setting("match_page", bool(state))
        self.logger.info(f"Match page debug {'enabled' if state else 'disabled'}")

    def _on_progress_validation_debug_changed(self, state):
        """Handle progress validation debug checkbox changes."""
        self._set_debug_setting("progress_validation", bool(state))
        self.logger.info(f"Progress validation debug {'enabled' if state else 'disabled'}")

    def _on_version_level_changed(self, button):
        """Handle version level radio button changes."""
        # Check if developer mode is enabled
        if not self.permission_manager.developer_mode:
            # If not in developer mode, show a warning and revert the selection
            QMessageBox.warning(
                self,
                self.tr("Developer Mode Required"),
                self.tr("You must enable Developer Mode to change the Application Version Level.")
            )

            # Revert to the current version level
            current_version = self.permission_manager.version_level
            for btn in self.version_button_group.buttons():
                if btn.property("version_id") == current_version:
                    btn.setChecked(True)
                    break
            return

        # Continue with version change if in developer mode
        version_id = button.property("version_id")
        if not version_id:
            self.logger.warning("No version_id property found on radio button")
            return

        # Update the permission manager
        self.permission_manager.version_level = version_id
        self.logger.info(f"Version level changed to: {version_id}")

        # Show confirmation message
        QMessageBox.information(
            self,
            self.tr("Version Level Changed"),
            self.tr(f"Application version level changed to {button.text()}.\n"
                   f"Some features may be restricted based on this version level.")
        )

    def _on_dev_mode_changed(self, state):
        """Handle developer mode checkbox changes."""
        # If enabling developer mode, verify password first
        if state and not self.permission_manager.developer_mode:
            # Prompt for password
            password = self._prompt_for_password()
            if not password:
                # User cancelled
                self.dev_mode_checkbox.setChecked(False)
                return

            # Verify password
            if not self.permission_manager.verify_password(password):
                QMessageBox.critical(
                    self,
                    self.tr("Authentication Failed"),
                    self.tr("Incorrect password. Developer mode not enabled.")
                )
                self.dev_mode_checkbox.setChecked(False)
                return

        # Update the permission manager
        self.permission_manager.developer_mode = bool(state)
        self.logger.info(f"Developer mode {'enabled' if state else 'disabled'}")

        # Update version level controls
        if hasattr(self, 'version_radio_container'):
            self.version_radio_container.setEnabled(state)

        if hasattr(self, 'version_level_message'):
            self.version_level_message.setVisible(not state)

        # Update tab visibility based on developer mode
        self._update_tab_visibility()

        # If disabling developer mode, close any open Settings windows
        if not state:
            # Find and close any open Settings windows
            from app.windows.settings_window import SettingsWindow
            for widget in QApplication.topLevelWidgets():
                if isinstance(widget, SettingsWindow):
                    self.logger.info("Closing Settings window due to developer mode being disabled")
                    widget.close()

        # Show confirmation message
        if state:
            QMessageBox.information(
                self,
                self.tr("Developer Mode Enabled"),
                self.tr("Developer mode is now enabled.\n"
                       "All permission restrictions are bypassed.\n"
                       "You can now change the Application Version Level.")
            )

    def _on_change_password(self):
        """Handle change password button click."""
        current_password = self.current_password.text()
        new_password = self.new_password.text()
        confirm_password = self.confirm_password.text()

        # Validate inputs
        if not current_password:
            QMessageBox.warning(
                self,
                self.tr("Missing Input"),
                self.tr("Please enter your current password.")
            )
            return

        if not new_password:
            QMessageBox.warning(
                self,
                self.tr("Missing Input"),
                self.tr("Please enter a new password.")
            )
            return

        if new_password != confirm_password:
            QMessageBox.warning(
                self,
                self.tr("Password Mismatch"),
                self.tr("New password and confirmation do not match.")
            )
            return

        # Verify current password
        if not self.permission_manager.verify_password(current_password):
            QMessageBox.warning(
                self,
                self.tr("Authentication Failed"),
                self.tr("Current password is incorrect.")
            )
            return

        # Get the password hint
        password_hint = self.password_hint_field.text().strip()

        # Set new password with hint
        if self.permission_manager.set_password(new_password, password_hint):
            QMessageBox.information(
                self,
                self.tr("Password Changed"),
                self.tr("Developer password has been updated successfully.")
            )

            # Clear password fields
            self.current_password.clear()
            self.new_password.clear()
            self.confirm_password.clear()

            # Keep the hint as it is

            # Update password security warning
            if self.permission_manager.is_using_default_password():
                self.password_security_warning.setText(
                    self.tr("Warning: Using default password. For security, please change the password.")
                )
                self.password_security_warning.setStyleSheet("color: red; font-weight: bold;")
            else:
                self.password_security_warning.setText(
                    self.tr("Using custom password. Good security practice!")
                )
                self.password_security_warning.setStyleSheet("color: green; font-weight: bold;")
        else:
            QMessageBox.critical(
                self,
                self.tr("Error"),
                self.tr("Failed to update password.")
            )

    def _show_password_hint(self):
        """Show the password hint."""
        # Get the stored hint
        stored_hint = self.permission_manager.get_password_hint()

        # Prepare the message
        if stored_hint:
            # Show the user's custom hint
            hint_message = self.tr("Your password hint:\n\n{}\n\n"
                                  "For security reasons, we recommend using a strong, unique password.").format(stored_hint)
        else:
            # Show the default hint if no custom hint is set
            hint_message = self.tr("No custom hint set.\n\n"
                                  "Default hint: The default password is the name of this application in lowercase.\n\n"
                                  "For security reasons, we recommend changing the password to something unique.")

        # Show the hint dialog
        QMessageBox.information(
            self,
            self.tr("Password Hint"),
            hint_message
        )

    def _prompt_for_password(self):
        """
        Show a dialog to prompt for the developer password.

        Returns:
            str: The entered password, or None if cancelled
        """
        # Create a dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(self.tr("Developer Authentication"))
        dialog.setMinimumWidth(300)

        # Create layout
        layout = QVBoxLayout(dialog)

        # Add password field
        layout.addWidget(QLabel(self.tr("Enter Developer Password:")))
        password_field = QLineEdit()
        password_field.setEchoMode(QLineEdit.EchoMode.Password)
        layout.addWidget(password_field)

        # Add buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec() == QDialog.DialogCode.Accepted:
            return password_field.text()
        return None

    def _reset_password_to_default(self):
        """Reset the developer password to the default."""
        # Ask for confirmation
        result = QMessageBox.question(
            self,
            self.tr("Reset Password"),
            self.tr("Are you sure you want to reset the developer password to default?\n\n"
                   "This action cannot be undone."),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if result == QMessageBox.StandardButton.Yes:
            # Reset the password
            if self.permission_manager.reset_password():
                QMessageBox.information(
                    self,
                    self.tr("Password Reset"),
                    self.tr("Developer password has been reset to default (footdata).")
                )

                # Clear password fields
                if hasattr(self, 'current_password'):
                    self.current_password.clear()
                if hasattr(self, 'new_password'):
                    self.new_password.clear()
                if hasattr(self, 'confirm_password'):
                    self.confirm_password.clear()

                # Clear the password hint field and setting
                if hasattr(self, 'password_hint_field'):
                    self.password_hint_field.clear()

                # Clear the stored hint in settings
                self.permission_manager.settings.remove("permissions/password_hint")

                # Update password warning to show default password is being used
                if hasattr(self, 'password_security_warning'):
                    self.password_security_warning.setText(
                        self.tr("Warning: Using default password. For security, please change the password.")
                    )
                    self.password_security_warning.setStyleSheet("color: red; font-weight: bold;")
            else:
                QMessageBox.critical(
                    self,
                    self.tr("Error"),
                    self.tr("Failed to reset password.")
                )

    def _update_tab_visibility(self):
        """Update tab visibility based on developer mode status."""
        # Get current developer mode status
        is_dev_mode = self.permission_manager.developer_mode

        # Show/hide tabs based on developer mode
        # We need to check if the tabs exist in case this is called during initialization
        if hasattr(self, 'version_level_tab_index') and hasattr(self, 'log_levels_tab_index') and hasattr(self, 'debug_options_tab_index') and hasattr(self, 'reset_data_tab_index'):
            # Version Level tab
            self.tab_widget.setTabVisible(self.version_level_tab_index, is_dev_mode)

            # Log Levels tab
            self.tab_widget.setTabVisible(self.log_levels_tab_index, is_dev_mode)

            # Debug Options tab
            self.tab_widget.setTabVisible(self.debug_options_tab_index, is_dev_mode)

            # Reset Data tab
            self.tab_widget.setTabVisible(self.reset_data_tab_index, is_dev_mode)

            # If developer mode is disabled and we're on a restricted tab, switch to the Developer Mode tab
            if not is_dev_mode and self.tab_widget.currentIndex() > 0:
                self.tab_widget.setCurrentIndex(0)

    def _setup_reset_data_tab(self):
        """Set up the Reset Data tab with data reset controls."""
        # Create main layout for the tab
        tab_layout = QVBoxLayout(self.reset_data_tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)

        # Create scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.Shape.NoFrame)

        # Create content widget for the scroll area
        content_widget = QWidget()
        reset_layout = QVBoxLayout(content_widget)

        # Club Data Section (Collapsible)
        self.club_groupbox = CollapsibleGroupBox(self.tr("Club Data"))
        club_content = QWidget()
        club_layout = QVBoxLayout(club_content)

        # Create checkboxes for each data type with path information
        self.reset_general_info_checkbox = QCheckBox(self.tr("General information (📁 data/club_data.json → club_name, short_name, nickname, etc.)"))
        self.reset_stadium_info_checkbox = QCheckBox(self.tr("Stadium information (📁 data/club_data.json → stadium_name, capacity, etc.)"))
        self.reset_contact_info_checkbox = QCheckBox(self.tr("Contact information (📁 data/club_data.json → contact_address*, phone, email, etc.)"))
        self.reset_management_staff_checkbox = QCheckBox(self.tr("Management staff (📁 data/club_data.json → management_staff[])"))
        self.reset_coaching_staff_checkbox = QCheckBox(self.tr("Coaching staff (📁 data/club_data.json → coaching_staff[])"))
        self.reset_medical_staff_checkbox = QCheckBox(self.tr("Medical staff (📁 data/club_data.json → medical_staff[])"))
        self.reset_kit_checkbox = QCheckBox(self.tr("Kit (📁 data/club_data.json → colors, settings + 📁 images/kit/*.png)"))
        self.reset_main_sponsors_checkbox = QCheckBox(self.tr("Main sponsors (📁 data/club_sponsors.json → main_sponsors[])"))
        self.reset_supporters_checkbox = QCheckBox(self.tr("Supporters (📁 data/club_sponsors.json → supporters[])"))
        self.reset_charity_checkbox = QCheckBox(self.tr("Charity (📁 data/club_sponsors.json → charity[])"))
        self.reset_sports_brand_checkbox = QCheckBox(self.tr("Sports brand (📁 data/club_sponsors.json → sports_brand[])"))
        self.reset_team_groups_checkbox = QCheckBox(self.tr("Team groups (📁 data/roster.db → team_groups table ⚠️ Referenced by players)"))

        # Set tooltips with detailed information
        self.reset_general_info_checkbox.setToolTip(self.tr("Resets: club_name, short_name, nickname, year_founded, city, country, region\n💾 Data Type: JSON fields (no IDs involved)"))
        self.reset_stadium_info_checkbox.setToolTip(self.tr("Resets: stadium_name, capacity, seating_capacity, surface_type, year_built, stadium_owner\n💾 Data Type: JSON fields (no IDs involved)"))
        self.reset_contact_info_checkbox.setToolTip(self.tr("Resets: contact_address1, contact_address2, contact_city, contact_state, contact_postal, contact_phone, contact_email, contact_website\n💾 Data Type: JSON fields (no IDs involved)"))
        self.reset_management_staff_checkbox.setToolTip(self.tr("Resets: All management staff entries in management_staff[] array\n💾 Data Type: JSON array (no auto-increment IDs)"))
        self.reset_coaching_staff_checkbox.setToolTip(self.tr("Resets: All coaching staff entries in coaching_staff[] array\n💾 Data Type: JSON array (no auto-increment IDs)"))
        self.reset_medical_staff_checkbox.setToolTip(self.tr("Resets: All medical staff entries in medical_staff[] array\n💾 Data Type: JSON array (no auto-increment IDs)"))
        self.reset_kit_checkbox.setToolTip(self.tr("Resets: color_1st, color_2nd, color_3rd, kit_*_image_exists, kit_*_settings + deletes all *.png files in images/kit/\n💾 Data Type: JSON fields + physical files (no IDs involved)"))
        self.reset_main_sponsors_checkbox.setToolTip(self.tr("Resets: All main sponsor entries in main_sponsors[] array\n💾 Data Type: JSON array (no auto-increment IDs)"))
        self.reset_supporters_checkbox.setToolTip(self.tr("Resets: All supporter entries in supporters[] array\n💾 Data Type: JSON array (no auto-increment IDs)"))
        self.reset_charity_checkbox.setToolTip(self.tr("Resets: All charity entries in charity[] array\n💾 Data Type: JSON array (no auto-increment IDs)"))
        self.reset_sports_brand_checkbox.setToolTip(self.tr("Resets: All sports brand entries in sports_brand[] array\n💾 Data Type: JSON array (no auto-increment IDs)"))
        self.reset_team_groups_checkbox.setToolTip(self.tr("Resets: team_groups table in roster.db\n🔢 Auto-increment: group_id will restart from 1\n⚠️ WARNING: Referenced by players.primary_group_id and players.secondary_group_id\n🔗 Dependencies: Will break player group assignments"))

        # Add checkboxes to layout vertically
        checkboxes = [
            self.reset_general_info_checkbox,
            self.reset_stadium_info_checkbox,
            self.reset_contact_info_checkbox,
            self.reset_management_staff_checkbox,
            self.reset_coaching_staff_checkbox,
            self.reset_medical_staff_checkbox,
            self.reset_kit_checkbox,
            self.reset_main_sponsors_checkbox,
            self.reset_supporters_checkbox,
            self.reset_charity_checkbox,
            self.reset_sports_brand_checkbox,
            self.reset_team_groups_checkbox
        ]

        for checkbox in checkboxes:
            club_layout.addWidget(checkbox)

        # Add club content to the collapsible group box
        self.club_groupbox.add_widget(club_content)

        # Connect section state change signals for persistence
        self.club_groupbox.collapsedChanged.connect(lambda collapsed: self._save_section_state("club_data", collapsed))

        # Roster Data Section (Collapsible)
        self.roster_groupbox = CollapsibleGroupBox(self.tr("Roster Data"))
        roster_content = QWidget()
        roster_layout = QVBoxLayout(roster_content)

        # Create checkboxes for roster data types with path information
        self.reset_players_checkbox = QCheckBox(self.tr("Players (📁 data/roster.db → players table + all player data)"))
        self.reset_player_profiles_checkbox = QCheckBox(self.tr("Player profiles (📁 data/roster.db → player_profile table)"))
        self.reset_player_attendance_checkbox = QCheckBox(self.tr("Player attendance (📁 data/roster.db → player_attendance table)"))
        self.reset_player_languages_checkbox = QCheckBox(self.tr("Player languages (📁 data/roster.db → player_languages table)"))
        self.reset_player_hobbies_checkbox = QCheckBox(self.tr("Player hobbies (📁 data/roster.db → player_hobbies table)"))
        self.reset_player_social_media_checkbox = QCheckBox(self.tr("Player social media (📁 data/roster.db → player_social_media table)"))
        self.reset_player_children_checkbox = QCheckBox(self.tr("Player children (📁 data/roster.db → player_children table)"))
        self.reset_player_academic_checkbox = QCheckBox(self.tr("Player academic achievements (📁 data/roster.db → player_academic_achievements table)"))
        self.reset_player_coaching_licenses_checkbox = QCheckBox(self.tr("Player coaching licenses (📁 data/roster.db → player_coaching_licenses table)"))
        self.reset_player_club_history_checkbox = QCheckBox(self.tr("Player club history (📁 data/roster.db → player_club_history table)"))
        self.reset_player_awards_checkbox = QCheckBox(self.tr("Player awards (📁 data/roster.db → player_awards table)"))
        self.reset_position_grid_checkbox = QCheckBox(self.tr("Position grid data (📁 data/roster.db → player_positions table)"))

        # Set tooltips with detailed information for roster data
        self.reset_players_checkbox.setToolTip(self.tr("Resets: All player records including personal info, physical data, status tags, transfer details\n🔢 Auto-increment: player_id will restart from 1\n⚠️ CRITICAL: This will invalidate ALL player-related data in other tables\n🔗 Dependencies: All player_* tables reference this via player_id"))
        self.reset_player_profiles_checkbox.setToolTip(self.tr("Resets: Player personality, mentality, work ethic, family info, background details\n💾 Data Type: 1-to-1 table with players (uses player_id as PRIMARY KEY)\n🔗 Dependencies: Requires existing players table"))
        self.reset_player_attendance_checkbox.setToolTip(self.tr("Resets: All attendance records (Present, Absent, Injured, etc.) for all players\n🔢 Auto-increment: attendance id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_player_languages_checkbox.setToolTip(self.tr("Resets: Languages spoken by players and proficiency levels\n🔢 Auto-increment: language_id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_player_hobbies_checkbox.setToolTip(self.tr("Resets: Player hobbies and interests outside football\n🔢 Auto-increment: hobby_id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_player_social_media_checkbox.setToolTip(self.tr("Resets: Player social media accounts and handles\n🔢 Auto-increment: social_id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_player_children_checkbox.setToolTip(self.tr("Resets: Information about players' children\n🔢 Auto-increment: child_id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_player_academic_checkbox.setToolTip(self.tr("Resets: Academic achievements and educational background\n🔢 Auto-increment: achievement_id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_player_coaching_licenses_checkbox.setToolTip(self.tr("Resets: Coaching licenses and certifications held by players\n🔢 Auto-increment: license_id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_player_club_history_checkbox.setToolTip(self.tr("Resets: Previous clubs, appearances, and goals history\n🔢 Auto-increment: history_id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_player_awards_checkbox.setToolTip(self.tr("Resets: Awards and honors received by players\n🔢 Auto-increment: award_entry_id will restart from 1\n🔗 Dependencies: References players.player_id"))
        self.reset_position_grid_checkbox.setToolTip(self.tr("Resets: Position grid ratings and tactical positioning data\n💾 Data Type: Grid coordinates and ratings stored per player\n🔗 Dependencies: References players.player_id"))

        # Add roster checkboxes to layout
        roster_checkboxes = [
            self.reset_players_checkbox,
            self.reset_player_profiles_checkbox,
            self.reset_player_attendance_checkbox,
            self.reset_player_languages_checkbox,
            self.reset_player_hobbies_checkbox,
            self.reset_player_social_media_checkbox,
            self.reset_player_children_checkbox,
            self.reset_player_academic_checkbox,
            self.reset_player_coaching_licenses_checkbox,
            self.reset_player_club_history_checkbox,
            self.reset_player_awards_checkbox,
            self.reset_position_grid_checkbox
        ]

        for checkbox in roster_checkboxes:
            roster_layout.addWidget(checkbox)

        # Add roster-specific buttons
        roster_buttons_layout = QHBoxLayout()
        self.roster_select_all_button = QPushButton(self.tr("Select All Roster"))
        self.roster_select_none_button = QPushButton(self.tr("Select None Roster"))
        self.roster_reset_all_button = QPushButton(self.tr("Reset All Roster Data"))

        # Style the Reset All Roster button to make it prominent
        self.roster_reset_all_button.setStyleSheet("""
            QPushButton {
                background-color: #d32f2f;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #b71c1c;
            }
            QPushButton:pressed {
                background-color: #8d1414;
            }
        """)

        self.roster_select_all_button.clicked.connect(self._select_all_roster_checkboxes)
        self.roster_select_none_button.clicked.connect(self._select_none_roster_checkboxes)
        self.roster_reset_all_button.clicked.connect(self._reset_all_roster_data)

        roster_buttons_layout.addWidget(self.roster_select_all_button)
        roster_buttons_layout.addWidget(self.roster_select_none_button)
        roster_buttons_layout.addStretch()
        roster_buttons_layout.addWidget(self.roster_reset_all_button)

        roster_layout.addLayout(roster_buttons_layout)

        # Add roster content to the collapsible group box
        self.roster_groupbox.add_widget(roster_content)

        # Connect section state change signals for persistence
        self.roster_groupbox.collapsedChanged.connect(lambda collapsed: self._save_section_state("roster_data", collapsed))

        # Add action buttons
        button_layout = QHBoxLayout()

        self.select_all_button = QPushButton(self.tr("Select All"))
        self.select_none_button = QPushButton(self.tr("Select None"))
        self.create_backup_button = QPushButton(self.tr("Create Backup"))
        self.reset_selected_button = QPushButton(self.tr("Reset Selected Data"))

        # Style the buttons
        self.reset_selected_button.setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; font-weight: bold; }")
        self.create_backup_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")

        button_layout.addWidget(self.select_all_button)
        button_layout.addWidget(self.select_none_button)
        button_layout.addWidget(self.create_backup_button)
        button_layout.addStretch()
        button_layout.addWidget(self.reset_selected_button)

        # Connect button signals
        self.select_all_button.clicked.connect(self._select_all_reset_options)
        self.select_none_button.clicked.connect(self._select_none_reset_options)
        self.create_backup_button.clicked.connect(self._create_manual_backup)
        self.reset_selected_button.clicked.connect(self._reset_selected_data)

        # Warning Section (Collapsible with red border)
        self.warning_groupbox = CollapsibleGroupBox(self.tr("⚠️ Warning"))
        self.warning_groupbox.setStyleSheet("CollapsibleGroupBox { border: 2px solid #ff6b6b; border-radius: 4px; }")
        warning_content = QWidget()
        warning_layout = QVBoxLayout(warning_content)

        # Add warning label with dependency information
        warning_text = self.tr("⚠️ Warning: Reset operations cannot be undone. Make sure to backup your data before proceeding.\n\n"
                              "🔢 Auto-Increment ID Behavior:\n"
                              "• Database tables with auto-increment IDs will restart from 1\n"
                              "• Example: If last player_id was 50, after reset next player gets ID = 1\n"
                              "• This provides a complete 'fresh start' experience\n"
                              "• All previous data and ID sequences are permanently lost\n\n"
                              "📋 Data Dependencies:\n"
                              "• Team Groups → Referenced by player assignments (primary/secondary groups)\n"
                              "• Players → Referenced by attendance, profiles, and all player-related data\n"
                              "• Staff Tables → May be referenced in match team sheets\n"
                              "• Kit Images → Physical files in images/kit/ directory will be deleted\n"
                              "• Sponsors → Complex nested data structures with multiple categories\n\n"
                              "💾 Files Affected:\n"
                              "• data/club_data.json (main club information)\n"
                              "• data/club_sponsors.json (sponsor information)\n"
                              "• data/roster.db (SQLite database with players, team groups, attendance, profiles)\n"
                              "• images/kit/*.png (kit image files)\n\n"
                              "🏃 Player Data Tables (with Auto-Increment IDs):\n"
                              "• players (player_id), player_attendance (id), player_languages (language_id)\n"
                              "• player_hobbies (hobby_id), player_social_media (social_id)\n"
                              "• player_children (child_id), player_academic_achievements (achievement_id)\n"
                              "• player_coaching_licenses (license_id), player_club_history (history_id)\n"
                              "• player_awards (award_entry_id), team_groups (group_id)")

        warning_label = QLabel(warning_text)
        warning_label.setStyleSheet("color: #ff6b6b; font-weight: bold; padding: 15px; background-color: #fff5f5; border: 1px solid #ff6b6b; border-radius: 4px; font-size: 11px;")
        warning_label.setWordWrap(True)

        warning_layout.addWidget(warning_label)
        self.warning_groupbox.add_widget(warning_content)

        # Connect section state change signals for persistence
        self.warning_groupbox.collapsedChanged.connect(lambda collapsed: self._save_section_state("warning", collapsed))

        # Evaluations Data Section (Collapsible)
        self.evaluations_groupbox = CollapsibleGroupBox(self.tr("Evaluations Data"))
        evaluations_content = QWidget()
        evaluations_layout = QVBoxLayout(evaluations_content)

        # Create checkbox for evaluation data reset
        self.reset_evaluations_checkbox = QCheckBox(self.tr("Player evaluations (📁 data/footdata.db → player_evaluations table - soft reset)"))
        self.reset_evaluations_checkbox.setToolTip(self.tr("Soft reset: Clears all evaluation ratings and data but preserves table structure\n💾 Data Type: Evaluation ratings, periods, and skill assessments\n🔗 Dependencies: References players.player_id (players preserved)\n⚠️ Note: This only clears evaluation values, not the evaluation system structure"))

        evaluations_layout.addWidget(self.reset_evaluations_checkbox)

        # Add evaluations-specific buttons
        evaluations_buttons_layout = QHBoxLayout()
        self.evaluations_select_button = QPushButton(self.tr("Select Evaluations"))
        self.evaluations_clear_button = QPushButton(self.tr("Clear All Evaluations"))

        # Style the Clear All Evaluations button
        self.evaluations_clear_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)

        self.evaluations_select_button.clicked.connect(self._select_evaluations_checkbox)
        self.evaluations_clear_button.clicked.connect(self._clear_all_evaluations)

        evaluations_buttons_layout.addWidget(self.evaluations_select_button)
        evaluations_buttons_layout.addStretch()
        evaluations_buttons_layout.addWidget(self.evaluations_clear_button)

        evaluations_layout.addLayout(evaluations_buttons_layout)

        # Add evaluations content to the collapsible group box
        self.evaluations_groupbox.add_widget(evaluations_content)

        # Connect section state change signals for persistence
        self.evaluations_groupbox.collapsedChanged.connect(lambda collapsed: self._save_section_state("evaluations_data", collapsed))

        # Add everything to the main layout
        reset_layout.addWidget(self.warning_groupbox)
        reset_layout.addWidget(self.club_groupbox)
        reset_layout.addWidget(self.evaluations_groupbox)
        reset_layout.addWidget(self.roster_groupbox)
        reset_layout.addLayout(button_layout)
        reset_layout.addStretch()

        # Set the content widget to the scroll area
        scroll_area.setWidget(content_widget)

        # Add the scroll area to the main tab layout
        tab_layout.addWidget(scroll_area)

        # Restore section states after all sections are created and added to layout
        self._restore_section_states()

    def _select_all_reset_options(self):
        """Select all reset options."""
        checkboxes = [
            # Club data checkboxes
            self.reset_general_info_checkbox,
            self.reset_stadium_info_checkbox,
            self.reset_contact_info_checkbox,
            self.reset_management_staff_checkbox,
            self.reset_coaching_staff_checkbox,
            self.reset_medical_staff_checkbox,
            self.reset_kit_checkbox,
            self.reset_main_sponsors_checkbox,
            self.reset_supporters_checkbox,
            self.reset_charity_checkbox,
            self.reset_sports_brand_checkbox,
            self.reset_team_groups_checkbox,
            # Evaluations data checkbox
            self.reset_evaluations_checkbox,
            # Roster data checkboxes
            self.reset_players_checkbox,
            self.reset_player_profiles_checkbox,
            self.reset_player_attendance_checkbox,
            self.reset_player_languages_checkbox,
            self.reset_player_hobbies_checkbox,
            self.reset_player_social_media_checkbox,
            self.reset_player_children_checkbox,
            self.reset_player_academic_checkbox,
            self.reset_player_coaching_licenses_checkbox,
            self.reset_player_club_history_checkbox,
            self.reset_player_awards_checkbox,
            self.reset_position_grid_checkbox
        ]

        for checkbox in checkboxes:
            checkbox.setChecked(True)

    def _select_none_reset_options(self):
        """Deselect all reset options."""
        checkboxes = [
            # Club data checkboxes
            self.reset_general_info_checkbox,
            self.reset_stadium_info_checkbox,
            self.reset_contact_info_checkbox,
            self.reset_management_staff_checkbox,
            self.reset_coaching_staff_checkbox,
            self.reset_medical_staff_checkbox,
            self.reset_kit_checkbox,
            self.reset_main_sponsors_checkbox,
            self.reset_supporters_checkbox,
            self.reset_charity_checkbox,
            self.reset_sports_brand_checkbox,
            self.reset_team_groups_checkbox,
            # Evaluations data checkbox
            self.reset_evaluations_checkbox,
            # Roster data checkboxes
            self.reset_players_checkbox,
            self.reset_player_profiles_checkbox,
            self.reset_player_attendance_checkbox,
            self.reset_player_languages_checkbox,
            self.reset_player_hobbies_checkbox,
            self.reset_player_social_media_checkbox,
            self.reset_player_children_checkbox,
            self.reset_player_academic_checkbox,
            self.reset_player_coaching_licenses_checkbox,
            self.reset_player_club_history_checkbox,
            self.reset_player_awards_checkbox,
            self.reset_position_grid_checkbox
        ]

        for checkbox in checkboxes:
            checkbox.setChecked(False)

    def _select_evaluations_checkbox(self):
        """Select the evaluations checkbox."""
        self.reset_evaluations_checkbox.setChecked(True)

    def _clear_all_evaluations(self):
        """Clear all evaluation data with confirmation."""
        # Show confirmation dialog
        if not self._confirm_evaluations_clear_operation():
            return

        # Perform the evaluation clearing
        self._perform_evaluations_clear_operation()

    def _confirm_evaluations_clear_operation(self):
        """Show confirmation dialog specifically for evaluation clearing."""
        message_parts = [
            self.tr("⚠️ CLEAR ALL EVALUATIONS ⚠️\n\n"),
            self.tr("You are about to clear ALL evaluation data. This will:\n\n"),
            self.tr("🗑️ CLEAR (SOFT RESET):\n"),
            self.tr("• All player evaluation ratings and assessments\n"),
            self.tr("• All evaluation periods (1st, 2nd, 3rd)\n"),
            self.tr("• All skill categories and ratings\n"),
            self.tr("• All evaluation timestamps and history\n\n"),
            self.tr("✅ PRESERVED:\n"),
            self.tr("• Player records (players remain intact)\n"),
            self.tr("• Evaluation system structure and settings\n"),
            self.tr("• All other player data (attendance, profiles, etc.)\n\n"),
            self.tr("💾 DATABASE AFFECTED:\n"),
            self.tr("• app/data/footdata.db → player_evaluations table (data only)\n\n"),
            self.tr("ℹ️ This is a SOFT RESET - only evaluation values are cleared.\n"),
            self.tr("Players can immediately receive new evaluations.\n\n"),
            self.tr("Are you sure you want to clear all evaluation data?")
        ]

        message = "".join(message_parts)

        # Show confirmation dialog
        result = QMessageBox.warning(
            self,
            self.tr("⚠️ CONFIRM CLEAR ALL EVALUATIONS"),
            message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        return result == QMessageBox.StandardButton.Yes

    def _perform_evaluations_clear_operation(self):
        """Perform the evaluation clearing operation."""
        import sqlite3
        from pathlib import Path

        try:
            # Get project root directory
            script_dir = Path(__file__).parent.parent.parent
            db_path = script_dir / "app" / "data" / "footdata.db"

            if not db_path.exists():
                raise FileNotFoundError(f"Database file not found: {db_path}")

            # Connect to database and clear evaluations
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='player_evaluations'")
            if not cursor.fetchone():
                raise ValueError("player_evaluations table does not exist in database")

            # Get count before clearing
            cursor.execute("SELECT COUNT(*) FROM player_evaluations")
            count_before = cursor.fetchone()[0]

            # Clear all evaluation data
            cursor.execute("DELETE FROM player_evaluations")

            # Reset auto-increment counter
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='player_evaluations'")

            conn.commit()
            conn.close()

            # Show success message
            QMessageBox.information(
                self,
                self.tr("Evaluations Cleared"),
                self.tr("Successfully cleared all evaluation data!\n\n"
                        "📊 Cleared {0} evaluation records\n"
                        "✅ Evaluation system ready for new data\n"
                        "✅ Players preserved and intact\n"
                        "✅ Auto-increment ID reset to 1\n\n"
                        "You can now start fresh with new evaluations.").format(count_before)
            )

            print(f"Successfully cleared {count_before} evaluation records")

        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Evaluation Clear Error"),
                self.tr("An error occurred while clearing evaluation data:\n\n{0}").format(str(e))
            )
            print(f"Error clearing evaluations: {e}")

    def _select_all_roster_checkboxes(self):
        """Select all roster-related checkboxes."""
        roster_checkboxes = [
            self.reset_players_checkbox,
            self.reset_player_profiles_checkbox,
            self.reset_player_attendance_checkbox,
            self.reset_player_languages_checkbox,
            self.reset_player_hobbies_checkbox,
            self.reset_player_social_media_checkbox,
            self.reset_player_children_checkbox,
            self.reset_player_academic_checkbox,
            self.reset_player_coaching_licenses_checkbox,
            self.reset_player_club_history_checkbox,
            self.reset_player_awards_checkbox,
            self.reset_position_grid_checkbox
        ]

        for checkbox in roster_checkboxes:
            checkbox.setChecked(True)

    def _select_none_roster_checkboxes(self):
        """Deselect all roster-related checkboxes."""
        roster_checkboxes = [
            self.reset_players_checkbox,
            self.reset_player_profiles_checkbox,
            self.reset_player_attendance_checkbox,
            self.reset_player_languages_checkbox,
            self.reset_player_hobbies_checkbox,
            self.reset_player_social_media_checkbox,
            self.reset_player_children_checkbox,
            self.reset_player_academic_checkbox,
            self.reset_player_coaching_licenses_checkbox,
            self.reset_player_club_history_checkbox,
            self.reset_player_awards_checkbox,
            self.reset_position_grid_checkbox
        ]

        for checkbox in roster_checkboxes:
            checkbox.setChecked(False)

    def _reset_all_roster_data(self):
        """Reset all roster data with confirmation and automatic backup."""
        # Define all roster-related items (excluding team_groups and matches)
        roster_items = [
            ("Database", "Players (⚠️ CRITICAL - affects all player data)"),
            ("Database", "Player Profiles"),
            ("Database", "Player Attendance"),
            ("Database", "Player Languages"),
            ("Database", "Player Hobbies"),
            ("Database", "Player Social Media"),
            ("Database", "Player Children"),
            ("Database", "Player Academic Achievements"),
            ("Database", "Player Coaching Licenses"),
            ("Database", "Player Club History"),
            ("Database", "Player Awards"),
            ("Database", "Position Grid"),
            ("Database", "Player Evaluations"),
            ("Database", "Player Selections")
        ]

        # Show enhanced confirmation dialog for roster reset
        if not self._confirm_roster_reset_operation(roster_items):
            return

        # Create backup before reset
        try:
            backup_path = self._create_full_backup()
            print(f"Backup created successfully at: {backup_path}")
        except Exception as e:
            result = QMessageBox.warning(
                self,
                self.tr("Backup Failed"),
                self.tr("Failed to create backup before reset:\n\n{0}\n\nDo you want to proceed without backup?").format(str(e)),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if result != QMessageBox.StandardButton.Yes:
                return

        # Perform the reset operations
        self._perform_roster_reset_operations(roster_items)

    def _confirm_roster_reset_operation(self, roster_items):
        """Show enhanced confirmation dialog specifically for roster reset."""
        message_parts = [
            self.tr("⚠️ CRITICAL WARNING: COMPLETE ROSTER RESET ⚠️\n\n"),
            self.tr("You are about to reset ALL roster data. This will:\n\n"),
            self.tr("🗑️ PERMANENTLY DELETE:\n"),
            self.tr("• All player records and personal information\n"),
            self.tr("• All player profiles, attendance, and evaluations\n"),
            self.tr("• All player languages, hobbies, and social media info\n"),
            self.tr("• All player children, academic achievements, and coaching licenses\n"),
            self.tr("• All player club history and awards\n"),
            self.tr("• All position grid data and player selections\n\n"),
            self.tr("✅ PRESERVED (NOT DELETED):\n"),
            self.tr("• Team groups and staff assignments\n"),
            self.tr("• Match data and competition records\n"),
            self.tr("• Club information and settings\n\n"),
            self.tr("🔢 RESET AUTO-INCREMENT IDs:\n"),
            self.tr("• All player IDs will restart from 1\n"),
            self.tr("• All related table IDs will restart from 1\n\n"),
            self.tr("💾 AUTOMATIC BACKUP:\n"),
            self.tr("• Complete backup will be created before reset\n"),
            self.tr("• Backup location: backup/ folder with full app structure\n"),
            self.tr("• Database: app/data/footdata.db → backup/app/data/footdata.db\n\n"),
            self.tr("⚠️ THIS ACTION CANNOT BE UNDONE (except via backup restore)!\n\n"),
            self.tr("Are you absolutely sure you want to proceed with the complete roster reset?")
        ]

        message = "".join(message_parts)

        # Show critical confirmation dialog
        result = QMessageBox.critical(
            self,
            self.tr("⚠️ CONFIRM COMPLETE ROSTER RESET"),
            message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        return result == QMessageBox.StandardButton.Yes

    def _create_full_backup(self):
        """Create a complete backup of the app folder structure."""
        import shutil
        import os
        from pathlib import Path
        from datetime import datetime

        # Get project root directory
        script_dir = Path(__file__).parent.parent.parent

        # Create backup directory with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_root = script_dir / "backup" / f"backup_{timestamp}"
        backup_app_dir = backup_root / "app"

        print(f"Creating backup at: {backup_root}")

        # Create backup directories
        backup_root.mkdir(parents=True, exist_ok=True)
        backup_app_dir.mkdir(parents=True, exist_ok=True)

        # Files and directories to backup from app folder
        app_dir = script_dir / "app"
        items_to_backup = [
            "data",  # All data files including footdata.db
            "windows",  # Window files
            "pages",  # Page files
            "widgets",  # Widget files
            "utils",  # Utility files
        ]

        # Copy app folder structure
        for item in items_to_backup:
            source_path = app_dir / item
            if source_path.exists():
                if source_path.is_dir():
                    shutil.copytree(source_path, backup_app_dir / item, dirs_exist_ok=True)
                    print(f"Backed up directory: app/{item}")
                else:
                    shutil.copy2(source_path, backup_app_dir / item)
                    print(f"Backed up file: app/{item}")

        # Also backup other important files from project root
        root_files_to_backup = [
            "main.py",
            "requirements.txt",
        ]

        for file_name in root_files_to_backup:
            source_file = script_dir / file_name
            if source_file.exists():
                shutil.copy2(source_file, backup_root / file_name)
                print(f"Backed up root file: {file_name}")

        # Backup media folder if it exists
        media_dir = script_dir / "media"
        if media_dir.exists():
            shutil.copytree(media_dir, backup_root / "media", dirs_exist_ok=True)
            print("Backed up media folder")

        print(f"Backup completed successfully at: {backup_root}")
        return backup_root

    def _perform_roster_reset_operations(self, roster_items):
        """Perform roster-specific reset operations."""
        import sqlite3
        from pathlib import Path

        success_count = 0
        error_count = 0
        errors = []

        try:
            # Get project root directory
            script_dir = Path(__file__).parent.parent.parent

            # Define the roster table mappings
            roster_table_mapping = {
                "Players (⚠️ CRITICAL - affects all player data)": "players",
                "Player Profiles": "player_profile",
                "Player Attendance": "player_attendance",
                "Player Languages": "player_languages",
                "Player Hobbies": "player_hobbies",
                "Player Social Media": "player_social_media",
                "Player Children": "player_children",
                "Player Academic Achievements": "player_academic_achievements",
                "Player Coaching Licenses": "player_coaching_licenses",
                "Player Club History": "player_club_history",
                "Player Awards": "player_awards",
                "Position Grid": "player_positions",
                "Player Evaluations": "player_evaluations",
                "Player Selections": "player_selections"
            }

            for category, item in roster_items:
                try:
                    if category == "Database" and item in roster_table_mapping:
                        table_name = roster_table_mapping[item]
                        self._reset_database_table(script_dir, table_name)
                        print(f"Successfully reset table: {table_name}")

                    success_count += 1

                except Exception as e:
                    error_count += 1
                    errors.append(f"{item}: {str(e)}")
                    print(f"Error resetting {item}: {e}")

            # Show results
            self._show_roster_reset_results(success_count, error_count, errors)

        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Roster Reset Error"),
                self.tr("An unexpected error occurred during the roster reset operation:\n\n{0}").format(str(e))
            )

    def _show_roster_reset_results(self, success_count, error_count, errors):
        """Show the results of the roster reset operation."""
        if error_count == 0:
            QMessageBox.information(
                self,
                self.tr("Roster Reset Complete"),
                self.tr("Successfully reset {0} roster data type(s).\n\n"
                        "✅ All player data has been cleared\n"
                        "✅ Auto-increment IDs have been reset to 1\n"
                        "✅ Team groups and matches preserved\n"
                        "✅ Backup created before reset\n\n"
                        "You can now start fresh with new player data.").format(success_count)
            )
        else:
            error_details = "\n".join(errors) if errors else self.tr("Unknown errors occurred")
            QMessageBox.warning(
                self,
                self.tr("Roster Reset Completed with Errors"),
                self.tr("Roster reset completed with {0} success(es) and {1} error(s).\n\n"
                        "Errors:\n{2}").format(
                    success_count, error_count, error_details
                )
            )

    def _create_manual_backup(self):
        """Create a manual backup when user clicks the backup button."""
        try:
            backup_path = self._create_full_backup()
            QMessageBox.information(
                self,
                self.tr("Backup Created"),
                self.tr("Backup created successfully!\n\n"
                        "Location: {0}\n\n"
                        "This backup contains:\n"
                        "• Complete app folder structure\n"
                        "• All data files (footdata.db, JSON files)\n"
                        "• All source code files\n"
                        "• Media files (images, etc.)\n\n"
                        "You can use this backup to restore your data if needed.").format(backup_path)
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Backup Failed"),
                self.tr("Failed to create backup:\n\n{0}").format(str(e))
            )

    def _reset_selected_data(self):
        """Reset the selected data types."""
        # Get all selected checkboxes
        selected_items = []

        # Check club data checkboxes
        if self.reset_general_info_checkbox.isChecked():
            selected_items.append(("Club Data", "General Information"))
        if self.reset_stadium_info_checkbox.isChecked():
            selected_items.append(("Club Data", "Stadium Information"))
        if self.reset_contact_info_checkbox.isChecked():
            selected_items.append(("Club Data", "Contact Information"))
        if self.reset_management_staff_checkbox.isChecked():
            selected_items.append(("Club Data", "Management Staff"))
        if self.reset_coaching_staff_checkbox.isChecked():
            selected_items.append(("Club Data", "Coaching Staff"))
        if self.reset_medical_staff_checkbox.isChecked():
            selected_items.append(("Club Data", "Medical Staff"))
        if self.reset_kit_checkbox.isChecked():
            selected_items.append(("Club Data", "Kit (colors + images)"))
        if self.reset_main_sponsors_checkbox.isChecked():
            selected_items.append(("Sponsors", "Main Sponsors"))
        if self.reset_supporters_checkbox.isChecked():
            selected_items.append(("Sponsors", "Supporters"))
        if self.reset_charity_checkbox.isChecked():
            selected_items.append(("Sponsors", "Charity"))
        if self.reset_sports_brand_checkbox.isChecked():
            selected_items.append(("Sponsors", "Sports Brand"))
        if self.reset_team_groups_checkbox.isChecked():
            selected_items.append(("Database", "Team Groups"))

        # Check evaluations data checkbox
        if self.reset_evaluations_checkbox.isChecked():
            selected_items.append(("Database", "Player Evaluations"))

        # Check roster data checkboxes
        if self.reset_players_checkbox.isChecked():
            selected_items.append(("Database", "Players (⚠️ CRITICAL - affects all player data)"))
        if self.reset_player_profiles_checkbox.isChecked():
            selected_items.append(("Database", "Player Profiles"))
        if self.reset_player_attendance_checkbox.isChecked():
            selected_items.append(("Database", "Player Attendance"))
        if self.reset_player_languages_checkbox.isChecked():
            selected_items.append(("Database", "Player Languages"))
        if self.reset_player_hobbies_checkbox.isChecked():
            selected_items.append(("Database", "Player Hobbies"))
        if self.reset_player_social_media_checkbox.isChecked():
            selected_items.append(("Database", "Player Social Media"))
        if self.reset_player_children_checkbox.isChecked():
            selected_items.append(("Database", "Player Children"))
        if self.reset_player_academic_checkbox.isChecked():
            selected_items.append(("Database", "Player Academic Achievements"))
        if self.reset_player_coaching_licenses_checkbox.isChecked():
            selected_items.append(("Database", "Player Coaching Licenses"))
        if self.reset_player_club_history_checkbox.isChecked():
            selected_items.append(("Database", "Player Club History"))
        if self.reset_player_awards_checkbox.isChecked():
            selected_items.append(("Database", "Player Awards"))
        if self.reset_position_grid_checkbox.isChecked():
            selected_items.append(("Database", "Position Grid"))

        # If nothing is selected, show a message
        if not selected_items:
            QMessageBox.information(
                self,
                self.tr("No Selection"),
                self.tr("Please select at least one data type to reset.")
            )
            return

        # Show confirmation dialog
        if not self._confirm_reset_operation(selected_items):
            return

        # Perform the reset operations
        self._perform_reset_operations(selected_items)

    def _confirm_reset_operation(self, selected_items):
        """Show confirmation dialog for reset operation."""
        # Group items by category for better display
        categories = {}
        for category, item in selected_items:
            if category not in categories:
                categories[category] = []
            categories[category].append(item)

        # Build confirmation message
        message_parts = [
            self.tr("Are you sure you want to reset the following data?\n\n"),
            self.tr("⚠️ WARNING: This action cannot be undone!\n\n")
        ]

        for category, items in categories.items():
            message_parts.append(f"📁 {category}:\n")
            for item in items:
                message_parts.append(f"  • {item}\n")
            message_parts.append("\n")

        message_parts.extend([
            self.tr("💾 Files that will be affected:\n"),
            self.tr("• data/club_data.json (club information)\n"),
            self.tr("• data/sponsors.json (sponsor information)\n"),
            self.tr("• app/data/footdata.db (player and team data)\n"),
            self.tr("• images/kit/*.png (kit image files)\n\n"),
            self.tr("This operation will permanently delete the selected data and restore default values.")
        ])

        message = "".join(message_parts)

        # Show confirmation dialog
        result = QMessageBox.warning(
            self,
            self.tr("Confirm Data Reset"),
            message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        return result == QMessageBox.StandardButton.Yes

    def _perform_reset_operations(self, selected_items):
        """Perform the actual reset operations."""
        import os
        import json
        import sqlite3
        import shutil
        from pathlib import Path

        success_count = 0
        error_count = 0
        errors = []
        club_data_changed = False

        try:
            # Get project root directory
            script_dir = Path(__file__).parent.parent.parent

            for category, item in selected_items:
                try:
                    if category == "Club Data":
                        club_data_changed = True
                        if "General Information" in item:
                            self._reset_club_general_info(script_dir)
                        elif "Stadium Information" in item:
                            self._reset_club_stadium_info(script_dir)
                        elif "Contact Information" in item:
                            self._reset_club_contact_info(script_dir)
                        elif "Management Staff" in item:
                            self._reset_club_management_staff(script_dir)
                        elif "Coaching Staff" in item:
                            self._reset_club_coaching_staff(script_dir)
                        elif "Medical Staff" in item:
                            self._reset_club_medical_staff(script_dir)
                        elif "Kit" in item:
                            self._reset_club_kit(script_dir)
                    elif category == "Sponsors":
                        if "Main Sponsors" in item:
                            self._reset_sponsors_category(script_dir, "main")
                        elif "Supporters" in item:
                            self._reset_sponsors_category(script_dir, "supporters")
                        elif "Charity" in item:
                            self._reset_sponsors_category(script_dir, "charity")
                        elif "Sports Brand" in item:
                            self._reset_sponsors_category(script_dir, "sports_brand")
                    elif category == "Database":
                        if "Team Groups" in item:
                            self._reset_database_table(script_dir, "team_groups")
                        elif "Player Evaluations" in item:
                            self._reset_database_table(script_dir, "player_evaluations")
                        elif "Players" in item:
                            self._reset_database_table(script_dir, "players")
                        elif "Player Profiles" in item:
                            self._reset_database_table(script_dir, "player_profile")
                        elif "Player Attendance" in item:
                            self._reset_database_table(script_dir, "player_attendance")
                        elif "Player Languages" in item:
                            self._reset_database_table(script_dir, "player_languages")
                        elif "Player Hobbies" in item:
                            self._reset_database_table(script_dir, "player_hobbies")
                        elif "Player Social Media" in item:
                            self._reset_database_table(script_dir, "player_social_media")
                        elif "Player Children" in item:
                            self._reset_database_table(script_dir, "player_children")
                        elif "Player Academic" in item:
                            self._reset_database_table(script_dir, "player_academic_achievements")
                        elif "Player Coaching" in item:
                            self._reset_database_table(script_dir, "player_coaching_licenses")
                        elif "Player Club History" in item:
                            self._reset_database_table(script_dir, "player_club_history")
                        elif "Player Awards" in item:
                            self._reset_database_table(script_dir, "player_awards")
                        elif "Position Grid" in item:
                            self._reset_database_table(script_dir, "position_grid")

                    success_count += 1

                except Exception as e:
                    error_count += 1
                    errors.append(f"{item}: {str(e)}")

            # Refresh Club Information window if club data was changed
            if club_data_changed:
                self._refresh_club_information_window()

            # Show results
            self._show_reset_results(success_count, error_count, errors)

        except Exception as e:
            QMessageBox.critical(
                self,
                self.tr("Reset Error"),
                self.tr("An unexpected error occurred during the reset operation:\n\n{0}").format(str(e))
            )

    def _refresh_club_information_window(self):
        """Refresh the Club Information window if it's open."""
        try:
            # Find the main window
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if not app:
                return

            # Find the main window
            main_window = None
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'club_window') and widget.__class__.__name__ == 'MainWindow':
                    main_window = widget
                    break

            if not main_window:
                print("Main window not found for club data refresh")
                return

            # Check if club window exists and is open
            if hasattr(main_window, 'club_window') and main_window.club_window:
                club_window = main_window.club_window
                print("Refreshing Club Information window after reset...")

                # Force reload the club data manager
                if hasattr(club_window, 'club_data_manager'):
                    club_window.club_data_manager.load_data()

                # Reload the UI data
                if hasattr(club_window, '_load_club_data'):
                    club_window._load_club_data()

                    # Force trigger the save methods to sync UI values with the manager
                    # This ensures that when the window closes, it saves the correct (reset) values

                    # General Information fields
                    if hasattr(club_window, '_save_club_name'):
                        club_window._save_club_name()
                    if hasattr(club_window, '_save_short_name'):
                        club_window._save_short_name()
                    if hasattr(club_window, '_save_nickname'):
                        club_window._save_nickname()
                    if hasattr(club_window, '_save_city'):
                        club_window._save_city()
                    if hasattr(club_window, '_save_country'):
                        # Get the current combo box index and trigger save
                        if hasattr(club_window, 'country_combo'):
                            current_index = club_window.country_combo.currentIndex()
                            club_window._save_country(current_index)
                    if hasattr(club_window, '_save_region'):
                        club_window._save_region()

                    # Stadium Information fields
                    if hasattr(club_window, '_save_stadium_name'):
                        club_window._save_stadium_name()
                    if hasattr(club_window, '_save_capacity'):
                        if hasattr(club_window, 'capacity_input'):
                            club_window._save_capacity(club_window.capacity_input.value())
                    if hasattr(club_window, '_save_seating_capacity'):
                        if hasattr(club_window, 'seating_capacity_input'):
                            club_window._save_seating_capacity(club_window.seating_capacity_input.value())
                    if hasattr(club_window, '_save_surface_type'):
                        if hasattr(club_window, 'surface_combo'):
                            current_index = club_window.surface_combo.currentIndex()
                            club_window._save_surface_type(current_index)
                    if hasattr(club_window, '_save_year_built'):
                        if hasattr(club_window, 'year_built_input'):
                            club_window._save_year_built(club_window.year_built_input.value())
                    if hasattr(club_window, '_save_stadium_owner'):
                        if hasattr(club_window, 'stadium_owner_combo'):
                            current_index = club_window.stadium_owner_combo.currentIndex()
                            club_window._save_stadium_owner(current_index)

                    # Contact Information fields
                    if hasattr(club_window, '_save_contact_address1'):
                        club_window._save_contact_address1()
                    if hasattr(club_window, '_save_contact_address2'):
                        club_window._save_contact_address2()
                    if hasattr(club_window, '_save_contact_city'):
                        club_window._save_contact_city()
                    if hasattr(club_window, '_save_contact_state'):
                        club_window._save_contact_state()
                    if hasattr(club_window, '_save_contact_postal'):
                        club_window._save_contact_postal()
                    if hasattr(club_window, '_save_contact_phone'):
                        club_window._save_contact_phone()
                    if hasattr(club_window, '_save_contact_email'):
                        club_window._save_contact_email()
                    if hasattr(club_window, '_save_contact_website'):
                        club_window._save_contact_website()

                    # Force refresh kit previews and settings after reset
                    if hasattr(club_window, '_update_kit_preview'):
                        kit_types = ["1st", "2nd", "3rd", "1st_gk", "2nd_gk"]
                        kit_ids = {"1st": "1", "2nd": "2", "3rd": "3", "1st_gk": "4", "2nd_gk": "5"}
                        for kit_type in kit_types:
                            kit_id = kit_ids[kit_type]
                            # Update both front and back previews
                            club_window._update_kit_preview(kit_id, "front")
                            club_window._update_kit_preview(kit_id, "back")

                    # Force refresh kit text previews (fonts, colors, etc.)
                    if hasattr(club_window, '_update_kit_text_previews'):
                        kit_types = ["1st", "2nd", "3rd", "1st_gk", "2nd_gk"]
                        for kit_type in kit_types:
                            club_window._update_kit_text_previews(kit_type)

                    # Force refresh kit color previews
                    if hasattr(club_window, '_update_kit_color_preview'):
                        club_window._update_kit_color_preview()

                    # Force refresh team groups staff data after staff reset
                    if hasattr(club_window, 'team_groups_widget'):
                        team_groups_widget = club_window.team_groups_widget
                        if hasattr(team_groups_widget, 'refresh_staff_data'):
                            team_groups_widget.refresh_staff_data()
                            print("Team Groups staff data refreshed")

                    # Force refresh staff tables after staff reset
                    # The staff tables are part of the main club window, not a separate widget
                    self._refresh_club_window_staff_data(club_window)

                    # Force refresh sponsors after sponsor reset
                    if hasattr(club_window, 'sponsors_panel'):
                        sponsors_panel = club_window.sponsors_panel
                        if hasattr(sponsors_panel, 'refresh_all_sponsors'):
                            sponsors_panel.refresh_all_sponsors()
                            print("Sponsors panel refreshed")
                        elif hasattr(sponsors_panel, 'save_data'):
                            # Force reload sponsors data
                            if hasattr(sponsors_panel, 'sponsors_manager'):
                                sponsors_panel.sponsors_manager.load_sponsors()
                            print("Sponsors data reloaded")

                    print("Club Information window refreshed and synchronized successfully")
                else:
                    print("Warning: _load_club_data method not found in club window")
            else:
                print("Club Information window is not currently open")

        except Exception as e:
            print(f"Error refreshing Club Information window: {e}")

    def _refresh_club_window_staff_data(self, club_window):
        """Centralized method to refresh all staff data in the club window."""
        try:
            # Force reload club data manager to get fresh data from JSON
            if hasattr(club_window, 'club_data_manager'):
                club_window.club_data_manager.load_data()
                print("Club data manager reloaded from JSON")

            # Refresh individual staff tables
            if hasattr(club_window, '_load_management_staff_data'):
                club_window._load_management_staff_data()
                print("Management staff table refreshed")
            if hasattr(club_window, '_load_coaching_staff_data'):
                club_window._load_coaching_staff_data()
                print("Coaching staff table refreshed")
            if hasattr(club_window, '_load_medical_staff_data'):
                club_window._load_medical_staff_data()
                print("Medical staff table refreshed")

            print("All staff data refreshed successfully")
        except Exception as e:
            print(f"Error refreshing staff data: {e}")

    def _show_reset_results(self, success_count, error_count, errors):
        """Show the results of the reset operation."""
        if error_count == 0:
            QMessageBox.information(
                self,
                self.tr("Reset Complete"),
                self.tr("Successfully reset {0} data type(s).\n\n"
                        "⚠️ Important: Please restart the application to see all changes take effect, "
                        "especially for kit images and some UI elements.").format(success_count)
            )
        else:
            error_details = "\n".join(errors) if errors else self.tr("Unknown errors occurred")
            QMessageBox.warning(
                self,
                self.tr("Reset Completed with Errors"),
                self.tr("Reset completed with {0} success(es) and {1} error(s).\n\n"
                        "⚠️ Important: Please restart the application to see all changes take effect.\n\n"
                        "Errors:\n{2}").format(
                    success_count, error_count, error_details
                )
            )

    def _reset_club_general_info(self, script_dir):
        """Reset club general information and delete club logo."""
        from app.data.club_data_manager import ClubDataManager
        import os

        manager = ClubDataManager()
        defaults = manager._get_default_data()

        # Delete club logo file
        project_root = script_dir
        club_logo_dir = project_root / "media" / "club_logo"
        club_logo_file = club_logo_dir / "Club_logo.png"

        print(f"DEBUG: Looking for club logo: {club_logo_file}")
        if club_logo_file.exists():
            try:
                os.remove(club_logo_file)
                print(f"DEBUG: ✅ DELETED club logo: {club_logo_file}")
            except Exception as e:
                print(f"DEBUG: ❌ Error deleting club logo: {e}")
        else:
            print(f"DEBUG: Club logo file not found: {club_logo_file}")

        # Reset general info fields
        general_fields = ["club_name", "short_name", "nickname", "year_founded", "city", "country", "region"]
        for field in general_fields:
            manager.set_data(field, defaults[field])

        manager.save_data()
        print("General information reset: data and club logo deleted")

    def _reset_club_stadium_info(self, script_dir):
        """Reset club stadium information and delete stadium image."""
        from app.data.club_data_manager import ClubDataManager
        import os

        manager = ClubDataManager()
        defaults = manager._get_default_data()

        # Delete stadium image file
        project_root = script_dir
        stadium_dir = project_root / "media" / "stadium"
        stadium_file = stadium_dir / "stadium.png"

        print(f"DEBUG: Looking for stadium image: {stadium_file}")
        if stadium_file.exists():
            try:
                os.remove(stadium_file)
                print(f"DEBUG: ✅ DELETED stadium image: {stadium_file}")
            except Exception as e:
                print(f"DEBUG: ❌ Error deleting stadium image: {e}")
        else:
            print(f"DEBUG: Stadium image file not found: {stadium_file}")

        # Reset stadium info fields
        stadium_fields = ["stadium_name", "capacity", "seating_capacity", "surface_type", "year_built", "stadium_owner", "stadium_image_exists"]
        for field in stadium_fields:
            manager.set_data(field, defaults[field])

        manager.save_data()
        print("Stadium information reset: data and stadium image deleted")

    def _reset_club_contact_info(self, script_dir):
        """Reset club contact information."""
        from app.data.club_data_manager import ClubDataManager

        manager = ClubDataManager()
        defaults = manager._get_default_data()

        # Reset contact info fields
        contact_fields = ["contact_address1", "contact_address2", "contact_city", "contact_state",
                         "contact_postal", "contact_phone", "contact_email", "contact_website"]
        for field in contact_fields:
            manager.set_data(field, defaults[field])

        manager.save_data()

    def _reset_club_management_staff(self, script_dir):
        """Reset club management staff and clear group assignments."""
        from app.data.club_data_manager import ClubDataManager
        from app.data.roster_manager import RosterManager

        manager = ClubDataManager()
        defaults = manager._get_default_data()

        manager.set_data("management_staff", defaults["management_staff"])
        manager.save_data()

        # Clear management staff assignments from team groups
        roster_manager = RosterManager()
        groups = roster_manager.get_all_team_groups()
        for group in groups:
            group_id = group.get('group_id')
            if group_id:
                # Clear M1 and M2 columns (management staff assignments)
                update_data = {'M1': None, 'M2': None}
                roster_manager.update_team_group(group_id, update_data)
        print("Management staff reset and group assignments cleared")

    def _reset_club_coaching_staff(self, script_dir):
        """Reset club coaching staff and clear group assignments."""
        from app.data.club_data_manager import ClubDataManager
        from app.data.roster_manager import RosterManager

        print("DEBUG: Starting coaching staff reset...")
        manager = ClubDataManager()
        defaults = manager._get_default_data()

        print(f"DEBUG: Default coaching_staff: {defaults['coaching_staff']}")
        print(f"DEBUG: Current coaching_staff before reset: {manager.get_data('coaching_staff')}")

        manager.set_data("coaching_staff", defaults["coaching_staff"])
        print(f"DEBUG: Coaching_staff after set_data: {manager.get_data('coaching_staff')}")

        manager.save_data()
        print("DEBUG: Data saved to JSON file")

        # Verify the save worked
        manager_verify = ClubDataManager()
        print(f"DEBUG: Coaching_staff after reload: {manager_verify.get_data('coaching_staff')}")

        # Clear coaching staff assignments from team groups
        roster_manager = RosterManager()
        groups = roster_manager.get_all_team_groups()
        for group in groups:
            group_id = group.get('group_id')
            if group_id:
                # Clear C1, C2, C3, C4 columns (coaching staff assignments)
                update_data = {'C1': None, 'C2': None, 'C3': None, 'C4': None}
                roster_manager.update_team_group(group_id, update_data)
        print("Coaching staff reset and group assignments cleared")

    def _reset_club_medical_staff(self, script_dir):
        """Reset club medical staff and clear group assignments."""
        from app.data.club_data_manager import ClubDataManager
        from app.data.roster_manager import RosterManager

        manager = ClubDataManager()
        defaults = manager._get_default_data()

        manager.set_data("medical_staff", defaults["medical_staff"])
        manager.save_data()

        # Clear medical staff assignments from team groups
        roster_manager = RosterManager()
        groups = roster_manager.get_all_team_groups()
        for group in groups:
            group_id = group.get('group_id')
            if group_id:
                # Clear D1 column (medical staff assignments)
                update_data = {'D1': None}
                roster_manager.update_team_group(group_id, update_data)
        print("Medical staff reset and group assignments cleared")

    def _reset_club_kit(self, script_dir):
        """Reset club kit colors, settings, and clear kit images from app."""
        from app.data.club_data_manager import ClubDataManager

        manager = ClubDataManager()
        defaults = manager._get_default_data()

        # Reset basic kit color fields
        kit_color_fields = ["color_1st", "color_2nd", "color_3rd"]
        for field in kit_color_fields:
            manager.set_data(field, defaults[field])

        # Reset ALL kit-related fields (images, fonts, colors, outlines, etc.)
        kit_types = ["1st", "2nd", "3rd", "1st_gk", "2nd_gk"]
        kit_sides = ["front", "back"]
        setting_elements = ["name", "number"]
        kit_ids = {"1st": "1", "2nd": "2", "3rd": "3", "1st_gk": "4", "2nd_gk": "5"}

        for key in kit_types:
            kit_id = kit_ids[key]

            # Reset image existence flags
            for side in kit_sides:
                field_name = f"kit_{kit_id}_{side}_exists"
                if field_name in defaults:
                    manager.set_data(field_name, defaults[field_name])

            # Reset all text settings (font, color, size, position, outline)
            for element in setting_elements:
                # Font color
                field_name = f"kit_{kit_id}_{element}_color"
                if field_name in defaults:
                    manager.set_data(field_name, defaults[field_name])

                # Font family
                field_name = f"kit_{kit_id}_{element}_font_family"
                if field_name in defaults:
                    manager.set_data(field_name, defaults[field_name])

                # Font size
                field_name = f"kit_{kit_id}_{element}_font_size"
                if field_name in defaults:
                    manager.set_data(field_name, defaults[field_name])

                # Vertical position
                field_name = f"kit_{kit_id}_{element}_vpos"
                if field_name in defaults:
                    manager.set_data(field_name, defaults[field_name])

                # Outline enabled (must be False)
                field_name = f"kit_{kit_id}_{element}_outline_enabled"
                if field_name in defaults:
                    manager.set_data(field_name, False)

                # Outline color
                field_name = f"kit_{kit_id}_{element}_outline_color"
                if field_name in defaults:
                    manager.set_data(field_name, defaults[field_name])

                # Outline thickness
                field_name = f"kit_{kit_id}_{element}_outline_thickness"
                if field_name in defaults:
                    manager.set_data(field_name, defaults[field_name])

        manager.save_data()
        print("Kit data reset: All kit settings, colors, fonts, and image flags have been reset to defaults")

    def _reset_sponsors_category(self, script_dir, category):
        """Reset a specific sponsors category and delete sponsor image files."""
        from app.data.club_sponsors_manager import ClubSponsorsManager
        import os
        from pathlib import Path

        print(f"DEBUG: Starting sponsor reset for category '{category}'")
        print(f"DEBUG: script_dir = {script_dir}")
        manager = ClubSponsorsManager()
        defaults = manager._get_default_data()

        if category in defaults:
            print(f"DEBUG: Category '{category}' found in defaults")

            # Delete actual image files for this category
            # script_dir is already the project root (C:\Users\<USER>\Desktop\532)
            project_root = script_dir
            print(f"DEBUG: Using script_dir as project_root = {project_root}")
            sponsor_image_dir = project_root / "media" / "sponsors" / category
            print(f"DEBUG: Looking for images in: {sponsor_image_dir}")

            if sponsor_image_dir.exists():
                print(f"DEBUG: Directory exists, listing all files...")
                try:
                    all_files = list(sponsor_image_dir.iterdir())
                    print(f"DEBUG: Files found: {[f.name for f in all_files]}")
                except Exception as e:
                    print(f"DEBUG: Error listing files: {e}")

                deleted_count = 0
                # Delete main1.png, main2.png, main3.png specifically
                for index in [1, 2, 3]:
                    image_filename = f"{category}{index}.png"  # main1.png, main2.png, main3.png
                    image_filepath = sponsor_image_dir / image_filename
                    print(f"DEBUG: Checking: {image_filepath}")
                    if image_filepath.exists():
                        try:
                            os.remove(image_filepath)
                            print(f"DEBUG: ✅ DELETED: {image_filepath}")
                            deleted_count += 1
                        except Exception as e:
                            print(f"DEBUG: ❌ Error deleting {image_filepath}: {e}")
                    else:
                        print(f"DEBUG: File not found: {image_filepath}")
                print(f"DEBUG: Total deleted: {deleted_count} files")
            else:
                print(f"DEBUG: Image directory does not exist: {sponsor_image_dir}")

            # Reset sponsor data to defaults
            manager._data[category] = defaults[category]
            print(f"DEBUG: Data reset to defaults")

            # Clear all logo_exists flags for this category
            num_sponsors = len(manager._data[category])
            for index in range(num_sponsors):
                manager.set_logo_exists(category, index, False)

            manager.save_sponsors()
            print(f"DEBUG: Data saved to file")
            print(f"Sponsor category '{category}' reset: data and image files deleted")
        else:
            print(f"DEBUG: Category '{category}' NOT found in defaults. Available: {list(defaults.keys())}")

    def _reset_database_table(self, script_dir, table_name):
        """Reset a database table by clearing all data."""
        import sqlite3

        db_path = script_dir / "app" / "data" / "footdata.db"

        if not db_path.exists():
            raise FileNotFoundError(f"Database file not found: {db_path}")

        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                raise ValueError(f"Table '{table_name}' does not exist in database")

            # Clear the table
            cursor.execute(f"DELETE FROM {table_name}")

            # Reset auto-increment counter if the table has an auto-increment primary key
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            table_sql = cursor.fetchone()
            if table_sql and "AUTOINCREMENT" in table_sql[0].upper():
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table_name}'")

            conn.commit()
            conn.close()

        except sqlite3.Error as e:
            if 'conn' in locals():
                conn.close()
            raise Exception(f"Database error: {e}")

    def _save_section_state(self, section_name, collapsed):
        """Save the collapsed state of a section to settings."""
        settings = QSettings()
        settings.setValue(f"developer_settings/reset_data/{section_name}_collapsed", collapsed)

    def _restore_section_state(self, section_name, groupbox, default_collapsed=False):
        """Restore the collapsed state of a section from settings."""
        settings = QSettings()
        collapsed = settings.value(f"developer_settings/reset_data/{section_name}_collapsed", default_collapsed, type=bool)
        groupbox.set_collapsed(collapsed)

    def _restore_window_state(self):
        """Restore window geometry."""
        settings = QSettings()

        # Restore window geometry
        geometry = settings.value("developer_settings/geometry")
        if geometry:
            self.restoreGeometry(geometry)

    def _restore_section_states(self):
        """Restore the collapsed states of all Reset Data sections."""
        # Restore section states
        self._restore_section_state("warning", self.warning_groupbox, default_collapsed=False)
        self._restore_section_state("club_data", self.club_groupbox, default_collapsed=False)
        self._restore_section_state("evaluations_data", self.evaluations_groupbox, default_collapsed=False)
        self._restore_section_state("roster_data", self.roster_groupbox, default_collapsed=False)

    def closeEvent(self, event):
        """Save window state when closing."""
        settings = QSettings()

        # Save window geometry
        settings.setValue("developer_settings/geometry", self.saveGeometry())

        # Accept the close event
        event.accept()

    def retranslateUi(self):
        """Update all translatable strings in the UI."""
        self.setWindowTitle(self.tr("Developer Settings"))

        # Update tab titles
        self.tab_widget.setTabText(0, self.tr("Developer Mode"))

        # Only update other tab titles if they're visible
        if hasattr(self, 'version_level_tab_index') and self.tab_widget.isTabVisible(self.version_level_tab_index):
            self.tab_widget.setTabText(self.version_level_tab_index, self.tr("Application Version Level"))

        if hasattr(self, 'log_levels_tab_index') and self.tab_widget.isTabVisible(self.log_levels_tab_index):
            self.tab_widget.setTabText(self.log_levels_tab_index, self.tr("Log Levels"))

        if hasattr(self, 'debug_options_tab_index') and self.tab_widget.isTabVisible(self.debug_options_tab_index):
            self.tab_widget.setTabText(self.debug_options_tab_index, self.tr("Debug Options"))

        if hasattr(self, 'reset_data_tab_index') and self.tab_widget.isTabVisible(self.reset_data_tab_index):
            self.tab_widget.setTabText(self.reset_data_tab_index, self.tr("Reset Data"))

        # Developer Mode tab
        if hasattr(self, 'dev_mode_checkbox'):
            self.dev_mode_checkbox.setText(self.tr("Enable Developer Mode"))

        if hasattr(self, 'password_security_warning'):
            if self.permission_manager.is_using_default_password():
                self.password_security_warning.setText(
                    self.tr("Warning: Using default password. For security, please change the password.")
                )
            else:
                self.password_security_warning.setText(
                    self.tr("Using custom password. Good security practice!")
                )

        if hasattr(self, 'change_password_button'):
            self.change_password_button.setText(self.tr("Change Password"))

        if hasattr(self, 'password_hint_button'):
            self.password_hint_button.setText(self.tr("Show Password Hint"))

        # Update password hint field placeholder
        if hasattr(self, 'password_hint_field'):
            self.password_hint_field.setPlaceholderText(self.tr("Enter a hint to help remember your password"))

        # Update password field tooltips and text
        if hasattr(self, 'current_show_button'):
            self.current_show_button.setToolTip(self.tr("Show/hide password"))
            # Only update text if it's not already set by the toggle state
            if not self.current_show_button.isChecked():
                self.current_show_button.setText(self.tr("Show"))
            else:
                self.current_show_button.setText(self.tr("Hide"))

        if hasattr(self, 'new_show_button'):
            self.new_show_button.setToolTip(self.tr("Show/hide password"))
            if not self.new_show_button.isChecked():
                self.new_show_button.setText(self.tr("Show"))
            else:
                self.new_show_button.setText(self.tr("Hide"))

        if hasattr(self, 'confirm_show_button'):
            self.confirm_show_button.setToolTip(self.tr("Show/hide password"))
            if not self.confirm_show_button.isChecked():
                self.confirm_show_button.setText(self.tr("Show"))
            else:
                self.confirm_show_button.setText(self.tr("Hide"))

        # Version Level tab
        if hasattr(self, 'version_level_message'):
            self.version_level_message.setText(self.tr("Version level can only be changed in Developer Mode"))

        # Log Levels tab
        if hasattr(self, 'debug_groupbox'):
            self.debug_groupbox.setTitle(self.tr("Component Log Levels"))

            # Retranslate logger labels
            for i, (display_name, logger_name) in enumerate(LOGGERS_TO_CONTROL.items()):
                item = self.debug_layout.itemAtPosition(i, 0)
                if item and item.widget():
                    label = item.widget()
                    label.setText(self.tr(display_name) + ":")

        # Close button
        if hasattr(self, 'close_button'):
            self.close_button.setText(self.tr("Close"))
