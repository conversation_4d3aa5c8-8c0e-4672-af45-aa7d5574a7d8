import logging
from PySide6.QtCore import QObject, QEvent, QSettings, QByteArray, QCoreApplication, Qt
from PySide6.QtWidgets import QWidget, QApplication, QMainWindow, QDialog, QMessageBox, QMenu, QFrame

log = logging.getLogger(__name__)

# Import RosterPage only for type checking and skipping its handling
try:
    # Use a forward reference string if RosterPage is complex or causes circular imports
    # from ..pages.roster_page import RosterPage
    # Or dynamically import if necessary, but direct import is cleaner if possible
    from app.pages.roster_page import RosterPage
    ROSTER_PAGE_CLASS_NAME = RosterPage.__name__
except ImportError:
    log.warning("Could not import RosterPage. Skipping exclusion logic for it.")
    RosterPage = None # Set to None if import fails
    ROSTER_PAGE_CLASS_NAME = "RosterPage" # Fallback to string comparison

# --- Classes that should have geometry persistence (whitelist approach) ---
ALLOWED_CLASSES = {
    "QMainWindow",  # Main application windows
    "MainWindow",   # Our custom MainWindow class
    "QDialog",      # Dialog windows
    "NewMatchPage", # Custom page windows
    "MatchesPage",  # Custom page windows
    "OptionsPage",  # Custom page windows
    "ClubWindow",   # Custom windows
    "RosterWindow", # Custom windows
    # Add other custom window classes as needed
}

# --- Add classes to explicitly ignore (fallback for edge cases) ---
IGNORED_CLASSES = {
    ROSTER_PAGE_CLASS_NAME, # Already handled
    QMessageBox.__name__,
    QMenu.__name__,
    QFrame.__name__, # Ignore frames treated as windows for now
}
# -------------------------------------

class GeometryPersistenceFilter(QObject):
    """
    An event filter that automatically saves and restores the geometry
    (size and position) of top-level windows using QSettings.

    It skips windows that have missing objectName or are of a specific
    type (e.g., RosterPage) that handles its own persistence.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        # Ensure QSettings has organization/app name set beforehand
        if not QCoreApplication.organizationName() or not QCoreApplication.applicationName():
            log.warning("QSettings may not work reliably: OrganizationName or ApplicationName not set.")

    def eventFilter(self, watched: QObject, event: QEvent) -> bool:
        # Ensure the object is a QWidget and specifically a window
        if not isinstance(watched, QWidget) or not watched.isWindow():
            return super().eventFilter(watched, event)

        widget = watched # Cast for clarity
        widget_class_name = widget.__class__.__name__

        # --- Only handle allowed classes (whitelist approach) --- #
        if widget_class_name not in ALLOWED_CLASSES:
             # Skip widgets that don't need geometry persistence
             return super().eventFilter(watched, event)

        # --- Skip specific ignored classes (fallback) --- #
        if widget_class_name in IGNORED_CLASSES:
             return super().eventFilter(watched, event)
        # ------------------------------------- #

        # Debug: Log events for MainWindow
        if widget_class_name == "MainWindow":
            log.debug(f"🔍 MainWindow event: {event.type()}")

        # --- Restore Geometry on Polish (before showing) ---
        if event.type() == QEvent.Type.Polish:
            object_name = widget.objectName()
            if not object_name:
                # Only log once per widget class instance to avoid spam
                if not getattr(widget, "_geom_persist_warned", False):
                    # Use the stored class name
                    log.warning(f"Cannot restore geometry for window of type {widget_class_name} because objectName is not set.")
                    widget._geom_persist_warned = True # Mark as warned
                return super().eventFilter(watched, event)

            # Reset warned flag if object name is now present
            if getattr(widget, "_geom_persist_warned", False):
                 delattr(widget, "_geom_persist_warned")

            settings = QSettings()
            key = f"WindowGeometry/{object_name}"
            geometry_data = settings.value(key)

            if isinstance(geometry_data, QByteArray) and not geometry_data.isEmpty():
                print(f"🔄 Restoring geometry for {object_name} from {key}")
                print(f"🔄 Geometry data size: {len(geometry_data)} bytes")
                if widget.restoreGeometry(geometry_data):
                    print(f"✅ Successfully restored geometry for {object_name}")
                    print(f"✅ Restored position: {widget.pos()}")
                    print(f"✅ Restored size: {widget.size()}")
                    #log.debug(f"Restored geometry for {object_name}")
                    pass # Successfully restored
                else:
                    print(f"❌ Failed to restore geometry for {object_name} from key {key}")
                    log.warning(f"Failed to restore geometry for {object_name} from key {key}")
            elif geometry_data is not None: # Data exists but is wrong type/empty
                print(f"⚠️ Invalid geometry data found for {object_name} at key {key}")
                log.warning(f"Invalid geometry data found for {object_name} at key {key}")
            else:
                print(f"ℹ️ No geometry data found for {object_name} at key {key}")


        # --- Save Geometry on Close ---
        elif event.type() == QEvent.Type.Close:
            object_name = widget.objectName()
            if not object_name:
                 # Warning potentially logged during Polish event
                 # If it was set later, we still can't save without it here
                 if not getattr(widget, "_geom_persist_warned_close", False):
                     log.warning(f"Cannot save geometry for closing window of type {widget_class_name} because objectName is not set.")
                     widget._geom_persist_warned_close = True
                 return super().eventFilter(watched, event)

            # Reset warned flag if object name is now present
            if getattr(widget, "_geom_persist_warned_close", False):
                 delattr(widget, "_geom_persist_warned_close")

            # Ensure the widget is not hidden (might be closing implicitly)
            # and has a valid size (sometimes closing happens before proper sizing)
            if not widget.isHidden() and widget.size().isValid() and not widget.isMinimized():
                log.debug(f"💾 Saving geometry for {object_name}")
                log.debug(f"💾 Current position: {widget.pos()}")
                log.debug(f"💾 Current size: {widget.size()}")
                settings = QSettings()
                key = f"WindowGeometry/{object_name}"
                geometry_data = widget.saveGeometry()
                if not geometry_data.isEmpty():
                    settings.setValue(key, geometry_data)
                    log.debug(f"✅ Successfully saved geometry for {object_name} to {key}")
                    log.debug(f"✅ Saved data size: {len(geometry_data)} bytes")
                    #log.debug(f"Saved geometry for {object_name}")
                else:
                    log.warning(f"❌ Failed to save geometry for {object_name} (saveGeometry returned empty)")
            else:
                log.debug(f"⏭️ Skipping geometry save for {object_name} (hidden: {widget.isHidden()}, valid size: {widget.size().isValid()}, minimized: {widget.isMinimized()})")
                #log.debug(f"Skipping geometry save for {object_name} (hidden, invalid size, or minimized)")


        # Pass event processing to the base class
        return super().eventFilter(watched, event)

def install_geometry_persistence():
    """Installs the global geometry persistence filter on the QApplication instance."""
    app = QApplication.instance()
    if not app:
        log.error("QApplication instance not found. Cannot install geometry persistence filter.")
        return None

    # Check if already installed
    if hasattr(app, "_geometry_persistence_filter") and app._geometry_persistence_filter:
         log.warning("Geometry persistence filter seems to be already installed.")
         return app._geometry_persistence_filter


    log.info("Installing global geometry persistence filter.")
    filter_instance = GeometryPersistenceFilter(parent=app)
    app.installEventFilter(filter_instance)

    # Store reference on app to prevent garbage collection and allow checking
    app._geometry_persistence_filter = filter_instance
    return filter_instance