from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QSplitter,
    QGroupBox, QScrollArea, QFormLayout, QLabel, QGridLayout, QTabWidget
)
from PySide6.QtCore import Qt, QCoreApplication, QSettings
from app.data.club_data_manager import ClubDataManager
from app.utils.debug_utils import match_page_debug


class MatchesPage(QWidget):
    """A page for managing matches."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("MatchesPage")
        self.setWindowTitle(self.tr("Matches"))
        # Set window flags to create a standalone window
        self.setWindowFlags(Qt.WindowType.Window)
        # Ensure it behaves as a normal window that doesn't block other windows
        self.setWindowModality(Qt.WindowModality.NonModal)

        # Track new match window
        self.new_match_window = None

        # Initialize club data manager
        self.club_data_manager = ClubDataManager()

        # Initialize UI
        self._init_ui()

        # Restore window state after UI is created
        self._restore_window_state()

    def _init_ui(self):
        """Initialize the user interface."""
        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create top section with buttons (no border)
        self._create_top_section(main_layout)

        # Create vertical splitter for left and right panels
        self._create_splitter_section(main_layout)

    def _create_top_section(self, main_layout):
        """Create the top section with basic buttons."""
        # Create horizontal layout for buttons
        button_layout = QHBoxLayout()

        # Create buttons
        self.load_match_btn = QPushButton(self.tr("Load Match"))
        self.add_new_match_btn = QPushButton(self.tr("Add New Match"))

        # Add buttons to layout
        button_layout.addWidget(self.load_match_btn)
        button_layout.addWidget(self.add_new_match_btn)

        # Add spacer to push buttons to the left
        button_layout.addStretch()

        # Connect button signals (placeholder for now)
        self.load_match_btn.clicked.connect(self._load_match)
        self.add_new_match_btn.clicked.connect(self._add_new_match)

        # Add button layout to main layout
        main_layout.addLayout(button_layout)

    def _create_splitter_section(self, main_layout):
        """Create the vertical splitter with left and right panels."""
        # Create vertical splitter
        self.splitter = QSplitter(Qt.Orientation.Horizontal)

        # Create left panel with sections
        left_panel = self._create_left_panel()

        # Create right panel with tabs
        right_panel = self._create_right_panel()

        # Add panels to splitter
        self.splitter.addWidget(left_panel)
        self.splitter.addWidget(right_panel)

        # Set initial splitter proportions (50/50)
        self.splitter.setSizes([400, 600])

        # Connect splitter signal to save state when moved
        self.splitter.splitterMoved.connect(self._save_splitter_state)

        # Add splitter to main layout
        main_layout.addWidget(self.splitter)

    def _create_left_panel(self):
        """Create the left panel with sections."""
        # Create scroll area for the left panel
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.Shape.NoFrame)

        # Create content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # Create sections
        sections = [
            "Information",
            "Teams and Score",
            "Statistics",
            "Advanced Statistics",
            "Venue",
            "Officials"
        ]

        for section_name in sections:
            section_group = QGroupBox(self.tr(section_name))

            if section_name == "Information":
                # Create Information section with read-only labels
                section_layout = self._create_information_section()
            elif section_name == "Teams and Score":
                # Create Teams and Score section with read-only labels
                section_layout = self._create_teams_score_section()
            elif section_name == "Statistics":
                # Create Statistics section with read-only labels
                section_layout = self._create_statistics_section()
            elif section_name == "Advanced Statistics":
                # Create Advanced Statistics section with read-only labels
                section_layout = self._create_advanced_statistics_section()
            elif section_name == "Venue":
                # Create Venue section with read-only labels
                section_layout = self._create_venue_section()
            elif section_name == "Officials":
                # Create Officials section with read-only labels
                section_layout = self._create_officials_section()
            else:
                # Add placeholder content for other sections
                section_layout = QVBoxLayout()
                placeholder_widget = QWidget()
                placeholder_widget.setMinimumHeight(50)
                placeholder_widget.setStyleSheet("background-color: #ffffff; border: 1px solid #ddd;")
                section_layout.addWidget(placeholder_widget)

            section_group.setLayout(section_layout)
            content_layout.addWidget(section_group)

        # Add stretch to push sections to top
        content_layout.addStretch()

        # Set the content widget to the scroll area
        scroll_area.setWidget(content_widget)

        return scroll_area

    def _create_right_panel(self):
        """Create the right panel with tabs."""
        # Create tab widget
        tab_widget = QTabWidget()

        # Create tabs
        tabs = [
            ("Players", self._create_players_tab()),
            ("Opponent", self._create_opponent_tab()),
            ("Timeline", self._create_timeline_tab()),
            ("Tactics", self._create_tactics_tab()),
            ("Staff", self._create_staff_tab())
        ]

        # Add tabs to widget
        for tab_name, tab_content in tabs:
            tab_widget.addTab(tab_content, self.tr(tab_name))

        return tab_widget

    def _create_players_tab(self):
        """Create the Players tab content."""
        players_widget = QWidget()
        players_layout = QVBoxLayout(players_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Players tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        players_layout.addWidget(placeholder_label)

        return players_widget

    def _create_opponent_tab(self):
        """Create the Opponent tab content."""
        opponent_widget = QWidget()
        opponent_layout = QVBoxLayout(opponent_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Opponent tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        opponent_layout.addWidget(placeholder_label)

        return opponent_widget

    def _create_timeline_tab(self):
        """Create the Timeline tab content."""
        timeline_widget = QWidget()
        timeline_layout = QVBoxLayout(timeline_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Timeline tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        timeline_layout.addWidget(placeholder_label)

        return timeline_widget

    def _create_tactics_tab(self):
        """Create the Tactics tab content."""
        tactics_widget = QWidget()
        tactics_layout = QVBoxLayout(tactics_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Tactics tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        tactics_layout.addWidget(placeholder_label)

        return tactics_widget

    def _create_staff_tab(self):
        """Create the Staff tab content."""
        staff_widget = QWidget()
        staff_layout = QVBoxLayout(staff_widget)

        # Placeholder content
        placeholder_label = QLabel(self.tr("Staff tab content will be added here"))
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("color: #666; font-style: italic; padding: 20px;")
        staff_layout.addWidget(placeholder_label)

        return staff_widget

    def _create_information_section(self):
        """Create the Information section with read-only labels."""
        form_layout = QFormLayout()

        # Match ID (read-only label)
        self.match_id_label = QLabel(self.tr("No match selected"))
        self.match_id_label.setStyleSheet("color: #666; font-style: italic;")
        form_layout.addRow(self.tr("Match ID:"), self.match_id_label)

        # Date (read-only label)
        self.date_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Date:"), self.date_label)

        # Time (read-only label)
        self.time_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Time:"), self.time_label)

        # Competition (read-only label)
        self.competition_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Competition:"), self.competition_label)

        # Season (read-only label)
        self.season_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Season:"), self.season_label)

        # Matchday (read-only label)
        self.matchday_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Matchday:"), self.matchday_label)

        # Round (read-only label)
        self.round_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Round:"), self.round_label)

        return form_layout

    def _create_teams_score_section(self):
        """Create the Teams and Score section with read-only labels."""
        form_layout = QFormLayout()

        # Home/Away (read-only label)
        self.home_away_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Home/Away:"), self.home_away_label)

        # Opponent (read-only label)
        self.opponent_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Opponent:"), self.opponent_label)

        # Full Time Score (read-only label)
        self.score_label = QLabel(self.tr("-- - --"))
        form_layout.addRow(self.tr("Full Time Score:"), self.score_label)

        # Half Time Score (read-only label)
        self.halftime_score_label = QLabel(self.tr("-- - --"))
        form_layout.addRow(self.tr("Half Time Score:"), self.halftime_score_label)

        # Extra Time Score (read-only label)
        self.extratime_score_label = QLabel(self.tr("-- - --"))
        form_layout.addRow(self.tr("Extra Time Score:"), self.extratime_score_label)

        # Penalty Score (read-only label)
        self.penalty_score_label = QLabel(self.tr("-- - --"))
        form_layout.addRow(self.tr("Penalty Score:"), self.penalty_score_label)

        # Opponents own goals (read-only label)
        self.opponents_own_goals_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Opponents own goals:"), self.opponents_own_goals_label)

        # Result (read-only label)
        self.result_label = QLabel(self.tr("--"))
        self.result_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(self.tr("Result:"), self.result_label)

        # Match duration (read-only label)
        self.match_duration_label = QLabel(self.tr("--"))
        self.match_duration_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(self.tr("Match duration:"), self.match_duration_label)

        # Stoppage time (read-only label)
        self.stoppage_time_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Stoppage time:"), self.stoppage_time_label)

        # ET duration (read-only label)
        self.et_duration_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("ET duration:"), self.et_duration_label)

        # Total duration (read-only label)
        self.total_duration_label = QLabel(self.tr("--"))
        self.total_duration_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        form_layout.addRow(self.tr("Total duration:"), self.total_duration_label)

        return form_layout

    def _get_competition_duration(self, competition_name):
        """Get the duration value for the specified competition."""
        if not competition_name:
            return 90  # Default value

        settings = QSettings()
        settings.beginGroup("football_competitions")
        count = settings.value("count", 0, int)

        for i in range(count):
            name = settings.value(f"{i}/name", "")
            if name == competition_name:
                duration = settings.value(f"{i}/duration", 90, int)
                settings.endGroup()
                return duration

        settings.endGroup()
        return 90  # Default value if not found

    def _update_duration_fields(self, match_data):
        """Update duration-related fields based on match data."""
        # Get competition duration
        competition_name = match_data.get('competition', '')
        match_duration = self._get_competition_duration(competition_name)
        self.match_duration_label.setText(f"{match_duration} min")

        # Get stoppage time from match data
        stoppage_time = match_data.get('stoppage_time', 0)
        self.stoppage_time_label.setText(f"{stoppage_time} min")

        # Get ET duration from match data
        et_duration = match_data.get('et_duration', 0) if match_data.get('et_played', False) else 0
        if et_duration > 0:
            self.et_duration_label.setText(f"{et_duration} min")
        else:
            self.et_duration_label.setText("--")

        # Calculate and display total duration
        total_duration = match_duration + stoppage_time + et_duration
        self.total_duration_label.setText(f"{total_duration} min")

    def _create_statistics_section(self):
        """Create the Statistics section with read-only labels."""
        stats_layout = QVBoxLayout()

        # Create header with team names
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Empty label for the first column (stat name)
        header_layout.addWidget(QLabel(""), 1)

        # Team name labels
        self.team_name_label = QLabel(self._get_club_name())
        self.opponent_name_label = QLabel(self.tr("Opponent"))

        # Set alignment and style for team name labels
        for label in [self.team_name_label, self.opponent_name_label]:
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            font = label.font()
            font.setBold(True)
            label.setFont(font)

        header_layout.addWidget(self.team_name_label, 1)
        header_layout.addWidget(self.opponent_name_label, 1)

        stats_layout.addWidget(header_widget)

        # Create grid for statistics
        stats_grid_widget = QWidget()
        stats_grid = QGridLayout(stats_grid_widget)
        stats_grid.setContentsMargins(0, 0, 0, 0)

        # Define statistics fields
        stat_fields = [
            "final_score",
            "attempts",
            "on_target",
            "passes",
            "completed_passes",
            "fouls",
            "yellow_cards",
            "red_cards",
            "offsides",
            "corners",
            "possession",
            "xgoals"
        ]

        # Define display names for statistics
        stat_display_names = {
            "final_score": self.tr("Final Score"),
            "attempts": self.tr("Attempts"),
            "on_target": self.tr("On Target"),
            "passes": self.tr("Passes"),
            "completed_passes": self.tr("Completed Passes"),
            "fouls": self.tr("Fouls Committed"),
            "yellow_cards": self.tr("Yellow Cards"),
            "red_cards": self.tr("Red Cards"),
            "offsides": self.tr("Offsides"),
            "corners": self.tr("Corners"),
            "possession": self.tr("Possession (%)"),
            "xgoals": self.tr("xGoals")
        }

        # Create labels for each statistic
        self.team_stats_labels = {}
        self.opponent_stats_labels = {}

        # Add each statistic to the grid
        for row, field in enumerate(stat_fields):
            # Add label
            label = QLabel(stat_display_names[field])

            # Make the Final Score label bold and more visible
            if field == "final_score":
                font = label.font()
                font.setBold(True)
                font.setPointSize(font.pointSize() + 1)
                label.setFont(font)

            stats_grid.addWidget(label, row, 0)

            # Create read-only labels for team and opponent stats
            team_stat_label = QLabel(self.tr("--"))
            opponent_stat_label = QLabel(self.tr("--"))

            # Set alignment for stat labels
            team_stat_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            opponent_stat_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            # Special styling for Final Score
            if field == "final_score":
                for score_label in [team_stat_label, opponent_stat_label]:
                    font = score_label.font()
                    font.setBold(True)
                    score_label.setFont(font)

            # Add labels to the grid
            stats_grid.addWidget(team_stat_label, row, 1)
            stats_grid.addWidget(opponent_stat_label, row, 2)

            # Store references to the labels
            self.team_stats_labels[field] = team_stat_label
            self.opponent_stats_labels[field] = opponent_stat_label

        stats_layout.addWidget(stats_grid_widget)

        return stats_layout

    def _create_advanced_statistics_section(self):
        """Create the Advanced Statistics section with read-only labels."""
        adv_stats_layout = QVBoxLayout()

        # Create header with team names
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Empty label for the first column (stat name)
        header_layout.addWidget(QLabel(""), 1)

        # Team name labels
        self.adv_team_name_label = QLabel(self._get_club_name())
        self.adv_opponent_name_label = QLabel(self.tr("Opponent"))

        # Set alignment and style for team name labels
        for label in [self.adv_team_name_label, self.adv_opponent_name_label]:
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            font = label.font()
            font.setBold(True)
            label.setFont(font)

        header_layout.addWidget(self.adv_team_name_label, 1)
        header_layout.addWidget(self.adv_opponent_name_label, 1)

        adv_stats_layout.addWidget(header_widget)

        # Create grid for advanced statistics
        adv_stats_grid_widget = QWidget()
        adv_stats_grid = QGridLayout(adv_stats_grid_widget)
        adv_stats_grid.setContentsMargins(0, 0, 0, 0)

        # Define advanced statistics fields
        adv_stat_fields = [
            "crosses",
            "touches",
            "tackles",
            "interceptions",
            "aerials_won",
            "clearances",
            "long_balls",
            "saves"
        ]

        # Define display names for advanced statistics
        adv_stat_display_names = {
            "crosses": self.tr("Crosses"),
            "touches": self.tr("Touches"),
            "tackles": self.tr("Tackles"),
            "interceptions": self.tr("Interceptions"),
            "aerials_won": self.tr("Aerials Won"),
            "clearances": self.tr("Clearances"),
            "long_balls": self.tr("Long Balls"),
            "saves": self.tr("Saves")
        }

        # Create labels for each advanced statistic
        self.team_adv_stats_labels = {}
        self.opponent_adv_stats_labels = {}

        # Add each advanced statistic to the grid
        for row, field in enumerate(adv_stat_fields):
            # Add label
            label = QLabel(adv_stat_display_names[field])
            adv_stats_grid.addWidget(label, row, 0)

            # Create read-only labels for team and opponent stats
            team_stat_label = QLabel(self.tr("--"))
            opponent_stat_label = QLabel(self.tr("--"))

            # Set alignment for stat labels
            team_stat_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            opponent_stat_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            # Add labels to the grid
            adv_stats_grid.addWidget(team_stat_label, row, 1)
            adv_stats_grid.addWidget(opponent_stat_label, row, 2)

            # Store references to the labels
            self.team_adv_stats_labels[field] = team_stat_label
            self.opponent_adv_stats_labels[field] = opponent_stat_label

        adv_stats_layout.addWidget(adv_stats_grid_widget)

        return adv_stats_layout

    def _create_venue_section(self):
        """Create the Venue section with read-only labels."""
        form_layout = QFormLayout()

        # Stadium (read-only label)
        self.venue_stadium_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Stadium:"), self.venue_stadium_label)

        # City (read-only label)
        self.venue_city_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("City:"), self.venue_city_label)

        # Country (read-only label)
        self.venue_country_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Country:"), self.venue_country_label)

        # Weather (read-only label)
        self.venue_weather_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Weather:"), self.venue_weather_label)

        # Broadcast (read-only label)
        self.venue_broadcast_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Broadcast:"), self.venue_broadcast_label)

        return form_layout

    def _create_officials_section(self):
        """Create the Officials section with read-only labels."""
        form_layout = QFormLayout()

        # Referee labels (1-6)
        self.referee_labels = {}
        for i in range(1, 7):
            referee_label = QLabel(self.tr("--"))
            form_layout.addRow(self.tr(f"Referee {i}:"), referee_label)
            self.referee_labels[f"referee_{i}"] = referee_label

        # Referee rating (read-only label)
        self.referee_rating_label = QLabel(self.tr("--"))
        form_layout.addRow(self.tr("Referee Rating:"), self.referee_rating_label)

        # Comments (read-only label)
        self.referee_comments_label = QLabel(self.tr("--"))
        self.referee_comments_label.setWordWrap(True)  # Allow text wrapping
        self.referee_comments_label.setMaximumHeight(80)  # Limit height
        form_layout.addRow(self.tr("Comments:"), self.referee_comments_label)

        return form_layout

    def _get_club_name(self):
        """Get club name from club data manager."""
        try:
            club_name = self.club_data_manager.get_data("club_name", "")
            return club_name if club_name else self.tr("My Team")
        except Exception as e:
            print(f"ERROR: Failed to get club name: {e}")
            return self.tr("My Team")

    def _update_information_display(self, match_data):
        """Update information labels with match data."""
        if not hasattr(self, 'match_id_label'):
            return

        # Update Match ID
        match_id = match_data.get('match_id', '')
        self.match_id_label.setText(str(match_id) if match_id else self.tr("No match selected"))

        # Update Date
        date = match_data.get('date', '')
        self.date_label.setText(date if date else self.tr("--"))

        # Update Time
        time = match_data.get('time', '')
        self.time_label.setText(time if time else self.tr("--"))

        # Update Competition
        competition = match_data.get('competition', '')
        self.competition_label.setText(competition if competition else self.tr("--"))

        # Update Season
        season = match_data.get('season', '')
        self.season_label.setText(season if season else self.tr("--"))

        # Update Matchday
        matchday = match_data.get('matchday', '')
        self.matchday_label.setText(str(matchday) if matchday else self.tr("--"))

        # Update Round
        round_num = match_data.get('round', '')
        self.round_label.setText(str(round_num) if round_num else self.tr("--"))

    def _update_teams_score_display(self, match_data):
        """Update teams and score labels with match data."""
        if not hasattr(self, 'home_away_label'):
            return

        # Update Home/Away
        home_away = match_data.get('home_away', '')
        self.home_away_label.setText(home_away if home_away else self.tr("--"))

        # Update Opponent
        opponent = match_data.get('opponent', '')
        self.opponent_label.setText(opponent if opponent else self.tr("--"))

        # Update Full Time Score
        home_score = match_data.get('home_score', '')
        away_score = match_data.get('away_score', '')
        if home_score != '' and away_score != '':
            self.score_label.setText(f"{home_score} - {away_score}")
        else:
            self.score_label.setText(self.tr("-- - --"))

        # Update Half Time Score
        ht_home = match_data.get('halftime_home_score', '')
        ht_away = match_data.get('halftime_away_score', '')
        if ht_home != '' and ht_away != '':
            self.halftime_score_label.setText(f"{ht_home} - {ht_away}")
        else:
            self.halftime_score_label.setText(self.tr("-- - --"))

        # Update Extra Time Score
        et_home = match_data.get('extratime_home_score', '')
        et_away = match_data.get('extratime_away_score', '')
        if et_home != '' and et_away != '':
            self.extratime_score_label.setText(f"{et_home} - {et_away}")
        else:
            self.extratime_score_label.setText(self.tr("-- - --"))

        # Update Penalty Score
        pen_home = match_data.get('penalty_home_score', '')
        pen_away = match_data.get('penalty_away_score', '')
        if pen_home != '' and pen_away != '':
            self.penalty_score_label.setText(f"{pen_home} - {pen_away}")
        else:
            self.penalty_score_label.setText(self.tr("-- - --"))

        # Update Opponents own goals
        opponents_own_goals = match_data.get('opponents_own_goals', '')
        if opponents_own_goals != '' and opponents_own_goals != '0':
            self.opponents_own_goals_label.setText(str(opponents_own_goals))
        else:
            self.opponents_own_goals_label.setText(self.tr("--"))

        # Update Result
        result = match_data.get('result', '')
        self.result_label.setText(result if result else self.tr("--"))

    def _update_venue_display(self, match_data):
        """Update venue labels with match data."""
        if not hasattr(self, 'venue_stadium_label'):
            return

        # Update Stadium
        stadium = match_data.get('venue_stadium', '')
        self.venue_stadium_label.setText(stadium if stadium else self.tr("--"))

        # Update City
        city = match_data.get('venue_city', '')
        self.venue_city_label.setText(city if city else self.tr("--"))

        # Update Country
        country = match_data.get('venue_country', '')
        self.venue_country_label.setText(country if country else self.tr("--"))

        # Update Weather
        weather = match_data.get('venue_weather', '')
        self.venue_weather_label.setText(weather if weather else self.tr("--"))

        # Update Broadcast
        broadcast = match_data.get('venue_broadcast', '')
        self.venue_broadcast_label.setText(broadcast if broadcast else self.tr("--"))

    def _update_officials_display(self, match_data):
        """Update officials labels with match data."""
        if not hasattr(self, 'referee_labels'):
            return

        # Update Referee fields (1-6)
        for i in range(1, 7):
            referee_key = f"referee_{i}"
            referee_name = match_data.get(referee_key, '')
            if referee_key in self.referee_labels:
                self.referee_labels[referee_key].setText(referee_name if referee_name else self.tr("--"))

        # Update Referee Rating
        rating = match_data.get('referee_rating', '')
        if hasattr(self, 'referee_rating_label'):
            if rating:
                self.referee_rating_label.setText(f"{rating}/10")
            else:
                self.referee_rating_label.setText(self.tr("--"))

        # Update Comments
        comments = match_data.get('referee_comments', '')
        if hasattr(self, 'referee_comments_label'):
            self.referee_comments_label.setText(comments if comments else self.tr("--"))

    def _update_statistics_display(self, match_data):
        """Update statistics labels with match data."""
        if not hasattr(self, 'team_stats_labels'):
            return

        # Update team name labels
        opponent = match_data.get('opponent', 'Opponent')
        if hasattr(self, 'team_name_label'):
            self.team_name_label.setText(self._get_club_name())
        if hasattr(self, 'opponent_name_label'):
            self.opponent_name_label.setText(opponent)

        # Define statistics fields
        stat_fields = [
            "final_score",
            "attempts",
            "on_target",
            "passes",
            "completed_passes",
            "fouls",
            "yellow_cards",
            "red_cards",
            "offsides",
            "corners",
            "possession",
            "xgoals"
        ]

        # Update each statistic
        for field in stat_fields:
            # Team stats
            team_key = f"team_{field}"
            team_value = match_data.get(team_key, '')
            if field in self.team_stats_labels:
                if field == "final_score":
                    # Special handling for final score
                    home_score = match_data.get('home_score', '')
                    away_score = match_data.get('away_score', '')
                    home_away = match_data.get('home_away', 'Home')
                    if home_score != '' and away_score != '':
                        if home_away == 'Home':
                            self.team_stats_labels[field].setText(str(home_score))
                        else:
                            self.team_stats_labels[field].setText(str(away_score))
                    else:
                        self.team_stats_labels[field].setText(self.tr("--"))
                elif field == "possession":
                    # Add % suffix for possession
                    if team_value != '':
                        self.team_stats_labels[field].setText(f"{team_value}%")
                    else:
                        self.team_stats_labels[field].setText(self.tr("--"))
                else:
                    self.team_stats_labels[field].setText(str(team_value) if team_value != '' else self.tr("--"))

            # Opponent stats
            opponent_key = f"opponent_{field}"
            opponent_value = match_data.get(opponent_key, '')
            if field in self.opponent_stats_labels:
                if field == "final_score":
                    # Special handling for final score
                    home_score = match_data.get('home_score', '')
                    away_score = match_data.get('away_score', '')
                    home_away = match_data.get('home_away', 'Home')
                    if home_score != '' and away_score != '':
                        if home_away == 'Home':
                            self.opponent_stats_labels[field].setText(str(away_score))
                        else:
                            self.opponent_stats_labels[field].setText(str(home_score))
                    else:
                        self.opponent_stats_labels[field].setText(self.tr("--"))
                elif field == "possession":
                    # Add % suffix for possession
                    if opponent_value != '':
                        self.opponent_stats_labels[field].setText(f"{opponent_value}%")
                    else:
                        self.opponent_stats_labels[field].setText(self.tr("--"))
                else:
                    self.opponent_stats_labels[field].setText(str(opponent_value) if opponent_value != '' else self.tr("--"))

    def _update_advanced_statistics_display(self, match_data):
        """Update advanced statistics labels with match data."""
        if not hasattr(self, 'team_adv_stats_labels'):
            return

        # Update team name labels
        opponent = match_data.get('opponent', 'Opponent')
        if hasattr(self, 'adv_team_name_label'):
            self.adv_team_name_label.setText(self._get_club_name())
        if hasattr(self, 'adv_opponent_name_label'):
            self.adv_opponent_name_label.setText(opponent)

        # Define advanced statistics fields
        adv_stat_fields = [
            "crosses",
            "touches",
            "tackles",
            "interceptions",
            "aerials_won",
            "clearances",
            "long_balls",
            "saves"
        ]

        # Update each advanced statistic
        for field in adv_stat_fields:
            # Team stats
            team_key = f"team_{field}"
            team_value = match_data.get(team_key, '')
            if field in self.team_adv_stats_labels:
                self.team_adv_stats_labels[field].setText(str(team_value) if team_value != '' else self.tr("--"))

            # Opponent stats
            opponent_key = f"opponent_{field}"
            opponent_value = match_data.get(opponent_key, '')
            if field in self.opponent_adv_stats_labels:
                self.opponent_adv_stats_labels[field].setText(str(opponent_value) if opponent_value != '' else self.tr("--"))

    def _update_match_display(self, match_data):
        """Update all match display sections with data."""
        self._update_information_display(match_data)
        self._update_teams_score_display(match_data)
        self._update_duration_fields(match_data)
        self._update_statistics_display(match_data)
        self._update_advanced_statistics_display(match_data)
        self._update_venue_display(match_data)
        self._update_officials_display(match_data)

    def _load_match(self):
        """Load an existing match (placeholder)."""
        match_page_debug("Load Match button clicked - placeholder")

        # Example of how to use the match display update:
        match_data = {
            # Information section
            'match_id': 1,
            'date': '2024-01-15',
            'time': '15:00',
            'competition': 'Premier League',
            'season': '2023-2024',
            'matchday': 20,
            'round': 1,
            # Teams and Score section
            'home_away': 'Home',
            'opponent': 'Arsenal FC',
            'home_score': 2,
            'away_score': 1,
            'halftime_home_score': 1,
            'halftime_away_score': 0,
            'result': 'Win',
            # Duration fields
            'stoppage_time': 4,
            'et_played': False,
            'et_duration': 0,
            # Statistics section
            'team_attempts': 15,
            'opponent_attempts': 8,
            'team_on_target': 6,
            'opponent_on_target': 3,
            'team_passes': 450,
            'opponent_passes': 320,
            'team_completed_passes': 380,
            'opponent_completed_passes': 250,
            'team_fouls': 12,
            'opponent_fouls': 18,
            'team_yellow_cards': 2,
            'opponent_yellow_cards': 3,
            'team_red_cards': 0,
            'opponent_red_cards': 1,
            'team_offsides': 3,
            'opponent_offsides': 5,
            'team_corners': 7,
            'opponent_corners': 4,
            'team_possession': 65,
            'opponent_possession': 35,
            'team_xgoals': 2.3,
            'opponent_xgoals': 1.1,
            # Advanced Statistics section
            'team_crosses': 12,
            'opponent_crosses': 8,
            'team_touches': 520,
            'opponent_touches': 380,
            'team_tackles': 18,
            'opponent_tackles': 22,
            'team_interceptions': 15,
            'opponent_interceptions': 12,
            'team_aerials_won': 8,
            'opponent_aerials_won': 6,
            'team_clearances': 20,
            'opponent_clearances': 25,
            'team_long_balls': 35,
            'opponent_long_balls': 42,
            'team_saves': 2,
            'opponent_saves': 4,
            # Venue section
            'venue_stadium': 'Example Stadium',
            'venue_city': 'Example City',
            'venue_country': 'Germany',
            'venue_weather': 'Sunny',
            'venue_broadcast': 'ESPN',
            # Officials section
            'referee_1': 'John Smith',
            'referee_2': 'Jane Doe',
            'referee_3': '',
            'referee_4': '',
            'referee_5': '',
            'referee_6': '',
            'referee_rating': 7,
            'referee_comments': 'Good performance overall'
        }
        self._update_match_display(match_data)

    def _add_new_match(self):
        """Open the New Match page."""
        match_page_debug("Add New Match button clicked - opening New Match page")

        # Check if new match window is already open
        if self.new_match_window is None or not self.new_match_window.isVisible():
            if self.new_match_window is None:
                # Import here to avoid circular import
                from app.pages.newmatch_page import NewMatchPage
                # Create new match page with shared club data manager to avoid duplicate file loading
                self.new_match_window = NewMatchPage(None, club_data_manager=self.club_data_manager)
                # Set up window destruction handling
                self.new_match_window.destroyed.connect(self._on_new_match_window_destroyed)
                # Set up close event handling to show matches window again
                self.new_match_window.window_closed.connect(self._on_new_match_window_closed)

            # Save window state before hiding
            self._save_window_state()

            # Hide the matches window and show the new match window
            self.hide()
            self.new_match_window.show()
            self.new_match_window.activateWindow()
            self.new_match_window.raise_()
        else:
            # Window is already open, just bring it to front
            self.new_match_window.raise_()
            self.new_match_window.activateWindow()

    def _on_new_match_window_destroyed(self):
        """Handle when the new match window is destroyed."""
        self.new_match_window = None

    def _on_new_match_window_closed(self):
        """Handle when the new match window is closed - show matches window again."""
        # Restore window state before showing
        self._restore_window_state()

        # Show the matches window again
        self.show()
        self.activateWindow()
        self.raise_()

    def _restore_window_state(self):
        """Restore window size and splitter position from settings."""
        settings = QSettings()

        # Restore window size
        size = settings.value("matches_page/window_size")
        if size:
            self.resize(size)
        else:
            # Set default size if no saved size
            self.resize(1200, 800)

        # Restore splitter position
        splitter_state = settings.value("matches_page/splitter_state")
        if splitter_state:
            self.splitter.restoreState(splitter_state)

    def _save_window_state(self):
        """Save window size and splitter position to settings."""
        settings = QSettings()
        settings.setValue("matches_page/window_size", self.size())
        settings.setValue("matches_page/splitter_state", self.splitter.saveState())

    def _save_splitter_state(self):
        """Save splitter position when moved."""
        settings = QSettings()
        settings.setValue("matches_page/splitter_state", self.splitter.saveState())

    def closeEvent(self, event):
        """Handle window close event to save state."""
        self._save_window_state()
        super().closeEvent(event)

    def tr(self, text):
        """Translation method."""
        return QCoreApplication.translate("MatchesPage", text)