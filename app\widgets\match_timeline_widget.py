"""
Match Timeline Widget for displaying and editing match events in a timeline format.
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QLineEdit, QCheckBox, QGroupBox,
    QScrollArea, QFrame, QSizePolicy, QMessageBox, QFormLayout
)
from PySide6.QtCore import Qt, Signal, QSettings, QRect, QPoint
from PySide6.QtGui import QFont, QPainter, QPen, QBrush, QColor, QFontMetrics


@dataclass
class TimelineEvent:
    """Represents a single event in the match timeline."""
    minute: int
    team: str  # "our_team", "opponent", "match"
    event_type: str  # "goal", "yellow", "red", "sub_in", "sub_out", "injury", "match_start", "half_time", "match_end"
    player_name: str = ""
    details: str = ""
    linked_event_id: Optional[int] = None  # For linking goal+assist, sub in/out
    custom_icon: str = ""

    def __post_init__(self):
        """Set default icon based on event type."""
        if not self.custom_icon:
            self.custom_icon = self.get_default_icon()

    def get_default_icon(self) -> str:
        """Get default icon for event type."""
        icons = {
            "goal": "⚽",
            "assist": "🅰️",
            "yellow": "🟨",
            "red": "🟥",
            "sub_in": "🔄",
            "sub_out": "🔄",
            "injury": "🏥",
            "match_start": "▶️",
            "half_time": "⏸️",
            "match_end": "⏹️"
        }
        return icons.get(self.event_type, "📌")


class TimelineSettings:
    """Settings for timeline display and behavior."""

    def __init__(self):
        self.show_our_team = True
        self.show_opponent = False  # Default off for now
        self.show_match_events = True
        self.enabled_categories = ["goals", "cards", "subs", "injuries"]
        self.timeline_layout = "split"  # "split", "same_line", "stacked"

    def save_to_settings(self, match_id: str = "default"):
        """Save settings to QSettings."""
        settings = QSettings()
        settings.beginGroup(f"timeline_settings_{match_id}")
        settings.setValue("show_our_team", self.show_our_team)
        settings.setValue("show_opponent", self.show_opponent)
        settings.setValue("show_match_events", self.show_match_events)
        settings.setValue("enabled_categories", self.enabled_categories)
        settings.setValue("timeline_layout", self.timeline_layout)
        settings.endGroup()

    def load_from_settings(self, match_id: str = "default"):
        """Load settings from QSettings."""
        settings = QSettings()
        settings.beginGroup(f"timeline_settings_{match_id}")
        self.show_our_team = settings.value("show_our_team", True, bool)
        self.show_opponent = settings.value("show_opponent", False, bool)
        self.show_match_events = settings.value("show_match_events", True, bool)
        self.enabled_categories = settings.value("enabled_categories", ["goals", "cards", "subs", "injuries"])
        self.timeline_layout = settings.value("timeline_layout", "split", str)
        settings.endGroup()


class TimelineCanvas(QWidget):
    """Custom widget for drawing the timeline with events."""

    event_clicked = Signal(int)  # event index
    timeline_clicked = Signal(int)  # minute clicked

    def __init__(self, parent=None):
        super().__init__(parent)
        self.events = []
        self.settings = None
        self.setMinimumHeight(120)
        self.setMaximumHeight(200)

        # Timeline configuration
        self.margin_left = 50
        self.margin_right = 30
        self.margin_top = 20
        self.margin_bottom = 20
        self.row_height = 30
        self.max_rows = 3
        self.min_event_distance = 25  # Minimum pixels between events in same row

        # Colors
        self.timeline_color = QColor("#4CAF50")
        self.our_team_color = QColor("#2196F3")
        self.opponent_color = QColor("#F44336")
        self.match_event_color = QColor("#666666")
        self.minute_marker_color = QColor("#999999")

    def set_events(self, events: List[TimelineEvent]):
        """Set the events to display on the timeline."""
        self.events = events
        self.update()

    def set_settings(self, settings: TimelineSettings):
        """Set the timeline settings."""
        self.settings = settings
        self.update()

    def paintEvent(self, event):
        """Paint the timeline and events."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Calculate timeline dimensions
        timeline_width = self.width() - self.margin_left - self.margin_right
        timeline_y = self.height() // 2

        # Draw main timeline
        self._draw_timeline(painter, timeline_width, timeline_y)

        # Draw minute markers
        self._draw_minute_markers(painter, timeline_width, timeline_y)

        # Draw events
        if self.events and self.settings:
            self._draw_events(painter, timeline_width, timeline_y)

    def _draw_timeline(self, painter: QPainter, width: int, y: int):
        """Draw the main timeline line."""
        painter.setPen(QPen(self.timeline_color, 3))
        start_x = self.margin_left
        end_x = self.margin_left + width
        painter.drawLine(start_x, y, end_x, y)

    def _draw_minute_markers(self, painter: QPainter, width: int, y: int):
        """Draw minute markers on the timeline."""
        painter.setPen(QPen(self.minute_marker_color, 1))
        painter.setFont(QFont("Arial", 8))

        # Draw markers every 15 minutes
        for minute in [0, 15, 30, 45, 60, 75, 90]:
            x = self._minute_to_x(minute, width)

            # Draw tick mark
            painter.drawLine(x, y - 5, x, y + 5)

            # Draw minute label
            text = f"{minute}'"
            text_rect = painter.fontMetrics().boundingRect(text)
            text_x = x - text_rect.width() // 2
            text_y = y + 20
            painter.drawText(text_x, text_y, text)

    def _draw_events(self, painter: QPainter, width: int, timeline_y: int):
        """Draw events on the timeline with staggered rows."""
        # Filter events based on settings
        visible_events = self._get_visible_events()

        # Assign events to rows
        event_rows = self._assign_events_to_rows(visible_events, width)

        # Draw events in each row
        for row_index, row_events in enumerate(event_rows):
            row_y = timeline_y + (row_index - 1) * self.row_height  # Row 0 = above, 1 = main, 2 = below

            for event_info in row_events:
                event, event_index = event_info
                self._draw_single_event(painter, event, event_index, width, row_y, timeline_y)

    def _get_visible_events(self) -> List[tuple]:
        """Get events that should be visible based on settings."""
        visible = []
        for i, event in enumerate(self.events):
            if self._should_show_event(event):
                visible.append((event, i))
        return visible

    def _should_show_event(self, event: TimelineEvent) -> bool:
        """Check if event should be shown based on current settings."""
        if not self.settings:
            return True

        if event.team == "our_team" and not self.settings.show_our_team:
            return False
        if event.team == "opponent" and not self.settings.show_opponent:
            return False
        if event.team == "match" and not self.settings.show_match_events:
            return False
        return True

    def _assign_events_to_rows(self, events: List[tuple], width: int) -> List[List[tuple]]:
        """Assign events to rows to avoid overlapping."""
        rows = [[] for _ in range(self.max_rows)]

        # Sort events by minute
        events_sorted = sorted(events, key=lambda x: x[0].minute)

        for event_info in events_sorted:
            event, event_index = event_info
            event_x = self._minute_to_x(event.minute, width)

            # Try to place in each row
            placed = False
            for row_index in range(self.max_rows):
                if self._can_place_in_row(event_x, rows[row_index], width):
                    rows[row_index].append(event_info)
                    placed = True
                    break

            # If couldn't place anywhere, force into last row
            if not placed:
                rows[-1].append(event_info)

        return rows

    def _can_place_in_row(self, event_x: int, row_events: List[tuple], width: int) -> bool:
        """Check if an event can be placed in a row without overlapping."""
        for existing_event_info in row_events:
            existing_event, _ = existing_event_info
            existing_x = self._minute_to_x(existing_event.minute, width)

            if abs(event_x - existing_x) < self.min_event_distance:
                return False
        return True

    def _draw_single_event(self, painter: QPainter, event: TimelineEvent, event_index: int,
                          width: int, event_y: int, timeline_y: int):
        """Draw a single event on the timeline."""
        event_x = self._minute_to_x(event.minute, width)

        # Get event color
        color = self._get_event_color(event)

        # Draw connection line to main timeline if not on main row
        if event_y != timeline_y:
            painter.setPen(QPen(color, 1, Qt.PenStyle.DashLine))
            painter.drawLine(event_x, event_y, event_x, timeline_y)

        # Draw event icon background
        icon_size = 20
        icon_rect = QRect(event_x - icon_size//2, event_y - icon_size//2, icon_size, icon_size)

        painter.setPen(QPen(color, 2))
        painter.setBrush(QBrush(color.lighter(150)))
        painter.drawEllipse(icon_rect)

        # Draw event icon text
        painter.setPen(QPen(QColor("white")))
        painter.setFont(QFont("Arial", 10, QFont.Weight.Bold))

        # Use emoji or first letter of event type
        icon_text = event.custom_icon if event.custom_icon else event.event_type[0].upper()

        # Center the text in the circle
        text_rect = painter.fontMetrics().boundingRect(icon_text)
        text_x = event_x - text_rect.width() // 2
        text_y = event_y + text_rect.height() // 4
        painter.drawText(text_x, text_y, icon_text)

        # Draw minute label
        painter.setPen(QPen(QColor("black")))
        painter.setFont(QFont("Arial", 8))
        minute_text = f"{event.minute}'"
        minute_rect = painter.fontMetrics().boundingRect(minute_text)
        minute_x = event_x - minute_rect.width() // 2
        minute_y = event_y + icon_size//2 + 15
        painter.drawText(minute_x, minute_y, minute_text)

    def _get_event_color(self, event: TimelineEvent) -> QColor:
        """Get the color for an event based on its team."""
        if event.team == "our_team":
            return self.our_team_color
        elif event.team == "opponent":
            return self.opponent_color
        else:
            return self.match_event_color

    def _minute_to_x(self, minute: int, width: int) -> int:
        """Convert minute to x coordinate on timeline."""
        max_minute = 90  # Could be extended for extra time
        ratio = minute / max_minute
        return self.margin_left + int(ratio * width)

    def _x_to_minute(self, x: int, width: int) -> int:
        """Convert x coordinate to minute."""
        timeline_x = x - self.margin_left
        ratio = timeline_x / width
        return int(ratio * 90)

    def mousePressEvent(self, event):
        """Handle mouse clicks on the timeline."""
        if event.button() == Qt.MouseButton.LeftButton:
            # Check if clicked on an event
            clicked_event = self._get_event_at_position(event.pos())
            if clicked_event is not None:
                self.event_clicked.emit(clicked_event)
            else:
                # Clicked on timeline - get minute
                width = self.width() - self.margin_left - self.margin_right
                minute = self._x_to_minute(event.pos().x(), width)
                if 0 <= minute <= 90:
                    self.timeline_clicked.emit(minute)

    def _get_event_at_position(self, pos: QPoint) -> Optional[int]:
        """Get the event index at the given position, if any."""
        if not self.events or not self.settings:
            return None

        width = self.width() - self.margin_left - self.margin_right
        timeline_y = self.height() // 2

        visible_events = self._get_visible_events()
        event_rows = self._assign_events_to_rows(visible_events, width)

        # Check each event
        for row_index, row_events in enumerate(event_rows):
            row_y = timeline_y + (row_index - 1) * self.row_height

            for event_info in row_events:
                event, event_index = event_info
                event_x = self._minute_to_x(event.minute, width)

                # Check if click is within event icon
                icon_size = 20
                if (abs(pos.x() - event_x) <= icon_size//2 and
                    abs(pos.y() - row_y) <= icon_size//2):
                    return event_index

        return None


class MatchTimelineWidget(QWidget):
    """Widget for displaying and editing match timeline events."""

    # Signals
    event_added = Signal(object)  # TimelineEvent
    event_updated = Signal(object)  # TimelineEvent
    event_deleted = Signal(int)  # event index
    import_from_stats_requested = Signal()  # Request to import from player stats

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("app.timeline")

        # Data
        self.events: List[TimelineEvent] = []
        self.settings = TimelineSettings()
        self.match_id = None

        # UI Components
        self.timeline_canvas = None
        self.event_list_widget = None

        self._init_ui()
        self._load_default_events()

    def _init_ui(self):
        """Initialize the user interface."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Settings panel
        settings_panel = self._create_settings_panel()
        main_layout.addWidget(settings_panel)

        # Timeline canvas
        timeline_panel = self._create_timeline_panel()
        main_layout.addWidget(timeline_panel)

        # Event entry panel
        entry_panel = self._create_event_entry_panel()
        main_layout.addWidget(entry_panel)

        # Event list
        list_panel = self._create_event_list_panel()
        main_layout.addWidget(list_panel)

    def _create_settings_panel(self) -> QWidget:
        """Create the timeline settings panel."""
        group = QGroupBox("Timeline Settings")
        layout = QHBoxLayout(group)

        # Show/hide toggles
        self.show_our_team_cb = QCheckBox("Show Our Team Events")
        self.show_our_team_cb.setChecked(self.settings.show_our_team)
        self.show_our_team_cb.toggled.connect(self._on_settings_changed)

        self.show_opponent_cb = QCheckBox("Show Opponent Events")
        self.show_opponent_cb.setChecked(self.settings.show_opponent)
        self.show_opponent_cb.toggled.connect(self._on_settings_changed)

        self.show_match_events_cb = QCheckBox("Show Match Events")
        self.show_match_events_cb.setChecked(self.settings.show_match_events)
        self.show_match_events_cb.toggled.connect(self._on_settings_changed)

        layout.addWidget(self.show_our_team_cb)
        layout.addWidget(self.show_opponent_cb)
        layout.addWidget(self.show_match_events_cb)
        layout.addStretch()

        return group

    def _create_timeline_panel(self) -> QWidget:
        """Create the visual timeline panel."""
        group = QGroupBox("Match Timeline")
        layout = QVBoxLayout(group)

        # Timeline canvas
        self.timeline_canvas = TimelineCanvas()
        self.timeline_canvas.set_settings(self.settings)

        # Connect timeline canvas signals
        self.timeline_canvas.event_clicked.connect(self._on_timeline_event_clicked)
        self.timeline_canvas.timeline_clicked.connect(self._on_timeline_clicked)

        layout.addWidget(self.timeline_canvas)
        return group

    def _create_event_entry_panel(self) -> QWidget:
        """Create the event entry panel."""
        group = QGroupBox("Add Event")
        layout = QFormLayout(group)

        # Minute input
        self.minute_input = QSpinBox()
        self.minute_input.setRange(0, 120)  # 0-120 minutes (including extra time)
        self.minute_input.setSuffix("'")

        # Team selection
        self.team_combo = QComboBox()
        self.team_combo.addItems(["Our Team", "Opponent", "Match Event"])

        # Event type
        self.event_type_combo = QComboBox()
        self.event_type_combo.addItems([
            "Goal", "Assist", "Yellow Card", "Red Card",
            "Substitution In", "Substitution Out", "Injury",
            "Match Start", "Half Time", "Match End"
        ])

        # Player name
        self.player_input = QLineEdit()
        self.player_input.setPlaceholderText("Player name (if applicable)")

        # Details
        self.details_input = QLineEdit()
        self.details_input.setPlaceholderText("Additional details (optional)")

        # Buttons layout
        buttons_layout = QHBoxLayout()

        # Add event button
        self.add_event_btn = QPushButton("Add Event")
        self.add_event_btn.clicked.connect(self._add_event)

        # Import from stats button
        self.import_stats_btn = QPushButton("Import from Player Stats")
        self.import_stats_btn.clicked.connect(self._request_import_from_stats)

        buttons_layout.addWidget(self.add_event_btn)
        buttons_layout.addWidget(self.import_stats_btn)
        buttons_layout.addStretch()

        # Layout
        layout.addRow("Minute:", self.minute_input)
        layout.addRow("Team:", self.team_combo)
        layout.addRow("Event Type:", self.event_type_combo)
        layout.addRow("Player:", self.player_input)
        layout.addRow("Details:", self.details_input)
        layout.addRow("", buttons_layout)

        return group

    def _create_event_list_panel(self) -> QWidget:
        """Create the event list panel."""
        group = QGroupBox("Event List")
        layout = QVBoxLayout(group)

        # Scroll area for events
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setMinimumHeight(200)

        self.event_list_widget = QWidget()
        self.event_list_layout = QVBoxLayout(self.event_list_widget)
        self.event_list_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        scroll.setWidget(self.event_list_widget)
        layout.addWidget(scroll)

        return group

    def _load_default_events(self):
        """Load default match events."""
        # Add default match events
        default_events = [
            TimelineEvent(0, "match", "match_start", "", "Match Start"),
            TimelineEvent(45, "match", "half_time", "", "Half Time"),
            TimelineEvent(90, "match", "match_end", "", "Match End")
        ]

        for event in default_events:
            self.events.append(event)

        self._refresh_event_list()

    def _on_settings_changed(self):
        """Handle settings changes."""
        self.settings.show_our_team = self.show_our_team_cb.isChecked()
        self.settings.show_opponent = self.show_opponent_cb.isChecked()
        self.settings.show_match_events = self.show_match_events_cb.isChecked()

        # Refresh display
        self._refresh_event_list()
        self._refresh_timeline_canvas()

    def _on_timeline_event_clicked(self, event_index: int):
        """Handle clicking on an event in the timeline."""
        if 0 <= event_index < len(self.events):
            event = self.events[event_index]
            self.logger.info(f"Timeline event clicked: {event.minute}' {event.event_type}")
            # TODO: Show event details or edit dialog

    def _on_timeline_clicked(self, minute: int):
        """Handle clicking on the timeline to add event at specific minute."""
        self.minute_input.setValue(minute)
        self.logger.info(f"Timeline clicked at minute: {minute}")

    def _refresh_timeline_canvas(self):
        """Refresh the timeline canvas display."""
        self.timeline_canvas.set_events(self.events)
        self.timeline_canvas.set_settings(self.settings)

    def _request_import_from_stats(self):
        """Request to import events from player stats."""
        self.import_from_stats_requested.emit()

    def _add_event(self):
        """Add a new event to the timeline."""
        # Get values from inputs
        minute = self.minute_input.value()
        team_text = self.team_combo.currentText()
        event_type_text = self.event_type_combo.currentText()
        player = self.player_input.text().strip()
        details = self.details_input.text().strip()

        # Convert UI values to internal format
        team_map = {
            "Our Team": "our_team",
            "Opponent": "opponent",
            "Match Event": "match"
        }

        event_type_map = {
            "Goal": "goal",
            "Assist": "assist",
            "Yellow Card": "yellow",
            "Red Card": "red",
            "Substitution In": "sub_in",
            "Substitution Out": "sub_out",
            "Injury": "injury",
            "Match Start": "match_start",
            "Half Time": "half_time",
            "Match End": "match_end"
        }

        team = team_map.get(team_text, "our_team")
        event_type = event_type_map.get(event_type_text, "goal")

        # Create event
        event = TimelineEvent(
            minute=minute,
            team=team,
            event_type=event_type,
            player_name=player,
            details=details
        )

        # Add to list
        self.events.append(event)

        # Sort events by minute
        self.events.sort(key=lambda e: e.minute)

        # Refresh display
        self._refresh_event_list()
        self._refresh_timeline_canvas()

        # Clear inputs
        self._clear_inputs()

        # Emit signal
        self.event_added.emit(event)

    def _clear_inputs(self):
        """Clear the input fields."""
        self.minute_input.setValue(0)
        self.team_combo.setCurrentIndex(0)
        self.event_type_combo.setCurrentIndex(0)
        self.player_input.clear()
        self.details_input.clear()

    def _refresh_event_list(self):
        """Refresh the event list display."""
        # Clear existing widgets
        for i in reversed(range(self.event_list_layout.count())):
            child = self.event_list_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # Add events based on settings
        for i, event in enumerate(self.events):
            if self._should_show_event(event):
                event_widget = self._create_event_widget(event, i)
                self.event_list_layout.addWidget(event_widget)

    def _should_show_event(self, event: TimelineEvent) -> bool:
        """Check if event should be shown based on current settings."""
        if event.team == "our_team" and not self.settings.show_our_team:
            return False
        if event.team == "opponent" and not self.settings.show_opponent:
            return False
        if event.team == "match" and not self.settings.show_match_events:
            return False
        return True

    def _create_event_widget(self, event: TimelineEvent, index: int) -> QWidget:
        """Create a widget for displaying a single event."""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.Box)
        widget.setStyleSheet("QFrame { border: 1px solid #ddd; border-radius: 4px; padding: 5px; margin: 2px; }")

        layout = QHBoxLayout(widget)
        layout.setContentsMargins(8, 4, 8, 4)

        # Event info
        info_text = f"{event.minute}' {event.custom_icon} "

        if event.player_name:
            info_text += f"{event.player_name} "

        info_text += f"({event.event_type.replace('_', ' ').title()})"

        if event.details:
            info_text += f" - {event.details}"

        info_label = QLabel(info_text)

        # Team color coding
        if event.team == "our_team":
            info_label.setStyleSheet("color: #2196F3; font-weight: bold;")
        elif event.team == "opponent":
            info_label.setStyleSheet("color: #F44336; font-weight: bold;")
        else:
            info_label.setStyleSheet("color: #666; font-weight: bold;")

        layout.addWidget(info_label)
        layout.addStretch()

        # Delete button
        delete_btn = QPushButton("Delete")
        delete_btn.setMaximumWidth(60)
        delete_btn.clicked.connect(lambda: self._delete_event(index))
        layout.addWidget(delete_btn)

        return widget

    def _delete_event(self, index: int):
        """Delete an event from the timeline."""
        if 0 <= index < len(self.events):
            event = self.events.pop(index)
            self._refresh_event_list()
            self._refresh_timeline_canvas()
            self.event_deleted.emit(index)

    def set_match_id(self, match_id: str):
        """Set the current match ID and load settings."""
        self.match_id = match_id
        self.settings.load_from_settings(match_id)
        self._update_settings_ui()

    def _update_settings_ui(self):
        """Update the settings UI based on current settings."""
        self.show_our_team_cb.setChecked(self.settings.show_our_team)
        self.show_opponent_cb.setChecked(self.settings.show_opponent)
        self.show_match_events_cb.setChecked(self.settings.show_match_events)

    def get_events(self) -> List[TimelineEvent]:
        """Get all timeline events."""
        return self.events.copy()

    def set_events(self, events: List[TimelineEvent]):
        """Set timeline events."""
        self.events = events.copy()
        self.events.sort(key=lambda e: e.minute)
        self._refresh_event_list()
        self._refresh_timeline_canvas()

    def clear_events(self):
        """Clear all events except default match events."""
        self.events.clear()
        self._load_default_events()
