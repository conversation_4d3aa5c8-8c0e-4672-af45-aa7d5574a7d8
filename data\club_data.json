{"club_name": "AC Milan", "short_name": "ACM", "nickname": "", "year_founded": 1899, "city": "Milano", "country": "Italy", "region": "", "logo_exists": true, "stadium_name": "San Siro", "capacity": 90000, "seating_capacity": 90000, "surface_type": "Grass", "year_built": 1950, "stadium_owner": "Club Owned", "stadium_image_exists": true, "color_1st": "#ffffff", "color_2nd": "#000000", "color_3rd": "#000000", "contact_address1": "", "contact_address2": "", "contact_city": "", "contact_state": "", "contact_postal": "", "contact_phone": "", "contact_email": "", "contact_website": "", "medical_staff": [{"ID": "M003", "Type": "Head of Sports Science", "Name": "<PERSON>rio <PERSON>", "Phone": "", "Email": "", "Office": "", "Working hours": ""}, {"ID": "M002", "Type": "Physiotherapists", "Name": "<PERSON>", "Phone": "", "Email": "", "Office": "", "Working hours": ""}, {"ID": "M001", "Type": "Club Doctor", "Name": "<PERSON>", "Phone": "", "Email": "", "Office": "", "Working hours": ""}, {"ID": "M004", "Type": "Physiotherapists", "Name": "<PERSON><PERSON>", "Phone": "", "Email": "", "Office": "", "Working hours": ""}], "coaching_staff": [{"ID": "C001", "Type": "Youth Coach (U18, U17, etc.)", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C002", "Type": "Assistant Coach", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C003", "Type": "Goalkeeping Coach", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C004", "Type": "Technical Director", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C005", "Type": "Youth Coach (U18, U17, etc.)", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C006", "Type": "Assistant Coach", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C007", "Type": "Goalkeeping Coach", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C008", "Type": "Fitness Coach", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C009", "Type": "Youth Coach (U18, U17, etc.)", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C010", "Type": "Assistant Coach", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C011", "Type": "Goalkeeping Coach", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C012", "Type": "Fitness Coach", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": "C013", "Type": "Head Coach", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}], "management_staff": [{"ID": "MG003", "Type": "Chairman", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": "MG004", "Type": "Chief Executive Officer (CEO)", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": "MG005", "Type": "Operations Manager", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": "MG006", "Type": "Club Technical Director", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": "MG007", "Type": "Chief Financial Officer (CFO)", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": "MG008", "Type": "Head of Marketing", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}], "kit_1_front_exists": false, "kit_1_back_exists": false, "kit_1_name_color": "#000000", "kit_1_name_font_family": "<PERSON><PERSON>", "kit_1_name_font_size": 10, "kit_1_name_vpos": 0, "kit_1_name_outline_enabled": false, "kit_1_name_outline_color": "#FFFFFF", "kit_1_name_outline_thickness": 1, "kit_1_number_color": "#000000", "kit_1_number_font_family": "<PERSON><PERSON>", "kit_1_number_font_size": 24, "kit_1_number_vpos": 0, "kit_1_number_outline_enabled": false, "kit_1_number_outline_color": "#FFFFFF", "kit_1_number_outline_thickness": 1, "kit_2_front_exists": false, "kit_2_back_exists": false, "kit_2_name_color": "#000000", "kit_2_name_font_family": "<PERSON><PERSON>", "kit_2_name_font_size": 10, "kit_2_name_vpos": 0, "kit_2_name_outline_enabled": false, "kit_2_name_outline_color": "#FFFFFF", "kit_2_name_outline_thickness": 1, "kit_2_number_color": "#000000", "kit_2_number_font_family": "<PERSON><PERSON>", "kit_2_number_font_size": 24, "kit_2_number_vpos": 0, "kit_2_number_outline_enabled": false, "kit_2_number_outline_color": "#FFFFFF", "kit_2_number_outline_thickness": 1, "kit_3_front_exists": false, "kit_3_back_exists": false, "kit_3_name_color": "#000000", "kit_3_name_font_family": "<PERSON><PERSON>", "kit_3_name_font_size": 10, "kit_3_name_vpos": 0, "kit_3_name_outline_enabled": false, "kit_3_name_outline_color": "#FFFFFF", "kit_3_name_outline_thickness": 1, "kit_3_number_color": "#000000", "kit_3_number_font_family": "<PERSON><PERSON>", "kit_3_number_font_size": 24, "kit_3_number_vpos": 0, "kit_3_number_outline_enabled": false, "kit_3_number_outline_color": "#FFFFFF", "kit_3_number_outline_thickness": 1, "kit_4_front_exists": false, "kit_4_back_exists": false, "kit_4_name_color": "#000000", "kit_4_name_font_family": "<PERSON><PERSON>", "kit_4_name_font_size": 10, "kit_4_name_vpos": 0, "kit_4_name_outline_enabled": false, "kit_4_name_outline_color": "#FFFFFF", "kit_4_name_outline_thickness": 1, "kit_4_number_color": "#000000", "kit_4_number_font_family": "<PERSON><PERSON>", "kit_4_number_font_size": 24, "kit_4_number_vpos": 0, "kit_4_number_outline_enabled": false, "kit_4_number_outline_color": "#FFFFFF", "kit_4_number_outline_thickness": 1, "kit_5_front_exists": false, "kit_5_back_exists": false, "kit_5_name_color": "#000000", "kit_5_name_font_family": "<PERSON><PERSON>", "kit_5_name_font_size": 10, "kit_5_name_vpos": 0, "kit_5_name_outline_enabled": false, "kit_5_name_outline_color": "#FFFFFF", "kit_5_name_outline_thickness": 1, "kit_5_number_color": "#000000", "kit_5_number_font_family": "<PERSON><PERSON>", "kit_5_number_font_size": 24, "kit_5_number_vpos": 0, "kit_5_number_outline_enabled": false, "kit_5_number_outline_color": "#FFFFFF", "kit_5_number_outline_thickness": 1}