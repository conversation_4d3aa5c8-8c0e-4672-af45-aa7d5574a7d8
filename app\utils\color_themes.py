"""
Color themes for the FOOTDATA8 application.

This module defines predefined color themes that can be applied to the application.
Each theme is a complete set of all color constants organized by category.
"""

from app.utils.color_constants import DEFAULT_COLORS

# Default theme - uses the default colors from color_constants.py
DEFAULT_THEME = DEFAULT_COLORS.copy()

# Colorful/Rainbow theme - vibrant, distinct colors
COLORFUL_THEME = {
    # UI Colors
    "chart_header_bg": "#6200EA",       # Vibrant purple background for chart headers

    # Player Status Colors
    "goalkeeper_highlight": "#1E88E5",  # Bright blue for goalkeeper names
    "selected_player": "#00C853",       # Bright green for selected players
    "unselected_player": "#FF3D00",     # Bright red for unselected players
    "player_list_bg": "#F5F5F5",        # Light gray background for player lists

    # Evaluation Rating Colors
    "rating_excellent": "#00C853",      # Bright green for ratings ≥ 9.0
    "rating_excellent_text": "#FFFFFF", # White text for excellent ratings
    "rating_good": "#8BC34A",           # Lime green for ratings ≥ 7.0
    "rating_good_text": "#000000",      # Black text for good ratings
    "rating_average": "#FFEB3B",        # Bright yellow for ratings ≥ 4.0
    "rating_average_text": "#000000",   # Black text for average ratings
    "rating_poor": "#FF5252",           # Bright red for ratings < 4.0
    "rating_poor_text": "#000000",      # Black text for poor ratings
    "group_header_bg": "#E1F5FE",       # Light blue for group headers

    # Evaluation Chart Colors
    "evaluation_low": "#FF5252",        # Bright red for low ratings (0-3)
    "evaluation_medium": "#FFEB3B",     # Bright yellow for medium ratings (4-6)
    "evaluation_high": "#00C853",       # Bright green for high ratings (7-10)

    # Attendance Status Colors
    "attendance_present": "#00C853",    # Bright green for present status
    "attendance_absent": "#FF5252",     # Bright red for absent status
    "attendance_injured": "#FFEB3B",    # Bright yellow for injured status
    "attendance_injured_present": "#FF9800", # Bright orange for injured but present
    "attendance_special": "#9E9E9E",    # Medium gray for special leave

    # Attendance Calendar Colors
    "attendance_weekend": "#F5F5F5",    # Light gray for weekend days
    "attendance_today": "#E1F5FE",      # Light blue for today
    "attendance_out_of_season_bg": "#9E9E9E", # Medium gray for out-of-season dates
    "attendance_out_of_season_text": "#616161", # Darker gray text for out-of-season dates
    "attendance_name_column_bg": "#F5F5F5", # Light gray background for name column
    "attendance_week_divider": "#E0E0E0", # Light gray for week dividers

    # Position Rating Colors
    "position_best_fit": "#FF5252",     # Bright red for position rating 3 (Best Fit)
    "position_can_play": "#FF9800",     # Bright orange for position rating 2 (Can Play)
    "position_alternative": "#FFEB3B",  # Bright yellow for position rating 1 (Alternative)

    # Season Timeline Colors
    "season_bg": "#E1F5FE",             # Light blue with transparency for season
    "macrocycle_bg": "#B3E5FC",         # Slightly darker blue for macrocycle
    "mesocycle_preparation": "#FFCDD2", # Light red for preparation mesocycle
    "mesocycle_basic": "#C8E6C9",       # Light green for basic mesocycle
    "mesocycle_competition": "#FFF9C4", # Light yellow for competition mesocycle
    "mesocycle_transition": "#D1C4E9",  # Light purple for transition mesocycle
    "evaluation_1st": "#EF9A9A",        # Light red for 1st evaluation
    "evaluation_2nd": "#E57373",        # Medium red for 2nd evaluation
    "evaluation_3rd": "#EF5350",        # Darker red for 3rd evaluation

    # Trend Indicator Colors
    "trend_significant_improvement": "#00C853", # Bright green for significant improvement
    "trend_moderate_improvement": "#8BC34A",    # Lime green for moderate improvement
    "trend_no_change": "#9E9E9E",               # Medium gray for no change
    "trend_moderate_regression": "#FF5252",     # Bright red for moderate regression
    "trend_significant_regression": "#D50000",  # Deep red for significant regression

    # Match Validation Colors
    "match_validation_serious": "#FF0000",      # Bright red for serious data missing/errors
    "match_validation_warning": "#FFA500",      # Orange for warnings
    "match_validation_ok": "#00FF00",           # Bright green for checked ok
    "match_missing_minutes_highlight": "#FFEEEE", # Very light red for missing minutes cell highlighting
}

# Blue theme - various shades of blue
BLUE_THEME = {
    # UI Colors
    "chart_header_bg": "#0D47A1",       # Dark blue background for chart headers

    # Player Status Colors
    "goalkeeper_highlight": "#1976D2",  # Medium blue for goalkeeper names
    "selected_player": "#4FC3F7",       # Light blue for selected players
    "unselected_player": "#B71C1C",     # Dark red for unselected players
    "player_list_bg": "#E3F2FD",        # Very light blue background for player lists

    # Evaluation Rating Colors
    "rating_excellent": "#0D47A1",      # Dark blue for ratings ≥ 9.0
    "rating_excellent_text": "#FFFFFF", # White text for excellent ratings
    "rating_good": "#1976D2",           # Medium blue for ratings ≥ 7.0
    "rating_good_text": "#FFFFFF",      # White text for good ratings
    "rating_average": "#42A5F5",        # Light blue for ratings ≥ 4.0
    "rating_average_text": "#000000",   # Black text for average ratings
    "rating_poor": "#90CAF9",           # Very light blue for ratings < 4.0
    "rating_poor_text": "#000000",      # Black text for poor ratings
    "group_header_bg": "#BBDEFB",       # Pale blue for group headers

    # Evaluation Chart Colors
    "evaluation_low": "#90CAF9",        # Very light blue for low ratings (0-3)
    "evaluation_medium": "#42A5F5",     # Light blue for medium ratings (4-6)
    "evaluation_high": "#1565C0",       # Dark blue for high ratings (7-10)

    # Attendance Status Colors
    "attendance_present": "#1976D2",    # Medium blue for present status
    "attendance_absent": "#B71C1C",     # Dark red for absent status
    "attendance_injured": "#FFC107",    # Amber for injured status
    "attendance_injured_present": "#FF9800", # Orange for injured but present
    "attendance_special": "#9E9E9E",    # Medium gray for special leave

    # Attendance Calendar Colors
    "attendance_weekend": "#E3F2FD",    # Very light blue for weekend days
    "attendance_today": "#BBDEFB",      # Pale blue for today
    "attendance_out_of_season_bg": "#9E9E9E", # Medium gray for out-of-season dates
    "attendance_out_of_season_text": "#616161", # Darker gray text for out-of-season dates
    "attendance_name_column_bg": "#E3F2FD", # Very light blue background for name column
    "attendance_week_divider": "#BBDEFB", # Pale blue for week dividers

    # Position Rating Colors
    "position_best_fit": "#0D47A1",     # Dark blue for position rating 3 (Best Fit)
    "position_can_play": "#1976D2",     # Medium blue for position rating 2 (Can Play)
    "position_alternative": "#42A5F5",  # Light blue for position rating 1 (Alternative)

    # Season Timeline Colors
    "season_bg": "#E3F2FD",             # Very light blue for season
    "macrocycle_bg": "#BBDEFB",         # Pale blue for macrocycle
    "mesocycle_preparation": "#1976D2", # Medium blue for preparation mesocycle
    "mesocycle_basic": "#42A5F5",       # Light blue for basic mesocycle
    "mesocycle_competition": "#90CAF9", # Very light blue for competition mesocycle
    "mesocycle_transition": "#64B5F6",  # Light blue for transition mesocycle
    "evaluation_1st": "#1976D2",        # Medium blue for 1st evaluation
    "evaluation_2nd": "#1565C0",        # Darker blue for 2nd evaluation
    "evaluation_3rd": "#0D47A1",        # Dark blue for 3rd evaluation

    # Trend Indicator Colors
    "trend_significant_improvement": "#0D47A1", # Dark blue for significant improvement
    "trend_moderate_improvement": "#1976D2",    # Medium blue for moderate improvement
    "trend_no_change": "#9E9E9E",               # Medium gray for no change
    "trend_moderate_regression": "#B71C1C",     # Dark red for moderate regression
    "trend_significant_regression": "#7F0000",  # Very dark red for significant regression

    # Match Validation Colors
    "match_validation_serious": "#B71C1C",      # Dark red for serious data missing/errors
    "match_validation_warning": "#FF9800",      # Orange for warnings
    "match_validation_ok": "#4CAF50",           # Green for checked ok
    "match_missing_minutes_highlight": "#F0F8FF", # Very light blue for missing minutes cell highlighting
}

# Red theme - red-focused palette
RED_THEME = {
    # UI Colors
    "chart_header_bg": "#B71C1C",       # Dark red background for chart headers

    # Player Status Colors
    "goalkeeper_highlight": "#1976D2",  # Blue for goalkeeper names (keep this blue for visibility)
    "selected_player": "#4CAF50",       # Green for selected players (keep this green for visibility)
    "unselected_player": "#D32F2F",     # Medium red for unselected players
    "player_list_bg": "#FFEBEE",        # Very light red background for player lists

    # Evaluation Rating Colors
    "rating_excellent": "#B71C1C",      # Dark red for ratings ≥ 9.0
    "rating_excellent_text": "#FFFFFF", # White text for excellent ratings
    "rating_good": "#D32F2F",           # Medium red for ratings ≥ 7.0
    "rating_good_text": "#FFFFFF",      # White text for good ratings
    "rating_average": "#EF5350",        # Light red for ratings ≥ 4.0
    "rating_average_text": "#000000",   # Black text for average ratings
    "rating_poor": "#FFCDD2",           # Very light red for ratings < 4.0
    "rating_poor_text": "#000000",      # Black text for poor ratings
    "group_header_bg": "#FFEBEE",       # Very light red for group headers

    # Evaluation Chart Colors
    "evaluation_low": "#FFCDD2",        # Very light red for low ratings (0-3)
    "evaluation_medium": "#EF5350",     # Light red for medium ratings (4-6)
    "evaluation_high": "#B71C1C",       # Dark red for high ratings (7-10)

    # Attendance Status Colors
    "attendance_present": "#4CAF50",    # Green for present status (keep this green for visibility)
    "attendance_absent": "#D32F2F",     # Medium red for absent status
    "attendance_injured": "#FFC107",    # Amber for injured status (keep this amber for visibility)
    "attendance_injured_present": "#FF9800", # Orange for injured but present (keep this orange for visibility)
    "attendance_special": "#9E9E9E",    # Medium gray for special leave

    # Attendance Calendar Colors
    "attendance_weekend": "#FFEBEE",    # Very light red for weekend days
    "attendance_today": "#FFCDD2",      # Light red for today
    "attendance_out_of_season_bg": "#9E9E9E", # Medium gray for out-of-season dates
    "attendance_out_of_season_text": "#616161", # Darker gray text for out-of-season dates
    "attendance_name_column_bg": "#FFEBEE", # Very light red background for name column
    "attendance_week_divider": "#FFCDD2", # Light red for week dividers

    # Position Rating Colors
    "position_best_fit": "#B71C1C",     # Dark red for position rating 3 (Best Fit)
    "position_can_play": "#D32F2F",     # Medium red for position rating 2 (Can Play)
    "position_alternative": "#EF5350",  # Light red for position rating 1 (Alternative)

    # Season Timeline Colors
    "season_bg": "#FFEBEE",             # Very light red for season
    "macrocycle_bg": "#FFCDD2",         # Light red for macrocycle
    "mesocycle_preparation": "#EF9A9A", # Medium-light red for preparation mesocycle
    "mesocycle_basic": "#E57373",       # Medium red for basic mesocycle
    "mesocycle_competition": "#EF5350", # Medium-dark red for competition mesocycle
    "mesocycle_transition": "#F44336",  # Darker red for transition mesocycle
    "evaluation_1st": "#E57373",        # Medium red for 1st evaluation
    "evaluation_2nd": "#F44336",        # Darker red for 2nd evaluation
    "evaluation_3rd": "#D32F2F",        # Dark red for 3rd evaluation

    # Trend Indicator Colors
    "trend_significant_improvement": "#4CAF50", # Green for significant improvement (keep this green for visibility)
    "trend_moderate_improvement": "#8BC34A",    # Light green for moderate improvement (keep this green for visibility)
    "trend_no_change": "#9E9E9E",               # Medium gray for no change
    "trend_moderate_regression": "#F44336",     # Darker red for moderate regression
    "trend_significant_regression": "#B71C1C",  # Dark red for significant regression

    # Match Validation Colors
    "match_validation_serious": "#B71C1C",      # Dark red for serious data missing/errors
    "match_validation_warning": "#FF9800",      # Orange for warnings
    "match_validation_ok": "#4CAF50",           # Green for checked ok
    "match_missing_minutes_highlight": "#FFEBEE", # Very light red for missing minutes cell highlighting
}

# Green theme - green-focused palette
GREEN_THEME = {
    # UI Colors
    "chart_header_bg": "#1B5E20",       # Dark green background for chart headers

    # Player Status Colors
    "goalkeeper_highlight": "#1976D2",  # Blue for goalkeeper names (keep this blue for visibility)
    "selected_player": "#2E7D32",       # Dark green for selected players
    "unselected_player": "#D32F2F",     # Red for unselected players (keep this red for visibility)
    "player_list_bg": "#E8F5E9",        # Very light green background for player lists

    # Evaluation Rating Colors
    "rating_excellent": "#1B5E20",      # Very dark green for ratings ≥ 9.0
    "rating_excellent_text": "#FFFFFF", # White text for excellent ratings
    "rating_good": "#2E7D32",           # Dark green for ratings ≥ 7.0
    "rating_good_text": "#FFFFFF",      # White text for good ratings
    "rating_average": "#4CAF50",        # Medium green for ratings ≥ 4.0
    "rating_average_text": "#000000",   # Black text for average ratings
    "rating_poor": "#A5D6A7",           # Light green for ratings < 4.0
    "rating_poor_text": "#000000",      # Black text for poor ratings
    "group_header_bg": "#E8F5E9",       # Very light green for group headers

    # Evaluation Chart Colors
    "evaluation_low": "#A5D6A7",        # Light green for low ratings (0-3)
    "evaluation_medium": "#4CAF50",     # Medium green for medium ratings (4-6)
    "evaluation_high": "#1B5E20",       # Very dark green for high ratings (7-10)

    # Attendance Status Colors
    "attendance_present": "#2E7D32",    # Dark green for present status
    "attendance_absent": "#D32F2F",     # Red for absent status (keep this red for visibility)
    "attendance_injured": "#FFC107",    # Amber for injured status (keep this amber for visibility)
    "attendance_injured_present": "#FF9800", # Orange for injured but present (keep this orange for visibility)
    "attendance_special": "#9E9E9E",    # Medium gray for special leave

    # Attendance Calendar Colors
    "attendance_weekend": "#E8F5E9",    # Very light green for weekend days
    "attendance_today": "#C8E6C9",      # Light green for today
    "attendance_out_of_season_bg": "#9E9E9E", # Medium gray for out-of-season dates
    "attendance_out_of_season_text": "#616161", # Darker gray text for out-of-season dates
    "attendance_name_column_bg": "#E8F5E9", # Very light green background for name column
    "attendance_week_divider": "#C8E6C9", # Light green for week dividers

    # Position Rating Colors
    "position_best_fit": "#1B5E20",     # Very dark green for position rating 3 (Best Fit)
    "position_can_play": "#2E7D32",     # Dark green for position rating 2 (Can Play)
    "position_alternative": "#4CAF50",  # Medium green for position rating 1 (Alternative)

    # Season Timeline Colors
    "season_bg": "#E8F5E9",             # Very light green for season
    "macrocycle_bg": "#C8E6C9",         # Light green for macrocycle
    "mesocycle_preparation": "#A5D6A7", # Medium-light green for preparation mesocycle
    "mesocycle_basic": "#81C784",       # Medium green for basic mesocycle
    "mesocycle_competition": "#66BB6A", # Medium-dark green for competition mesocycle
    "mesocycle_transition": "#4CAF50",  # Darker green for transition mesocycle
    "evaluation_1st": "#81C784",        # Medium green for 1st evaluation
    "evaluation_2nd": "#4CAF50",        # Darker green for 2nd evaluation
    "evaluation_3rd": "#2E7D32",        # Dark green for 3rd evaluation

    # Trend Indicator Colors
    "trend_significant_improvement": "#1B5E20", # Very dark green for significant improvement
    "trend_moderate_improvement": "#4CAF50",    # Medium green for moderate improvement
    "trend_no_change": "#9E9E9E",               # Medium gray for no change
    "trend_moderate_regression": "#F44336",     # Red for moderate regression (keep this red for visibility)
    "trend_significant_regression": "#B71C1C",  # Dark red for significant regression (keep this red for visibility)

    # Match Validation Colors
    "match_validation_serious": "#B71C1C",      # Dark red for serious data missing/errors
    "match_validation_warning": "#FF9800",      # Orange for warnings
    "match_validation_ok": "#1B5E20",           # Very dark green for checked ok
    "match_missing_minutes_highlight": "#FFF5F5", # Very light red for missing minutes cell highlighting
}

# Light theme - light background with dark text
LIGHT_THEME = {
    # UI Colors
    "chart_header_bg": "#5C6BC0",       # Medium indigo background for chart headers

    # Player Status Colors
    "goalkeeper_highlight": "#1976D2",  # Medium blue for goalkeeper names
    "selected_player": "#4CAF50",       # Medium green for selected players
    "unselected_player": "#F44336",     # Medium red for unselected players
    "player_list_bg": "#FFFFFF",        # White background for player lists

    # Evaluation Rating Colors
    "rating_excellent": "#C8E6C9",      # Light green for ratings ≥ 9.0
    "rating_excellent_text": "#1B5E20", # Dark green text for excellent ratings
    "rating_good": "#E8F5E9",           # Very light green for ratings ≥ 7.0
    "rating_good_text": "#2E7D32",      # Dark green text for good ratings
    "rating_average": "#FFF9C4",        # Light yellow for ratings ≥ 4.0
    "rating_average_text": "#F57F17",   # Dark amber text for average ratings
    "rating_poor": "#FFEBEE",           # Light red for ratings < 4.0
    "rating_poor_text": "#B71C1C",      # Dark red text for poor ratings
    "group_header_bg": "#F5F5F5",       # Light gray for group headers

    # Evaluation Chart Colors
    "evaluation_low": "#FFCDD2",        # Light red for low ratings (0-3)
    "evaluation_medium": "#FFF9C4",     # Light yellow for medium ratings (4-6)
    "evaluation_high": "#C8E6C9",       # Light green for high ratings (7-10)

    # Attendance Status Colors
    "attendance_present": "#C8E6C9",    # Light green for present status
    "attendance_absent": "#FFCDD2",     # Light red for absent status
    "attendance_injured": "#FFF9C4",    # Light yellow for injured status
    "attendance_injured_present": "#FFE0B2", # Light orange for injured but present
    "attendance_special": "#F5F5F5",    # Light gray for special leave

    # Attendance Calendar Colors
    "attendance_weekend": "#FAFAFA",    # Very light gray for weekend days
    "attendance_today": "#E3F2FD",      # Very light blue for today
    "attendance_out_of_season_bg": "#EEEEEE", # Light gray for out-of-season dates
    "attendance_out_of_season_text": "#9E9E9E", # Medium gray text for out-of-season dates
    "attendance_name_column_bg": "#FFFFFF", # White background for name column
    "attendance_week_divider": "#EEEEEE", # Light gray for week dividers

    # Position Rating Colors
    "position_best_fit": "#FFCDD2",     # Light red for position rating 3 (Best Fit)
    "position_can_play": "#FFE0B2",     # Light orange for position rating 2 (Can Play)
    "position_alternative": "#FFF9C4",  # Light yellow for position rating 1 (Alternative)

    # Season Timeline Colors
    "season_bg": "#FFFFFF",             # White for season
    "macrocycle_bg": "#F5F5F5",         # Light gray for macrocycle
    "mesocycle_preparation": "#FFEBEE", # Very light red for preparation mesocycle
    "mesocycle_basic": "#E8F5E9",       # Very light green for basic mesocycle
    "mesocycle_competition": "#FFF8E1", # Very light amber for competition mesocycle
    "mesocycle_transition": "#E3F2FD",  # Very light blue for transition mesocycle
    "evaluation_1st": "#FFCDD2",        # Light red for 1st evaluation
    "evaluation_2nd": "#EF9A9A",        # Medium-light red for 2nd evaluation
    "evaluation_3rd": "#E57373",        # Medium red for 3rd evaluation

    # Trend Indicator Colors
    "trend_significant_improvement": "#C8E6C9", # Light green for significant improvement
    "trend_moderate_improvement": "#E8F5E9",    # Very light green for moderate improvement
    "trend_no_change": "#EEEEEE",               # Light gray for no change
    "trend_moderate_regression": "#FFEBEE",     # Very light red for moderate regression
    "trend_significant_regression": "#FFCDD2",  # Light red for significant regression

    # Match Validation Colors
    "match_validation_serious": "#F44336",      # Medium red for serious data missing/errors
    "match_validation_warning": "#FF9800",      # Orange for warnings
    "match_validation_ok": "#4CAF50",           # Medium green for checked ok
    "match_missing_minutes_highlight": "#FFEBEE", # Very light red for missing minutes cell highlighting
}

# Dark theme - dark background with light text
DARK_THEME = {
    # UI Colors
    "chart_header_bg": "#263238",       # Very dark blue-gray background for chart headers

    # Player Status Colors
    "goalkeeper_highlight": "#64B5F6",  # Light blue for goalkeeper names
    "selected_player": "#81C784",       # Light green for selected players
    "unselected_player": "#E57373",     # Light red for unselected players
    "player_list_bg": "#424242",        # Dark gray background for player lists

    # Evaluation Rating Colors
    "rating_excellent": "#2E7D32",      # Dark green for ratings ≥ 9.0
    "rating_excellent_text": "#FFFFFF", # White text for excellent ratings
    "rating_good": "#388E3C",           # Medium-dark green for ratings ≥ 7.0
    "rating_good_text": "#FFFFFF",      # White text for good ratings
    "rating_average": "#FFA000",        # Dark amber for ratings ≥ 4.0
    "rating_average_text": "#FFFFFF",   # White text for average ratings
    "rating_poor": "#D32F2F",           # Dark red for ratings < 4.0
    "rating_poor_text": "#FFFFFF",      # White text for poor ratings
    "group_header_bg": "#616161",       # Medium-dark gray for group headers

    # Evaluation Chart Colors
    "evaluation_low": "#D32F2F",        # Dark red for low ratings (0-3)
    "evaluation_medium": "#FFA000",     # Dark amber for medium ratings (4-6)
    "evaluation_high": "#2E7D32",       # Dark green for high ratings (7-10)

    # Attendance Status Colors
    "attendance_present": "#388E3C",    # Medium-dark green for present status
    "attendance_absent": "#D32F2F",     # Dark red for absent status
    "attendance_injured": "#FFA000",    # Dark amber for injured status
    "attendance_injured_present": "#F57C00", # Dark orange for injured but present
    "attendance_special": "#757575",    # Medium-dark gray for special leave

    # Attendance Calendar Colors
    "attendance_weekend": "#616161",    # Medium-dark gray for weekend days
    "attendance_today": "#37474F",      # Very dark blue-gray for today
    "attendance_out_of_season_bg": "#424242", # Dark gray for out-of-season dates
    "attendance_out_of_season_text": "#9E9E9E", # Medium gray text for out-of-season dates
    "attendance_name_column_bg": "#424242", # Dark gray background for name column
    "attendance_week_divider": "#616161", # Medium-dark gray for week dividers

    # Position Rating Colors
    "position_best_fit": "#D32F2F",     # Dark red for position rating 3 (Best Fit)
    "position_can_play": "#F57C00",     # Dark orange for position rating 2 (Can Play)
    "position_alternative": "#FFA000",  # Dark amber for position rating 1 (Alternative)

    # Season Timeline Colors
    "season_bg": "#424242",             # Dark gray for season
    "macrocycle_bg": "#616161",         # Medium-dark gray for macrocycle
    "mesocycle_preparation": "#C62828", # Very dark red for preparation mesocycle
    "mesocycle_basic": "#2E7D32",       # Dark green for basic mesocycle
    "mesocycle_competition": "#F57F17", # Dark amber for competition mesocycle
    "mesocycle_transition": "#1565C0",  # Dark blue for transition mesocycle
    "evaluation_1st": "#D32F2F",        # Dark red for 1st evaluation
    "evaluation_2nd": "#C62828",        # Darker red for 2nd evaluation
    "evaluation_3rd": "#B71C1C",        # Very dark red for 3rd evaluation

    # Trend Indicator Colors
    "trend_significant_improvement": "#2E7D32", # Dark green for significant improvement
    "trend_moderate_improvement": "#388E3C",    # Medium-dark green for moderate improvement
    "trend_no_change": "#757575",               # Medium-dark gray for no change
    "trend_moderate_regression": "#D32F2F",     # Dark red for moderate regression
    "trend_significant_regression": "#B71C1C",  # Very dark red for significant regression

    # Match Validation Colors
    "match_validation_serious": "#D32F2F",      # Dark red for serious data missing/errors
    "match_validation_warning": "#FFA000",      # Dark amber for warnings
    "match_validation_ok": "#2E7D32",           # Dark green for checked ok
    "match_missing_minutes_highlight": "#4A2C2A", # Dark red-brown for missing minutes cell highlighting
}

# Grey tones theme - monochromatic grey palette
GREY_THEME = {
    # UI Colors
    "chart_header_bg": "#424242",       # Dark gray background for chart headers

    # Player Status Colors
    "goalkeeper_highlight": "#424242",  # Dark gray for goalkeeper names
    "selected_player": "#616161",       # Medium-dark gray for selected players
    "unselected_player": "#9E9E9E",     # Medium gray for unselected players
    "player_list_bg": "#F5F5F5",        # Light gray background for player lists

    # Evaluation Rating Colors
    "rating_excellent": "#212121",      # Very dark gray for ratings ≥ 9.0
    "rating_excellent_text": "#FFFFFF", # White text for excellent ratings
    "rating_good": "#424242",           # Dark gray for ratings ≥ 7.0
    "rating_good_text": "#FFFFFF",      # White text for good ratings
    "rating_average": "#757575",        # Medium gray for ratings ≥ 4.0
    "rating_average_text": "#FFFFFF",   # White text for average ratings
    "rating_poor": "#BDBDBD",           # Light gray for ratings < 4.0
    "rating_poor_text": "#000000",      # Black text for poor ratings
    "group_header_bg": "#EEEEEE",       # Very light gray for group headers

    # Evaluation Chart Colors
    "evaluation_low": "#BDBDBD",        # Light gray for low ratings (0-3)
    "evaluation_medium": "#757575",     # Medium gray for medium ratings (4-6)
    "evaluation_high": "#212121",       # Very dark gray for high ratings (7-10)

    # Attendance Status Colors
    "attendance_present": "#424242",    # Dark gray for present status
    "attendance_absent": "#9E9E9E",     # Medium gray for absent status
    "attendance_injured": "#757575",    # Medium-dark gray for injured status
    "attendance_injured_present": "#616161", # Medium-dark gray for injured but present
    "attendance_special": "#BDBDBD",    # Light gray for special leave

    # Attendance Calendar Colors
    "attendance_weekend": "#F5F5F5",    # Light gray for weekend days
    "attendance_today": "#E0E0E0",      # Light gray for today
    "attendance_out_of_season_bg": "#BDBDBD", # Light gray for out-of-season dates
    "attendance_out_of_season_text": "#757575", # Medium-dark gray text for out-of-season dates
    "attendance_name_column_bg": "#F5F5F5", # Light gray background for name column
    "attendance_week_divider": "#E0E0E0", # Light gray for week dividers

    # Position Rating Colors
    "position_best_fit": "#424242",     # Dark gray for position rating 3 (Best Fit)
    "position_can_play": "#616161",     # Medium-dark gray for position rating 2 (Can Play)
    "position_alternative": "#757575",  # Medium gray for position rating 1 (Alternative)

    # Season Timeline Colors
    "season_bg": "#F5F5F5",             # Light gray for season
    "macrocycle_bg": "#EEEEEE",         # Very light gray for macrocycle
    "mesocycle_preparation": "#E0E0E0", # Light gray for preparation mesocycle
    "mesocycle_basic": "#BDBDBD",       # Light gray for basic mesocycle
    "mesocycle_competition": "#9E9E9E", # Medium gray for competition mesocycle
    "mesocycle_transition": "#757575",  # Medium-dark gray for transition mesocycle
    "evaluation_1st": "#9E9E9E",        # Medium gray for 1st evaluation
    "evaluation_2nd": "#757575",        # Medium-dark gray for 2nd evaluation
    "evaluation_3rd": "#616161",        # Medium-dark gray for 3rd evaluation

    # Trend Indicator Colors
    "trend_significant_improvement": "#212121", # Very dark gray for significant improvement
    "trend_moderate_improvement": "#424242",    # Dark gray for moderate improvement
    "trend_no_change": "#9E9E9E",               # Medium gray for no change
    "trend_moderate_regression": "#757575",     # Medium-dark gray for moderate regression
    "trend_significant_regression": "#616161",  # Medium-dark gray for significant regression

    # Match Validation Colors
    "match_validation_serious": "#424242",      # Dark gray for serious data missing/errors
    "match_validation_warning": "#757575",      # Medium-dark gray for warnings
    "match_validation_ok": "#212121",           # Very dark gray for checked ok
    "match_missing_minutes_highlight": "#F0F0F0", # Very light gray for missing minutes cell highlighting
}

# Define all available themes
THEMES = {
    "default": DEFAULT_THEME,
    "colorful": COLORFUL_THEME,
    "blue": BLUE_THEME,
    "red": RED_THEME,
    "green": GREEN_THEME,
    "light": LIGHT_THEME,
    "dark": DARK_THEME,
    "grey": GREY_THEME
}

def get_theme_names():
    """
    Get a list of available theme names.

    Returns:
        A list of theme names
    """
    return list(THEMES.keys())

def get_theme(theme_name):
    """
    Get a theme by name.

    Args:
        theme_name: The name of the theme

    Returns:
        A dictionary of color values for the theme
    """
    if theme_name not in THEMES:
        raise ValueError(f"Unknown theme: {theme_name}")

    return THEMES[theme_name]
