#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QCoreApplication

# Set application info for QSettings
QCoreApplication.setOrganizationName("FootData")
QCoreApplication.setApplicationName("FootData")

from app.data.roster_manager import <PERSON><PERSON>er<PERSON>anager
from app.data.matches_manager import MatchesManager
from examplematches_page import MatchesPage

def main():
    app = QApplication(sys.argv)
    
    # Create test managers
    roster_manager = RosterManager()
    matches_manager = MatchesManager()
    
    # Create and show the matches page
    window = MatchesPage(roster_manager, matches_manager)
    window.resize(1400, 900)
    window.show()
    
    # Print debug info about the table
    print("=== Table Debug Info ===")
    table = window.player_stats_table
    print(f"Column count: {table.columnCount()}")
    
    # Check column visibility
    for i in range(table.columnCount()):
        header_item = table.horizontalHeaderItem(i)
        if header_item:
            column_name = header_item.text()
            is_hidden = table.isColumnHidden(i)
            print(f"Column {i}: '{column_name}' - Hidden: {is_hidden}")
            
            # Check specifically for our target columns
            if column_name in ["Attempts", "On Target"]:
                print(f"  *** TARGET COLUMN: {column_name} at index {i} - Hidden: {is_hidden}")
    
    # Clean up on exit
    app.aboutToQuit.connect(roster_manager.close_db)
    app.aboutToQuit.connect(matches_manager.close_db)
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
