"""
Timeline Completion Dialogs for smart event timing input.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSpinBox, QLineEdit, QCheckBox, QGroupBox,
    QFormLayout, QButtonGroup, QRadioButton, QScrollArea,
    QFrame, QGridLayout, QMessageBox, QDialogButtonBox, QWidget
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor


class GoalCompletionDialog(QDialog):
    """Dialog for completing goal timing information."""

    goals_completed = Signal(list)  # List of minutes

    def __init__(self, player_name: str, goal_count: int, playing_time: Tuple[int, int], parent=None):
        super().__init__(parent)
        self.player_name = player_name
        self.goal_count = goal_count
        self.playing_time = playing_time
        self.goal_minutes = []

        self.setWindowTitle(f"Complete Goals - {player_name}")
        self.setModal(True)
        self.resize(400, 300)

        self._init_ui()

    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)

        # Header
        header = self._create_header()
        layout.addWidget(header)

        # Goal inputs
        goals_section = self._create_goals_section()
        layout.addWidget(goals_section)

        # Suggestions
        suggestions_section = self._create_suggestions_section()
        layout.addWidget(suggestions_section)

        # Buttons
        buttons = self._create_buttons()
        layout.addWidget(buttons)

    def _create_header(self) -> QWidget:
        """Create the header section."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Title
        title = QLabel(f"Set goal minutes for {self.player_name}")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title.setFont(title_font)

        # Info
        info = QLabel(f"Player was on pitch: {self.playing_time[0]}' - {self.playing_time[1]}'")
        info.setStyleSheet("color: #666; font-style: italic;")

        layout.addWidget(title)
        layout.addWidget(info)

        return widget

    def _create_goals_section(self) -> QWidget:
        """Create the goals input section."""
        group = QGroupBox(f"Goals ({self.goal_count})")
        layout = QFormLayout(group)

        self.goal_inputs = []

        for i in range(self.goal_count):
            # Goal minute input
            minute_input = QSpinBox()
            minute_input.setRange(self.playing_time[0], max(self.playing_time[1], 120))
            minute_input.setSuffix("'")
            minute_input.setValue(self.playing_time[0])

            # Quick set buttons
            quick_buttons = QHBoxLayout()

            # Common goal times within playing time
            common_times = [15, 23, 30, 45, 67, 78, 89]
            valid_times = [t for t in common_times
                          if self.playing_time[0] <= t <= self.playing_time[1]]

            for time in valid_times[:4]:  # Show max 4 quick buttons
                btn = QPushButton(f"{time}'")
                btn.setMaximumWidth(40)
                btn.clicked.connect(lambda checked, m=time, inp=minute_input: inp.setValue(m))
                quick_buttons.addWidget(btn)

            quick_buttons.addStretch()

            # Layout
            goal_layout = QVBoxLayout()
            goal_layout.addWidget(minute_input)

            quick_widget = QWidget()
            quick_widget.setLayout(quick_buttons)
            goal_layout.addWidget(quick_widget)

            goal_widget = QWidget()
            goal_widget.setLayout(goal_layout)

            layout.addRow(f"Goal {i+1}:", goal_widget)
            self.goal_inputs.append(minute_input)

        return group

    def _create_suggestions_section(self) -> QWidget:
        """Create the suggestions section."""
        group = QGroupBox("Quick Fill")
        layout = QHBoxLayout(group)

        # Common patterns
        if self.goal_count == 2:
            pattern_btn = QPushButton("First Half + Second Half")
            pattern_btn.clicked.connect(self._fill_half_pattern)
            layout.addWidget(pattern_btn)

        # Even distribution
        distribute_btn = QPushButton("Distribute Evenly")
        distribute_btn.clicked.connect(self._distribute_evenly)
        layout.addWidget(distribute_btn)

        layout.addStretch()

        return group

    def _create_buttons(self) -> QWidget:
        """Create the dialog buttons."""
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self._accept)
        buttons.rejected.connect(self.reject)

        return buttons

    def _fill_half_pattern(self):
        """Fill with first half + second half pattern."""
        if len(self.goal_inputs) >= 2:
            # First goal in first half
            first_half_time = min(45, self.playing_time[1])
            self.goal_inputs[0].setValue(min(30, first_half_time))

            # Second goal in second half
            second_half_start = max(46, self.playing_time[0])
            self.goal_inputs[1].setValue(min(70, self.playing_time[1]))

    def _distribute_evenly(self):
        """Distribute goals evenly across playing time."""
        if not self.goal_inputs:
            return

        start_time = self.playing_time[0] + 5  # Start 5 minutes after coming on
        end_time = self.playing_time[1] - 5    # End 5 minutes before going off

        if start_time >= end_time:
            # Short playing time, just use middle
            middle = (self.playing_time[0] + self.playing_time[1]) // 2
            for inp in self.goal_inputs:
                inp.setValue(middle)
        else:
            # Distribute evenly
            interval = (end_time - start_time) // len(self.goal_inputs)
            for i, inp in enumerate(self.goal_inputs):
                minute = start_time + (i * interval)
                inp.setValue(minute)

    def _accept(self):
        """Accept the dialog and emit the goal minutes."""
        minutes = []

        for inp in self.goal_inputs:
            minute = inp.value()

            # Validate minute is within playing time
            if not (self.playing_time[0] <= minute <= self.playing_time[1]):
                QMessageBox.warning(
                    self,
                    "Invalid Time",
                    f"Goal at {minute}' is outside playing time "
                    f"({self.playing_time[0]}' - {self.playing_time[1]}')"
                )
                return

            minutes.append(minute)

        # Check for duplicate minutes
        if len(set(minutes)) != len(minutes):
            QMessageBox.warning(
                self,
                "Duplicate Times",
                "Goals cannot happen at the same minute. Please use different times."
            )
            return

        self.goal_minutes = sorted(minutes)
        self.goals_completed.emit(self.goal_minutes)
        self.accept()


class AssistCompletionDialog(QDialog):
    """Dialog for completing assist timing information."""

    assists_completed = Signal(list)  # List of minutes

    def __init__(self, player_name: str, assist_count: int, goal_minutes: List[int], parent=None):
        super().__init__(parent)
        self.player_name = player_name
        self.assist_count = assist_count
        self.goal_minutes = sorted(goal_minutes)
        self.assist_minutes = []

        self.setWindowTitle(f"Complete Assists - {player_name}")
        self.setModal(True)
        self.resize(400, 250)

        self._init_ui()

    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)

        # Header
        header = self._create_header()
        layout.addWidget(header)

        # Assist inputs
        assists_section = self._create_assists_section()
        layout.addWidget(assists_section)

        # Buttons
        buttons = self._create_buttons()
        layout.addWidget(buttons)

    def _create_header(self) -> QWidget:
        """Create the header section."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Title
        title = QLabel(f"Set assist minutes for {self.player_name}")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title.setFont(title_font)

        # Available goals
        if self.goal_minutes:
            goals_text = ", ".join([f"{m}'" for m in self.goal_minutes])
            info = QLabel(f"Available goal times: {goals_text}")
        else:
            info = QLabel("No goals scored yet - you can still add assist times")
        info.setStyleSheet("color: #666; font-style: italic;")

        layout.addWidget(title)
        layout.addWidget(info)

        return widget

    def _create_assists_section(self) -> QWidget:
        """Create the assists input section."""
        group = QGroupBox(f"Assists ({self.assist_count})")
        layout = QFormLayout(group)

        self.assist_inputs = []

        for i in range(self.assist_count):
            # Assist selection
            assist_combo = QComboBox()

            # Add goal options
            for minute in self.goal_minutes:
                assist_combo.addItem(f"Goal at {minute}'", minute)

            # Add custom option
            assist_combo.addItem("Custom minute", -1)
            assist_combo.addItem("No specific goal", 0)

            # Custom minute input (initially hidden)
            custom_input = QSpinBox()
            custom_input.setRange(0, 120)
            custom_input.setSuffix("'")
            custom_input.setVisible(False)

            # Connect combo change to show/hide custom input
            assist_combo.currentDataChanged.connect(
                lambda data, inp=custom_input: inp.setVisible(data == -1)
            )

            # Layout
            assist_layout = QVBoxLayout()
            assist_layout.addWidget(assist_combo)
            assist_layout.addWidget(custom_input)

            assist_widget = QWidget()
            assist_widget.setLayout(assist_layout)

            layout.addRow(f"Assist {i+1}:", assist_widget)
            self.assist_inputs.append((assist_combo, custom_input))

        return group

    def _create_buttons(self) -> QWidget:
        """Create the dialog buttons."""
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self._accept)
        buttons.rejected.connect(self.reject)

        return buttons

    def _accept(self):
        """Accept the dialog and emit the assist minutes."""
        minutes = []

        for combo, custom_input in self.assist_inputs:
            data = combo.currentData()

            if data == -1:  # Custom minute
                minute = custom_input.value()
            elif data == 0:  # No specific goal
                minute = 0  # Will be handled as "general assist"
            else:  # Specific goal minute
                minute = data

            if minute > 0:  # Only add real minutes
                minutes.append(minute)

        self.assist_minutes = sorted(minutes)
        self.assists_completed.emit(self.assist_minutes)
        self.accept()


class CardCompletionDialog(QDialog):
    """Dialog for completing card timing information."""

    cards_completed = Signal(list)  # List of minutes

    def __init__(self, player_name: str, card_type: str, card_count: int, parent=None):
        super().__init__(parent)
        self.player_name = player_name
        self.card_type = card_type  # "yellow" or "red"
        self.card_count = card_count
        self.card_minutes = []

        self.setWindowTitle(f"Complete {card_type.title()} Cards - {player_name}")
        self.setModal(True)
        self.resize(400, 250)

        self._init_ui()

    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)

        # Header
        header = self._create_header()
        layout.addWidget(header)

        # Card inputs
        cards_section = self._create_cards_section()
        layout.addWidget(cards_section)

        # Buttons
        buttons = self._create_buttons()
        layout.addWidget(buttons)

    def _create_header(self) -> QWidget:
        """Create the header section."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Title
        title = QLabel(f"Set {self.card_type} card minutes for {self.player_name}")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title.setFont(title_font)

        # Info
        info = QLabel("Cards can happen any time during the match (0-120+ minutes)")
        if self.card_type == "yellow" and self.card_count == 2:
            info.setText("Note: 2nd yellow card will automatically create a red card at the same minute")
        info.setStyleSheet("color: #666; font-style: italic;")

        layout.addWidget(title)
        layout.addWidget(info)

        return widget

    def _create_cards_section(self) -> QWidget:
        """Create the cards input section."""
        group = QGroupBox(f"{self.card_type.title()} Cards ({self.card_count})")
        layout = QFormLayout(group)

        self.card_inputs = []

        for i in range(self.card_count):
            # Card minute input
            minute_input = QSpinBox()
            minute_input.setRange(0, 120)
            minute_input.setSuffix("'")
            minute_input.setValue(45)  # Default to middle of match

            # Quick set buttons for common card times
            quick_buttons = QHBoxLayout()

            common_times = [15, 30, 45, 60, 75, 89]
            for time in common_times[:4]:  # Show max 4 quick buttons
                btn = QPushButton(f"{time}'")
                btn.setMaximumWidth(40)
                btn.clicked.connect(lambda checked, m=time, inp=minute_input: inp.setValue(m))
                quick_buttons.addWidget(btn)

            quick_buttons.addStretch()

            # Layout
            card_layout = QVBoxLayout()
            card_layout.addWidget(minute_input)

            quick_widget = QWidget()
            quick_widget.setLayout(quick_buttons)
            card_layout.addWidget(quick_widget)

            card_widget = QWidget()
            card_widget.setLayout(card_layout)

            card_label = f"{self.card_type.title()} Card {i+1}:"
            if self.card_type == "yellow" and i == 1:
                card_label += " (→ Red)"

            layout.addRow(card_label, card_widget)
            self.card_inputs.append(minute_input)

        return group

    def _create_buttons(self) -> QWidget:
        """Create the dialog buttons."""
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self._accept)
        buttons.rejected.connect(self.reject)

        return buttons

    def _accept(self):
        """Accept the dialog and emit the card minutes."""
        minutes = []

        for inp in self.card_inputs:
            minute = inp.value()
            minutes.append(minute)

        # Validate no duplicate minutes for same player
        if len(set(minutes)) != len(minutes):
            QMessageBox.warning(
                self,
                "Duplicate Times",
                f"{self.card_type.title()} cards cannot happen at the same minute. "
                "Please use different times."
            )
            return

        self.card_minutes = sorted(minutes)
        self.cards_completed.emit(self.card_minutes)
        self.accept()
