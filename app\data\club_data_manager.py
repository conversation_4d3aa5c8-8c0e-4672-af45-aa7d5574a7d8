import json
from pathlib import Path

class ClubDataManager:
    """Manages loading and saving of club data to a JSON file."""

    DEFAULT_FILENAME = "club_data.json"

    def __init__(self, data_dir="data"):
        """
        Initializes the manager.

        Args:
            data_dir (str): The directory relative to the project root
                           where the data file should be stored.
        """
        # Determine the project root directory (assuming this script is in app/data)
        script_dir = Path(__file__).parent
        project_root = script_dir.parent.parent

        self.data_dir = project_root / data_dir
        self.data_filepath = self.data_dir / self.DEFAULT_FILENAME
        self._data = {} # Internal dictionary to hold current data
        self.load_data() # Load data on initialization

    def _get_default_data(self):
        """Returns the default structure and values for the club data."""
        # Base structure
        data = {
            "club_name": "",
            "short_name": "",
            "nickname": "",
            "year_founded": 2024,
            "city": "",
            "country": None,
            "region": "",
            "logo_exists": False,
            "stadium_name": "",
            "capacity": 0,
            "seating_capacity": 0,
            "surface_type": None,
            "year_built": 2024,
            "stadium_owner": None,
            "stadium_image_exists": False,
            "color_1st": "#ffffff",
            "color_2nd": "#000000",
            "color_3rd": "#000000",
            "contact_address1": "",
            "contact_address2": "",
            "contact_city": "",
            "contact_state": "",
            "contact_postal": "",
            "contact_phone": "",
            "contact_email": "",
            "contact_website": "",
            "medical_staff": [], # Add default empty list for medical staff
            "coaching_staff": [], # Add default empty list for coaching staff
            "management_staff": [] # Add default empty list for management staff
        }

        # Add kit image existence flags and settings
        kit_types = ["1st", "2nd", "3rd", "1st_gk", "2nd_gk"]
        kit_sides = ["front", "back"]
        setting_elements = ["name", "number"]
        # Use numbers matching kit_id for data keys (1, 2, 3, 4, 5)
        kit_ids = {"1st": "1", "2nd": "2", "3rd": "3", "1st_gk": "4", "2nd_gk": "5"}

        for key in kit_types:
            kit_id = kit_ids[key]
            for side in kit_sides:
                data[f"kit_{kit_id}_{side}_exists"] = False

            for element in setting_elements:
                data[f"kit_{kit_id}_{element}_color"] = "#000000" # Default black
                data[f"kit_{kit_id}_{element}_font_family"] = "Arial" # Default font
                # Different default sizes for name vs number
                default_size = 10 if element == "name" else 24
                data[f"kit_{kit_id}_{element}_font_size"] = default_size
                data[f"kit_{kit_id}_{element}_vpos"] = 0 # Default vertical offset
                # Add outline defaults
                data[f"kit_{kit_id}_{element}_outline_enabled"] = False
                data[f"kit_{kit_id}_{element}_outline_color"] = "#FFFFFF" # Default white outline
                data[f"kit_{kit_id}_{element}_outline_thickness"] = 1 # Default 1px thickness

        return data

    def load_data(self):
        """Loads data from the JSON file into the internal _data dictionary."""
        defaults = self._get_default_data()
        if not self.data_filepath.exists():
            print(f"Data file not found: {self.data_filepath}. Using default data.")
            self._data = defaults
            self.save_data() # Create the file with defaults if it doesn't exist
            return

        try:
            with open(self.data_filepath, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
                # Merge loaded data with defaults to handle missing keys gracefully
                self._data = {**defaults, **loaded_data}
                print(f"Data loaded successfully from {self.data_filepath}")

        except (json.JSONDecodeError, IOError) as e:
            print(f"Error loading data from {self.data_filepath}: {e}. Using default data.")
            self._data = defaults
        except Exception as e: # Catch any other unexpected errors
             print(f"Unexpected error loading data: {e}. Using default data.")
             self._data = defaults

    def save_data(self):
        """Saves the current internal _data dictionary to the JSON file."""
        try:
            # Ensure the data directory exists
            self.data_dir.mkdir(parents=True, exist_ok=True)

            with open(self.data_filepath, 'w', encoding='utf-8') as f:
                json.dump(self._data, f, indent=4, ensure_ascii=False)
            print(f"Data saved successfully to {self.data_filepath}")

        except IOError as e:
            print(f"Error saving data to {self.data_filepath}: {e}")
        except Exception as e: # Catch any other unexpected errors
             print(f"Unexpected error saving data: {e}")

    def get_data(self, key=None, default=None):
        """Gets a specific value from the loaded data.

        Args:
            key (str, optional): The key to retrieve. If None, returns all data.
            default: The default value to return if key is not found.

        Returns:
            The value for the specified key, or all data if key is None.
        """
        if key is None:
            return self._data.copy()  # Return a copy of all data

        # Use default from _get_default_data if key exists there, else use provided default
        default_value = self._get_default_data().get(key, default)
        return self._data.get(key, default_value)

    def set_data(self, key, value):
        """Sets a specific value in the internal data dictionary. Does not save automatically."""
        if key in self._get_default_data(): # Only allow setting known keys initially
            self._data[key] = value
        else:
            print(f"Warning: Attempted to set unknown data key '{key}'. Ignored.")
            # Later, we might allow adding new keys if needed, but safer for now.

    def get_all_data(self):
        """Returns a copy of the entire internal data dictionary."""
        return self.get_data()  # Use the get_data method with no key to get all data

# Example usage (for testing)
if __name__ == '__main__':
    manager = ClubDataManager()
    print("Initial data:", manager.get_all_data())

    manager.set_data("club_name", "Test FC")
    manager.set_data("color_1st", "#ff0000")
    manager.set_data("unknown_field", "test") # Should print a warning

    print("Modified data (before save):", manager.get_all_data())
    manager.save_data()

    # Test loading again
    manager2 = ClubDataManager()
    print("Reloaded data:", manager2.get_all_data())
    print("Club name:", manager2.get_data("club_name"))
    print("Region:", manager2.get_data("region")) # Should be default

