#!/usr/bin/env python3
"""
Screen size calculator for dashboard layout optimization.
Calculates maximum columns for different card sizes based on current screen.
"""

from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import QSettings
import logging

logger = logging.getLogger(__name__)

class ScreenCalculator:
    """Calculates optimal dashboard layout based on screen size and card dimensions."""

    def __init__(self):
        # Card size definitions (width, height)
        self.card_sizes = {
            'small': (180, 110),
            'medium': (220, 130),
            'large': (260, 150)
        }

        # Dashboard layout constants
        self.grid_spacing = 15  # Space between cards
        self.dashboard_margins = 40  # 20px each side
        self.header_height = 50
        self.footer_height = 50
        self.window_chrome_width = 20
        self.window_chrome_height = 60
        self.safety_margin_width = 50
        self.safety_margin_height = 30

    def get_current_screen_info(self, window=None):
        """Get information about the current screen where the window is located."""
        app = QApplication.instance()
        if not app:
            logger.error("No QApplication instance found")
            return None

        if window and hasattr(window, 'screen'):
            # Get screen where window is currently located
            current_screen = window.screen()
        else:
            # Fallback to primary screen
            current_screen = app.primaryScreen()

        if not current_screen:
            logger.error("No screen found")
            return None

        screen_geometry = current_screen.availableGeometry()
        screen_info = {
            'name': current_screen.name(),
            'width': screen_geometry.width(),
            'height': screen_geometry.height(),
            'dpi': current_screen.logicalDotsPerInch(),
            'scale_factor': current_screen.logicalDotsPerInch() / 96.0,
            'geometry': screen_geometry
        }

        logger.debug(f"🖥️ Current screen: {screen_info['name']}")
        logger.debug(f"🖥️ Available size: {screen_info['width']}x{screen_info['height']}")
        logger.debug(f"🖥️ DPI: {screen_info['dpi']}, Scale: {screen_info['scale_factor']:.2f}")

        return screen_info

    def calculate_max_columns_for_card_size(self, screen_info, card_size):
        """Calculate maximum columns that fit on screen for given card size."""
        if not screen_info or card_size not in self.card_sizes:
            return 4  # Safe fallback

        card_width, card_height = self.card_sizes[card_size]
        available_width = screen_info['width']

        # Calculate maximum width we can use (leave some margin for window chrome)
        max_window_width = available_width - 50  # Leave 50px margin from screen edge

        # Calculate available width for dashboard content
        available_dashboard_width = (max_window_width -
                                   self.dashboard_margins -
                                   self.window_chrome_width -
                                   self.safety_margin_width)

        # Calculate maximum columns that fit
        # Formula: (available_width - (columns-1)*spacing) / card_width >= columns
        # Rearranged: available_width >= columns*card_width + (columns-1)*spacing
        # Simplified: available_width >= columns*(card_width + spacing) - spacing

        max_columns = 1
        for columns in range(1, 15):  # Test up to 15 columns
            required_width = (columns * card_width) + ((columns - 1) * self.grid_spacing)
            if required_width <= available_dashboard_width:
                max_columns = columns
            else:
                break

        logger.debug(f"📊 {card_size.capitalize()} cards ({card_width}x{card_height}): max {max_columns} columns")
        logger.debug(f"   Available dashboard width: {available_dashboard_width}px")
        logger.debug(f"   Required for {max_columns} columns: {(max_columns * card_width) + ((max_columns - 1) * self.grid_spacing)}px")

        return max_columns

    def get_screen_limits(self, window=None):
        """Get maximum columns for each card size on current screen."""
        screen_info = self.get_current_screen_info(window)
        if not screen_info:
            # Fallback limits for unknown screen
            return {
                'small': 8,
                'medium': 6,
                'large': 5
            }

        limits = {}
        for card_size in self.card_sizes.keys():
            limits[card_size] = self.calculate_max_columns_for_card_size(screen_info, card_size)

        logger.debug(f"🎯 Screen limits: {limits}")
        return limits

    def get_valid_columns_for_card_size(self, card_size, window=None):
        """Get list of valid column counts for given card size on current screen."""
        limits = self.get_screen_limits(window)
        max_columns = limits.get(card_size, 8)

        # Always allow 4-5 columns (minimum reasonable options)
        valid_columns = list(range(4, min(max_columns + 1, 11)))  # Cap at 10 columns max

        logger.debug(f"✅ Valid columns for {card_size} cards: {valid_columns}")
        return valid_columns

    def get_valid_card_sizes_for_columns(self, columns, window=None):
        """Get list of valid card sizes for given column count on current screen."""
        limits = self.get_screen_limits(window)
        valid_sizes = []

        for card_size, max_cols in limits.items():
            if columns <= max_cols:
                valid_sizes.append(card_size)

        logger.debug(f"✅ Valid card sizes for {columns} columns: {valid_sizes}")
        return valid_sizes

    def validate_current_settings(self, window=None):
        """Validate current dashboard settings against screen limits."""
        settings = QSettings()
        current_columns = settings.value("dashboard/grid_columns", 6, type=int)
        current_card_size = settings.value("dashboard/card_size", "small")

        limits = self.get_screen_limits(window)
        max_columns_for_size = limits.get(current_card_size, 8)

        is_valid = current_columns <= max_columns_for_size

        logger.debug(f"🔍 Current settings validation:")
        logger.debug(f"   Columns: {current_columns}, Card size: {current_card_size}")
        logger.debug(f"   Max columns for {current_card_size}: {max_columns_for_size}")
        logger.debug(f"   Valid: {'✅' if is_valid else '❌'}")

        if not is_valid:
            # Suggest corrections
            suggested_columns = min(current_columns, max_columns_for_size)
            valid_sizes = self.get_valid_card_sizes_for_columns(current_columns, window)

            logger.debug(f"💡 Suggestions:")
            logger.debug(f"   Reduce columns to: {suggested_columns}")
            logger.debug(f"   Or change card size to: {valid_sizes}")

        return {
            'valid': is_valid,
            'current_columns': current_columns,
            'current_card_size': current_card_size,
            'max_columns_for_size': max_columns_for_size,
            'suggested_columns': min(current_columns, max_columns_for_size) if not is_valid else current_columns,
            'valid_sizes_for_columns': self.get_valid_card_sizes_for_columns(current_columns, window)
        }

# Global instance
screen_calculator = ScreenCalculator()

def get_screen_calculator():
    """Get the global screen calculator instance."""
    return screen_calculator
