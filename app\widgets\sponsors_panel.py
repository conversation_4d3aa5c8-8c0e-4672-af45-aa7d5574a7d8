import sys
import os
import shutil
from pathlib import Path
from datetime import date

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL (Pillow) not available - default sponsor images will not be generated")

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit,
    QPushButton, QFileDialog, QComboBox, QDateEdit, QGroupBox, QFormLayout,
    QSizePolicy, QMessageBox, QFrame, QTabWidget, QScrollArea # Added QTabWidget, QScrollArea
)
from PySide6.QtCore import Qt, QSize, QDate, QCoreApplication, QFileInfo, QRegularExpression, QTranslator # Added QFileInfo, QRegularExpression, QTranslator
from PySide6.QtGui import QPixmap, QIntValidator, QDoubleValidator, QIcon, QFont, QImageReader, QRegularExpressionValidator # Added QImageReader, QRegularExpressionValidator
from PySide6.QtWidgets import QApplication

# Import the updated manager
from app.data.club_sponsors_manager import ClubSponsorsManager

class SponsorSectionWidget(QWidget):
    """Widget for displaying and editing details of a single sponsor within a category."""
    # Define image constraints (can be adjusted)
    LOGO_MAX_SIZE_KB = 300
    LOGO_MAX_WIDTH = 500
    LOGO_MAX_HEIGHT = 300

    def __init__(self, category, index, sponsors_manager=None, parent=None):
        """
        Args:
            category (str): The category key (e.g., 'main', 'supporters').
            index (int): The 0-based index within the category list.
            sponsors_manager (ClubSponsorsManager): The data manager instance.
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(SponsorSectionWidget, self).__init__(parent)
        self.category = category
        self.index = index
        self.sponsors_manager = sponsors_manager

        # Determine paths based on category
        script_dir = Path(__file__).parent
        self.project_root = script_dir.parent.parent
        # Create category-specific directory path
        self.sponsor_image_dir = self.project_root / "media" / "sponsors" / self.category
        # Filename includes category and 1-based index for user-friendliness in filesystem
        self.image_filename = f"{self.category}{self.index + 1}.png"
        self.image_filepath = self.sponsor_image_dir / self.image_filename

        # Define both possible naming conventions for default images
        default_filename = f"{self.category}_default.png"
        alt_default_filename = f"default_{self.category}.png"
        default_filepath = self.sponsor_image_dir / default_filename
        alt_default_filepath = self.sponsor_image_dir / alt_default_filename

        # Use whichever default file exists, preferring the original naming convention
        if default_filepath.exists():
            self.default_image_filepath = default_filepath
        elif alt_default_filepath.exists():
            self.default_image_filepath = alt_default_filepath
        else:
            # If neither exists, keep the original convention as the intended filepath
            self.default_image_filepath = default_filepath

        self.current_image_path = None # Store the actual path being displayed

        self.init_ui()
        self._setup_connections()

    def init_ui(self):
        # --- Main Layout ---
        main_layout = QHBoxLayout(self) # Horizontal split: Form | Logo/Notes
        main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # --- Left Side: Form Layout for primary fields AND NOTES --- RE-MODIFIED
        left_side_widget = QWidget() # Container for the entire left side
        left_side_layout = QVBoxLayout(left_side_widget) # Vertical layout for left side
        left_side_layout.setContentsMargins(0,0,0,0)

        # Form Fields (Top part of left side)
        form_layout = QFormLayout() # Keep the form layout for fields
        form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        self.form_layout = form_layout  # Store reference for robust label updates

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText(self.tr("Sponsor Name..."))
        self.category_input = QLineEdit()
        # Set display value during retranslateUi, not here
        self.category_input.setReadOnly(True) # Read-only, set programmatically
        self.type_input = QLineEdit()
        self.type_input.setPlaceholderText(self.tr("Type..."))
        self.deal_start_date = QDateEdit(calendarPopup=True)
        self.deal_start_date.setDate(QDate.currentDate())

        self.deal_end_date = QDateEdit(calendarPopup=True)
        self.deal_end_date.setDate(QDate.currentDate())
        self.deal_end_date.setMinimumDate(self.deal_start_date.date()) # Initial minimum
        self.deal_term_combo = QComboBox()
        # Update items and make them translatable
        self.deal_term_combo.addItems([
             self.tr("Select deal"), # Placeholder
             self.tr("Daily"),
             self.tr("Monthly"),
             self.tr("Season"),
             self.tr("Yearly"),
             self.tr("Other")
        ])
        self.fee_input = QLineEdit()
        # Fee Input Validation
        fee_validator = QRegularExpressionValidator(QRegularExpression(r"^[0-9]*\.?[0-9]{0,2}$"))
        self.fee_input.setValidator(fee_validator)
        # Optional: Add validator for fee if it should be numeric
        # self.fee_input.setValidator(QDoubleValidator(0, 1e12, 2)) # Example: Allow large numbers with 2 decimals

        form_layout.addRow(self.tr("Sponsor Name:"), self.name_input)
        form_layout.addRow(self.tr("Category:"), self.category_input)
        form_layout.addRow(self.tr("Type:"), self.type_input)
        form_layout.addRow(self.tr("Deal Start:"), self.deal_start_date)
        form_layout.addRow(self.tr("Deal End:"), self.deal_end_date)
        form_layout.addRow(self.tr("Deal Term:"), self.deal_term_combo)
        form_layout.addRow(self.tr("Fee/Value:"), self.fee_input)

        left_side_layout.addLayout(form_layout) # Add form layout to the top of the left side VBox

        # -- Notes Area -- MOVED TO LEFT SIDE AGAIN
        self.notes_group_box = QGroupBox(self.tr("Notes"))
        self.notes_group_box.setObjectName("notes_group_box")
        notes_layout = QVBoxLayout(self.notes_group_box)
        self.notes_edit = QTextEdit()
        self.notes_edit.setAcceptRichText(False)
        self.notes_edit.setPlaceholderText(self.tr("Enter any relevant notes here..."))
        self.notes_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        notes_layout.addWidget(self.notes_edit)

        left_side_layout.addWidget(self.notes_group_box) # Add notes below the form
        left_side_layout.setStretchFactor(self.notes_group_box, 1) # Allow notes to stretch vertically on the left

        main_layout.addWidget(left_side_widget, 1) # Add left side widget, stretch factor 1

        # --- Separator Line (Optional) ---
        line = QFrame()
        line.setFrameShape(QFrame.Shape.VLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        main_layout.addWidget(line)

        # --- Right Side: Logo, Contact ONLY --- RE-MODIFIED
        right_side_layout = QVBoxLayout()
        right_side_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # -- Logo Area --
        # MODIFIED: Using QVBoxLayout for Logo + Buttons + Constraints
        logo_area_main_layout = QVBoxLayout()
        logo_area_main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter) # Center items horizontally

        # Logo Preview Area (Fixed size)
        self.logo_preview_label = QLabel(self.tr("No Logo"))
        self.logo_preview_label.setFixedSize(200, 75) # CHANGED SIZE
        self.logo_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.logo_preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        self.logo_preview_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        logo_area_main_layout.addWidget(self.logo_preview_label)

        # Logo Buttons (Horizontal layout below preview)
        logo_buttons_layout = QHBoxLayout()
        logo_buttons_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.upload_button = QPushButton(self.tr("Upload Logo"))
        self.remove_button = QPushButton(self.tr("Remove Logo"))
        self.remove_button.setEnabled(False)

        logo_buttons_layout.addWidget(self.upload_button)
        logo_buttons_layout.addWidget(self.remove_button)
        logo_area_main_layout.addLayout(logo_buttons_layout)

        # Logo Constraints Label (Below buttons)
        self.logo_constraints_label = QLabel(
            self.tr("(PNG, <{kb}KB, <{w}x{h}px)").format(
                kb=self.LOGO_MAX_SIZE_KB, w=self.LOGO_MAX_WIDTH, h=self.LOGO_MAX_HEIGHT
            )
        )
        font = QFont()
        font.setPointSize(8)
        self.logo_constraints_label.setFont(font)
        self.logo_constraints_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_area_main_layout.addWidget(self.logo_constraints_label)

        right_side_layout.addLayout(logo_area_main_layout)

        # -- Contact Info (Form Layout) --
        self.contact_group_box = QGroupBox(self.tr("Contact Info"))
        self.contact_group_box.setObjectName("contact_group_box")
        contact_form_layout = QFormLayout(self.contact_group_box)
        contact_form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        self.contact_input = QLineEdit()
        self.contact_input.setPlaceholderText(self.tr("Contact Person..."))
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText(self.tr("Email..."))
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText(self.tr("Phone..."))

        contact_form_layout.addRow(self.tr("Contact Person:"), self.contact_input)
        contact_form_layout.addRow(self.tr("Email:"), self.email_input)
        contact_form_layout.addRow(self.tr("Phone:"), self.phone_input)

        right_side_layout.addWidget(self.contact_group_box)

        right_side_layout.addStretch(1) # Add stretch to push contact/logo up

        main_layout.addLayout(right_side_layout, 1) # Add right side, stretch factor 1

    def _setup_connections(self):
        # Connect inputs to validation slot
        self.name_input.editingFinished.connect(lambda: self._validate_field(self.name_input))
        self.type_input.editingFinished.connect(lambda: self._validate_field(self.type_input)) # Validate type too? Optional.
        self.fee_input.editingFinished.connect(lambda: self._validate_field(self.fee_input))
        self.contact_input.editingFinished.connect(lambda: self._validate_field(self.contact_input)) # Optional
        self.email_input.editingFinished.connect(lambda: self._validate_field(self.email_input))
        self.phone_input.editingFinished.connect(lambda: self._validate_field(self.phone_input)) # Optional

        # Connect date edits to validation and end date minimum update
        self.deal_start_date.dateChanged.connect(self._update_end_date_minimum)
        self.deal_start_date.dateChanged.connect(lambda: self._validate_field(self.deal_start_date))
        self.deal_end_date.dateChanged.connect(lambda: self._validate_field(self.deal_end_date))

        # Connect combo box (no validation needed here, but keep save connection)
        self.deal_term_combo.currentIndexChanged.connect(self.save_data)

        # Connect text edit (validation not critical here, keep save connection)
        self.notes_edit.textChanged.connect(self.save_data)

        # Connect buttons
        self.upload_button.clicked.connect(self._upload_image)
        self.remove_button.clicked.connect(self._remove_image)

    def _update_end_date_minimum(self, start_date):
        # Set the minimum date for the end date picker to the selected start date
        self.deal_end_date.setMinimumDate(start_date)
        # Optional: Also ensure the current end date is not before the new start date
        if self.deal_end_date.date() < start_date:
            self.deal_end_date.setDate(start_date)
        # Re-validate end date whenever start date changes
        self._validate_field(self.deal_end_date)

    def _validate_field(self, widget):
        """Validates the input of a specific widget and updates its style."""
        is_valid = True
        error_tooltip = "" # Initialize with a default empty string

        if widget == self.name_input:
            is_valid = bool(widget.text().strip())
            error_tooltip = self.tr("Sponsor name cannot be empty.")
        elif widget == self.email_input:
            text = widget.text().strip()
            if text: # Only validate if not empty
                is_valid = "@" in text and "." in text.split("@")[-1] # Basic check
            error_tooltip = self.tr("Please enter a valid email address.")
        elif widget == self.fee_input:
            # Validator handles format, just check if needed based on other logic?
            # For now, consider it valid if validator allows it (or it's empty)
            is_valid = True # Assuming validator is sufficient for format
            error_tooltip = self.tr("Please enter a valid numeric fee/value.") # Tooltip might still be useful
        elif widget == self.deal_start_date or widget == self.deal_end_date:
            is_valid = self.deal_end_date.date() >= self.deal_start_date.date()
            error_tooltip = self.tr("End date cannot be before start date.")
        elif widget == self.type_input:
            # Type field validation is optional, consider it valid
            is_valid = True
            error_tooltip = self.tr("Type cannot be empty.")
        else:
            # For any other widgets, default to valid with no tooltip
            is_valid = True
            error_tooltip = ""

        self._set_widget_valid_state(widget, is_valid, error_tooltip)

    def _set_widget_valid_state(self, widget, is_valid, error_tooltip="Invalid input"):
        """Sets the visual state (property and style polish) for a widget based on validity."""
        if not is_valid:
            widget.setProperty("invalid", True)
            widget.setToolTip(error_tooltip)
        else:
            widget.setProperty("invalid", False)
            widget.setToolTip("") # Clear tooltip on valid

        # Re-polish the widget to apply the style changes based on the property
        widget.style().unpolish(widget)
        widget.style().polish(widget)
        widget.update() # Ensure visual update

    def _upload_image(self):
        # 1. Ensure target directory exists
        try:
            self.sponsor_image_dir.mkdir(parents=True, exist_ok=True)
            print(f"Ensuring sponsor image directory exists: {self.sponsor_image_dir}")
        except OSError as e:
            print(f"Error creating sponsor directory {self.sponsor_image_dir}: {e}")
            QMessageBox.warning(self, self.tr("Upload Error"), self.tr("Could not create directory for sponsor logos."))
            return

        # 2. Open QFileDialog
        file_filter = self.tr("PNG Images (*.png)")
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.tr("Select Sponsor Logo"),
            str(self.project_root), # Start in project root or last used directory
            file_filter
        )

        if not file_path:
            return # User cancelled

        # 3. Validate selected file
        file_info = QFileInfo(file_path)
        file_size_kb = file_info.size() / 1024

        # Check Size
        if file_size_kb > self.LOGO_MAX_SIZE_KB:
            error_msg = self.tr("Error: File size exceeds {kb}KB.").format(kb=self.LOGO_MAX_SIZE_KB)
            self.logo_constraints_label.setText(f'<font color="red">{error_msg}</font>')
            print(error_msg)
            return

        # Check Dimensions - MODIFIED
        reader = QImageReader(file_path)
        if not reader.canRead():
            error_msg = self.tr("Error: Cannot read image file.")
            self.logo_constraints_label.setText(f'<font color="red">{error_msg}</font>')
            print(error_msg)
            return

        img_size = reader.size()
        # Check width and height separately
        if img_size.width() > self.LOGO_MAX_WIDTH or img_size.height() > self.LOGO_MAX_HEIGHT:
            error_msg = self.tr("Error: Dimensions exceed {w}x{h}px.").format(
                w=self.LOGO_MAX_WIDTH, h=self.LOGO_MAX_HEIGHT
            )
            self.logo_constraints_label.setText(f'<font color="red">{error_msg}</font>')
            print(error_msg)
            return

        # 4. If valid, copy/overwrite
        try:
            # Make sure the directory exists
            if not self.sponsor_image_dir.exists():
                self.sponsor_image_dir.mkdir(parents=True, exist_ok=True)
                print(f"Created sponsor image directory: {self.sponsor_image_dir}")

            # Now copy the file
            shutil.copy2(file_path, self.image_filepath)
            print(f"Sponsor logo copied to: {self.image_filepath}")

            # 5. Update data manager
            self.sponsors_manager.set_logo_exists(self.category, self.index, True)

            # Force saving the updated logo_exists flag to the file immediately
            self.save_data()

            # 6. Update display
            self._update_logo_display()

            # 7. Verify file exists after copy
            if self.image_filepath.exists():
                print(f"VERIFIED: Logo file exists at {self.image_filepath}")
            else:
                print(f"WARNING: Logo file was not found after copy at {self.image_filepath}")
        except Exception as e:
            # 7. Handle errors
            print(f"Error copying sponsor logo from {file_path} to {self.image_filepath}: {e}")
            error_msg = self.tr("Error copying logo!")
            self.logo_constraints_label.setText(f'<font color="red">{error_msg}</font>')
            # Ensure manager state is consistent if copy failed
            if self.sponsors_manager.get_logo_exists(self.category, self.index):
                self.sponsors_manager.set_logo_exists(self.category, self.index, False)
                self._update_logo_display() # Update display again

    def _remove_image(self):
        print(f"Attempting to remove logo for {self.category}[{self.index}]")
        # 1. Check if self.image_filepath exists
        if self.image_filepath.exists():
            print(f"Found logo file at {self.image_filepath}")
            # 2. Try to remove
            try:
                os.remove(self.image_filepath)
                print(f"Removed sponsor logo file: {self.image_filepath}")
                # 3. Update data manager
                self.sponsors_manager.set_logo_exists(self.category, self.index, False)
                # Force save to ensure flag change is saved to file
                self.save_data()
                print(f"Updated logo_exists flag to False and saved to file")
                # 4. Update display
                self._update_logo_display()
            except (FileNotFoundError, PermissionError, OSError) as e:
                # 5. Handle errors
                print(f"Error removing sponsor logo file {self.image_filepath}: {e}")
                QMessageBox.warning(self, self.tr("Remove Error"), self.tr("Could not remove the logo file. Check permissions."))
                # Don't change manager state if removal failed, file might still be there.
        else:
            print(f"Logo file {self.image_filepath} does not exist, cannot remove.")
            # Ensure manager state is consistent if file is already gone
            if self.sponsors_manager.get_logo_exists(self.category, self.index):
                print(f"Updating inconsistent logo_exists flag from True to False")
                self.sponsors_manager.set_logo_exists(self.category, self.index, False)
                # Force save to ensure flag change is saved to file
                self.save_data()
                print(f"Saved updated logo_exists flag to file")
                self._update_logo_display() # Update display to reflect missing file

    def _update_logo_display(self):
        # Ensure we have a category default image
        if not self.default_image_filepath.exists():
            self.ensure_category_default_image()

        # 1. Get status from manager
        logo_should_exist = self.sponsors_manager.get_logo_exists(self.category, self.index)
        print(f"Updating logo display for {self.category}[{self.index}], logo_should_exist={logo_should_exist}")

        # 2. Determine path to load
        path_to_load = None
        specific_file_exists = self.image_filepath.exists()
        category_default_file_exists = self.default_image_filepath.exists()
        global_default_file_path = self.project_root / "media" / "sponsors" / "default.png" # Define global default path
        global_default_file_exists = global_default_file_path.exists()

        is_specific_image_set = False # Track if we end up using the specific image

        # Debug paths
        print(f"Specific image path: {self.image_filepath}, exists={specific_file_exists}")
        print(f"Category default path: {self.default_image_filepath}, exists={category_default_file_exists}")
        print(f"Global default path: {global_default_file_path}, exists={global_default_file_exists}")

        if logo_should_exist and specific_file_exists:
            path_to_load = self.image_filepath
            is_specific_image_set = True
            print(f"Loading specific logo image: {self.image_filepath}")
        elif category_default_file_exists:
            path_to_load = self.default_image_filepath
            print(f"Loading category default image: {self.default_image_filepath}")
        elif global_default_file_exists: # ADDED: Check for global default
            path_to_load = global_default_file_path
            print(f"Loading global default image: {global_default_file_path}")
        else:
            print(f"No suitable logo image found, will display 'No Logo'")
        # If logo_should_exist is True but specific_file_exists is False, we fall through
        # and attempt to load the category default, then the global default, or show "No Logo"

        # Check if we need to update logo_exists based on file existence
        if logo_should_exist != specific_file_exists:
            print(f"Logo existence mismatch: flag={logo_should_exist}, file={specific_file_exists}. Updating flag to match file.")
            self.sponsors_manager.set_logo_exists(self.category, self.index, specific_file_exists)
            # Force save to ensure flag is updated in the file
            if hasattr(self.sponsors_manager, 'save_sponsors'):
                self.sponsors_manager.save_sponsors()

        # 3. Load QPixmap
        pixmap_loaded = False
        if path_to_load:
            try:
                pixmap = QPixmap(str(path_to_load)) # Ensure path is string for QPixmap
                if not pixmap.isNull():
                    # 4. Scale and set pixmap
                    scaled_pixmap = pixmap.scaled(
                        QSize(200, 75), # Match QLabel size
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    self.logo_preview_label.setPixmap(scaled_pixmap)
                    self.logo_preview_label.setText("") # Clear placeholder text
                    self.logo_preview_label.setStyleSheet("") # Clear placeholder style
                    pixmap_loaded = True
                    self.current_image_path = path_to_load # Store the loaded path
                    print(f"Successfully loaded and displayed logo from {path_to_load}")
                else:
                    print(f"Warning: Loaded null pixmap from {path_to_load}")
            except Exception as e:
                print(f"Error loading sponsor logo preview from {path_to_load}: {e}")

        # 5. Handle case where no pixmap was loaded
        if not pixmap_loaded:
            self.logo_preview_label.setPixmap(QPixmap()) # Clear pixmap
            self.logo_preview_label.setText(self.tr("No Logo"))
            self.logo_preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;") # Restore placeholder style
            self.current_image_path = None

        # 6. Update constraints label - MODIFIED format
        default_constraints_text = self.tr("(PNG, <{kb}KB, <{w}x{h}px)").format(
            kb=self.LOGO_MAX_SIZE_KB, w=self.LOGO_MAX_WIDTH, h=self.LOGO_MAX_HEIGHT
        )
        if is_specific_image_set:
            # Show filename relative to project root media dir for clarity
            try:
                rel_path = os.path.relpath(self.image_filepath, self.project_root)
                self.logo_constraints_label.setText(str(rel_path))
            except ValueError:
                 self.logo_constraints_label.setText(self.image_filepath.name) # Fallback to just filename
            self.logo_constraints_label.setStyleSheet("") # Clear error color
        else:
            self.logo_constraints_label.setText(default_constraints_text)
            self.logo_constraints_label.setStyleSheet("") # Clear error color

        # 7. Enable/disable remove button
        self.remove_button.setEnabled(is_specific_image_set)

    def load_data(self):
        # Load data into UI fields
        print(f"Loading data for sponsor section {self.category} {self.index + 1}")
        data = self.sponsors_manager.get_sponsor_data(self.category, self.index)

        # --- Populate Fields ---
        self.name_input.setText(data.get("sponsor_name", ""))
        self.type_input.setText(data.get("type", ""))
        # Category field is read-only, set in init_ui

        # Dates - Ensure conversion from ISO string to QDate
        start_date_str = data.get("deal_start", date.today().isoformat()) # Default to today if missing
        end_date_str = data.get("deal_end", date.today().isoformat())
        try:
            q_start_date = QDate.fromString(start_date_str, Qt.DateFormat.ISODate)
            q_end_date = QDate.fromString(end_date_str, Qt.DateFormat.ISODate)
            self.deal_start_date.setDate(q_start_date if q_start_date.isValid() else QDate.currentDate())
            self.deal_end_date.setDate(q_end_date if q_end_date.isValid() else QDate.currentDate())
            # Ensure minimum date is set correctly based on loaded start date
            self.deal_end_date.setMinimumDate(self.deal_start_date.date())
        except Exception as e:
            print(f"Error parsing dates for {self.category}[{self.index}]: {e}. Using current date.")
            self.deal_start_date.setDate(QDate.currentDate())
            self.deal_end_date.setDate(QDate.currentDate())

        # Combo box - Find text, default to index 0 if not found
        term_text = data.get("deal_term", "Select deal")
        term_index = self.deal_term_combo.findText(term_text)
        self.deal_term_combo.setCurrentIndex(max(0, term_index)) # Use max(0,...) handles -1 if not found

        # Fee - Ensure it's displayed as a string
        self.fee_input.setText(str(data.get("fee", "")))

        # Contact Info
        self.contact_input.setText(data.get("contact", ""))
        self.email_input.setText(data.get("email", ""))
        self.phone_input.setText(data.get("phone", ""))

        # Notes
        self.notes_edit.setPlainText(data.get("notes", ""))

        # --- Update Visual Elements ---
        self._update_logo_display() # Update logo based on loaded 'logo_exists' flag

        # Add validation call after loading data to set initial state
        self._validate_all_fields()

    def save_data(self):
        # First, commit any pending edits in text fields by clearing focus
        # This ensures editingFinished signals are emitted if a field still has focus
        focused_widget = QApplication.focusWidget()
        if focused_widget and isinstance(focused_widget, QLineEdit) and focused_widget.parent() is self:
            # If a text field in this widget has focus, clear it to commit changes
            focused_widget.clearFocus()

        # Gather data from UI fields and update manager
        section_data = {}

        # Get sponsor name and debug it
        sponsor_name = self.name_input.text()
        section_data["sponsor_name"] = sponsor_name
        print(f"DEBUG: Saving section data for {self.category}[{self.index}] name={sponsor_name}")

        section_data["type"] = self.type_input.text()
        # Category is read-only, no need to save
        section_data["deal_start"] = self.deal_start_date.date().toString(Qt.DateFormat.ISODate)
        section_data["deal_end"] = self.deal_end_date.date().toString(Qt.DateFormat.ISODate)
        section_data["deal_term"] = self.deal_term_combo.currentText()
        # Fee - Keep as string for flexibility, validation could be added
        section_data["fee"] = self.fee_input.text()
        section_data["contact"] = self.contact_input.text()
        section_data["email"] = self.email_input.text()
        section_data["phone"] = self.phone_input.text()
        # Make sure we're getting text from the notes_edit widget (QTextEdit)
        section_data["notes"] = self.notes_edit.toPlainText()

        # Explicitly set logo_exists flag based on current state
        logo_exists = self.sponsors_manager.get_logo_exists(self.category, self.index)
        # Make sure it matches file system state
        if logo_exists != self.image_filepath.exists():
            logo_exists = self.image_filepath.exists()
            self.sponsors_manager.set_logo_exists(self.category, self.index, logo_exists)

        # Add logo_exists to section_data to ensure it's explicitly saved
        section_data["logo_exists"] = logo_exists

        # Use the correct method name: update_sponsor_section
        try:
            # Call the update method to update internal manager state
            self.sponsors_manager.update_sponsor_section(self.category, self.index, section_data)

            # Verify the data was saved in the manager by retrieving it back
            saved_data = self.sponsors_manager.get_sponsor_data(self.category, self.index)
            if saved_data.get("sponsor_name") != sponsor_name:
                print(f"WARNING: Verification failed - sponsor name mismatch after save!")
                print(f"  Expected: '{sponsor_name}', Got: '{saved_data.get('sponsor_name')}'")
                # Force another update attempt
                self.sponsors_manager.update_sponsor_section(self.category, self.index, section_data)
        except Exception as e:
            print(f"ERROR in SponsorSectionWidget.save_data: {e}")

    def retranslateUi(self):
        """Updates all translatable text in the UI."""
        try:
            # Import tooltip helper
            from app.utils.tooltip_helper import set_tooltip, apply_window_tooltip
            import logging

            logger = logging.getLogger(__name__)
            logger.info(f"Retranslating SponsorSectionWidget UI for {self.category}[{self.index}]")

            # Apply tooltip to the widget itself
            apply_window_tooltip(self)

            # Set translated category display in the category input
            translated_category = self._get_translated_category()
            self.category_input.setText(translated_category)

            # Update form field labels
            if hasattr(self, 'form_layout'):
                label_map = [
                    (self.name_input, self.tr("Sponsor Name:")),
                    (self.category_input, self.tr("Category:")),
                    (self.type_input, self.tr("Type:")),
                    (self.deal_start_date, self.tr("Deal Start:")),
                    (self.deal_end_date, self.tr("Deal End:")),
                    (self.deal_term_combo, self.tr("Deal Term:")),
                    (self.fee_input, self.tr("Fee/Value:"))
                ]
                for widget, label_text in label_map:
                    if label := self.form_layout.labelForField(widget):
                        label.setText(label_text)

            # Add tooltips to form fields
            set_tooltip(self.name_input, "Enter the sponsor's name")
            set_tooltip(self.category_input, "The sponsor category")
            set_tooltip(self.type_input, "Enter the type of sponsorship")
            set_tooltip(self.deal_start_date, "Select when the sponsorship deal starts")
            set_tooltip(self.deal_end_date, "Select when the sponsorship deal ends")
            set_tooltip(self.deal_term_combo, "Select the term of the sponsorship deal")
            set_tooltip(self.fee_input, "Enter the monetary value of the sponsorship")

            # Add tooltips to logo section
            set_tooltip(self.logo_preview_label, "Preview of the sponsor's logo")
            set_tooltip(self.upload_button, "Upload a logo image for this sponsor")
            set_tooltip(self.remove_button, "Remove the current logo image")

            # Add tooltips to contact section
            set_tooltip(self.contact_input, "Enter the name of the contact person")
            set_tooltip(self.email_input, "Enter the contact person's email address")
            set_tooltip(self.phone_input, "Enter the contact person's phone number")
            set_tooltip(self.notes_edit, "Enter any additional notes about this sponsor")

            # Update placeholder texts
            self.name_input.setPlaceholderText(self.tr("Sponsor Name..."))
            self.type_input.setPlaceholderText(self.tr("Type..."))
            self.contact_input.setPlaceholderText(self.tr("Contact Person..."))
            self.notes_edit.setPlaceholderText(self.tr("Enter any relevant notes here..."))
            self.email_input.setPlaceholderText(self.tr("Email..."))
            self.phone_input.setPlaceholderText(self.tr("Phone..."))

            # Update deal term combo items
            current_text = self.deal_term_combo.currentText()
            self.deal_term_combo.clear()
            self.deal_term_combo.addItems([
                self.tr("Select deal"),
                self.tr("Daily"),
                self.tr("Monthly"),
                self.tr("Season"),
                self.tr("Yearly"),
                self.tr("Other")
            ])
            # Restore selection if possible
            index = self.deal_term_combo.findText(current_text)
            if index >= 0:
                self.deal_term_combo.setCurrentIndex(index)

            # Update group box titles
            if hasattr(self, 'notes_group_box'):
                self.notes_group_box.setTitle(self.tr("Notes"))
            if hasattr(self, 'contact_group_box'):
                self.contact_group_box.setTitle(self.tr("Contact Info"))

            # Update logo-related texts
            self.logo_preview_label.setText(self.tr("No Logo"))
            self.upload_button.setText(self.tr("Upload Logo"))
            self.remove_button.setText(self.tr("Remove Logo"))
            self.logo_constraints_label.setText(
                self.tr("(PNG, <{kb}KB, <{w}x{h}px)").format(
                    kb=self.LOGO_MAX_SIZE_KB,
                    w=self.LOGO_MAX_WIDTH,
                    h=self.LOGO_MAX_HEIGHT
                )
            )

            # Update contact form labels
            if contact_form := self.contact_group_box.layout():
                contact_label_map = [
                    (self.contact_input, self.tr("Contact Person:")),
                    (self.email_input, self.tr("Email:")),
                    (self.phone_input, self.tr("Phone:"))
                ]
                for widget, label_text in contact_label_map:
                    if label := contact_form.labelForField(widget):
                        label.setText(label_text)

        except Exception as e:
            print(f"Error in SponsorSectionWidget.retranslateUi: {e}")

    # Helper for translations within this widget
    def tr(self, text):
        return QCoreApplication.translate("SponsorSectionWidget", text)

    def _validate_all_fields(self):
        """Runs validation on all relevant fields, e.g., after loading data."""
        self._validate_field(self.name_input)
        self._validate_field(self.email_input)
        self._validate_field(self.fee_input)
        self._validate_field(self.deal_start_date) # This call validates both dates

    def _get_translated_category(self):
        """Get the translated category name from the parent SponsorsPanel if possible."""
        # Try to get the parent SponsorsPanel
        parent = self.parent()
        while parent and not hasattr(parent, 'category_display_names'):
            parent = parent.parent()

        if parent and hasattr(parent, 'category_display_names') and self.category in parent.category_display_names:
            return parent.category_display_names[self.category]

        # Fallback: translate locally
        return self.tr(self.category.replace('_', ' ').title())

    def ensure_category_default_image(self):
        """Create default images for this category if they don't exist."""
        # Check if one of the patterns already exists
        if self.default_image_filepath.exists():
            return  # We already have a default image

        # Create directory if it doesn't exist
        self.sponsor_image_dir.mkdir(parents=True, exist_ok=True)

        if PIL_AVAILABLE:
            try:
                # Create a 300x150 image with category-specific color
                # Different categories will have slightly different colored placeholder
                category_colors = {
                    "main": (200, 230, 255),       # Light blue
                    "supporters": (230, 255, 230),  # Light green
                    "charity": (255, 230, 230),     # Light red
                    "sports_brand": (255, 255, 200) # Light yellow
                }
                bg_color = category_colors.get(self.category, (240, 240, 240))

                img = Image.new('RGBA', (300, 150), color=bg_color + (255,))
                draw = ImageDraw.Draw(img)

                # Draw a gray border
                draw.rectangle([(0, 0), (299, 149)], outline=(180, 180, 180), width=2)

                # Add placeholder text with category name
                try:
                    font = ImageFont.truetype("arial", 18)
                except:
                    font = ImageFont.load_default()

                text = f"{self.category.title()} Sponsor"
                draw.text((150, 75), text, fill=(100, 100, 100), font=font, anchor="mm")

                # Save the image using the preferred convention
                save_path = self.sponsor_image_dir / f"{self.category}_default.png"
                img.save(str(save_path))
                print(f"Created category default image at {save_path}")

                # Update the path that will be used
                self.default_image_filepath = save_path

            except Exception as e:
                print(f"Could not create category default image: {e}")
        else:
            print(f"PIL not available - default category image not created for {self.category}")

    def showEvent(self, event):
        """Called when the widget becomes visible. Ensure logo is properly displayed."""
        super().showEvent(event)

        # Force visibility of logo label, which might not be updated properly during initial loading
        if hasattr(self, 'logo_preview_label'):
            self.logo_preview_label.setVisible(True)
            print(f"showEvent: Logo visibility enforced for {self.category}[{self.index}]")

            # If we have a current image path, verify the image is displayed
            if self.current_image_path:
                if self.logo_preview_label.pixmap() and self.logo_preview_label.pixmap().isNull():
                    print(f"  Reloading logo image in showEvent from {self.current_image_path}")
                    self._update_logo_display()  # Reload the image if needed

        # Accept the event
        event.accept()

    def changeEvent(self, event):
        """Handle change events, including language changes."""
        from PySide6.QtCore import QEvent

        if event.type() == QEvent.Type.LanguageChange:
            print(f"Language change event received in SponsorSectionWidget for {self.category}[{self.index}]")
            # First update all translatable text
            self.retranslateUi()
            # Then explicitly update the logo display to ensure images are shown
            self._update_logo_display()

        # Always call the base class implementation
        super().changeEvent(event)

class SponsorsPanel(QWidget):
    """Main panel containing sub-tabs for different sponsor categories."""
    def __init__(self, sponsors_manager, parent=None):
        super().__init__(parent)
        self.sponsors_manager = sponsors_manager
        # Dictionary to hold the list of SponsorSectionWidgets for each category
        self.category_widgets = {}

        # Ensure default sponsor image exists
        self._ensure_default_sponsor_image()

        # Ensure all category default images exist
        self._ensure_all_category_default_images()

        self.init_ui()
        self.retranslateUi()

        # Connect to application aboutToQuit signal to ensure data is saved
        app = QApplication.instance()
        if app:
            app.aboutToQuit.connect(self.save_data)
            print("Connected SponsorsPanel.save_data to QApplication.aboutToQuit signal")

    def _ensure_default_sponsor_image(self):
        """Create a default sponsor image if it doesn't exist."""
        # Determine path for default image
        script_dir = Path(__file__).parent
        project_root = script_dir.parent.parent
        default_sponsor_img_path = project_root / "media" / "sponsors" / "default.png"

        # Create directory if it doesn't exist
        default_sponsor_img_path.parent.mkdir(parents=True, exist_ok=True)

        # If default image doesn't exist, create a basic placeholder
        if not default_sponsor_img_path.exists():
            if PIL_AVAILABLE:
                try:
                    # Create a 300x150 white image
                    img = Image.new('RGBA', (300, 150), color=(255, 255, 255, 255))
                    draw = ImageDraw.Draw(img)

                    # Draw a gray border
                    draw.rectangle([(0, 0), (299, 149)], outline=(200, 200, 200), width=2)

                    # Add placeholder text
                    try:
                        font = ImageFont.truetype("arial", 18)
                    except:
                        font = ImageFont.load_default()

                    draw.text((150, 75), "No Logo", fill=(100, 100, 100), font=font, anchor="mm")

                    # Save the image
                    img.save(str(default_sponsor_img_path))
                    print(f"Created default sponsor image at {default_sponsor_img_path}")

                except Exception as e:
                    print(f"Could not create default sponsor image: {e}")
            else:
                print("PIL not available - default sponsor image not created")

    def _ensure_all_category_default_images(self):
        """Create default images for all categories."""
        script_dir = Path(__file__).parent
        project_root = script_dir.parent.parent

        # First determine the user's preferred naming convention from any existing files
        preferred_format = None
        media_dir = project_root / "media" / "sponsors"

        # Look for any existing default files to determine naming preference
        for category in self.sponsors_manager.CATEGORIES.keys():
            category_dir = media_dir / category
            if category_dir.exists():
                pattern1_file = category_dir / f"{category}_default.png"
                pattern2_file = category_dir / f"default_{category}.png"
                if pattern1_file.exists():
                    preferred_format = "category_default"
                    break
                elif pattern2_file.exists():
                    preferred_format = "default_category"
                    break

        # Check if main/default_main.png exists (from user's screenshot)
        if (media_dir / "main" / "default_main.png").exists():
            preferred_format = "default_category"

        # If no format preference was detected, use a default
        if preferred_format is None:
            preferred_format = "category_default"  # Use original format as default

        print(f"Using preferred naming format: {preferred_format}")

        # Now create any missing default images with the preferred naming
        for category in self.sponsors_manager.CATEGORIES.keys():
            # Check for both naming conventions
            category_dir = project_root / "media" / "sponsors" / category
            category_dir.mkdir(parents=True, exist_ok=True)

            if preferred_format == "default_category":
                default_path = category_dir / f"default_{category}.png"
            else:
                default_path = category_dir / f"{category}_default.png"

            # Create directory if it doesn't exist
            category_dir.mkdir(parents=True, exist_ok=True)

            # If the default doesn't exist, create it
            if not default_path.exists():
                if PIL_AVAILABLE:
                    try:
                        # Create a category-specific colored image
                        category_colors = {
                            "main": (200, 230, 255),       # Light blue
                            "supporters": (230, 255, 230),  # Light green
                            "charity": (255, 230, 230),     # Light red
                            "sports_brand": (255, 255, 200) # Light yellow
                        }
                        bg_color = category_colors.get(category, (240, 240, 240))

                        img = Image.new('RGBA', (300, 150), color=bg_color + (255,))
                        draw = ImageDraw.Draw(img)

                        # Draw a gray border
                        draw.rectangle([(0, 0), (299, 149)], outline=(180, 180, 180), width=2)

                        # Add placeholder text
                        try:
                            font = ImageFont.truetype("arial", 18)
                        except:
                            font = ImageFont.load_default()

                        text = f"{category.title()} Sponsor"
                        draw.text((150, 75), text, fill=(100, 100, 100), font=font, anchor="mm")

                        # Save with preferred naming convention
                        img.save(str(default_path))
                        print(f"Created default image for {category} category at {default_path}")
                    except Exception as e:
                        print(f"Could not create default image for {category}: {e}")
                else:
                    print(f"PIL not available - default image for {category} not created")
            else:
                print(f"Default image for {category} already exists at {default_path}")

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Category translations for reference (technical keys -> display names)
        self.category_display_names = {
            "main": self.tr("Main"),
            "supporters": self.tr("Supporters"),
            "charity": self.tr("Charity"),
            "sports_brand": self.tr("Sports Brand")
        }

        # Iterate through categories defined in the manager
        for category, count in self.sponsors_manager.CATEGORIES.items():
            # Create a scroll area for each category tab
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setFrameShape(QFrame.Shape.NoFrame) # Optional: remove border

            # Create a container widget for the scroll area content
            category_content_widget = QWidget()
            scroll_area.setWidget(category_content_widget) # Place container in scroll area

            # Create a layout for the container widget (holds the section group boxes)
            category_layout = QVBoxLayout(category_content_widget)
            category_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

            self.category_widgets[category] = [] # Initialize list for this category

            # Create the required number of SponsorSectionWidgets
            for i in range(count):
                group_box = QGroupBox(f"{category.replace('_', ' ').title()} Sponsor #{i + 1}") # Placeholder title
                section_widget = SponsorSectionWidget(category, i, self.sponsors_manager)
                self.category_widgets[category].append(section_widget)

                group_layout = QVBoxLayout()
                group_layout.addWidget(section_widget)
                group_box.setLayout(group_layout)

                category_layout.addWidget(group_box)

            category_layout.addStretch() # Add stretch inside the scrollable content

            # Add the scroll area (containing the sections) as a tab
            # Use category key for internal mapping, display name for tab text
            display_name = self._translate_category(category)
            self.tab_widget.addTab(scroll_area, display_name) # Title will be updated in retranslateUi

        # Connect tab changed signal to save data
        self.tab_widget.currentChanged.connect(self._on_sponsor_tab_changed)

    def _translate_category(self, category):
        """Special method to handle translation of category names
        Works around translation file issues by providing fallback translations"""
        # First check if we have a predefined translation in our dictionary
        if hasattr(self, 'category_display_names') and category in self.category_display_names:
            return self.category_display_names[category]

        # Fallback: Replace underscores with spaces and title case
        formatted_name = category.replace('_', ' ').title()
        # Try to translate through regular tr() method
        translated = self.tr(formatted_name)
        # If translation is the same as input (not found), use the formatted name
        if translated == formatted_name:
            return formatted_name
        return translated

    def load_data(self):
        """Load all sponsor data from manager into UI widgets."""
        print(f"SponsorsPanel: Loading data for all sections in all categories")

        # Force sponsors manager to reload from file first
        if hasattr(self.sponsors_manager, 'load_sponsors'):
            print("SponsorsPanel: Explicitly calling sponsors_manager.load_sponsors() to refresh data")
            try:
                self.sponsors_manager.load_sponsors()

                # Debug the loaded data immediately after loading
                if hasattr(self.sponsors_manager, '_data'):
                    categories_loaded = len(self.sponsors_manager._data.keys())
                    sample_data = {}
                    if 'main' in self.sponsors_manager._data and len(self.sponsors_manager._data['main']) > 0:
                        sample_data['main'] = self.sponsors_manager._data['main'][0]
                    if 'supporters' in self.sponsors_manager._data and len(self.sponsors_manager._data['supporters']) > 0:
                        sample_data['supporters'] = self.sponsors_manager._data['supporters'][0]
                    print(f"DEBUG: Loaded {categories_loaded} categories. Sample data: {sample_data}")

                print("==== ClubSponsorsManager: Finished load_sponsors successfully ====")
            except Exception as e:
                print(f"ERROR loading sponsors data from file: {e}")

        # Now load the data into the UI - important to disconnect signals during loading
        for category, section_widgets in self.category_widgets.items():
            for i, section_widget in enumerate(section_widgets):
                print(f"Loading data for sponsor section {category} {i+1}")

                # Disconnect signals for this widget to prevent unintended saves during loading
                try:
                    # First, find all QLineEdit widgets that might trigger save signals
                    input_fields = section_widget.findChildren(QLineEdit)
                    for field in input_fields:
                        field.blockSignals(True)

                    # Block signals for other relevant widgets if needed
                    if hasattr(section_widget, 'notes_edit'):
                        section_widget.notes_edit.blockSignals(True)
                    if hasattr(section_widget, 'deal_term_combo'):
                        section_widget.deal_term_combo.blockSignals(True)
                    if hasattr(section_widget, 'deal_start_date'):
                        section_widget.deal_start_date.blockSignals(True)
                    if hasattr(section_widget, 'deal_end_date'):
                        section_widget.deal_end_date.blockSignals(True)

                    print(f"  Signals disconnected for {category} {i+1}")
                except Exception as e:
                    print(f"Error disconnecting signals for {category}[{i}]: {e}")

                # Load data into the widget
                try:
                    section_widget.load_data()
                    # Verify what was loaded
                    if hasattr(section_widget, 'name_input'):
                        print(f"  Completed loading data for {category} {i+1}, name='{section_widget.name_input.text()}'")
                except Exception as e:
                    print(f"Error loading data for {category}[{i}]: {e}")

                # Reconnect signals after loading
                try:
                    # Reconnect signals for all input fields
                    for field in input_fields:
                        field.blockSignals(False)

                    # Unblock signals for other relevant widgets
                    if hasattr(section_widget, 'notes_edit'):
                        section_widget.notes_edit.blockSignals(False)
                    if hasattr(section_widget, 'deal_term_combo'):
                        section_widget.deal_term_combo.blockSignals(False)
                    if hasattr(section_widget, 'deal_start_date'):
                        section_widget.deal_start_date.blockSignals(False)
                    if hasattr(section_widget, 'deal_end_date'):
                        section_widget.deal_end_date.blockSignals(False)

                    print(f"  Signals reconnected for {category} {i+1}")
                except Exception as e:
                    print(f"Error reconnecting signals for {category}[{i}]: {e}")

        # Verify loaded data
        try:
            main_sponsor = self.sponsors_manager.get_sponsor_data("main", 0)
            print(f"VERIFY: First main sponsor loaded. Name='{main_sponsor.get('sponsor_name', '')}', Type='{main_sponsor.get('type', '')}'")
        except Exception as e:
            print(f"Error verifying data after load: {e}")

        print("SponsorsPanel: Finished loading all sections from manager")

    def save_data(self):
        # Trigger save_data on each section widget first to update the manager's internal dict
        print("SponsorsPanel: Triggering save on individual sections...")
        sections_count = 0
        for category, section_widgets in self.category_widgets.items():
            for i, section_widget in enumerate(section_widgets):
                try:
                    section_widget.save_data() # This updates the manager's internal state
                    sections_count += 1
                except Exception as e:
                    print(f"Error saving section {category}[{i}]: {e}")
        print(f"SponsorsPanel: Processed {sections_count} section widgets")

        # Now, tell the manager to save its entire state to the file
        print("SponsorsPanel: Calling manager.save_sponsors()")
        try:
            if self.sponsors_manager:
                self.sponsors_manager.save_sponsors()
                print("SponsorsPanel: sponsors_manager.save_sponsors() completed")
            else:
                print("ERROR: sponsors_manager is None in SponsorsPanel.save_data")
        except Exception as e:
            print(f"ERROR: Failed to save sponsors data to file: {e}")
            # Try to recreate manager as a last resort if it somehow got corrupted
            try:
                from app.data.club_sponsors_manager import ClubSponsorsManager
                print("Attempting to recreate sponsors manager to recover...")
                self.sponsors_manager = ClubSponsorsManager()
                # Try saving again with new manager instance
                self.sponsors_manager.save_sponsors()
                print("Recovery save attempt complete.")
            except Exception as recovery_error:
                print(f"CRITICAL: Recovery save failed: {recovery_error}")

    def retranslateUi(self):
        """Retranslates the panel, including sub-tab titles and section widgets."""
        print("Retranslating SponsorsPanel UI")

        # Import tooltip helper
        from app.utils.tooltip_helper import set_tooltip, apply_tab_tooltips, apply_window_tooltip
        import logging

        logger = logging.getLogger(__name__)
        logger.info("Retranslating SponsorsPanel UI")

        # Apply tooltip to the widget itself
        apply_window_tooltip(self)

        # Apply tooltips to all tabs
        if hasattr(self, 'tab_widget'):
            apply_tab_tooltips(self.tab_widget)

        # Update the category display names dictionary first
        self.category_display_names = {
            "main": self.tr("Main"),
            "supporters": self.tr("Supporters"),
            "charity": self.tr("Charity"),
            "sports_brand": self.tr("Sports Brand")
        }

        # Retranslate sub-tab titles and add tooltips
        tab_index = 0
        tab_tooltips = {
            "main": "Manage main sponsors",
            "supporters": "Manage supporter sponsors",
            "charity": "Manage charity sponsors",
            "sports_brand": "Manage sports brand sponsors"
        }

        for category in self.sponsors_manager.CATEGORIES.keys():
            display_name = self._translate_category(category)
            try:
                self.tab_widget.setTabText(tab_index, display_name)

                # Set tooltip for the tab
                if category in tab_tooltips:
                    tooltip_text = self.tr(tab_tooltips[category])
                    self.tab_widget.setTabToolTip(tab_index, tooltip_text)

            except IndexError:
                 print(f"Warning: Tab index {tab_index} out of range during SponsorsPanel retranslate.")
            tab_index += 1

        # Retranslate each section widget (which will retranslate group boxes and internal labels)
        for category, section_widgets in self.category_widgets.items():
            for i, section_widget in enumerate(section_widgets):
                 # Retranslate the GroupBox title containing the section
                 # Find the GroupBox this section is in (it's the parent of the section widget)
                 # We placed section_widget inside group_layout which is inside group_box in init_ui
                 group_box = section_widget.parentWidget()
                 if isinstance(group_box, QGroupBox):
                      translated_category = self._translate_category(category)
                      # Use f-string with the translated category
                      group_box.setTitle(f"{translated_category} {self.tr('Sponsor')} #{i + 1}")
                 else:
                     # If parent isn't GroupBox, maybe it's the layout? Check layout's parent.
                     try:
                          layout = section_widget.parentWidget()
                          if layout:
                              group_box_check = layout.parentWidget()
                              if isinstance(group_box_check, QGroupBox):
                                   translated_category = self._translate_category(category)
                                   group_box_check.setTitle(f"{translated_category} {self.tr('Sponsor')} #{i + 1}")
                              else:
                                   print(f"Warning: Could not find QGroupBox parent for section {category} {i+1} during retranslate (Layout parent check). Parent was: {type(group_box_check)}")
                          else:
                              print(f"Warning: Could not find layout parent for section {category} {i+1} during retranslate.")
                     except AttributeError:
                          print(f"Warning: Error accessing parent widgets for section {category} {i+1} during retranslate (AttributeError). Parent was: {type(group_box)}")

                 # Call the section's own retranslate method (and make sure we handle any error)
                 try:
                     if hasattr(section_widget, 'retranslateUi'):
                         section_widget.retranslateUi()
                     else:
                         print(f"Warning: Section widget {category} {i+1} does not have retranslateUi method.")
                 except Exception as e:
                     print(f"Error retranslating section widget {category} {i+1}: {e}")

        # Force visual update
        self.update()

    # Helper for translations within this class if needed elsewhere
    def tr(self, text):
        return QCoreApplication.translate("SponsorsPanel", text)

    def getTranslationContext(self):
        """Returns the translation context for this widget.
        This is important for retrieving category names from translation files."""
        return "SponsorsPanel"

    def changeEvent(self, event):
        """Handle change events, including language changes for the entire panel."""
        from PySide6.QtCore import QEvent

        if event.type() == QEvent.Type.LanguageChange:
            print("Language change event received in SponsorsPanel")
            # First update all translatable text in the panel
            self.retranslateUi()

            # Then force update of all sponsor section widgets
            for category, section_widgets in self.category_widgets.items():
                for i, section_widget in enumerate(section_widgets):
                    try:
                        # Force logo display update for each section
                        if hasattr(section_widget, '_update_logo_display'):
                            print(f"Forcing logo display update for {category} sponsor #{i+1}")
                            section_widget._update_logo_display()
                    except Exception as e:
                        print(f"Error updating logo display for {category}[{i}]: {e}")

        # Always call the base class implementation
        super().changeEvent(event)

    def _on_sponsor_tab_changed(self, index):
        # Save data when switching between sponsor tabs
        print(f"SponsorsPanel: Switching to tab {index}")

        # Force data saving by calling save_data on all sections
        # This ensures that any unsaved changes in any section are properly saved
        self.save_data()

        # Get the category for the current tab to ensure its sections are up to date
        categories = list(self.sponsors_manager.CATEGORIES.keys())
        if 0 <= index < len(categories):
            current_category = categories[index]
            print(f"SponsorsPanel: Ensuring {current_category} sections are up to date")

    def closeEvent(self, event):
        """Ensure data is saved when the panel is closed."""
        print("SponsorsPanel closeEvent: Saving data before close...")
        self.save_data()
        print("SponsorsPanel closeEvent: Save completed")
        event.accept() # Allow the panel to close

# Example Run Block (for testing SponsorsPanel in isolation)
if __name__ == '__main__':
    QCoreApplication.setOrganizationName("TestOrg")
    QCoreApplication.setApplicationName("SponsorsPanelTest")
    app = QApplication(sys.argv)

    # Need a translator for tr() to work during init_ui/retranslateUi
    translator = QTranslator()
    # Load translations if available, otherwise tr() returns the source text
    # Example: if translator.load("translations/sponsors_panel_es.qm"):
    #    QCoreApplication.installTranslator(translator)

    # Instantiate the manager (it will load/create data)
    sponsors_manager = ClubSponsorsManager()

    # Create and show the panel
    panel = SponsorsPanel(sponsors_manager)
    panel.setWindowTitle("Sponsors Panel Test")
    panel.resize(600, 700) # Adjust size as needed
    panel.show()

    # Load data into the UI after showing
    panel.load_data()

    # Connect close event to save data (optional for testing)
    # original_closeEvent = panel.closeEvent
    # def test_closeEvent(event):
    #    panel.save_data()
    #    original_closeEvent(event)
    # panel.closeEvent = test_closeEvent

    sys.exit(app.exec())