<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1">
<context>
    <name>DateChangeConfirmationDialog</name>
    <message>
        <location filename="../app/pages/options_page.py" line="43"/>
        <source>Confirm Date Changes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="59"/>
        <source>Changing the season dates will affect other date ranges in the application. Please review the changes below and select how you want to proceed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="75"/>
        <source>Season Date Changes:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="85"/>
        <location filename="../app/pages/options_page.py" line="111"/>
        <source>Old range: {0} to {1}
New range: {2} to {3}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="101"/>
        <source>Macrocycle Date Changes:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="127"/>
        <source>Microcycle Date Changes:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="139"/>
        <source>{0}: {1} to {2} → {3} to {4}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="155"/>
        <source>Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="158"/>
        <source>Adjust all dates to maintain relationships</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="162"/>
        <source>Automatically adjust all dependent dates to maintain their relative positions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="164"/>
        <source>Keep dates where possible</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="167"/>
        <source>Only adjust dates that would be invalid with the new season dates</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>OptionsPage</name>
    <message>
        <location filename="../app/pages/options_page.py" line="232"/>
        <location filename="../app/pages/options_page.py" line="1584"/>
        <source>Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="310"/>
        <location filename="../app/pages/options_page.py" line="1810"/>
        <source>Season Dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="317"/>
        <location filename="../app/pages/options_page.py" line="1636"/>
        <source>Set the start date of the season</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="324"/>
        <source>Set the end date of the season</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="327"/>
        <location filename="../app/pages/options_page.py" line="1826"/>
        <location filename="../app/pages/options_page.py" line="1841"/>
        <location filename="../app/pages/options_page.py" line="1853"/>
        <location filename="../app/pages/options_page.py" line="1875"/>
        <location filename="../app/pages/options_page.py" line="1884"/>
        <location filename="../app/pages/options_page.py" line="2055"/>
        <location filename="../app/pages/options_page.py" line="2235"/>
        <source>Start Date:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="328"/>
        <location filename="../app/pages/options_page.py" line="1829"/>
        <location filename="../app/pages/options_page.py" line="1842"/>
        <location filename="../app/pages/options_page.py" line="1860"/>
        <location filename="../app/pages/options_page.py" line="1878"/>
        <location filename="../app/pages/options_page.py" line="1887"/>
        <location filename="../app/pages/options_page.py" line="2056"/>
        <location filename="../app/pages/options_page.py" line="2236"/>
        <source>End Date:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="349"/>
        <location filename="../app/pages/options_page.py" line="682"/>
        <location filename="../app/pages/options_page.py" line="1626"/>
        <location filename="../app/pages/options_page.py" line="1698"/>
        <source>Periodization</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="359"/>
        <source>Set the start date of the Macrocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="366"/>
        <source>Set the end date of the Macrocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="369"/>
        <location filename="../app/pages/options_page.py" line="1726"/>
        <location filename="../app/pages/options_page.py" line="1743"/>
        <source>Start Macrocycle:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="370"/>
        <location filename="../app/pages/options_page.py" line="1729"/>
        <location filename="../app/pages/options_page.py" line="1746"/>
        <source>End Macrocycle:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="383"/>
        <location filename="../app/pages/options_page.py" line="1751"/>
        <source>Validate &amp;&amp; Save Dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="385"/>
        <location filename="../app/pages/options_page.py" line="1644"/>
        <source>Validate the macrocycle dates and save if valid</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="388"/>
        <location filename="../app/pages/options_page.py" line="1754"/>
        <source>Periodization Chart</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="390"/>
        <location filename="../app/pages/options_page.py" line="1646"/>
        <source>Show a chart of intensity values across all periodization cycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="403"/>
        <location filename="../app/pages/options_page.py" line="1701"/>
        <source>Preparation Mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="410"/>
        <location filename="../app/pages/options_page.py" line="483"/>
        <location filename="../app/pages/options_page.py" line="550"/>
        <location filename="../app/pages/options_page.py" line="617"/>
        <location filename="../app/pages/options_page.py" line="1915"/>
        <source>ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="411"/>
        <location filename="../app/pages/options_page.py" line="484"/>
        <location filename="../app/pages/options_page.py" line="551"/>
        <location filename="../app/pages/options_page.py" line="618"/>
        <location filename="../app/pages/options_page.py" line="1916"/>
        <source>Start Microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="412"/>
        <location filename="../app/pages/options_page.py" line="485"/>
        <location filename="../app/pages/options_page.py" line="552"/>
        <location filename="../app/pages/options_page.py" line="619"/>
        <location filename="../app/pages/options_page.py" line="1917"/>
        <source>End Microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="413"/>
        <location filename="../app/pages/options_page.py" line="486"/>
        <location filename="../app/pages/options_page.py" line="553"/>
        <location filename="../app/pages/options_page.py" line="620"/>
        <location filename="../app/pages/options_page.py" line="1918"/>
        <source>Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="414"/>
        <location filename="../app/pages/options_page.py" line="487"/>
        <location filename="../app/pages/options_page.py" line="554"/>
        <location filename="../app/pages/options_page.py" line="621"/>
        <location filename="../app/pages/options_page.py" line="1919"/>
        <source>Target 1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="415"/>
        <location filename="../app/pages/options_page.py" line="488"/>
        <location filename="../app/pages/options_page.py" line="555"/>
        <location filename="../app/pages/options_page.py" line="622"/>
        <location filename="../app/pages/options_page.py" line="1920"/>
        <source>Target 2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="416"/>
        <location filename="../app/pages/options_page.py" line="489"/>
        <location filename="../app/pages/options_page.py" line="556"/>
        <location filename="../app/pages/options_page.py" line="623"/>
        <location filename="../app/pages/options_page.py" line="1921"/>
        <source>Target 3</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="417"/>
        <location filename="../app/pages/options_page.py" line="490"/>
        <location filename="../app/pages/options_page.py" line="557"/>
        <location filename="../app/pages/options_page.py" line="624"/>
        <location filename="../app/pages/options_page.py" line="1922"/>
        <source>Intensity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="419"/>
        <location filename="../app/pages/options_page.py" line="492"/>
        <location filename="../app/pages/options_page.py" line="559"/>
        <location filename="../app/pages/options_page.py" line="626"/>
        <location filename="../app/pages/options_page.py" line="1925"/>
        <source>Notes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="450"/>
        <location filename="../app/pages/options_page.py" line="527"/>
        <location filename="../app/pages/options_page.py" line="594"/>
        <location filename="../app/pages/options_page.py" line="661"/>
        <location filename="../app/pages/options_page.py" line="1764"/>
        <location filename="../app/pages/options_page.py" line="1772"/>
        <location filename="../app/pages/options_page.py" line="1780"/>
        <location filename="../app/pages/options_page.py" line="1788"/>
        <location filename="../app/pages/options_page.py" line="1998"/>
        <source>Add Microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="451"/>
        <location filename="../app/pages/options_page.py" line="528"/>
        <location filename="../app/pages/options_page.py" line="595"/>
        <location filename="../app/pages/options_page.py" line="662"/>
        <location filename="../app/pages/options_page.py" line="1766"/>
        <location filename="../app/pages/options_page.py" line="1774"/>
        <location filename="../app/pages/options_page.py" line="1782"/>
        <location filename="../app/pages/options_page.py" line="1790"/>
        <location filename="../app/pages/options_page.py" line="2181"/>
        <source>Edit Microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="452"/>
        <location filename="../app/pages/options_page.py" line="529"/>
        <location filename="../app/pages/options_page.py" line="596"/>
        <location filename="../app/pages/options_page.py" line="663"/>
        <location filename="../app/pages/options_page.py" line="1768"/>
        <location filename="../app/pages/options_page.py" line="1776"/>
        <location filename="../app/pages/options_page.py" line="1784"/>
        <location filename="../app/pages/options_page.py" line="1792"/>
        <source>Remove Microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="476"/>
        <location filename="../app/pages/options_page.py" line="1704"/>
        <source>Basic Mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="543"/>
        <location filename="../app/pages/options_page.py" line="1707"/>
        <source>Competition Mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="610"/>
        <location filename="../app/pages/options_page.py" line="1710"/>
        <source>Transition Mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="681"/>
        <location filename="../app/pages/options_page.py" line="1625"/>
        <source>Season</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="702"/>
        <location filename="../app/pages/options_page.py" line="1595"/>
        <source>Defined Zones:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="704"/>
        <location filename="../app/pages/options_page.py" line="1606"/>
        <source>Select a zone to view/edit assigned nationalities.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="707"/>
        <location filename="../app/pages/options_page.py" line="1596"/>
        <source>Add...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="708"/>
        <location filename="../app/pages/options_page.py" line="737"/>
        <location filename="../app/pages/options_page.py" line="1597"/>
        <location filename="../app/pages/options_page.py" line="1602"/>
        <source>Remove</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="709"/>
        <location filename="../app/pages/options_page.py" line="1598"/>
        <source>Rename...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="723"/>
        <location filename="../app/pages/options_page.py" line="1599"/>
        <source>Available Nationalities:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="724"/>
        <location filename="../app/pages/options_page.py" line="1600"/>
        <source>Assigned Nationalities:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="728"/>
        <location filename="../app/pages/options_page.py" line="1608"/>
        <source>Nationalities not assigned to the selected zone.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="732"/>
        <location filename="../app/pages/options_page.py" line="1610"/>
        <source>Nationalities currently assigned to the selected zone.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="736"/>
        <location filename="../app/pages/options_page.py" line="1601"/>
        <source>Add</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="738"/>
        <source>Assign selected available nationality to the current zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="739"/>
        <source>Remove selected assigned nationality from the current zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="764"/>
        <location filename="../app/pages/options_page.py" line="1585"/>
        <source>Football Rules</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="765"/>
        <location filename="../app/pages/options_page.py" line="1586"/>
        <source>Nationality Zones</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="766"/>
        <location filename="../app/pages/options_page.py" line="1590"/>
        <location filename="../app/pages/options_page.py" line="1945"/>
        <location filename="../app/pages/options_page.py" line="1966"/>
        <source>Dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="923"/>
        <location filename="../app/pages/options_page.py" line="2090"/>
        <location filename="../app/pages/options_page.py" line="2270"/>
        <source>End date must be after start date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="937"/>
        <source>Season cannot exceed {max_days} days</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="987"/>
        <location filename="../app/pages/options_page.py" line="1302"/>
        <source>Invalid Date Range</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="989"/>
        <source>The selected date range is invalid. Reverting to the last valid dates.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1271"/>
        <source>Macrocycle end date must be after start date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1276"/>
        <source>Macrocycle start date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1280"/>
        <source>Macrocycle end date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1304"/>
        <source>The selected macrocycle date range is invalid. Reverting to the last valid dates.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1406"/>
        <source>• Macrocycle end date must be after start date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1410"/>
        <source>• Macrocycle start date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1413"/>
        <source>• Macrocycle end date must be within season dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1429"/>
        <source>The following issues were found with the macrocycle dates:

</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1431"/>
        <source>Date Range Issues:
</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1436"/>
        <source>Microcycles Outside Macrocycle Range:
</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1442"/>
        <source>Would you like to fix these issues automatically?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1444"/>
        <source>Validation Issues</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1458"/>
        <source>Issues Fixed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1460"/>
        <source>The issues have been fixed and dates have been saved.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1484"/>
        <source>Validation Successful</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1486"/>
        <source>All dates are valid and have been saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1612"/>
        <source>Add a new nationality zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1614"/>
        <source>Remove the selected nationality zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1616"/>
        <source>Rename the selected nationality zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1618"/>
        <source>Add selected nationalities to the current zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1620"/>
        <source>Remove selected nationalities from the current zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1638"/>
        <source>Set the end date of the season (must be within 365/366 days of start date)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1640"/>
        <source>Set the start date of the Macrocycle (must be within season dates)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1642"/>
        <source>Set the end date of the Macrocycle (must be within season dates)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1650"/>
        <source>Microcycles for the preparation mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1652"/>
        <source>Add a new microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1654"/>
        <source>Edit the selected microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1656"/>
        <source>Remove the selected microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1660"/>
        <source>Microcycles for the basic mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1662"/>
        <source>Add a new basic microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1664"/>
        <source>Edit the selected basic microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1666"/>
        <source>Remove the selected basic microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1670"/>
        <source>Microcycles for the competition mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1672"/>
        <source>Add a new competition microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1674"/>
        <source>Edit the selected competition microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1676"/>
        <source>Remove the selected competition microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1680"/>
        <source>Microcycles for the transition mesocycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1682"/>
        <source>Add a new transition microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1684"/>
        <source>Edit the selected transition microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="1686"/>
        <source>Remove the selected transition microcycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2054"/>
        <location filename="../app/pages/options_page.py" line="2234"/>
        <source>Name:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2057"/>
        <location filename="../app/pages/options_page.py" line="2237"/>
        <source>Target 1:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2058"/>
        <location filename="../app/pages/options_page.py" line="2238"/>
        <source>Target 2:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2059"/>
        <location filename="../app/pages/options_page.py" line="2239"/>
        <source>Target 3:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2060"/>
        <location filename="../app/pages/options_page.py" line="2240"/>
        <source>Intensity:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2061"/>
        <location filename="../app/pages/options_page.py" line="2241"/>
        <source>Notes:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2096"/>
        <location filename="../app/pages/options_page.py" line="2276"/>
        <source>Start date must be within macrocycle dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2101"/>
        <location filename="../app/pages/options_page.py" line="2281"/>
        <source>End date must be within macrocycle dates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2114"/>
        <source>Microcycle dates cannot overlap with existing microcycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2172"/>
        <location filename="../app/pages/options_page.py" line="2387"/>
        <source>No Selection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2173"/>
        <source>Please select a microcycle to edit.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2296"/>
        <source>Start date must be after the previous microcycle&apos;s end date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2301"/>
        <source>End date must be before the next microcycle&apos;s start date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2373"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2375"/>
        <source>No periodization data available. Please add microcycles to at least one mesocycle.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2388"/>
        <source>Please select a microcycle to remove.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2397"/>
        <location filename="../app/pages/options_page.py" line="2554"/>
        <source>Confirm Removal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2398"/>
        <source>Are you sure you want to remove this microcycle?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2524"/>
        <source>Add Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2524"/>
        <source>Enter name for the new zone:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2528"/>
        <location filename="../app/pages/options_page.py" line="2543"/>
        <location filename="../app/pages/options_page.py" line="2591"/>
        <location filename="../app/pages/options_page.py" line="2611"/>
        <source>Invalid Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2528"/>
        <location filename="../app/pages/options_page.py" line="2543"/>
        <location filename="../app/pages/options_page.py" line="2591"/>
        <location filename="../app/pages/options_page.py" line="2611"/>
        <source>Zone name cannot be empty.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2531"/>
        <location filename="../app/pages/options_page.py" line="2596"/>
        <source>Duplicate Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2531"/>
        <location filename="../app/pages/options_page.py" line="2596"/>
        <source>A zone with this name already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2555"/>
        <source>Are you sure you want to remove the zone &apos;{}&apos;?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2583"/>
        <source>Rename Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../app/pages/options_page.py" line="2584"/>
        <source>Enter new name for zone &apos;{}&apos;:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
