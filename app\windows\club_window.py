import sys
import os
import shutil
from functools import partial # Import functools for partial
from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QGroupBox, QFormLayout, QLineEdit, QSpinBox, QComboBox, QPushButton,
    QFileDialog, QSizePolicy, QStyle, QColorDialog, QFrame, QScrollArea,
    QFontComboBox, QCheckBox, QGraphicsDropShadowEffect, QTableView, QHeaderView,
    QStyledItemDelegate, QAbstractItemView, QAbstractItemDelegate, QMessageBox # <-- Add QMessageBox
)
from PySide6.QtGui import QPixmap, QIcon, QFont, QColor, QImageReader, QPainter, QPen, QStandardItemModel, QStandardItem # <-- Add QStandardItem
from PySide6.QtCore import QCoreApplication, Qt, QTranslator, QSize, QFileInfo, Signal, QEvent, QSettings, QByteArray # Changed pyqtSignal to Signal, added QEvent
import datetime
import sqlite3
import pycountry
from pathlib import Path

# Import the data manager
from app.data.club_data_manager import ClubDataManager
# Import the sponsors manager and panel - ADDED
from app.data.club_sponsors_manager import ClubSponsorsManager
from app.widgets.sponsors_panel import SponsorsPanel
# Import the filter dialog
from app.dialogs.staff_filter_dialog import StaffFilterDialog
# Import constants
from app.utils.constants import countries # UPDATED import

# Import the data manager
from app.data.roster_manager import RosterManager
# Import the new widget
from app.widgets.team_groups_widget import TeamGroupsWidget

# New Custom Label Class
class OutlinedLabel(QLabel):
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self._text_color = QColor(Qt.GlobalColor.black)
        self._outline_enabled = False
        self._outline_color = QColor(Qt.GlobalColor.white)
        self._outline_thickness = 1

    def setTextColor(self, color):
        if isinstance(color, QColor) and self._text_color != color:
            self._text_color = color
            self.update() # Trigger repaint

    def setOutlineEnabled(self, enabled):
        if self._outline_enabled != bool(enabled):
            self._outline_enabled = bool(enabled)
            self.update()

    def setOutlineColor(self, color):
         if isinstance(color, QColor) and self._outline_color != color:
            self._outline_color = color
            self.update()

    def setOutlineThickness(self, thickness):
        thickness = max(1, int(thickness)) # Ensure at least 1px
        if self._outline_thickness != thickness:
            self._outline_thickness = thickness
            self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        text = self.text()
        rect = self.rect()
        font = self.font()
        alignment = self.alignment()

        painter.setFont(font)

        if self._outline_enabled and self._outline_thickness > 0:
            # pen = QPen(self._outline_color, self._outline_thickness * 2) # QPen width seems less reliable for text
            pen = QPen(self._outline_color)
            pen.setJoinStyle(Qt.PenJoinStyle.RoundJoin) # Make corners look better
            painter.setPen(pen)

            # Draw outline by drawing text multiple times with offsets
            # Scale the thickness value for visual appearance
            # Map input range 1-10 to visual range 1-4
            visual_thickness = max(1, int( (self._outline_thickness - 1) * (3 / 9) + 1 ))
            # Draw in 8 directions for a smoother outline
            for i in range(-visual_thickness, visual_thickness + 1):
                for j in range(-visual_thickness, visual_thickness + 1):
                    if i == 0 and j == 0: continue # Skip center point
                    # Optional: skip corners for a 4-direction outline
                    # if abs(i) + abs(j) > visual_thickness: continue
                    painter.drawText(rect.translated(i, j), int(alignment), text)

        # Draw the main text
        painter.setPen(QPen(self._text_color))
        painter.drawText(rect, int(alignment), text)

# --- Custom Delegate for Staff Type ---
class StaffTypeDelegate(QStyledItemDelegate):
    def __init__(self, types_list, placeholder_text="-- Select Type --", parent=None):
        super().__init__(parent)
        self.types = types_list # Store the passed (translated) list
        self.placeholder_text = placeholder_text # Store translated placeholder
        self.source_types = [] # Will store the original English values

    def set_source_types(self, source_types):
        """Set the original English source types."""
        self.source_types = source_types
        print(f"Source types set: {self.source_types}, translated types: {self.types}")

    def createEditor(self, parent, option, index):
        editor = QComboBox(parent)
        # Use the translated placeholder
        print(f"DEBUG StaffTypeDelegate.createEditor: Received placeholder='{self.placeholder_text}', types={self.types}") # DEBUG LINE ADDED
        editor.addItems([self.placeholder_text] + self.types)
        return editor

    def setEditorData(self, editor, index):
        # First try to get the source value from UserRole (English value)
        source_value = index.model().data(index, Qt.ItemDataRole.UserRole)

        # If we have a source value, find the corresponding translated item
        if source_value and self.source_types:
            # Find the index of the source value in the source_types list
            source_index = -1
            for i, source_type in enumerate(self.source_types):
                if source_type == source_value:
                    source_index = i
                    break

            # If found and within range of our translated types, use that index
            if source_index >= 0 and source_index < len(self.types):
                # Add 1 to account for the placeholder item
                editor.setCurrentIndex(source_index + 1)
                print(f"setEditorData: Set index {source_index + 1} for source value '{source_value}'")
                return

        # Fallback to the old method if we couldn't find a match using source value
        value = index.model().data(index, Qt.ItemDataRole.EditRole)
        current_index = editor.findText(value)
        if current_index >= 0:
            editor.setCurrentIndex(current_index)
            print(f"setEditorData: Found text match for '{value}' at index {current_index}")
        else:
            # If we still can't find it, try to find a match in the source types
            if value and self.source_types:
                for i, source_type in enumerate(self.source_types):
                    if source_type == value:
                        # Found a match in source types, use the corresponding translated item
                        editor.setCurrentIndex(i + 1)  # +1 for placeholder
                        print(f"setEditorData: Found source match for '{value}' at index {i + 1}")
                        return

            # If all else fails, use the placeholder
            editor.setCurrentIndex(0)
            print(f"setEditorData: No match found for '{value}', using placeholder")

    def setModelData(self, editor, model, index):
        value = editor.currentText()
        if editor.currentIndex() > 0: # Don't save the placeholder
            # Get the selected index (minus 1 for placeholder)
            selected_index = editor.currentIndex() - 1

            # Store both the display value and the source value
            model.setData(index, value, Qt.ItemDataRole.EditRole)

            # If we have source types and the index is valid, store the English value
            if self.source_types and selected_index < len(self.source_types):
                source_value = self.source_types[selected_index]
                model.setData(index, source_value, Qt.ItemDataRole.UserRole)
                print(f"Stored source value '{source_value}' for '{value}'")
        else:
            model.setData(index, "", Qt.ItemDataRole.EditRole) # Set empty if placeholder selected
            model.setData(index, "", Qt.ItemDataRole.UserRole) # Clear source value too

    def updateEditorGeometry(self, editor, option, index):
        editor.setGeometry(option.rect)
# --- End Custom Delegate ---

# --- Custom TableView for Enter Key Navigation ---
class MedicalTableView(QTableView):
    def keyPressEvent(self, event):
        current_index = self.currentIndex()
        is_editing = self.state() == QAbstractItemView.State.EditingState
        is_enter_key = event.key() in (Qt.Key.Key_Return, Qt.Key.Key_Enter)

        if is_editing and is_enter_key and current_index.isValid():
            editor = self.focusWidget() # Get the current editor widget
            # Commit data and close the editor
            self.commitData(editor)
            self.closeEditor(editor, QAbstractItemDelegate.EndEditHint.NoHint)

            model = self.model()
            current_row = current_index.row()
            current_col = current_index.column()
            next_row = current_row
            next_col = current_col + 1

            # Find next editable cell (skip non-editable like ID potentially)
            while next_col < model.columnCount():
                next_index_temp = model.index(next_row, next_col)
                if next_index_temp.flags() & Qt.ItemFlag.ItemIsEditable:
                    break
                next_col += 1
            else: # Reached end of row, wrap to next row
                next_row += 1
                next_col = 0 # Start searching from first column of next row
                while next_row < model.rowCount() and next_col < model.columnCount():
                     next_index_temp = model.index(next_row, next_col)
                     if next_index_temp.flags() & Qt.ItemFlag.ItemIsEditable:
                         break
                     next_col += 1
                else: # Reached end of table or couldn't find editable cell
                    event.accept()
                    return # Don't call super, just stop here

            # Move to the next editable index and start editing
            next_index = model.index(next_row, next_col)
            if next_index.isValid():
                self.setCurrentIndex(next_index)
                self.edit(next_index)
                event.accept()
                return # Handled
            else: # Could not find a valid next index
                 event.accept() # Still accept the Enter key press
                 return

        # Default handling for other keys or when not editing
        super().keyPressEvent(event)
# --- End Custom TableView ---

class ClubWindow(QWidget):
    # --- MODIFIED: Accept club_data_manager in init ---
    def __init__(self, club_data_manager=None, parent=None):
        super().__init__(parent)
        self.current_logo_path = None # To store the path of the current logo
        self.current_stadium_image_path = None # To store the path of the current stadium image
        self.medical_staff_filters = {} # Store active filters
        self.coaching_staff_filters = {} # ADDED BACK: Store coaching filters
        self.management_staff_filters = {} # Store active management filters

        # Connect to language change signal from parent window
        parent_window = self.parent()
        if parent_window and hasattr(parent_window, 'language_changed'):
            parent_window.language_changed.connect(self.retranslateUi)
            print("Connected club_window.retranslateUi to language_changed signal")

        # Install event filter to catch language change events
        app = QApplication.instance()
        if app:
            app.installEventFilter(self)

        # Define source lists for translatable combo box items
        self._surface_types_source = ["Grass", "Artificial Turf"]
        self._stadium_owners_source = ["Club Owned", "Rented"]
        self._medical_staff_types_source = [
            "Head Physio", "Physiotherapists", "Head of Sports Science",
            "Sports Scientists", "Club Doctor"
        ]
        self._coaching_staff_types_source = [ # Added Coaching roles
             "Manager", "Head Coach", "Assistant Manager", "Technical Director",
             "Director of Football", "First Team Coach", "Assistant Coach",
             "Reserve Team Coach (U23/U21 Coach)", "Youth Coach (U18, U17, etc.)",
             "Head of Youth Development", "Goalkeeping Coach", "Fitness Coach",
             "Set Piece Coach", "Defensive Coach", "Attacking Coach",
             "Transition Coach", "Chief Scout", "Scout", "Head of Recruitment",
             "Performance Analyst", "Opposition Analyst", "Data Analyst"
        ]
        self._management_staff_types_source = [ # Management roles
            "Owner", "President", "Chairman", "Chief Executive Officer (CEO)",
            "Managing Director", "Director of Football", "Sporting Director",
            "Club Technical Director", # CHANGED from "Technical Director"
            "Chief Financial Officer (CFO)", "Finance Director",
            "Commercial Director", "Head of Marketing", "Head of Sponsorship",
            "Communications Director", "Head of Communications",
            "Public Relations (PR) Manager", "Club Secretary", "Operations Manager",
            "Legal Advisor / Club Lawyer", "Head of Human Resources (HR)",
            "Academy Director", "Honorary Vice President", "Chief Commercial Officer",
            "Chief Brand Officer"
        ]

        # Kit Image Constraints
        self.KIT_IMAGE_MAX_SIZE_KB = 300
        self.KIT_IMAGE_MAX_DIMENSION = 300
        self.KIT_IMAGE_FOLDER = os.path.join(os.getcwd(), "media", "kit")

        # Ensure kit directory exists
        os.makedirs(self.KIT_IMAGE_FOLDER, exist_ok=True)

        # --- MODIFIED: Use passed manager or create default, RENAME to club_data_manager --- #
        self.club_data_manager = club_data_manager if club_data_manager else ClubDataManager()
        # ----------------------------------------------------------------------------------- #
        self.sponsors_manager = ClubSponsorsManager() # Instantiate sponsors manager

        # Initialize RosterManager needed for TeamGroupsWidget
        # TODO: Consider passing RosterManager as well for consistency
        self.manager = RosterManager() # ADDED RosterManager instance

        self.init_ui()

        # Connect to language change signal
        app = QApplication.instance()
        if app:
            app.installEventFilter(self)

        self.setObjectName("ClubWindow") # <-- ADDED objectName

        self.setWindowTitle(self.tr("Club Information"))
        self.setMinimumSize(600, 400)

    def init_ui(self):
        """Set up the user interface."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)

        # Import tooltip helper
        from app.utils.tooltip_helper import set_tooltip

        # Create main tab widget for pages (renamed from pages_tab_widget)
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumWidth(600)
        self.tab_widget.setMinimumHeight(400)
        main_layout.addWidget(self.tab_widget)

        # IMPORTANT: Connect the tab change signal to the handler
        self.tab_widget.currentChanged.connect(self._on_main_tab_changed)

        # Add tooltip to the main window
        set_tooltip(self, "View and edit club information")

        # Create Tabs (Widgets that will contain the layout for each tab)
        self.tab_information = QWidget()
        self.tab_contact = QWidget()
        self.tab_staff = QWidget()
        self.tab_kit = QWidget()
        # self.tab_sponsors = QWidget() # REMOVED Placeholder

        # Instantiate the Sponsors Panel - ADDED
        # It requires the sponsors_manager instance
        self.sponsors_panel = SponsorsPanel(self.sponsors_manager)

        # --- Setup Information Tab (with ScrollArea) --- # MODIFIED
        info_main_layout = QVBoxLayout(self.tab_information) # Main layout for the tab page
        info_main_layout.setContentsMargins(0,0,0,0) # Use full tab space

        # Create Scroll Area
        info_scroll_area = QScrollArea()
        info_scroll_area.setWidgetResizable(True) # Crucial for layout expansion
        info_scroll_area.setFrameShape(QFrame.Shape.NoFrame) # Optional: remove border
        info_main_layout.addWidget(info_scroll_area)

        # Create Container Widget for Scroll Area Content
        info_content_widget = QWidget()
        info_scroll_area.setWidget(info_content_widget) # Place container inside scroll area

        # Create Layout for the Container Widget (This holds the actual content)
        info_content_layout = QVBoxLayout(info_content_widget)
        info_content_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # -- General Info Group Box --
        self.general_group_box = QGroupBox()
        general_group_box_main_layout = QVBoxLayout(self.general_group_box) # Main layout for the group box

        # Top Area (Form Fields and Logo Preview)
        top_area_layout = QHBoxLayout() # Horizontal layout for form and preview

        # Form Fields (Left Side)
        general_form_layout = QFormLayout() # Keep using QFormLayout for inputs

        self.name_input = QLineEdit()
        self.short_name_input = QLineEdit()
        self.nickname_input = QLineEdit()
        self.year_founded_input = QSpinBox()
        self.year_founded_input.setRange(1800, datetime.date.today().year) # Sensible range
        self.year_founded_input.setValue(datetime.date.today().year) # Default to current year
        self.city_input = QLineEdit()
        self.country_combo = QComboBox()
        self.country_combo.addItem("--- Select Country ---") # Placeholder
        self.country_combo.addItems(countries)
        self.region_input = QLineEdit() # Optional region/continent

        general_form_layout.addRow(self.tr("Club Name:"), self.name_input)
        general_form_layout.addRow(self.tr("Short Name:"), self.short_name_input)
        general_form_layout.addRow(self.tr("Nickname:"), self.nickname_input)
        general_form_layout.addRow(self.tr("Year Founded:"), self.year_founded_input)
        general_form_layout.addRow(self.tr("City:"), self.city_input)
        general_form_layout.addRow(self.tr("Country:"), self.country_combo)
        general_form_layout.addRow(self.tr("Region/Continent:"), self.region_input)

        # Logo Preview (Right Side)
        self.logo_preview_label = QLabel(self.tr("No Logo"))
        self.logo_preview_label.setFixedSize(150, 150)
        self.logo_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.logo_preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;") # Basic placeholder style
        self.logo_preview_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)

        # Logo Remove Button (Top right of preview area)
        self.remove_logo_button = QPushButton()
        # Use Close 'X' Icon
        icon = self.style().standardIcon(QStyle.StandardPixmap.SP_DialogCloseButton)
        self.remove_logo_button.setIcon(icon)
        self.remove_logo_button.setFixedSize(20, 20) # Small square size
        self.remove_logo_button.setToolTip(self.tr("Remove Logo"))
        self.remove_logo_button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.remove_logo_button.setEnabled(False) # Disabled initially
        self.remove_logo_button.clicked.connect(self._handle_remove_logo)

        # Logo Upload Button (moved here)
        self.logo_upload_button = QPushButton()
        self.logo_upload_button.clicked.connect(self._handle_logo_upload)

        # Logo Constraints Label (moved here)
        self.logo_constraints_label = QLabel()
        font = QFont()
        font.setPointSize(8) # Smaller font size
        self.logo_constraints_label.setFont(font)
        self.logo_constraints_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Layout for Preview, Remove Button, Upload Button, Constraints Label
        preview_layout = QVBoxLayout()
        preview_layout.addWidget(self.remove_logo_button, 0, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignTop)
        preview_layout.addWidget(self.logo_preview_label, 0, Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(self.logo_upload_button, 0, Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(self.logo_constraints_label, 0, Qt.AlignmentFlag.AlignCenter)
        preview_layout.setContentsMargins(0,0,0,0)
        preview_layout.addStretch() # Add stretch to push contents up

        # Add form and preview+controls layout to top layout
        top_area_layout.addLayout(general_form_layout) # Form on the left
        top_area_layout.addLayout(preview_layout) # Preview and controls on the right

        # Add layouts to the group box's main layout
        general_group_box_main_layout.addLayout(top_area_layout)

        # Add group box to the scroll area's content layout
        info_content_layout.addWidget(self.general_group_box)

        # -- Stadium Group Box --
        self.stadium_group_box = QGroupBox()
        stadium_group_box_main_layout = QVBoxLayout(self.stadium_group_box) # Main layout

        # Top Area (Form Fields and Stadium Preview)
        stadium_top_area_layout = QHBoxLayout()

        # Form Fields (Left Side)
        stadium_form_layout = QFormLayout() # Keep using QFormLayout for inputs

        self.stadium_name_input = QLineEdit()
        self.capacity_input = QSpinBox()
        self.capacity_input.setRange(0, 300000) # Max capacity range
        self.seating_capacity_input = QSpinBox()
        self.seating_capacity_input.setRange(0, 300000) # Max capacity range
        self.surface_combo = QComboBox()
        self.surface_combo.addItem(self.tr("--- Select Surface ---")) # Add placeholder
        # Add translatable items
        for surface in self._surface_types_source:
            self.surface_combo.addItem(self.tr(surface))
        self.year_built_input = QSpinBox()
        self.year_built_input.setRange(1800, datetime.date.today().year) # Sensible range
        self.stadium_owner_combo = QComboBox()
        self.stadium_owner_combo.addItem(self.tr("--- Select Owner ---")) # Add placeholder
        # Add translatable items
        for owner in self._stadium_owners_source:
            self.stadium_owner_combo.addItem(self.tr(owner))

        stadium_form_layout.addRow(self.tr("Stadium Name:"), self.stadium_name_input)
        stadium_form_layout.addRow(self.tr("Capacity:"), self.capacity_input)
        stadium_form_layout.addRow(self.tr("Seating Capacity:"), self.seating_capacity_input)
        stadium_form_layout.addRow(self.tr("Surface Type:"), self.surface_combo)
        stadium_form_layout.addRow(self.tr("Year Built:"), self.year_built_input)
        stadium_form_layout.addRow(self.tr("Stadium Owner:"), self.stadium_owner_combo)

        # Stadium Preview (Right Side)
        self.stadium_preview_label = QLabel(self.tr("No Stadium Image"))
        self.stadium_preview_label.setFixedSize(150, 150)
        self.stadium_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.stadium_preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        self.stadium_preview_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)

        # Stadium Remove Button
        self.remove_stadium_button = QPushButton()
        # Use Close 'X' Icon
        icon = self.style().standardIcon(QStyle.StandardPixmap.SP_DialogCloseButton)
        self.remove_stadium_button.setIcon(icon)
        self.remove_stadium_button.setFixedSize(20, 20) # Small square size
        self.remove_stadium_button.setToolTip(self.tr("Remove Stadium Image"))
        self.remove_stadium_button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.remove_stadium_button.setEnabled(False)
        self.remove_stadium_button.clicked.connect(self._handle_remove_stadium_image)

        # Stadium Upload Button (moved here)
        self.stadium_upload_button = QPushButton()
        self.stadium_upload_button.clicked.connect(self._handle_stadium_upload)

        # Stadium Constraints Label (moved here)
        self.stadium_constraints_label = QLabel()
        font = QFont()
        font.setPointSize(8) # Smaller font size
        self.stadium_constraints_label.setFont(font)
        self.stadium_constraints_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Layout for Stadium Preview, Remove Button, Upload Button, Constraints Label
        stadium_preview_layout = QVBoxLayout()
        stadium_preview_layout.addWidget(self.remove_stadium_button, 0, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignTop)
        stadium_preview_layout.addWidget(self.stadium_preview_label, 0, Qt.AlignmentFlag.AlignCenter)
        stadium_preview_layout.addWidget(self.stadium_upload_button, 0, Qt.AlignmentFlag.AlignCenter)
        stadium_preview_layout.addWidget(self.stadium_constraints_label, 0, Qt.AlignmentFlag.AlignCenter)
        stadium_preview_layout.setContentsMargins(0,0,0,0)
        stadium_preview_layout.addStretch() # Add stretch

        # Add form and preview+controls layout to top layout
        stadium_top_area_layout.addLayout(stadium_form_layout)
        stadium_top_area_layout.addLayout(stadium_preview_layout)

        # Add layouts to the group box's main layout
        stadium_group_box_main_layout.addLayout(stadium_top_area_layout)

        # Add group box to the scroll area's content layout
        info_content_layout.addWidget(self.stadium_group_box)
        info_content_layout.addStretch() # Add stretch inside the scrollable content

        # --- Setup Contact Tab ---
        contact_layout = QVBoxLayout(self.tab_contact)
        contact_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        # Remove placeholder if it exists (the layout might be empty now)
        while contact_layout.count():
            item = contact_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        self.contact_group_box = QGroupBox(self.tr("Contact Information"))
        self.contact_form_layout = QFormLayout(self.contact_group_box) # Set layout directly

        self.contact_address1_input = QLineEdit()
        self.contact_address2_input = QLineEdit()
        self.contact_city_input = QLineEdit()
        self.contact_state_input = QLineEdit()
        self.contact_postal_input = QLineEdit()
        self.contact_phone_input = QLineEdit()
        self.contact_email_input = QLineEdit()
        self.contact_website_input = QLineEdit()

        self.contact_form_layout.addRow(self.tr("Address Line 1:"), self.contact_address1_input)
        self.contact_form_layout.addRow(self.tr("Address Line 2:"), self.contact_address2_input)
        self.contact_form_layout.addRow(self.tr("City/Town:"), self.contact_city_input)
        self.contact_form_layout.addRow(self.tr("State/Province:"), self.contact_state_input)
        self.contact_form_layout.addRow(self.tr("Postal Code:"), self.contact_postal_input)
        self.contact_form_layout.addRow(self.tr("Phone:"), self.contact_phone_input)
        self.contact_form_layout.addRow(self.tr("Email:"), self.contact_email_input)
        self.contact_form_layout.addRow(self.tr("Website:"), self.contact_website_input)

        contact_layout.addWidget(self.contact_group_box)
        contact_layout.addStretch()

        # --- Setup Other Tabs (Placeholders) ---
        staff_main_layout = QVBoxLayout(self.tab_staff)
        self.staff_sub_tab_widget = QTabWidget()
        staff_main_layout.addWidget(self.staff_sub_tab_widget)

        # Create Staff Sub-Tab Widgets
        self.tab_management = QWidget()
        self.tab_coaching = QWidget()
        self.tab_medical = QWidget()

        # Add Sub-Tabs to Staff Tab Widget
        self.staff_sub_tab_widget.addTab(self.tab_management, "")
        self.staff_sub_tab_widget.addTab(self.tab_coaching, "")
        self.staff_sub_tab_widget.addTab(self.tab_medical, "")

        # Populate Staff Sub-Tabs (Placeholders)
        management_layout = QVBoxLayout(self.tab_management)
        # self.management_placeholder_label = QLabel() # Remove placeholder label
        # management_layout.addWidget(self.management_placeholder_label) # Remove placeholder label
        # management_layout.addStretch() # Remove placeholder label stretch

        # Define Management Headers (moved here temporarily for edit clarity)
        self.management_staff_headers = [
            "ID", "Type", "Name", "Phone 1", "Phone 2", "Email",
            "City", "Country", "Address", "Office", "Working hours"
        ]

        # --- Management Staff Table Setup ---
        # Button Layout
        management_button_layout = QHBoxLayout()
        self.add_management_button = QPushButton(self.tr("Add"))
        self.remove_management_button = QPushButton(self.tr("Remove"))
        self.search_management_input = QLineEdit()
        self.search_management_input.setPlaceholderText(self.tr("Search..."))
        self.clear_search_management_button = QPushButton(self.tr("Clear")) # Ensure text is Clear
        # OMITTING self.clear_search_management_button.setFixedSize(...)
        self.filter_management_button = QPushButton(self.tr("Filter"))
        self.clear_filter_management_button = QPushButton(self.tr("Clear Filter"))

        management_button_layout.addWidget(self.add_management_button)
        management_button_layout.addWidget(self.remove_management_button)
        management_button_layout.addStretch(1)
        self.management_search_label = QLabel(self.tr("Search:"))
        management_button_layout.addWidget(self.management_search_label)
        management_button_layout.addWidget(self.search_management_input)
        management_button_layout.addWidget(self.clear_search_management_button)
        management_button_layout.addWidget(self.filter_management_button)
        management_button_layout.addWidget(self.clear_filter_management_button)
        management_layout.addLayout(management_button_layout)

        # Table View
        self.management_staff_table = MedicalTableView() # Reuse custom class
        self.management_staff_table.setSortingEnabled(True)
        self.management_staff_table.horizontalHeader().setSectionsMovable(True)
        self.management_staff_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectItems)
        self.management_staff_table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.management_staff_table.verticalHeader().setVisible(False)
        self.management_staff_table.setEditTriggers(QTableView.EditTrigger.SelectedClicked | QTableView.EditTrigger.AnyKeyPressed)

        self.management_staff_model = QStandardItemModel(0, len(self.management_staff_headers)) # Rows=0, Cols based on headers
        # Headers set in retranslateUi
        self.management_staff_table.setModel(self.management_staff_model)

        # Set the custom delegate for the 'Type' column (column index 1)
        # Pass the list of types defined in __init__
        self.management_type_delegate = StaffTypeDelegate(
            types_list=[], # Populated in retranslateUi
            placeholder_text="", # Populated in retranslateUi
            parent=self.management_staff_table
        )
        # Set the source types (English values)
        self.management_type_delegate.set_source_types(self._management_staff_types_source)
        self.management_staff_table.setItemDelegateForColumn(1, self.management_type_delegate)

        # Set the custom delegate for the 'Country' column (column index 7)
        # We can reuse StaffTypeDelegate if we treat countries like types
        self.management_country_delegate = StaffTypeDelegate(
             types_list=countries, # Use global country list
             placeholder_text="--- Select Country ---", # Set appropriate placeholder
             parent=self.management_staff_table
        )
        self.management_staff_table.setItemDelegateForColumn(7, self.management_country_delegate)

        # Set initial column widths (adjust as needed)
        col_widths = [50, 180, 150, 100, 100, 150, 100, 120, 150, 100, 120] # Example widths
        for i, width in enumerate(col_widths):
            if i < len(self.management_staff_headers):
                 self.management_staff_table.setColumnWidth(i, width)

        management_layout.addWidget(self.management_staff_table)
        # --- End Management Staff Table Setup ---

        coaching_layout = QVBoxLayout(self.tab_coaching)
        # --- Coaching Staff Table Setup --- # ADDED SECTION
        # Headers (Moved here for clarity during setup)
        self.coaching_staff_headers = [
            "ID", "Type", "Name", "Phone 1", "Phone 2", "Email",
            "City", "Country", "Address", "Office", "Working hours"
        ]

        # Button Layout
        coaching_button_layout = QHBoxLayout()
        self.add_coaching_staff_button = QPushButton(self.tr("Add"))
        self.remove_coaching_staff_button = QPushButton(self.tr("Remove"))
        self.search_coaching_staff_input = QLineEdit()
        self.search_coaching_staff_input.setPlaceholderText(self.tr("Search..."))
        self.clear_search_coaching_button = QPushButton(self.tr("Clear"))
        # This is the button in question:
        # ADD BACK: self.filter_coaching_staff_button = QPushButton(self.tr("Filter"))
        self.filter_coaching_staff_button = QPushButton(self.tr("Filter"))
        # --- ADDED: Connect signal immediately after creation ---
        # --- TESTING: Connect to pressed signal instead of clicked ---
        # REMOVE: self.filter_coaching_staff_button.pressed.connect(self._filter_coaching_staff)
        # ----------------------------------------------------------
        # ADD BACK: self.clear_filter_coaching_button = QPushButton(self.tr("Clear Filter"))
        self.clear_filter_coaching_button = QPushButton(self.tr("Clear Filter"))

        # Adding widgets to the layout:
        coaching_button_layout.addWidget(self.add_coaching_staff_button)
        coaching_button_layout.addWidget(self.remove_coaching_staff_button)
        coaching_button_layout.addStretch(1)
        self.coaching_search_label = QLabel(self.tr("Search:"))
        coaching_button_layout.addWidget(self.coaching_search_label)
        coaching_button_layout.addWidget(self.search_coaching_staff_input)
        coaching_button_layout.addWidget(self.clear_search_coaching_button)
        # This is where it's added to the visible layout:
        # ADD BACK: coaching_button_layout.addWidget(self.filter_coaching_staff_button)
        coaching_button_layout.addWidget(self.filter_coaching_staff_button)
        # ADD BACK: coaching_button_layout.addWidget(self.clear_filter_coaching_button)
        coaching_button_layout.addWidget(self.clear_filter_coaching_button)
        coaching_layout.addLayout(coaching_button_layout)

        # Table View
        self.coaching_staff_table = MedicalTableView() # Reuse custom class
        self.coaching_staff_table.setSortingEnabled(True)
        self.coaching_staff_table.horizontalHeader().setSectionsMovable(True)
        self.coaching_staff_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectItems)
        self.coaching_staff_table.setSelectionMode(QTableView.SelectionMode.SingleSelection)
        self.coaching_staff_table.verticalHeader().setVisible(False)
        self.coaching_staff_table.setEditTriggers(QTableView.EditTrigger.SelectedClicked | QTableView.EditTrigger.AnyKeyPressed)

        self.coaching_staff_model = QStandardItemModel(0, len(self.coaching_staff_headers))
        # Headers set in retranslateUi
        self.coaching_staff_table.setModel(self.coaching_staff_model)

        # Set the custom delegate for the 'Type' column (column index 1)
        self.coaching_type_delegate = StaffTypeDelegate(
            types_list=[], # Populated in retranslateUi
            placeholder_text="", # Populated in retranslateUi
            parent=self.coaching_staff_table
        )
        # Set the source types (English values)
        self.coaching_type_delegate.set_source_types(self._coaching_staff_types_source)
        self.coaching_staff_table.setItemDelegateForColumn(1, self.coaching_type_delegate)

        # Set the custom delegate for the 'Country' column (column index 7)
        self.coaching_country_delegate = StaffTypeDelegate(
             types_list=countries, # Use global country list
             placeholder_text="--- Select Country ---", # Set appropriate placeholder
             parent=self.coaching_staff_table
        )
        self.coaching_staff_table.setItemDelegateForColumn(7, self.coaching_country_delegate)

        # Set initial column widths (adjust as needed)
        coaching_col_widths = [50, 180, 150, 100, 100, 150, 100, 120, 150, 100, 120] # Example widths
        for i, width in enumerate(coaching_col_widths):
            if i < len(self.coaching_staff_headers):
                 self.coaching_staff_table.setColumnWidth(i, width)

        coaching_layout.addWidget(self.coaching_staff_table)
        # --- End Coaching Staff Table Setup ---

        medical_layout = QVBoxLayout(self.tab_medical)
        # REMOVE self.medical_placeholder_label = QLabel()
        # REMOVE medical_layout.addWidget(self.medical_placeholder_label)
        # REMOVE medical_layout.addStretch()

        # --- Medical Staff Table Setup ---
        # Button Layout
        medical_button_layout = QHBoxLayout()
        self.add_staff_button = QPushButton(self.tr("Add"))
        self.remove_staff_button = QPushButton(self.tr("Remove"))
        self.search_staff_input = QLineEdit()
        self.search_staff_input.setPlaceholderText(self.tr("Search..."))
        self.clear_search_button = QPushButton(self.tr("Clear")) # Ensure text is Clear
        # OMITTING self.clear_search_button.setFixedSize(...)
        self.filter_staff_button = QPushButton(self.tr("Filter"))
        self.clear_filter_button = QPushButton(self.tr("Clear Filter")) # New Button

        medical_button_layout.addWidget(self.add_staff_button)
        medical_button_layout.addWidget(self.remove_staff_button)
        medical_button_layout.addStretch(1)
        self.medical_search_label = QLabel(self.tr("Search:"))
        medical_button_layout.addWidget(self.medical_search_label)
        medical_button_layout.addWidget(self.search_staff_input)
        medical_button_layout.addWidget(self.clear_search_button)
        medical_button_layout.addWidget(self.filter_staff_button)
        medical_button_layout.addWidget(self.clear_filter_button)
        medical_layout.addLayout(medical_button_layout)

        # Table View
        self.medical_staff_table = MedicalTableView() # Changed to custom class
        self.medical_staff_table.setSortingEnabled(True)
        self.medical_staff_table.horizontalHeader().setSectionsMovable(True)
        self.medical_staff_table.setSelectionBehavior(QTableView.SelectionBehavior.SelectItems)
        self.medical_staff_table.setSelectionMode(QTableView.SelectionMode.SingleSelection) # Start with single selection
        self.medical_staff_table.verticalHeader().setVisible(False) # Hide row numbers
        self.medical_staff_table.setEditTriggers(QTableView.EditTrigger.SelectedClicked | QTableView.EditTrigger.AnyKeyPressed)

        self.medical_staff_model = QStandardItemModel(0, 7) # Rows=0, Cols=7 initially
        self.medical_staff_headers = [
            "ID", "Type", "Name", "Phone", "Email", "Office", "Working hours"
        ]
        # Apply translation to the headers
        translated_headers = [self.tr(h) for h in self.medical_staff_headers]
        self.medical_staff_model.setHorizontalHeaderLabels(translated_headers)

        self.medical_staff_table.setModel(self.medical_staff_model)

        # Set the custom delegate for the 'Type' column (column index 1)
        # Pass the *translated* list to the delegate
        translated_types = [self.tr(t) for t in self._medical_staff_types_source]
        translated_placeholder = self.tr("-- Select Type --")
        type_delegate = StaffTypeDelegate(
            types_list=translated_types,
            placeholder_text=translated_placeholder,
            parent=self.medical_staff_table
        )
        # Set the source types (English values)
        type_delegate.set_source_types(self._medical_staff_types_source)
        self.medical_staff_table.setItemDelegateForColumn(1, type_delegate)

        # Set initial column widths (adjust as needed)
        self.medical_staff_table.setColumnWidth(0, 50)  # ID
        self.medical_staff_table.setColumnWidth(1, 150) # Type
        self.medical_staff_table.setColumnWidth(2, 150) # Name
        # Let others stretch or set fixed widths
        self.medical_staff_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Interactive) # Phone - Changed to Interactive
        self.medical_staff_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.Interactive) # Email - Changed to Interactive
        self.medical_staff_table.setColumnWidth(5, 100) # Office
        self.medical_staff_table.setColumnWidth(6, 120) # Working hours

        medical_layout.addWidget(self.medical_staff_table)
        # --- End Medical Staff Table Setup ---

        # --- Setup Kit Tab (with ScrollArea) --- # MODIFIED
        kit_page_layout = QVBoxLayout(self.tab_kit) # Main layout for the tab page
        kit_page_layout.setContentsMargins(0,0,0,0)

        # Create Scroll Area
        kit_scroll_area = QScrollArea()
        kit_scroll_area.setWidgetResizable(True)
        kit_scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        kit_page_layout.addWidget(kit_scroll_area)

        # Create Container Widget for Scroll Area Content
        kit_content_widget = QWidget()
        kit_scroll_area.setWidget(kit_content_widget)

        # Create Layout for the Container Widget (This holds the sub-tab widget)
        kit_content_layout = QVBoxLayout(kit_content_widget)

        self.kit_sub_tab_widget = QTabWidget()
        kit_content_layout.addWidget(self.kit_sub_tab_widget) # Add sub-tabs to scrollable content

        # Create Kit Sub-Tab Widgets
        self.tab_kit_colors = QWidget()
        self.tab_kit_1st = QWidget()
        self.tab_kit_2nd = QWidget()
        self.tab_kit_3rd = QWidget()
        self.tab_kit_1st_gk = QWidget()
        self.tab_kit_2nd_gk = QWidget()

        # Add Kit Sub-Tabs to Kit Tab Widget
        self.kit_sub_tab_widget.addTab(self.tab_kit_colors, self.tr("Colors"))
        self.kit_sub_tab_widget.addTab(self.tab_kit_1st, self.tr("1st Kit"))
        self.kit_sub_tab_widget.addTab(self.tab_kit_2nd, self.tr("2nd Kit"))
        self.kit_sub_tab_widget.addTab(self.tab_kit_3rd, self.tr("3rd Kit"))
        self.kit_sub_tab_widget.addTab(self.tab_kit_1st_gk, self.tr("1st GK Kit"))
        self.kit_sub_tab_widget.addTab(self.tab_kit_2nd_gk, self.tr("2nd GK Kit"))

        # --- Populate Kit Sub-Tabs ---

        # -- Colors Sub-Tab --
        colors_tab_main_layout = QHBoxLayout(self.tab_kit_colors)
        colors_tab_main_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
        colors_form_layout = QFormLayout()
        colors_form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.FieldsStayAtSizeHint)

        # --- Labels ---
        self.label_color_1st = QLabel(self.tr("1st Color:")) # Set initial text
        self.label_color_2nd = QLabel(self.tr("2nd Color:")) # Set initial text
        self.label_color_3rd = QLabel(self.tr("3rd Color:")) # Set initial text

        # --- Buttons ---
        self.color_button_1st = QPushButton(" ")
        self.color_button_1st.setFixedSize(50, 25); self.color_button_1st.setFlat(False)
        self.color_button_1st.clicked.connect(self._handle_color_pick_1st)
        self.color_button_2nd = QPushButton(" ")
        self.color_button_2nd.setFixedSize(50, 25); self.color_button_2nd.setFlat(False)
        self.color_button_2nd.clicked.connect(self._handle_color_pick_2nd)
        self.color_button_3rd = QPushButton(" ")
        self.color_button_3rd.setFixedSize(50, 25); self.color_button_3rd.setFlat(False)
        self.color_button_3rd.clicked.connect(self._handle_color_pick_3rd)
        colors_form_layout.addRow(self.label_color_1st, self.color_button_1st)
        colors_form_layout.addRow(self.label_color_2nd, self.color_button_2nd)
        colors_form_layout.addRow(self.label_color_3rd, self.color_button_3rd)
        colors_form_layout.setVerticalSpacing(10); colors_form_layout.setContentsMargins(10, 10, 10, 10)
        preview_container_widget = QWidget()
        preview_container_layout = QVBoxLayout(preview_container_widget)
        preview_container_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignRight)
        preview_container_layout.setContentsMargins(10, 10, 10, 10)
        self.color_preview_frame = QFrame(); self.color_preview_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.color_preview_frame.setFixedSize(120, 70); self.color_preview_frame.setStyleSheet("background-color: white; border: 1px solid black;")
        preview_frame_layout = QVBoxLayout(self.color_preview_frame)
        preview_frame_layout.setContentsMargins(5, 5, 5, 5)
        self.color_preview_label = QLabel("FOOT|DATA"); self.color_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font_color_preview = QFont(); font_color_preview.setPointSize(10); font_color_preview.setBold(True)
        self.color_preview_label.setFont(font_color_preview)
        self.color_preview_label.setStyleSheet("color: black; background-color: transparent; border: none;")
        preview_frame_layout.addWidget(self.color_preview_label)
        preview_container_layout.addWidget(self.color_preview_frame)
        preview_container_layout.addStretch(1)
        colors_tab_main_layout.addLayout(colors_form_layout, 1)
        colors_tab_main_layout.addWidget(preview_container_widget, 0)

        # -- Kit Sub-Tabs (New Structure) --
        kit_map = {
            "1st": (self.tab_kit_1st, "1"),
            "2nd": (self.tab_kit_2nd, "2"),
            "3rd": (self.tab_kit_3rd, "3"),
            "1st_gk": (self.tab_kit_1st_gk, "4"), # Use 4 for 1st GK
            "2nd_gk": (self.tab_kit_2nd_gk, "5")  # Use 5 for 2nd GK
        }

        small_font = QFont()
        small_font.setPointSize(8)
        close_icon = self.style().standardIcon(QStyle.StandardPixmap.SP_DialogCloseButton)

        for key, (kit_tab, kit_id) in kit_map.items():
            tab_layout = QVBoxLayout(kit_tab) # Main layout for the tab
            tab_layout.setAlignment(Qt.AlignmentFlag.AlignTop) # Align group boxes to top

            # Images Group Box
            images_group_box = QGroupBox(self.tr("Images")) # Wrap title
            images_group_box_main_layout = QHBoxLayout(images_group_box)
            setattr(self, f"kit_{key}_images_group_box", images_group_box)
            tab_layout.addWidget(images_group_box)

            # --- Front Side Section ---
            front_layout = QVBoxLayout()
            front_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignCenter)

            front_label = QLabel() # Text set in retranslateUi
            front_label.setText(self.tr("Front")) # Add initial translation
            front_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            setattr(self, f"kit_{key}_front_title_label", front_label)
            front_layout.addWidget(front_label)

            # Frame for Preview + Remove Button
            front_preview_frame = QFrame()
            front_preview_frame.setFixedSize(160, 160) # Slightly larger for border/padding
            front_preview_frame.setFrameShape(QFrame.Shape.StyledPanel)
            front_preview_frame_layout = QVBoxLayout(front_preview_frame)
            front_preview_frame_layout.setContentsMargins(5,5,5,5)

            # Remove button moved below preview
            front_remove_button = QPushButton("X") # Set text to X
            # front_remove_button.setIcon(close_icon) # Remove icon
            front_remove_button.setFixedSize(25, 25) # Adjusted size slightly for text
            front_remove_button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
            front_remove_button.setEnabled(False)
            # Connect with kit_id and side
            front_remove_button.clicked.connect(partial(self._handle_kit_remove, kit_id, "front"))
            setattr(self, f"kit_{key}_front_remove_button", front_remove_button)
            # front_preview_frame_layout.addWidget(front_remove_button, 0, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignTop) # Remove from here

            front_preview_label = QLabel()
            front_preview_label.setFixedSize(150, 150)
            front_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            front_preview_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
            setattr(self, f"kit_{key}_front_preview_label", front_preview_label)
            front_preview_frame_layout.addWidget(front_preview_label, 0, Qt.AlignmentFlag.AlignCenter)
            front_preview_frame_layout.addStretch() # Push preview up if needed

            front_layout.addWidget(front_preview_frame, 0, Qt.AlignmentFlag.AlignCenter)

            # --- Button Layout (Upload + Remove) ---
            front_button_layout = QHBoxLayout()
            front_button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            front_button_layout.setSpacing(6) # Add some spacing between framed buttons

            # --- Front Upload Button ---
            front_upload_button = QPushButton(self.tr("Upload"))
            front_upload_button.setObjectName("kitButton") # For main stylesheet targeting
            front_upload_button.clicked.connect(partial(self._handle_kit_upload, kit_id, "front"))
            setattr(self, f"kit_{key}_front_upload_button", front_upload_button)

            front_upload_frame = QFrame()
            front_upload_frame.setObjectName("kitButtonFrame") # For main stylesheet targeting
            frame_upload_layout = QHBoxLayout(front_upload_frame) # Use QHBoxLayout
            frame_upload_layout.setContentsMargins(0,0,0,0)
            frame_upload_layout.addWidget(front_upload_button)

            # Temporary inline styles for demonstration
            # front_upload_frame.setStyleSheet("QFrame#kitButtonFrame { border: 1px solid #adadad; border-radius: 12px; }") # REMOVED
            # front_upload_button.setStyleSheet("QPushButton#kitButton { background: transparent; border: none; padding: 4px 8px; }") # REMOVED

            front_button_layout.addWidget(front_upload_frame) # Add FRAME to layout

            # --- Front Remove Button ---
            front_remove_button = QPushButton(self.tr("Remove")) # CHANGED from "X"
            front_remove_button.setObjectName("kitButton") # For main stylesheet targeting
            # REMOVE front_remove_button.setFixedSize(25, 25)
            front_remove_button.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed) # Let height be fixed, width minimum
            front_remove_button.setEnabled(False)
            front_remove_button.clicked.connect(partial(self._handle_kit_remove, kit_id, "front"))
            setattr(self, f"kit_{key}_front_remove_button", front_remove_button)

            front_remove_frame = QFrame()
            front_remove_frame.setObjectName("kitButtonFrame") # For main stylesheet targeting
            frame_remove_layout = QHBoxLayout(front_remove_frame) # Use QHBoxLayout
            frame_remove_layout.setContentsMargins(0,0,0,0)
            frame_remove_layout.addWidget(front_remove_button)

            # Temporary inline styles for demonstration
            # front_remove_frame.setStyleSheet("QFrame#kitButtonFrame { border: 1px solid #adadad; border-radius: 12px; }") # REMOVED
            # front_remove_button.setStyleSheet("QPushButton#kitButton { background: transparent; border: none; padding: 4px 8px; }") # REMOVED

            front_button_layout.addWidget(front_remove_frame) # Add FRAME to layout

            front_layout.addLayout(front_button_layout) # Add the button layout

            # Constraints Label
            front_constraints_label = QLabel()
            front_constraints_label.setFont(small_font)
            front_constraints_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            setattr(self, f"kit_{key}_front_constraints_label", front_constraints_label)
            front_layout.addWidget(front_constraints_label, 0, Qt.AlignmentFlag.AlignCenter)

            front_layout.addStretch() # Add stretch to push content up

            images_group_box_main_layout.addLayout(front_layout)

            # --- Back Side Section ---
            back_layout = QVBoxLayout()
            back_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignCenter)

            back_label = QLabel() # Text set in retranslateUi
            back_label.setText(self.tr("Back")) # Add initial translation
            back_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            setattr(self, f"kit_{key}_back_title_label", back_label)
            back_layout.addWidget(back_label)

            # Frame for Preview + Remove Button
            back_preview_frame = QFrame()
            back_preview_frame.setFixedSize(160, 160)
            back_preview_frame.setFrameShape(QFrame.Shape.StyledPanel)
            back_preview_frame_layout = QVBoxLayout(back_preview_frame)
            back_preview_frame_layout.setContentsMargins(5,5,5,5)

            # Remove button moved below preview
            back_remove_button = QPushButton("X") # Set text to X
            # back_remove_button.setIcon(close_icon) # Remove icon
            back_remove_button.setFixedSize(25, 25) # Adjusted size slightly for text
            back_remove_button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
            back_remove_button.setEnabled(False)
            # Connect with kit_id and side
            back_remove_button.clicked.connect(partial(self._handle_kit_remove, kit_id, "back"))
            setattr(self, f"kit_{key}_back_remove_button", back_remove_button)
            # back_preview_frame_layout.addWidget(back_remove_button, 0, Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignTop) # Remove from here

            back_preview_label = QLabel()
            back_preview_label.setFixedSize(150, 150)
            back_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            back_preview_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
            setattr(self, f"kit_{key}_back_preview_label", back_preview_label)
            back_preview_frame_layout.addWidget(back_preview_label, 0, Qt.AlignmentFlag.AlignCenter)

            # --- Add Name/Number Preview Labels as children of back_preview_label ---
            # Store references using setattr for later access
            name_preview_label = OutlinedLabel("Player Name", parent=back_preview_label) # Changed to OutlinedLabel
            number_preview_label = OutlinedLabel("64", parent=back_preview_label) # Changed to OutlinedLabel
            setattr(self, f"kit_{key}_back_name_preview_label", name_preview_label)
            setattr(self, f"kit_{key}_back_number_preview_label", number_preview_label)

            # Initial Font Setup (will be overridden by loaded data)
            name_font = QFont("Arial", 10, QFont.Weight.Bold)
            number_font = QFont("Arial", 24, QFont.Weight.Bold)
            name_preview_label.setFont(name_font)
            number_preview_label.setFont(number_font)
            # REMOVED name_preview_label.setStyleSheet("color: black; background-color: transparent;")
            # REMOVED number_preview_label.setStyleSheet("color: black; background-color: transparent;")
            name_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            number_preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            # --- Initial Positioning (will be updated by _update_kit_text_previews) ---
            parent_width = 150
            parent_height = 150

            # Function to center and position (avoids code duplication)
            def position_label(label, base_y_factor, v_offset=0):
                label.adjustSize() # Adjust size first
                size = label.sizeHint()
                x = (parent_width - size.width()) / 2
                base_y = (parent_height * base_y_factor) - (size.height() / 2)
                y = base_y + v_offset # Apply offset later
                label.move(int(x), int(base_y)) # Use base_y for initial placement

            # Initial centered positions (roughly)
            num_base_y_factor = 0.55 # Slightly below center
            name_base_y_factor = 0.30 # Above number

            position_label(number_preview_label, num_base_y_factor)
            position_label(name_preview_label, name_base_y_factor)

            name_preview_label.show()
            number_preview_label.show()
            # --- End Name/Number Preview Labels ---

            back_preview_frame_layout.addStretch()

            back_layout.addWidget(back_preview_frame, 0, Qt.AlignmentFlag.AlignCenter)

            # --- Button Layout (Upload + Remove) ---
            back_button_layout = QHBoxLayout()
            back_button_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            back_button_layout.setSpacing(6) # Add some spacing

            # --- Back Upload Button ---
            back_upload_button = QPushButton(self.tr("Upload"))
            back_upload_button.setObjectName("kitButton")
            back_upload_button.clicked.connect(partial(self._handle_kit_upload, kit_id, "back"))
            setattr(self, f"kit_{key}_back_upload_button", back_upload_button)

            back_upload_frame = QFrame()
            back_upload_frame.setObjectName("kitButtonFrame")
            back_frame_upload_layout = QHBoxLayout(back_upload_frame)
            back_frame_upload_layout.setContentsMargins(0,0,0,0)
            back_frame_upload_layout.addWidget(back_upload_button)

            # back_upload_frame.setStyleSheet("QFrame#kitButtonFrame { border: 1px solid #adadad; border-radius: 12px; }") # REMOVED
            # back_upload_button.setStyleSheet("QPushButton#kitButton { background: transparent; border: none; padding: 4px 8px; }") # REMOVED

            back_button_layout.addWidget(back_upload_frame)

            # --- Back Remove Button ---
            back_remove_button = QPushButton(self.tr("Remove")) # CHANGED from "X"
            back_remove_button.setObjectName("kitButton")
            # REMOVE back_remove_button.setFixedSize(25, 25)
            back_remove_button.setSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
            back_remove_button.setEnabled(False)
            back_remove_button.clicked.connect(partial(self._handle_kit_remove, kit_id, "back"))
            setattr(self, f"kit_{key}_back_remove_button", back_remove_button)

            back_remove_frame = QFrame()
            back_remove_frame.setObjectName("kitButtonFrame")
            back_frame_remove_layout = QHBoxLayout(back_remove_frame)
            back_frame_remove_layout.setContentsMargins(0,0,0,0)
            back_frame_remove_layout.addWidget(back_remove_button)

            # back_remove_frame.setStyleSheet("QFrame#kitButtonFrame { border: 1px solid #adadad; border-radius: 12px; }") # REMOVED
            # back_remove_button.setStyleSheet("QPushButton#kitButton { background: transparent; border: none; padding: 4px 8px; }") # REMOVED

            back_button_layout.addWidget(back_remove_frame)

            back_layout.addLayout(back_button_layout) # Add the button layout

            # Constraints Label
            back_constraints_label = QLabel()
            back_constraints_label.setFont(small_font)
            back_constraints_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            setattr(self, f"kit_{key}_back_constraints_label", back_constraints_label)
            back_layout.addWidget(back_constraints_label, 0, Qt.AlignmentFlag.AlignCenter)

            back_layout.addStretch() # Add stretch to push content up

            images_group_box_main_layout.addLayout(back_layout)

            # Settings Group Box
            settings_group_box = QGroupBox(self.tr("Settings")) # Wrap title
            settings_group_box_layout = QVBoxLayout(settings_group_box)
            settings_group_box_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

            # Remove old placeholder if it exists (might not after revert)
            # Find and remove placeholder widgets if necessary before adding new ones.
            # This part assumes the layout might be empty or have the single placeholder.
            while settings_group_box_layout.count():
                 item = settings_group_box_layout.takeAt(0)
                 widget = item.widget()
                 if widget:
                     widget.deleteLater()
                 layout = item.layout()
                 if layout: # Handle removing stretch item too
                    pass # Stretch items don't need deleteLater

            # --- Name Settings Sub-Group ---
            name_settings_gb = QGroupBox(self.tr("Name")) # Wrap title
            # REMOVE name_settings_gb_layout = QVBoxLayout(name_settings_gb)
            # REMOVE name_settings_placeholder = QLabel(self.tr("Name Settings Placeholder")) # Wrap text
            # REMOVE name_settings_gb_layout.addWidget(name_settings_placeholder)
            # REMOVE name_settings_gb_layout.addStretch()
            name_settings_form_layout = QFormLayout(name_settings_gb) # Set FormLayout directly
            setattr(self, f"kit_{key}_name_settings_group_box", name_settings_gb)
            settings_group_box_layout.addWidget(name_settings_gb)

            # Add Name Setting Widgets
            name_color_button = QPushButton(" ") # Display color itself
            name_color_button.setFixedSize(50, 20)
            # name_color_button.clicked.connect(...) # Connect later
            setattr(self, f"kit_{key}_name_color_button", name_color_button)

            name_font_size_spinbox = QSpinBox()
            name_font_size_spinbox.setRange(6, 12) # Changed max range to 12
            name_font_size_spinbox.setValue(10)
            # name_font_size_spinbox.valueChanged.connect(...) # Connect later
            setattr(self, f"kit_{key}_name_font_size_spinbox", name_font_size_spinbox)

            name_vpos_spinbox = QSpinBox()
            name_vpos_spinbox.setRange(-100, 100) # Example range for vertical offset
            name_vpos_spinbox.setValue(0)
            # name_vpos_spinbox.valueChanged.connect(...) # Connect later
            setattr(self, f"kit_{key}_name_vpos_spinbox", name_vpos_spinbox)

            name_font_combo = QFontComboBox()
            # name_font_combo.currentFontChanged.connect(...) # Connect later
            setattr(self, f"kit_{key}_name_font_combo", name_font_combo)

            name_settings_form_layout.addRow(self.tr("Font Color:"), name_color_button)
            name_settings_form_layout.addRow(self.tr("Font Family:"), name_font_combo) # Add Font Combo
            name_settings_form_layout.addRow(self.tr("Font Size:"), name_font_size_spinbox)
            name_settings_form_layout.addRow(self.tr("Vertical Offset:"), name_vpos_spinbox)

            # Add Outline Settings for Name
            name_outline_checkbox = QCheckBox()
            setattr(self, f"kit_{key}_name_outline_checkbox", name_outline_checkbox)
            name_settings_form_layout.addRow(self.tr("Enable Outline:"), name_outline_checkbox)

            name_outline_color_button = QPushButton(" ")
            name_outline_color_button.setFixedSize(50, 20)
            name_outline_color_button.setVisible(False) # Initially hidden
            setattr(self, f"kit_{key}_name_outline_color_button", name_outline_color_button)
            name_settings_form_layout.addRow(self.tr("Outline Color:"), name_outline_color_button)

            name_outline_thickness_spinbox = QSpinBox()
            name_outline_thickness_spinbox.setRange(1, 10) # Example range for thickness
            name_outline_thickness_spinbox.setValue(1)
            name_outline_thickness_spinbox.setVisible(False) # Initially hidden
            setattr(self, f"kit_{key}_name_outline_thickness_spinbox", name_outline_thickness_spinbox)
            name_settings_form_layout.addRow(self.tr("Outline Thickness:"), name_outline_thickness_spinbox)

            # --- Number Settings Sub-Group ---
            number_settings_gb = QGroupBox(self.tr("Number")) # Wrap title
            # REMOVE number_settings_gb_layout = QVBoxLayout(number_settings_gb)
            # REMOVE number_settings_placeholder = QLabel(self.tr("Number Settings Placeholder")) # Wrap text
            # REMOVE number_settings_gb_layout.addWidget(number_settings_placeholder)
            # REMOVE number_settings_gb_layout.addStretch()
            number_settings_form_layout = QFormLayout(number_settings_gb) # Set FormLayout directly
            setattr(self, f"kit_{key}_number_settings_group_box", number_settings_gb)
            settings_group_box_layout.addWidget(number_settings_gb)

            # Add Number Setting Widgets
            number_color_button = QPushButton(" ")
            number_color_button.setFixedSize(50, 20)
            # number_color_button.clicked.connect(...) # Connect later
            setattr(self, f"kit_{key}_number_color_button", number_color_button)

            number_font_size_spinbox = QSpinBox()
            number_font_size_spinbox.setRange(12, 36) # Changed range to 12-36
            number_font_size_spinbox.setValue(24)
            # number_font_size_spinbox.valueChanged.connect(...) # Connect later
            setattr(self, f"kit_{key}_number_font_size_spinbox", number_font_size_spinbox)

            number_vpos_spinbox = QSpinBox()
            number_vpos_spinbox.setRange(-100, 100)
            number_vpos_spinbox.setValue(0)
            # number_vpos_spinbox.valueChanged.connect(...) # Connect later
            setattr(self, f"kit_{key}_number_vpos_spinbox", number_vpos_spinbox)

            number_font_combo = QFontComboBox()
            # number_font_combo.currentFontChanged.connect(...) # Connect later
            setattr(self, f"kit_{key}_number_font_combo", number_font_combo)

            number_settings_form_layout.addRow(self.tr("Font Color:"), number_color_button)
            number_settings_form_layout.addRow(self.tr("Font Family:"), number_font_combo) # Add Font Combo
            number_settings_form_layout.addRow(self.tr("Font Size:"), number_font_size_spinbox)
            number_settings_form_layout.addRow(self.tr("Vertical Offset:"), number_vpos_spinbox)

            # Add Outline Settings for Number
            number_outline_checkbox = QCheckBox()
            setattr(self, f"kit_{key}_number_outline_checkbox", number_outline_checkbox)
            number_settings_form_layout.addRow(self.tr("Enable Outline:"), number_outline_checkbox)

            number_outline_color_button = QPushButton(" ")
            number_outline_color_button.setFixedSize(50, 20)
            number_outline_color_button.setVisible(False) # Initially hidden
            setattr(self, f"kit_{key}_number_outline_color_button", number_outline_color_button)
            number_settings_form_layout.addRow(self.tr("Outline Color:"), number_outline_color_button)

            number_outline_thickness_spinbox = QSpinBox()
            number_outline_thickness_spinbox.setRange(1, 10)
            number_outline_thickness_spinbox.setValue(1)
            number_outline_thickness_spinbox.setVisible(False) # Initially hidden
            setattr(self, f"kit_{key}_number_outline_thickness_spinbox", number_outline_thickness_spinbox)
            number_settings_form_layout.addRow(self.tr("Outline Thickness:"), number_outline_thickness_spinbox)

            settings_group_box_layout.addStretch()
            setattr(self, f"kit_{key}_settings_group_box", settings_group_box)
            tab_layout.addWidget(settings_group_box)
            tab_layout.addStretch()

        # --- End Kit Tab Setup --- #

        # --- Add Main Tabs ---
        # ... (remains the same) ...



        # --- Add Tabs to Widget ---
        self.tab_widget.addTab(self.tab_information, "")
        self.tab_widget.addTab(self.tab_contact, "")
        self.tab_widget.addTab(self.tab_staff, "")
        self.tab_widget.addTab(self.tab_kit, "")
        # self.tab_widget.addTab(self.tab_sponsors, "") # REMOVED old placeholder tab
        self.tab_widget.addTab(self.sponsors_panel, "") # ADDED new sponsors panel tab

        # --- ADD Team Groups Tab ---
        # Ensure self.manager is initialized before this point
        if hasattr(self, 'manager'):
            # Pass BOTH managers (RosterManager and ClubDataManager)
            self.groups_widget = TeamGroupsWidget(manager=self.manager, club_data_manager=self.club_data_manager)
            self.groups_widget.setObjectName("TeamGroupsWidget")  # Set object name for findChild
            self.tab_widget.addTab(self.groups_widget, "") # Add the TeamGroupsWidget as a new tab
            print("DEBUG: Added Team Groups Tab") # Keep the debug message
        else:
            print("ERROR: RosterManager (self.manager) not found when trying to create TeamGroupsWidget!")
        # --- END Team Groups Tab ---

        # --- Connect Tab Change Signal ---
        self.tab_widget.currentChanged.connect(self._on_main_tab_changed)

        # Connections and Initial Load
        self.retranslateUi()
        self._load_club_data() # Load data from manager BEFORE connecting signals
        self._connect_save_signals() # Connect save signals AFTER loading data

        # -- Connect staff sub-tab changes to handler ---
        self.staff_sub_tab_widget.currentChanged.connect(self._on_staff_sub_tab_changed)

        # Store the form layout for later access
        self.general_form_layout = general_form_layout
        self.stadium_form_layout = stadium_form_layout  # Do the same for stadium form

    def _on_main_tab_changed(self, index):
        """Slot called when the main tab is changed."""
        print(f"ClubWindow main tab changed to index: {index}. Re-applying stylesheet to window.")
        app = QApplication.instance()
        if app:
            stylesheet = app.styleSheet()
            self.setStyleSheet(stylesheet)
        else:
            print("Warning: QApplication instance not found in _on_main_tab_changed.")

        # If the Staff tab is selected, make sure all interface elements are properly translated
        current_widget = self.tab_widget.widget(index)

        # Save sponsors data if we're switching from the sponsors tab
        previous_tab_index = getattr(self, '_previous_tab_index', None)
        if previous_tab_index is not None:
            previous_widget = self.tab_widget.widget(previous_tab_index)
            if hasattr(self, 'sponsors_panel') and previous_widget == self.sponsors_panel:
                print("Switching away from sponsors tab - saving data...")
                self.sponsors_panel.save_data()

        # Store current tab index for next time
        self._previous_tab_index = index

        # Refresh logic for Team Groups Tab
        if hasattr(self, 'groups_widget') and current_widget == self.groups_widget:
            if hasattr(self.groups_widget, 'refresh_staff_data'):
                print("Refreshing TeamGroupsWidget staff data...")
                self.groups_widget.refresh_staff_data()
            # Also retranslate the widget when switching to this tab
            if hasattr(self, 'groups_widget') and hasattr(self.groups_widget, 'retranslateUi'):
                print("Retranslating TeamGroupsWidget when tab is selected...")
                self.groups_widget.retranslateUi()

        # Staff tab translation updates
        if hasattr(self, 'tab_staff') and current_widget == self.tab_staff:
            print("Staff tab selected - Updating translations for all staff tables")

            # Force an update of the current staff sub-tab's content translations
            if hasattr(self, 'staff_sub_tab_widget'):
                self._on_staff_sub_tab_changed(self.staff_sub_tab_widget.currentIndex())

                # --- REMOVED: Sub-tab titles are now updated in retranslateUi ---
                # self.staff_sub_tab_widget.setTabText(0, self.tr("Management"))
                # self.staff_sub_tab_widget.setTabText(1, self.tr("Coaching"))
                # self.staff_sub_tab_widget.setTabText(2, self.tr("Medical"))

        # Handle changing between main tabs
        # Identify sponsors tab by name or index
        sponsors_tab_index = 4  # Based on the tab order in retranslateUi

        if index == sponsors_tab_index:
            print("DEBUG: Sponsors tab selected, ensuring image visibility and updating display")
            # Force update of sponsor logo visibility and display when tab is selected
            if hasattr(self, 'sponsors_panel') and hasattr(self.sponsors_panel, 'category_widgets'):
                for category, widgets in self.sponsors_panel.category_widgets.items():
                    for widget in widgets:
                        if hasattr(widget, 'logo_preview_label'):
                            widget.logo_preview_label.setVisible(True)
                            print(f"Tab change: Force visibility for {category} sponsor logo")
                        # Also force update of the logo display to ensure images are shown
                        if hasattr(widget, '_update_logo_display'):
                            widget._update_logo_display()
                            print(f"Tab change: Force update logo display for {category} sponsor")

        # Process tab-specific updates if needed
        QApplication.processEvents()

    def _apply_stylesheet_to_kit_buttons(self, stylesheet):
        """Explicitly applies the stylesheet to all kit Upload/Remove buttons."""
        print(f"Applying stylesheet directly to kit buttons...")
        kit_map_keys = ["1st", "2nd", "3rd", "1st_gk", "2nd_gk"]
        button_suffixes = [
            "front_upload_button", "front_remove_button",
            "back_upload_button", "back_remove_button"
        ]

        buttons_found = 0
        for key in kit_map_keys:
            for suffix in button_suffixes:
                attr_name = f"kit_{key}_{suffix}"
                try:
                    button = getattr(self, attr_name)
                    if button and isinstance(button, QPushButton):
                        # print(f"  Applying style to: {attr_name}") # Verbose debug
                        button.setStyleSheet(stylesheet)
                        buttons_found += 1
                except AttributeError:
                    print(f"  Warning: Could not find attribute {attr_name} in _apply_stylesheet_to_kit_buttons")
                except Exception as e:
                    print(f"  Error applying style to {attr_name}: {e}")
        print(f"Applied stylesheet to {buttons_found} kit buttons.")

    def _load_club_data(self):
        """Load data from the manager and populate the UI fields."""
        print("Loading club data into UI...")
        # General Info
        self.name_input.setText(self.club_data_manager.get_data("club_name", ""))
        self.short_name_input.setText(self.club_data_manager.get_data("short_name", ""))
        self.nickname_input.setText(self.club_data_manager.get_data("nickname", ""))
        self.year_founded_input.setValue(self.club_data_manager.get_data("year_founded", datetime.date.today().year))
        self.city_input.setText(self.club_data_manager.get_data("city", ""))

        country_text = self.club_data_manager.get_data("country")
        print(f"DEBUG: Loading country value: '{country_text}', type: {type(country_text)}")

        # FindText needs exact match, case-sensitive. Make sure saved data matches list.
        country_index = 0  # Default to first item (placeholder)
        if country_text:  # Check if not None and not empty string
            # Find index in combo box
            country_index = self.country_combo.findText(country_text, Qt.MatchFlag.MatchFixedString)
            if country_index == -1:  # If not found, try case-insensitive search
                for i in range(self.country_combo.count()):
                    if self.country_combo.itemText(i).lower() == country_text.lower():
                        country_index = i
                        break

        print(f"DEBUG: Found country index: {country_index}")
        self.country_combo.setCurrentIndex(max(0, country_index)) # Ensure index is valid
        print(f"DEBUG: Set country combo index to: {max(0, country_index)}")

        # Verify the value was loaded correctly
        current_index = self.country_combo.currentIndex()
        current_text = self.country_combo.itemText(current_index) if current_index > 0 else None
        print(f"DEBUG: Verified loaded country value: '{current_text}' (index {current_index})")

        self.region_input.setText(self.club_data_manager.get_data("region", ""))

        # Logo
        logo_exists = self.club_data_manager.get_data("logo_exists", False)
        base_dir = os.getcwd()
        potential_logo_path = os.path.join(base_dir, "media", "club_logo", "Club_logo.png")
        self.current_logo_path = potential_logo_path if logo_exists and os.path.exists(potential_logo_path) else None
        self._update_logo_preview() # Update UI based on loaded state

        # Stadium Info
        self.stadium_name_input.setText(self.club_data_manager.get_data("stadium_name", ""))
        self.capacity_input.setValue(self.club_data_manager.get_data("capacity", 0))
        self.seating_capacity_input.setValue(self.club_data_manager.get_data("seating_capacity", 0))

        surface_text = self.club_data_manager.get_data("surface_type")
        print(f"DEBUG: Loading surface_type value: '{surface_text}', type: {type(surface_text)}")

        # We find the *source* text in the combo box items (which were translated)
        surface_index = 0
        if surface_text:  # Check if not None and not empty string
            for i in range(1, self.surface_combo.count()): # Start from 1 to skip placeholder
                if self._surface_types_source[i-1] == surface_text:
                    surface_index = i
                    break
                # Try case-insensitive match as fallback
                elif self._surface_types_source[i-1].lower() == surface_text.lower():
                    surface_index = i
                    break

        print(f"DEBUG: Found surface index: {surface_index}")
        self.surface_combo.setCurrentIndex(surface_index)
        print(f"DEBUG: Set surface combo index to: {surface_index}")

        # Verify the value was loaded correctly
        current_index = self.surface_combo.currentIndex()
        current_source = self._surface_types_source[current_index - 1] if current_index > 0 else None
        print(f"DEBUG: Verified loaded surface_type value: '{current_source}' (index {current_index})")

        self.year_built_input.setValue(self.club_data_manager.get_data("year_built", datetime.date.today().year))

        owner_text = self.club_data_manager.get_data("stadium_owner")
        print(f"DEBUG: Loading stadium_owner value: '{owner_text}', type: {type(owner_text)}")

        owner_index = 0
        if owner_text:  # Check if not None and not empty string
             for i in range(1, self.stadium_owner_combo.count()):
                 if self._stadium_owners_source[i-1] == owner_text:
                     owner_index = i
                     break
                 # Try case-insensitive match as fallback
                 elif self._stadium_owners_source[i-1].lower() == owner_text.lower():
                     owner_index = i
                     break

        print(f"DEBUG: Found owner index: {owner_index}")
        self.stadium_owner_combo.setCurrentIndex(owner_index)
        print(f"DEBUG: Set owner combo index to: {owner_index}")

        # Verify the value was loaded correctly
        current_index = self.stadium_owner_combo.currentIndex()
        current_source = self._stadium_owners_source[current_index - 1] if current_index > 0 else None
        print(f"DEBUG: Verified loaded stadium_owner value: '{current_source}' (index {current_index})")

        # Stadium Image
        stadium_image_exists = self.club_data_manager.get_data("stadium_image_exists", False)
        potential_stadium_path = os.path.join(base_dir, "media", "stadium", "stadium.png")
        self.current_stadium_image_path = potential_stadium_path if stadium_image_exists and os.path.exists(potential_stadium_path) else None
        self._update_stadium_preview() # Update UI

        # Load and update kit colors
        print("Loading kit colors...") # Debug print
        self._update_color_preview("color_button_1st", "color_1st")
        self._update_color_preview("color_button_2nd", "color_2nd")
        self._update_color_preview("color_button_3rd", "color_3rd")
        self._update_kit_color_preview() # Update combined preview on load

        # Load initial kit image previews
        print("Loading kit images...")
        kit_map = {
            "1": "1st", "2": "2nd", "3": "3rd",
            "4": "1st_gk", "5": "2nd_gk"
        }
        for kit_id, kit_key in kit_map.items():
            self._update_kit_preview(kit_id, "front")
            self._update_kit_preview(kit_id, "back")

            # Load kit settings and update controls/previews
            # Name settings
            name_color = self.club_data_manager.get_data(f"kit_{kit_id}_name_color", "#000000")
            name_family = self.club_data_manager.get_data(f"kit_{kit_id}_name_font_family", "Arial")
            name_size = self.club_data_manager.get_data(f"kit_{kit_id}_name_font_size", 10)
            name_vpos = self.club_data_manager.get_data(f"kit_{kit_id}_name_vpos", 0)

            name_color_button = self._get_kit_setting_attribute(kit_key, "name", "color_button")
            name_font_combo = self._get_kit_setting_attribute(kit_key, "name", "font_combo")
            name_size_spin = self._get_kit_setting_attribute(kit_key, "name", "font_size_spinbox")
            name_vpos_spin = self._get_kit_setting_attribute(kit_key, "name", "vpos_spinbox")

            if name_color_button: name_color_button.setStyleSheet(f"background-color: {name_color}; border: 1px solid black;")
            if name_font_combo: name_font_combo.setCurrentFont(QFont(name_family))
            if name_size_spin: name_size_spin.setValue(name_size)
            if name_vpos_spin: name_vpos_spin.setValue(name_vpos)

            # Load Name Outline Settings
            name_outline_enabled = self.club_data_manager.get_data(f"kit_{kit_id}_name_outline_enabled", False)
            name_outline_color = self.club_data_manager.get_data(f"kit_{kit_id}_name_outline_color", "#FFFFFF")
            name_outline_thickness = self.club_data_manager.get_data(f"kit_{kit_id}_name_outline_thickness", 1)

            name_outline_check = self._get_kit_setting_attribute(kit_key, "name", "outline_checkbox")
            name_outline_color_btn = self._get_kit_setting_attribute(kit_key, "name", "outline_color_button")
            name_outline_thick_spin = self._get_kit_setting_attribute(kit_key, "name", "outline_thickness_spinbox")

            if name_outline_check: name_outline_check.setChecked(name_outline_enabled)
            if name_outline_color_btn:
                name_outline_color_btn.setStyleSheet(f"background-color: {name_outline_color}; border: 1px solid black;")
                name_outline_color_btn.setVisible(name_outline_enabled) # Set visibility
            if name_outline_thick_spin:
                name_outline_thick_spin.setValue(name_outline_thickness)
                name_outline_thick_spin.setVisible(name_outline_enabled) # Set visibility

            # Set initial widget visibility for Name outline settings (widgets only)
            if name_outline_color_btn: name_outline_color_btn.setVisible(name_outline_enabled)
            if name_outline_thick_spin: name_outline_thick_spin.setVisible(name_outline_enabled)
            # Set initial row visibility for Name outline settings (entire row including label)
            name_settings_gb = self._get_kit_setting_attribute(kit_key, "name", "settings_group_box")
            if name_settings_gb and isinstance(name_settings_gb.layout(), QFormLayout):
                name_form_layout = name_settings_gb.layout()
                try:
                    # Indices: 5=OutlineColor, 6=OutlineThickness
                    name_form_layout.setRowVisible(5, name_outline_enabled)
                    name_form_layout.setRowVisible(6, name_outline_enabled)
                except Exception as e:
                    print(f"Error setting initial row visibility for {kit_key}/name: {e}")

            # Number settings
            number_color = self.club_data_manager.get_data(f"kit_{kit_id}_number_color", "#000000")
            number_family = self.club_data_manager.get_data(f"kit_{kit_id}_number_font_family", "Arial")
            number_size = self.club_data_manager.get_data(f"kit_{kit_id}_number_font_size", 24)
            number_vpos = self.club_data_manager.get_data(f"kit_{kit_id}_number_vpos", 0)

            number_color_button = self._get_kit_setting_attribute(kit_key, "number", "color_button")
            number_font_combo = self._get_kit_setting_attribute(kit_key, "number", "font_combo")
            number_size_spin = self._get_kit_setting_attribute(kit_key, "number", "font_size_spinbox")
            number_vpos_spin = self._get_kit_setting_attribute(kit_key, "number", "vpos_spinbox")

            if number_color_button: number_color_button.setStyleSheet(f"background-color: {number_color}; border: 1px solid black;")
            if number_font_combo: number_font_combo.setCurrentFont(QFont(number_family))
            if number_size_spin: number_size_spin.setValue(number_size)
            if number_vpos_spin: number_vpos_spin.setValue(number_vpos)

            # Load Number Outline Settings
            number_outline_enabled = self.club_data_manager.get_data(f"kit_{kit_id}_number_outline_enabled", False)
            number_outline_color = self.club_data_manager.get_data(f"kit_{kit_id}_number_outline_color", "#FFFFFF")
            number_outline_thickness = self.club_data_manager.get_data(f"kit_{kit_id}_number_outline_thickness", 1)

            number_outline_check = self._get_kit_setting_attribute(kit_key, "number", "outline_checkbox")
            number_outline_color_btn = self._get_kit_setting_attribute(kit_key, "number", "outline_color_button")
            number_outline_thick_spin = self._get_kit_setting_attribute(kit_key, "number", "outline_thickness_spinbox")

            if number_outline_check: number_outline_check.setChecked(number_outline_enabled)
            if number_outline_color_btn:
                number_outline_color_btn.setStyleSheet(f"background-color: {number_outline_color}; border: 1px solid black;")
                number_outline_color_btn.setVisible(number_outline_enabled) # Set visibility
            if number_outline_thick_spin:
                number_outline_thick_spin.setValue(number_outline_thickness)
                number_outline_thick_spin.setVisible(number_outline_enabled) # Set visibility

            # Set initial widget visibility for Number outline settings (widgets only)
            if number_outline_color_btn: number_outline_color_btn.setVisible(number_outline_enabled)
            if number_outline_thick_spin: number_outline_thick_spin.setVisible(number_outline_enabled)
            # Set initial row visibility for Number outline settings (entire row including label)
            number_settings_gb = self._get_kit_setting_attribute(kit_key, "number", "settings_group_box")
            if number_settings_gb and isinstance(number_settings_gb.layout(), QFormLayout):
                number_form_layout = number_settings_gb.layout()
                try:
                    # Indices: 5=OutlineColor, 6=OutlineThickness
                    number_form_layout.setRowVisible(5, number_outline_enabled)
                    number_form_layout.setRowVisible(6, number_outline_enabled)
                except Exception as e:
                    print(f"Error setting initial row visibility for {kit_key}/number: {e}")

            # Update the text preview rendering (now happens after loading all settings)
            self._update_kit_text_previews(kit_key)

        # Load Contact Info
        print("Loading contact info...")
        self.contact_address1_input.setText(self.club_data_manager.get_data("contact_address1", ""))
        self.contact_address2_input.setText(self.club_data_manager.get_data("contact_address2", ""))
        self.contact_city_input.setText(self.club_data_manager.get_data("contact_city", ""))
        self.contact_state_input.setText(self.club_data_manager.get_data("contact_state", ""))
        self.contact_postal_input.setText(self.club_data_manager.get_data("contact_postal", ""))
        self.contact_phone_input.setText(self.club_data_manager.get_data("contact_phone", ""))
        self.contact_email_input.setText(self.club_data_manager.get_data("contact_email", ""))
        self.contact_website_input.setText(self.club_data_manager.get_data("contact_website", ""))

        # Load Medical Staff Data
        print("Loading medical staff data...")
        medical_staff_data = self.club_data_manager.get_data("medical_staff", [])
        self.medical_staff_model.removeRows(0, self.medical_staff_model.rowCount()) # Clear existing rows
        for row_idx, staff_member in enumerate(medical_staff_data):
            items = []
            for col_idx, header in enumerate(self.medical_staff_headers): # Use the header list for keys
                # Ensure data consistency, provide default empty string if key missing
                item_text = staff_member.get(header, "")
                item = QStandardItem(str(item_text)) # Ensure data is string for standard item

                # Special handling for Type column - store the source value in UserRole
                if header == "Type" and item_text:
                    # Store the original English value in UserRole
                    type_value = str(item_text)
                    # Check if this is a valid source type
                    if type_value in self._medical_staff_types_source:
                        print(f"Medical staff: Found source type '{type_value}' for row {row_idx}")
                        # We'll set the UserRole after inserting the row
                    else:
                        print(f"Medical staff: Type '{type_value}' not found in source types for row {row_idx}")

                items.append(item)

            self.medical_staff_model.insertRow(row_idx, items)

            # After inserting the row, set the UserRole for the Type column
            type_index = self.medical_staff_model.index(row_idx, 1) # Column 1 is Type
            type_value = items[1].text()
            if type_value and type_value in self._medical_staff_types_source:
                self.medical_staff_model.setData(type_index, type_value, Qt.ItemDataRole.UserRole)
                print(f"Medical staff: Set UserRole to '{type_value}' for row {row_idx}")

            # Open persistent editor for the Type column
            self.medical_staff_table.openPersistentEditor(type_index)

        # Load Coaching Staff Data
        print("Loading coaching staff data...")
        coaching_staff_data = self.club_data_manager.get_data("coaching_staff", [])
        self.coaching_staff_model.removeRows(0, self.coaching_staff_model.rowCount()) # Clear existing rows
        for row_idx, staff_member in enumerate(coaching_staff_data):
            items = []
            for col_idx, header in enumerate(self.coaching_staff_headers): # Use the header list for keys
                item_text = staff_member.get(header, "")
                item = QStandardItem(str(item_text)) # Ensure data is string

                # Special handling for Type column - store the source value in UserRole
                if header == "Type" and item_text:
                    # Store the original English value in UserRole
                    type_value = str(item_text)
                    # Check if this is a valid source type
                    if type_value in self._coaching_staff_types_source:
                        print(f"Coaching staff: Found source type '{type_value}' for row {row_idx}")
                        # We'll set the UserRole after inserting the row
                    else:
                        print(f"Coaching staff: Type '{type_value}' not found in source types for row {row_idx}")

                items.append(item)

            self.coaching_staff_model.insertRow(row_idx, items)

            # After inserting the row, set the UserRole for the Type column
            type_index = self.coaching_staff_model.index(row_idx, 1) # Column 1 is Type
            type_value = items[1].text()
            if type_value and type_value in self._coaching_staff_types_source:
                self.coaching_staff_model.setData(type_index, type_value, Qt.ItemDataRole.UserRole)
                print(f"Coaching staff: Set UserRole to '{type_value}' for row {row_idx}")

            # Open persistent editor for the Type column
            if type_index.isValid(): # Check if index is valid before opening editor
                 self.coaching_staff_table.openPersistentEditor(type_index)

        # Load Management Staff Data
        print("Loading management staff data...")
        management_staff_data = self.club_data_manager.get_data("management_staff", [])
        self.management_staff_model.removeRows(0, self.management_staff_model.rowCount())
        for row_idx, staff_member in enumerate(management_staff_data):
            items = []
            for col_idx, header in enumerate(self.management_staff_headers):
                item_text = staff_member.get(header, "")
                item = QStandardItem(str(item_text))

                # Special handling for Type column - store the source value in UserRole
                if header == "Type" and item_text:
                    # Store the original English value in UserRole
                    type_value = str(item_text)
                    # Check if this is a valid source type
                    if type_value in self._management_staff_types_source:
                        print(f"Management staff: Found source type '{type_value}' for row {row_idx}")
                        # We'll set the UserRole after inserting the row
                    else:
                        print(f"Management staff: Type '{type_value}' not found in source types for row {row_idx}")

                items.append(item)

            self.management_staff_model.insertRow(row_idx, items)

            # After inserting the row, set the UserRole for the Type column
            type_index = self.management_staff_model.index(row_idx, 1) # Col 1 = Type
            type_value = items[1].text()
            if type_value and type_value in self._management_staff_types_source:
                self.management_staff_model.setData(type_index, type_value, Qt.ItemDataRole.UserRole)
                print(f"Management staff: Set UserRole to '{type_value}' for row {row_idx}")

            # Open persistent editors for Type and Country
            country_index = self.management_staff_model.index(row_idx, 7) # Col 7 = Country
            if type_index.isValid(): self.management_staff_table.openPersistentEditor(type_index)
            if country_index.isValid(): self.management_staff_table.openPersistentEditor(country_index)

        # Load Sponsors Data - ADDED
        print("Loading sponsors panel data...")
        if hasattr(self, 'sponsors_panel'):
            self.sponsors_panel.load_data()
        else:
            print("Warning: sponsors_panel not found during _load_club_data.")

        print("Club data loaded.")

    def _connect_save_signals(self):
        """Connect UI widget signals to their respective save methods."""
        # General Info
        self.name_input.editingFinished.connect(self._save_club_name)
        self.short_name_input.editingFinished.connect(self._save_short_name)
        self.nickname_input.editingFinished.connect(self._save_nickname)
        self.year_founded_input.valueChanged.connect(self._save_year_founded)
        self.city_input.editingFinished.connect(self._save_city)
        self.country_combo.currentIndexChanged.connect(self._save_country)
        self.region_input.editingFinished.connect(self._save_region)
        # Logo handled separately

        # Stadium Info
        self.stadium_name_input.editingFinished.connect(self._save_stadium_name)
        self.capacity_input.valueChanged.connect(self._save_capacity)
        self.seating_capacity_input.valueChanged.connect(self._save_seating_capacity)
        self.surface_combo.currentIndexChanged.connect(self._save_surface_type)
        self.year_built_input.valueChanged.connect(self._save_year_built)
        self.stadium_owner_combo.currentIndexChanged.connect(self._save_stadium_owner)
        # Stadium Image handled separately

        # Colors handled separately later

        # TODO: Connect signals for other tabs when fields are added

        # Contact Info
        self.contact_address1_input.editingFinished.connect(self._save_contact_address1)
        self.contact_address2_input.editingFinished.connect(self._save_contact_address2)
        self.contact_city_input.editingFinished.connect(self._save_contact_city)
        self.contact_state_input.editingFinished.connect(self._save_contact_state)
        self.contact_postal_input.editingFinished.connect(self._save_contact_postal)
        self.contact_phone_input.editingFinished.connect(self._save_contact_phone)
        self.contact_email_input.editingFinished.connect(self._save_contact_email)
        self.contact_website_input.editingFinished.connect(self._save_contact_website)

        # Connect Kit Settings Signals
        kit_map_keys = ["1st", "2nd", "3rd", "1st_gk", "2nd_gk"]
        for kit_key in kit_map_keys:
            # Name Settings
            name_color_btn = self._get_kit_setting_attribute(kit_key, "name", "color_button")
            name_font_combo = self._get_kit_setting_attribute(kit_key, "name", "font_combo")
            name_size_spin = self._get_kit_setting_attribute(kit_key, "name", "font_size_spinbox")
            name_vpos_spin = self._get_kit_setting_attribute(kit_key, "name", "vpos_spinbox")

            if name_color_btn: name_color_btn.clicked.connect(partial(self._handle_kit_color_pick, kit_key, "name"))
            if name_font_combo: name_font_combo.currentFontChanged.connect(partial(self._handle_kit_font_change, kit_key=kit_key, element="name"))
            if name_size_spin: name_size_spin.valueChanged.connect(partial(self._handle_kit_spinbox_change, kit_key=kit_key, element="name", setting_type="font_size"))
            if name_vpos_spin: name_vpos_spin.valueChanged.connect(partial(self._handle_kit_spinbox_change, kit_key=kit_key, element="name", setting_type="vpos"))
            # Connect Name Outline Signals
            name_outline_check = self._get_kit_setting_attribute(kit_key, "name", "outline_checkbox")
            name_outline_color_btn = self._get_kit_setting_attribute(kit_key, "name", "outline_color_button")
            name_outline_thick_spin = self._get_kit_setting_attribute(kit_key, "name", "outline_thickness_spinbox")
            if name_outline_check: name_outline_check.stateChanged.connect(partial(self._handle_kit_outline_toggled, kit_key=kit_key, element="name"))
            if name_outline_color_btn: name_outline_color_btn.clicked.connect(partial(self._handle_kit_outline_color_pick, kit_key=kit_key, element="name"))
            if name_outline_thick_spin: name_outline_thick_spin.valueChanged.connect(partial(self._handle_kit_outline_thickness_change, kit_key=kit_key, element="name"))

            # Number Settings
            number_color_btn = self._get_kit_setting_attribute(kit_key, "number", "color_button")
            number_font_combo = self._get_kit_setting_attribute(kit_key, "number", "font_combo")
            number_size_spin = self._get_kit_setting_attribute(kit_key, "number", "font_size_spinbox")
            number_vpos_spin = self._get_kit_setting_attribute(kit_key, "number", "vpos_spinbox")

            if number_color_btn: number_color_btn.clicked.connect(partial(self._handle_kit_color_pick, kit_key, "number"))
            if number_font_combo: number_font_combo.currentFontChanged.connect(partial(self._handle_kit_font_change, kit_key=kit_key, element="number"))
            if number_size_spin: number_size_spin.valueChanged.connect(partial(self._handle_kit_spinbox_change, kit_key=kit_key, element="number", setting_type="font_size"))
            if number_vpos_spin: number_vpos_spin.valueChanged.connect(partial(self._handle_kit_spinbox_change, kit_key=kit_key, element="number", setting_type="vpos"))
            # Connect Number Outline Signals
            number_outline_check = self._get_kit_setting_attribute(kit_key, "number", "outline_checkbox")
            number_outline_color_btn = self._get_kit_setting_attribute(kit_key, "number", "outline_color_button")
            number_outline_thick_spin = self._get_kit_setting_attribute(kit_key, "number", "outline_thickness_spinbox")
            if number_outline_check: number_outline_check.stateChanged.connect(partial(self._handle_kit_outline_toggled, kit_key=kit_key, element="number"))
            if number_outline_color_btn: number_outline_color_btn.clicked.connect(partial(self._handle_kit_outline_color_pick, kit_key=kit_key, element="number"))
            if number_outline_thick_spin: number_outline_thick_spin.valueChanged.connect(partial(self._handle_kit_outline_thickness_change, kit_key=kit_key, element="number"))

        # Connect Medical Staff Buttons
        self.add_staff_button.clicked.connect(self._add_medical_staff)
        self.remove_staff_button.clicked.connect(self._remove_medical_staff)
        # Connect search signals
        self.search_staff_input.textChanged.connect(self._search_medical_staff)
        self.clear_search_button.clicked.connect(self._clear_medical_search)
        # Connect filter signals
        self.filter_staff_button.clicked.connect(self._filter_medical_staff)
        self.clear_filter_button.clicked.connect(self._clear_medical_staff_filter)

        # Connect Coaching Staff Buttons
        self.add_coaching_staff_button.clicked.connect(self._add_coaching_staff)
        self.remove_coaching_staff_button.clicked.connect(self._remove_coaching_staff)
        # Connect search signals
        self.search_coaching_staff_input.textChanged.connect(self._search_coaching_staff)
        self.clear_search_coaching_button.clicked.connect(self._clear_coaching_search)
        # --- REMOVED connection attempt here, moved to init_ui ---
        # self.filter_coaching_staff_button.clicked.connect(self._filter_coaching_staff)
        # ------------------------------------------------------
        # ADD BACK connection for clear filter button
        self.clear_filter_coaching_button.clicked.connect(self._clear_coaching_staff_filter)

        # Connect Management Staff Buttons
        self.add_management_button.clicked.connect(self._add_management_staff)
        self.remove_management_button.clicked.connect(self._remove_management_staff)
        self.search_management_input.textChanged.connect(self._search_management_staff)
        self.clear_search_management_button.clicked.connect(self._clear_management_search)
        self.filter_management_button.clicked.connect(self._filter_management_staff)
        self.clear_filter_management_button.clicked.connect(self._clear_management_staff_filter)

        # --- ADD Connections for Staff Table Model itemChanged --- #
        if hasattr(self, 'medical_staff_model'):
            self.medical_staff_model.itemChanged.connect(self._handle_medical_staff_item_changed)
        if hasattr(self, 'coaching_staff_model'):
            self.coaching_staff_model.itemChanged.connect(self._handle_coaching_staff_item_changed)
        if hasattr(self, 'management_staff_model'):
            self.management_staff_model.itemChanged.connect(self._handle_management_staff_item_changed)
        # ------------------------------------------------------- #

        # Load saved column states for all tables
        self._load_table_states()

        # Connect header state change signals to save column states
        self._connect_header_state_signals()

    def _add_medical_staff(self):
        """Adds a new empty row to the medical staff table."""
        # Generate a simple unique ID (e.g., M001, M002...)
        # This is basic, a better system might be needed for robustness
        current_max_id = 0
        for row in range(self.medical_staff_model.rowCount()):
            id_item = self.medical_staff_model.item(row, 0) # ID is column 0
            if id_item:
                try:
                    num_part = int(id_item.text().lstrip('M'))
                    current_max_id = max(current_max_id, num_part)
                except ValueError:
                    pass # Ignore non-standard IDs
        new_id = f"M{current_max_id + 1:03d}" # Format as M001, M002 etc.

        row_items = [QStandardItem(new_id)] + [QStandardItem("") for _ in range(self.medical_staff_model.columnCount() - 1)]
        new_row_index = self.medical_staff_model.rowCount()
        self.medical_staff_model.appendRow(row_items)
        # Open persistent editor for Type column in the new row
        type_index = self.medical_staff_model.index(new_row_index, 1) # Column 1 is Type
        self.medical_staff_table.openPersistentEditor(type_index)
        # Scroll to the new row (optional)
        self.medical_staff_table.scrollToBottom()
        # --- ADD Immediate Save --- #
        self._save_staff_list_to_manager('medical')
        # -------------------------- #

    def _remove_medical_staff(self):
        """Removes the selected row from the medical staff table."""
        selection_model = self.medical_staff_table.selectionModel()
        if not selection_model.hasSelection():
            return # No cell selected

        # Get selected indexes (could be individual cells)
        selected_indexes = selection_model.selectedIndexes()
        if not selected_indexes:
            return

        # Get unique rows from selected cells
        selected_rows = list(set(index.row() for index in selected_indexes))
        if not selected_rows:
            return

        # Check if any of the selected staff members are used in team groups
        for row in selected_rows:
            staff_id_item = self.medical_staff_model.item(row, 0)  # ID is in column 0
            staff_name_item = self.medical_staff_model.item(row, 2)  # Name is in column 2

            if staff_id_item and staff_name_item:
                staff_id = staff_id_item.data(Qt.ItemDataRole.DisplayRole)
                staff_name = staff_name_item.data(Qt.ItemDataRole.DisplayRole)

                # Convert staff_id to int if it's a string
                try:
                    staff_id = int(staff_id) if staff_id else None
                except (ValueError, TypeError):
                    staff_id = None

                if staff_id:
                    # Check if this staff member is used in any team group
                    team_groups_widget = self.findChild(QWidget, "TeamGroupsWidget")
                    if team_groups_widget and hasattr(team_groups_widget, "is_staff_id_used_in_groups"):
                        is_used, group_names = team_groups_widget.is_staff_id_used_in_groups(staff_id, 'medical')
                        if is_used:
                            # Format the group names for display
                            groups_text = ", ".join(group_names)
                            QMessageBox.warning(
                                self,
                                self.tr("Cannot Remove Staff Member"),
                                self.tr("Cannot remove staff member '{name}' because they are assigned to the following team group(s): {groups}. "
                                        "Please remove the staff member from these groups first.")
                                .format(name=staff_name, groups=groups_text)
                            )
                            return  # Don't proceed with deletion

        # Remove rows in reverse order to avoid index issues if multiple rows are selected
        for row in sorted(selected_rows, reverse=True):
            self.medical_staff_model.removeRow(row)
        # --- ADD Immediate Save --- #
        self._save_staff_list_to_manager('medical')
        # -------------------------- #

    def _save_club_name(self):
        print(f"Saving club_name: {self.name_input.text()}") # Debug print
        self.club_data_manager.set_data("club_name", self.name_input.text())
        # No save_data() here

    def _save_short_name(self):
        self.club_data_manager.set_data("short_name", self.short_name_input.text())

    def _save_nickname(self):
        self.club_data_manager.set_data("nickname", self.nickname_input.text())

    def _save_year_founded(self, value):
        self.club_data_manager.set_data("year_founded", value)

    def _save_city(self):
        self.club_data_manager.set_data("city", self.city_input.text())

    def _save_country(self, index):
        country = self.country_combo.itemText(index) if index > 0 else None
        print(f"DEBUG: Saving country value: '{country}' (index {index})")

        # Store the previous value for comparison
        prev_value = self.club_data_manager.get_data("country")
        print(f"DEBUG: Previous country value: '{prev_value}'")
        print(f"DEBUG: Previous country value type: {type(prev_value)}, new value type: {type(country)}")

        if prev_value != country:
            print(f"DEBUG: Changed country from '{prev_value}' to '{country}'")

        # Set the new value (this will auto-save for critical fields)
        self.club_data_manager.set_data("country", country)

        # Verify the value was saved correctly
        saved_value = self.club_data_manager.get_data("country")
        print(f"DEBUG: Verified saved country value: '{saved_value}'")

        # Double-check that the value was saved correctly
        if saved_value != country:
            print(f"DEBUG: Warning - country mismatch after save: expected '{country}', got '{saved_value}'")
            # Try to fix it by forcing a direct JSON update
            self.club_data_manager._data["country"] = country
            self.club_data_manager.save_data()
            # Verify again
            saved_value = self.club_data_manager.get_data("country")
            print(f"DEBUG: After fix attempt, country = '{saved_value}'")

        # Force save to ensure the value is written to the file
        print(f"DEBUG: Forcing save to ensure country is written to file")
        self.club_data_manager.save_data()

    def _save_region(self):
        self.club_data_manager.set_data("region", self.region_input.text())

    def _save_stadium_name(self):
        self.club_data_manager.set_data("stadium_name", self.stadium_name_input.text())

    def _save_capacity(self, value):
        self.club_data_manager.set_data("capacity", value)

    def _save_seating_capacity(self, value):
        self.club_data_manager.set_data("seating_capacity", value)

    def _save_surface_type(self, index):
        surface = self._surface_types_source[index - 1] if index > 0 else None
        print(f"DEBUG: Saving surface_type value: '{surface}' (index {index})")

        # Store the previous value for comparison
        prev_value = self.club_data_manager.get_data("surface_type")
        print(f"DEBUG: Previous surface_type value: '{prev_value}'")
        print(f"DEBUG: Previous surface_type value type: {type(prev_value)}, new value type: {type(surface)}")

        if prev_value != surface:
            print(f"DEBUG: Changed surface_type from '{prev_value}' to '{surface}'")

        # Set the value in the data manager (this will auto-save for critical fields)
        self.club_data_manager.set_data("surface_type", surface)

        # Verify the value was saved correctly
        saved_value = self.club_data_manager.get_data("surface_type")
        print(f"DEBUG: Verified saved surface_type value: '{saved_value}'")

        # Double-check that the value was saved correctly
        if saved_value != surface:
            print(f"DEBUG: Warning - surface_type mismatch after save: expected '{surface}', got '{saved_value}'")
            # Try to fix it by forcing a direct JSON update
            self.club_data_manager._data["surface_type"] = surface
            self.club_data_manager.save_data()
            # Verify again
            saved_value = self.club_data_manager.get_data("surface_type")
            print(f"DEBUG: After fix attempt, surface_type = '{saved_value}'")

        # Force save to ensure the value is written to the file
        print(f"DEBUG: Forcing save to ensure surface_type is written to file")
        self.club_data_manager.save_data()

    def _save_year_built(self, value):
        self.club_data_manager.set_data("year_built", value)

    def _save_stadium_owner(self, index):
        owner = self._stadium_owners_source[index - 1] if index > 0 else None
        print(f"DEBUG: Saving stadium_owner value: '{owner}' (index {index})")

        # Store the previous value for comparison
        prev_value = self.club_data_manager.get_data("stadium_owner")
        print(f"DEBUG: Previous stadium_owner value: '{prev_value}'")
        print(f"DEBUG: Previous stadium_owner value type: {type(prev_value)}, new value type: {type(owner)}")

        if prev_value != owner:
            print(f"DEBUG: Changed stadium_owner from '{prev_value}' to '{owner}'")

        # Set the value in the data manager (this will auto-save for critical fields)
        self.club_data_manager.set_data("stadium_owner", owner)

        # Verify the value was saved correctly
        saved_value = self.club_data_manager.get_data("stadium_owner")
        print(f"DEBUG: Verified saved stadium_owner value: '{saved_value}'")

        # Double-check that the value was saved correctly
        if saved_value != owner:
            print(f"DEBUG: Warning - stadium_owner mismatch after save: expected '{owner}', got '{saved_value}'")
            # Try to fix it by forcing a direct JSON update
            self.club_data_manager._data["stadium_owner"] = owner
            self.club_data_manager.save_data()
            # Verify again
            saved_value = self.club_data_manager.get_data("stadium_owner")
            print(f"DEBUG: After fix attempt, stadium_owner = '{saved_value}'")

        # Force save to ensure the value is written to the file
        print(f"DEBUG: Forcing save to ensure stadium_owner is written to file")
        self.club_data_manager.save_data()

    def _save_contact_address1(self):
        self.club_data_manager.set_data("contact_address1", self.contact_address1_input.text())

    def _save_contact_address2(self):
        self.club_data_manager.set_data("contact_address2", self.contact_address2_input.text())

    def _save_contact_city(self):
        self.club_data_manager.set_data("contact_city", self.contact_city_input.text())

    def _save_contact_state(self):
        self.club_data_manager.set_data("contact_state", self.contact_state_input.text())

    def _save_contact_postal(self):
        self.club_data_manager.set_data("contact_postal", self.contact_postal_input.text())

    def _save_contact_phone(self):
        self.club_data_manager.set_data("contact_phone", self.contact_phone_input.text())

    def _save_contact_email(self):
        self.club_data_manager.set_data("contact_email", self.contact_email_input.text())

    def _save_contact_website(self):
        self.club_data_manager.set_data("contact_website", self.contact_website_input.text())

    def _update_logo_preview(self):
        """Updates the QLabel with the current logo or the default logo."""
        pixmap_loaded = False
        image_path_to_load = None
        # Ensure this is always a boolean
        is_specific_logo_set = bool(self.current_logo_path and os.path.exists(self.current_logo_path))

        if is_specific_logo_set:
            image_path_to_load = self.current_logo_path
        else:
            # Try loading the default logo if no specific one is set/found
            # Use path relative to this script file for robustness
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.abspath(os.path.join(script_dir, '..', '..')) # Go up two levels
            default_logo_path = os.path.join(project_root, "media", "club_logo", "default.png")
            if os.path.exists(default_logo_path):
                image_path_to_load = default_logo_path

        if image_path_to_load:
            try:
                pixmap = QPixmap(image_path_to_load)
                print(f"DEBUG: Logo Pixmap loaded from {image_path_to_load}. Is null? {pixmap.isNull()}") # Debug print
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(QSize(150, 150), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    self.logo_preview_label.setPixmap(scaled_pixmap)
                    # self.logo_preview_label.setText("") # Handled by retranslateUi
                    self.logo_preview_label.setStyleSheet("") # Remove placeholder style
                    pixmap_loaded = True
                else:
                    print(f"Warning: Could not load pixmap from {image_path_to_load}")
            except Exception as e:
                print(f"Error loading logo preview from {image_path_to_load}: {e}")

        if not pixmap_loaded:
            # self.logo_preview_label.setText(self.tr("No Logo")) # Handled by retranslateUi
            self.logo_preview_label.setPixmap(QPixmap()) # Clear pixmap
            self.logo_preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;") # Restore placeholder style

        # Enable remove button only if a specific logo (not default) is currently set
        self.remove_logo_button.setEnabled(is_specific_logo_set)

        # Update logo preview text and constraints label based on current state
        logo_exists = self.current_logo_path and os.path.exists(self.current_logo_path)
        default_logo_exists = os.path.exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', "media", "club_logo", "default.png"))

        if logo_exists:
            self.logo_constraints_label.setText(self.tr("Logo: {}").format(os.path.basename(self.current_logo_path)))
            self.logo_preview_label.setText("") # Clear text if specific image is shown
        elif default_logo_exists:
             # If only default exists, show original constraints and clear preview text
            self.logo_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))
            self.logo_preview_label.setText("") # Clear text if default image is shown
        else:
            # If neither exists, show constraints and placeholder text
            self.logo_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))
            self.logo_preview_label.setText(self.tr("No Logo")) # Set placeholder text
        # Update remove button tooltip
        self.remove_logo_button.setToolTip(self.tr("Remove Logo"))

    def _update_stadium_preview(self):
        """Updates the QLabel with the current stadium image or the default."""
        pixmap_loaded = False
        image_path_to_load = None
        # Ensure this is always a boolean
        is_specific_image_set = bool(self.current_stadium_image_path and os.path.exists(self.current_stadium_image_path))

        if is_specific_image_set:
            image_path_to_load = self.current_stadium_image_path
        else:
            # Try loading the default stadium image
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.abspath(os.path.join(script_dir, '..', '..')) # Go up two levels
            default_stadium_path = os.path.join(project_root, "media", "stadium", "default.png")
            if os.path.exists(default_stadium_path):
                image_path_to_load = default_stadium_path

        if image_path_to_load:
            try:
                pixmap = QPixmap(image_path_to_load)
                print(f"DEBUG: Stadium Pixmap loaded from {image_path_to_load}. Is null? {pixmap.isNull()}") # Debug print
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(QSize(150, 150), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    self.stadium_preview_label.setPixmap(scaled_pixmap)
                    # self.stadium_preview_label.setText("") # Handled by retranslateUi
                    self.stadium_preview_label.setStyleSheet("")
                    pixmap_loaded = True
                else:
                    print(f"Warning: Could not load pixmap from {image_path_to_load}")
            except Exception as e:
                print(f"Error loading stadium preview from {image_path_to_load}: {e}")

        if not pixmap_loaded:
            # self.stadium_preview_label.setText(self.tr("No Stadium Image")) # Handled by retranslateUi
            self.stadium_preview_label.setPixmap(QPixmap())
            self.stadium_preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")

        # Enable remove button only if a specific stadium image is set
        self.remove_stadium_button.setEnabled(is_specific_image_set)

        # Update stadium preview text and constraints label based on current state
        stadium_image_exists = self.current_stadium_image_path and os.path.exists(self.current_stadium_image_path)
        default_stadium_exists = os.path.exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', "media", "stadium", "default.png"))

        if stadium_image_exists:
             self.stadium_constraints_label.setText(self.tr("Stadium Image: {}").format(os.path.basename(self.current_stadium_image_path)))
             self.stadium_preview_label.setText("") # Clear text if specific image is shown
        elif default_stadium_exists:
             # If only default exists, show original constraints and clear preview text
            self.stadium_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))
            self.stadium_preview_label.setText("") # Clear text if default image is shown
        else:
             # If neither exists, show constraints and placeholder text
            self.stadium_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))
            self.stadium_preview_label.setText(self.tr("No Stadium Image")) # Set placeholder text
        # Update stadium remove button tooltip
        self.remove_stadium_button.setToolTip(self.tr("Remove Stadium Image"))

    def _handle_logo_upload(self):
        """Handles the logo upload button click."""
        # Define the target directory and filename
        base_dir = os.getcwd()
        logos_dir = os.path.join(base_dir, "media", "club_logo") # Use underscore
        target_filename = "Club_logo.png" # Fixed filename
        target_path = os.path.join(logos_dir, target_filename)

        # Ensure the target directory exists
        try:
            os.makedirs(logos_dir, exist_ok=True)
        except OSError as e:
            print(f"Error creating directory {logos_dir}: {e}")
            self.logo_constraints_label.setText(self.tr("Error creating logo directory!"))
            return

        # Open file dialog
        file_filter = self.tr("PNG Images (*.png)")
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.tr("Select Logo Image"),
            "", # Start directory (empty means default/last)
            file_filter
        )

        if file_path: # Check if a file was selected
            try:
                # Copy and overwrite the file
                shutil.copy2(file_path, target_path)
                print(f"Logo copied to: {target_path}")
                self.current_logo_path = target_path
                self._update_logo_preview() # Update the preview immediately
                # --- Save logo status ---
                self.club_data_manager.set_data("logo_exists", True)
                self.logo_constraints_label.setText(self.tr("Logo: {}").format(target_filename))
            except Exception as e:
                print(f"Error copying logo from {file_path} to {target_path}: {e}")
                self.logo_constraints_label.setText(self.tr("Error copying logo!"))
                self.current_logo_path = None # Reset path on error
                self._update_logo_preview()
                # --- Save logo status on error ---
                self.club_data_manager.set_data("logo_exists", False)

    def _handle_remove_logo(self):
        """Handles the remove logo button click."""
        if self.current_logo_path and os.path.exists(self.current_logo_path):
            try:
                os.remove(self.current_logo_path)
                print(f"Removed logo file: {self.current_logo_path}")
                self.current_logo_path = None
                # --- Save logo status ---
                self.club_data_manager.set_data("logo_exists", False)
                self._update_logo_preview() # Refresh preview (should show default)
            except (FileNotFoundError, PermissionError, OSError) as e:
                print(f"Error removing logo file {self.current_logo_path}: {e}")
                # Optionally show an error message to the user
        else:
            print("No specific logo set or file does not exist, cannot remove.")
            # Ensure saved state is consistent
            if not (self.current_logo_path and os.path.exists(self.current_logo_path)):
                 if self.club_data_manager.get_data("logo_exists") is True:
                     self.club_data_manager.set_data("logo_exists", False)

    def _handle_stadium_upload(self):
        """Handles the stadium image upload button click."""
        base_dir = os.getcwd()
        stadium_dir = os.path.join(base_dir, "media", "stadium")
        target_filename = "stadium.png"
        target_path = os.path.join(stadium_dir, target_filename)

        try:
            os.makedirs(stadium_dir, exist_ok=True)
        except OSError as e:
            print(f"Error creating directory {stadium_dir}: {e}")
            self.stadium_constraints_label.setText(self.tr("Error creating stadium directory!"))
            return

        file_filter = self.tr("Image Files (*.png *.jpg *.jpeg)") # Allow JPG/JPEG too
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.tr("Select Stadium Image"),
            "",
            file_filter
        )

        if file_path:
            try:
                shutil.copy2(file_path, target_path)
                print(f"Stadium image copied to: {target_path}")
                self.current_stadium_image_path = target_path
                self._update_stadium_preview()
                # --- Save stadium image status ---
                self.club_data_manager.set_data("stadium_image_exists", True)
                self.stadium_constraints_label.setText(self.tr("Stadium Image: {}").format(target_filename))
            except Exception as e:
                print(f"Error copying stadium image from {file_path} to {target_path}: {e}")
                self.stadium_constraints_label.setText(self.tr("Error copying stadium image!"))
                self.current_stadium_image_path = None
                self._update_stadium_preview()
                # --- Save stadium image status on error ---
                self.club_data_manager.set_data("stadium_image_exists", False)

    def _handle_remove_stadium_image(self):
        """Handles the remove stadium image button click."""
        if self.current_stadium_image_path and os.path.exists(self.current_stadium_image_path):
            try:
                os.remove(self.current_stadium_image_path)
                print(f"Removed stadium image file: {self.current_stadium_image_path}")
                self.current_stadium_image_path = None
                # --- Save stadium image status ---
                self.club_data_manager.set_data("stadium_image_exists", False)
                self._update_stadium_preview()
            except (FileNotFoundError, PermissionError, OSError) as e:
                print(f"Error removing stadium image file {self.current_stadium_image_path}: {e}")
        else:
            print("No specific stadium image set or file does not exist, cannot remove.")
            # Ensure saved state is consistent
            if not (self.current_stadium_image_path and os.path.exists(self.current_stadium_image_path)):
                 if self.club_data_manager.get_data("stadium_image_exists") is True:
                     self.club_data_manager.set_data("stadium_image_exists", False)

    def tr(self, text):
        """Helper for translations within this class."""
        # Use the class name as the context
        return QCoreApplication.translate("ClubWindow", text)

    def retranslateUi(self):
        print("DEBUG: Entered retranslateUi in ClubWindow")

        # Import tooltip helper and direct translations
        from app.utils.tooltip_helper import set_tooltip, get_direct_translation, apply_tab_tooltips, apply_window_tooltip
        import logging

        logger = logging.getLogger(__name__)
        logger.info("Retranslating ClubWindow UI")

        # Apply tooltip to the window itself
        apply_window_tooltip(self)

        # Apply tooltips to all tabs
        if hasattr(self, 'tab_widget'):
            apply_tab_tooltips(self.tab_widget)

        # Apply tooltips to sub-tabs
        if hasattr(self, 'staff_sub_tab_widget'):
            apply_tab_tooltips(self.staff_sub_tab_widget)

        if hasattr(self, 'kit_sub_tab_widget'):
            apply_tab_tooltips(self.kit_sub_tab_widget)

        from PySide6.QtCore import QSettings

        # Get current language
        settings = QSettings()
        current_language = settings.value("general/language", "en")
        print(f"DEBUG: Current language in ClubWindow.retranslateUi: {current_language}")

        # Set window title with direct translation if available
        window_title = "Club Details"
        direct_translation = get_direct_translation(window_title, current_language)
        if direct_translation != window_title:
            self.setWindowTitle(direct_translation)
            print(f"DEBUG: Using direct translation for window title: '{direct_translation}'")
        else:
            self.setWindowTitle(self.tr(window_title))
            print(f"DEBUG: Using tr() for window title: '{self.tr(window_title)}'")

        # Update main tab titles with direct translations if available
        tab_titles = ["Information", "Contact", "Staff", "Kit", "Sponsors", "Team Groups"]

        for i, title in enumerate(tab_titles):
            if i < self.tab_widget.count():
                direct_translation = get_direct_translation(title, current_language)
                if direct_translation != title:
                    self.tab_widget.setTabText(i, direct_translation)
                    print(f"DEBUG: Using direct translation for tab {i}: '{direct_translation}'")
                else:
                    self.tab_widget.setTabText(i, self.tr(title))
                    print(f"DEBUG: Using tr() for tab {i}: '{self.tr(title)}'")

        # Special handling for Team Groups tab which might be at a different index
        idx = self.tab_widget.indexOf(self.groups_widget)
        if idx != -1 and idx >= len(tab_titles):
            team_groups_title = "Team Groups"
            direct_translation = get_direct_translation(team_groups_title, current_language)
            if direct_translation != team_groups_title:
                self.tab_widget.setTabText(idx, direct_translation)
                print(f"DEBUG: Using direct translation for Team Groups tab: '{direct_translation}'")
            else:
                self.tab_widget.setTabText(idx, self.tr(team_groups_title))
                print(f"DEBUG: Using tr() for Team Groups tab: '{self.tr(team_groups_title)}'")

        # Print all tab titles for debugging
        print("DEBUG: Current tab titles after translation:")
        for i in range(self.tab_widget.count()):
            print(f"  Tab {i}: '{self.tab_widget.tabText(i)}'")

        # Apply detailed tooltips to all UI elements
        self._apply_detailed_tooltips()


        # Add tooltips to main tabs with direct translations
        main_tab_tooltips = [
            "View and edit basic club information",
            "View and edit club contact details",
            "Manage club staff members",
            "Customize club kit designs",
            "Manage club sponsors",
            "Organize players into team groups"
        ]

        for i, tooltip in enumerate(main_tab_tooltips):
            if i < self.tab_widget.count():
                # Set tooltip with direct translation if available
                direct_tooltip = get_direct_translation(tooltip, current_language)
                if direct_tooltip != tooltip:
                    self.tab_widget.setTabToolTip(i, direct_tooltip)
                    print(f"DEBUG: Using direct translation for main tab tooltip {i}: '{direct_tooltip}'")
                else:
                    self.tab_widget.setTabToolTip(i, self.tr(tooltip))
                    print(f"DEBUG: Using tr() for main tab tooltip {i}: '{self.tr(tooltip)}'")

        # Special handling for Team Groups tab which might be at a different index
        if idx != -1 and idx >= len(main_tab_tooltips):
            team_groups_tooltip = "Organize players into team groups"
            direct_tooltip = get_direct_translation(team_groups_tooltip, current_language)
            if direct_tooltip != team_groups_tooltip:
                self.tab_widget.setTabToolTip(idx, direct_tooltip)
                print(f"DEBUG: Using direct translation for Team Groups tab tooltip: '{direct_tooltip}'")
            else:
                self.tab_widget.setTabToolTip(idx, self.tr(team_groups_tooltip))
                print(f"DEBUG: Using tr() for Team Groups tab tooltip: '{self.tr(team_groups_tooltip)}'")

        # Add tooltips to the window itself with direct translation
        window_tooltip = "View and edit club information"
        direct_window_tooltip = get_direct_translation(window_tooltip, current_language)
        if direct_window_tooltip != window_tooltip:
            set_tooltip(self, direct_window_tooltip)
            print(f"DEBUG: Using direct translation for window tooltip: '{direct_window_tooltip}'")
        else:
            set_tooltip(self, window_tooltip)
            print(f"DEBUG: Using default tooltip for window: '{window_tooltip}'")

        # Debug logging for tooltips
        print("DEBUG: Current main tab titles and tooltips after translation:")
        for i in range(self.tab_widget.count()):
            tab_text = self.tab_widget.tabText(i)
            tooltip_text = self.tab_widget.tabToolTip(i)
            print(f"  Tab {i}: '{tab_text}' -> Tooltip: '{tooltip_text}'")

        # Add additional debug for tooltip translation
        print(f"DEBUG: Chinese translation for 'View and edit club contact details': '{get_direct_translation('View and edit club contact details', 'zh')}'")
        print(f"DEBUG: Greek translation for 'View and edit club contact details': '{get_direct_translation('View and edit club contact details', 'el')}'")
        print(f"DEBUG: Albanian translation for 'View and edit club contact details': '{get_direct_translation('View and edit club contact details', 'sq')}'")

        # Force update tooltips for Contact tab
        contact_tooltip = "View and edit club contact details"
        direct_tooltip = get_direct_translation(contact_tooltip, current_language)
        self.tab_widget.setTabToolTip(1, direct_tooltip)
        print(f"DEBUG: Force-set Contact tab tooltip to: '{direct_tooltip}'")


        # --- ADDED: Update staff sub-tab titles here ---
        if hasattr(self, 'staff_sub_tab_widget'):
            # Staff sub-tab titles with direct translations
            staff_subtab_titles = ["Management", "Coaching", "Medical"]
            staff_subtab_tooltips = [
                "Manage club management staff",
                "Manage club coaching staff",
                "Manage club medical staff"
            ]

            for i, title in enumerate(staff_subtab_titles):
                if i < self.staff_sub_tab_widget.count():
                    # Set tab text with direct translation if available
                    direct_translation = get_direct_translation(title, current_language)
                    if direct_translation != title:
                        self.staff_sub_tab_widget.setTabText(i, direct_translation)
                        print(f"DEBUG: Using direct translation for staff sub-tab {i}: '{direct_translation}'")
                    else:
                        self.staff_sub_tab_widget.setTabText(i, self.tr(title))
                        print(f"DEBUG: Using tr() for staff sub-tab {i}: '{self.tr(title)}'")

                    # Set tooltip with direct translation if available
                    tooltip = staff_subtab_tooltips[i]
                    direct_tooltip = get_direct_translation(tooltip, current_language)
                    if direct_tooltip != tooltip:
                        self.staff_sub_tab_widget.setTabToolTip(i, direct_tooltip)
                        print(f"DEBUG: Using direct translation for staff sub-tab tooltip {i}: '{direct_tooltip}'")
                    else:
                        self.staff_sub_tab_widget.setTabToolTip(i, self.tr(tooltip))
                        print(f"DEBUG: Using tr() for staff sub-tab tooltip {i}: '{self.tr(tooltip)}'")

            # Debug logging for staff sub-tab tooltips
            print("DEBUG: Current staff sub-tab titles and tooltips after translation:")
            for i in range(self.staff_sub_tab_widget.count()):
                tab_text = self.staff_sub_tab_widget.tabText(i)
                tooltip_text = self.staff_sub_tab_widget.tabToolTip(i)
                print(f"  Staff Sub-Tab {i}: '{tab_text}' -> Tooltip: '{tooltip_text}'")
        # --- End Added ---

        # Add tooltips to kit sub-tabs if they exist
        if hasattr(self, 'kit_sub_tab_widget'):
            # Kit sub-tab titles with direct translations
            kit_subtab_titles = ["Colors", "1st Kit", "2nd Kit", "3rd Kit", "1st GK Kit", "2nd GK Kit"]
            kit_subtab_tooltips = [
                "Configure club colors",
                "Design primary kit",
                "Design secondary kit",
                "Design third kit",
                "Design primary goalkeeper kit",
                "Design secondary goalkeeper kit"
            ]

            for i, title in enumerate(kit_subtab_titles):
                if i < self.kit_sub_tab_widget.count():
                    # Set tab text with direct translation if available
                    direct_translation = get_direct_translation(title, current_language)
                    if direct_translation != title:
                        self.kit_sub_tab_widget.setTabText(i, direct_translation)
                        print(f"DEBUG: Using direct translation for kit sub-tab {i}: '{direct_translation}'")
                    else:
                        self.kit_sub_tab_widget.setTabText(i, self.tr(title))
                        print(f"DEBUG: Using tr() for kit sub-tab {i}: '{self.tr(title)}'")

                    # Set tooltip with direct translation if available
                    tooltip = kit_subtab_tooltips[i]
                    direct_tooltip = get_direct_translation(tooltip, current_language)
                    if direct_tooltip != tooltip:
                        self.kit_sub_tab_widget.setTabToolTip(i, direct_tooltip)
                        print(f"DEBUG: Using direct translation for kit sub-tab tooltip {i}: '{direct_tooltip}'")
                    else:
                        self.kit_sub_tab_widget.setTabToolTip(i, self.tr(tooltip))
                        print(f"DEBUG: Using tr() for kit sub-tab tooltip {i}: '{self.tr(tooltip)}'")

            # Debug logging for kit sub-tab tooltips
            print("DEBUG: Current kit sub-tab titles and tooltips after translation:")
            for i in range(self.kit_sub_tab_widget.count()):
                tab_text = self.kit_sub_tab_widget.tabText(i)
                tooltip_text = self.kit_sub_tab_widget.tabToolTip(i)
                print(f"  Kit Sub-Tab {i}: '{tab_text}' -> Tooltip: '{tooltip_text}'")

        # Update Information tab content
        self._update_information_tab_content()
        self._update_contact_tab_content()
        self._update_staff_tab_content()  # <-- Ensure this is called
        print("DEBUG: About to call _update_kit_tab_content from retranslateUi")
        self._update_kit_tab_content()

        # --- Sponsors Panel Translation ---
        if hasattr(self, 'sponsors_panel') and hasattr(self.sponsors_panel, 'retranslateUi'):
            print("DEBUG: About to call sponsors_panel.retranslateUi from ClubWindow.retranslateUi")
            self.sponsors_panel.retranslateUi()

            # Force visibility update on sponsors section widgets
            if hasattr(self.sponsors_panel, 'category_widgets'):
                for category, widgets in self.sponsors_panel.category_widgets.items():
                    for widget in widgets:
                        if hasattr(widget, 'logo_preview_label'):
                            widget.logo_preview_label.setVisible(True)
                            print(f"Force visibility for {category} sponsor logo")

        if hasattr(self, 'groups_widget') and hasattr(self.groups_widget, 'retranslateUi'):
            self.groups_widget.retranslateUi()

    def _update_information_tab_content(self):
        """Updates all translatable content in the Information tab."""
        print("Updating Information tab content...")  # Debug print

        # Update Group Box Titles
        if hasattr(self, 'general_group_box'):
            # Check current language and set title directly for Greek and Chinese
            from PySide6.QtCore import QSettings
            settings = QSettings()
            current_language = settings.value("general/language", "en")

            if current_language == "el":
                self.general_group_box.setTitle("Γενικές Πληροφορίες")
            elif current_language == "zh":
                self.general_group_box.setTitle("基本信息")
            else:
                self.general_group_box.setTitle(self.tr("General Information"))

        if hasattr(self, 'stadium_group_box'):
            # Check current language and set title directly
            if not 'current_language' in locals():
                from PySide6.QtCore import QSettings
                settings = QSettings()
                current_language = settings.value("general/language", "en")

            if current_language == "el":
                self.stadium_group_box.setTitle("Πληροφορίες Γηπέδου")
            elif current_language == "zh":
                self.stadium_group_box.setTitle("球场信息")
            else:
                self.stadium_group_box.setTitle(self.tr("Stadium Information"))

        # Update Form Labels - New approach
        if hasattr(self, 'general_form_layout'):
            form_labels = {
                self.name_input: self.tr("Club Name:"),
                self.short_name_input: self.tr("Short Name:"),
                self.nickname_input: self.tr("Nickname:"),
                self.year_founded_input: self.tr("Year Founded:"),
                self.city_input: self.tr("City:"),
                self.country_combo: self.tr("Country:"),
                self.region_input: self.tr("Region/Continent:")
            }

            # Update each label safely
            for widget, label_text in form_labels.items():
                try:
                    label_item = self.general_form_layout.labelForField(widget)
                    if label_item:
                        label_item.setText(label_text)
                except Exception as e:
                    print(f"Error updating label for {widget}: {e}")

        # Update Stadium Form Labels
        if hasattr(self, 'stadium_form_layout'):
            stadium_labels = {
                self.stadium_name_input: self.tr("Stadium Name:"),
                self.capacity_input: self.tr("Capacity:"),
                self.seating_capacity_input: self.tr("Seating Capacity:"),
                self.surface_combo: self.tr("Surface Type:"),
                self.year_built_input: self.tr("Year Built:"),
                self.stadium_owner_combo: self.tr("Stadium Owner:")
            }

            for widget, label_text in stadium_labels.items():
                try:
                    label_item = self.stadium_form_layout.labelForField(widget)
                    if label_item:
                        label_item.setText(label_text)
                except Exception as e:
                    print(f"Error updating stadium label for {widget}: {e}")

        # Update Combo Boxes while preserving selection
        if hasattr(self, 'country_combo'):
            current_text = self.country_combo.currentText()
            self.country_combo.clear()
            self.country_combo.addItem(self.tr("--- Select Country ---"))
            self.country_combo.addItems(countries)
            # Restore selection
            index = self.country_combo.findText(current_text)
            self.country_combo.setCurrentIndex(index if index >= 0 else 0)

        if hasattr(self, 'surface_combo'):
            current_text = self.surface_combo.currentText()
            self.surface_combo.clear()
            self.surface_combo.addItem(self.tr("--- Select Surface ---"))
            for surface in self._surface_types_source:
                self.surface_combo.addItem(self.tr(surface))
            # Restore selection
            index = self.surface_combo.findText(current_text)
            self.surface_combo.setCurrentIndex(index if index >= 0 else 0)

        if hasattr(self, 'stadium_owner_combo'):
            current_text = self.stadium_owner_combo.currentText()
            self.stadium_owner_combo.clear()
            self.stadium_owner_combo.addItem(self.tr("--- Select Owner ---"))
            for owner in self._stadium_owners_source:
                self.stadium_owner_combo.addItem(self.tr(owner))
            # Restore selection
            index = self.stadium_owner_combo.findText(current_text)
            self.stadium_owner_combo.setCurrentIndex(index if index >= 0 else 0)

        # Update Button Texts and Tooltips
        if hasattr(self, 'logo_upload_button'):
            self.logo_upload_button.setText(self.tr("Upload"))
        if hasattr(self, 'remove_logo_button'):
            self.remove_logo_button.setToolTip(self.tr("Remove Logo"))
        if hasattr(self, 'stadium_upload_button'):
            self.stadium_upload_button.setText(self.tr("Upload"))
        if hasattr(self, 'remove_stadium_button'):
            self.remove_stadium_button.setToolTip(self.tr("Remove Stadium Image"))

        # Update Constraint Labels
        if hasattr(self, 'logo_constraints_label'):
            if self.current_logo_path and os.path.exists(self.current_logo_path):
                self.logo_constraints_label.setText(
                    self.tr("Logo: {}").format(os.path.basename(self.current_logo_path)))
            else:
                self.logo_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))

        if hasattr(self, 'stadium_constraints_label'):
            if self.current_stadium_image_path and os.path.exists(self.current_stadium_image_path):
                self.stadium_constraints_label.setText(
                    self.tr("Stadium Image: {}").format(os.path.basename(self.current_stadium_image_path)))
            else:
                self.stadium_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))

    def _get_kit_widget(self, kit_id, side, widget_type):
        """Helper to get a specific kit widget attribute by kit_id and side."""
        kit_map = {
            "1": "1st", "2": "2nd", "3": "3rd",
            "4": "1st_gk", "5": "2nd_gk"
        }
        kit_key = kit_map.get(str(kit_id))
        if not kit_key:
            print(f"Error: Invalid kit_id '{kit_id}' in _get_kit_widget.")
            return None
        attr_name = f"kit_{kit_key}_{side}_{widget_type}"
        try:
            return getattr(self, attr_name)
        except AttributeError:
            print(f"Error: Could not find widget attribute '{attr_name}'.")
            return None

    def _get_kit_image_path(self, kit_id, side):
        """Constructs the expected path for a specific kit image."""
        filename = f"kit{kit_id}_{side}.png"
        return os.path.join(self.KIT_IMAGE_FOLDER, filename)

    def _get_default_kit_image_path(self, side):
        """Constructs the path for the default kit image."""
        filename = f"default_{side}.png"
        return os.path.join(self.KIT_IMAGE_FOLDER, filename)

    def _update_kit_preview(self, kit_id, side):
        """Updates the preview label, remove button, and constraints text for a kit side."""
        preview_label = self._get_kit_widget(kit_id, side, "preview_label")
        remove_button = self._get_kit_widget(kit_id, side, "remove_button")
        constraints_label = self._get_kit_widget(kit_id, side, "constraints_label")

        if not all([preview_label, remove_button, constraints_label]):
            print(f"Error: Missing UI elements for kit {kit_id} {side} in _update_kit_preview.")
            return # Cannot proceed if UI elements are missing

        data_key = f"kit_{kit_id}_{side}_exists"
        specific_image_path = self._get_kit_image_path(kit_id, side)
        default_image_path = self._get_default_kit_image_path(side)

        image_exists = self.club_data_manager.get_data(data_key, False)
        image_path_to_load = None
        is_specific_image_set = False # Track if we are using the kit-specific image

        if image_exists and os.path.exists(specific_image_path):
            image_path_to_load = specific_image_path
            is_specific_image_set = True
        elif os.path.exists(default_image_path):
            image_path_to_load = default_image_path
        else:
            # No specific or default image found
            image_path_to_load = None

        pixmap_loaded = False
        if image_path_to_load:
            try:
                pixmap = QPixmap(image_path_to_load)
                if not pixmap.isNull():
                    # Scale pixmap *before* setting it
                    scaled_pixmap = pixmap.scaled(QSize(150, 150), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    preview_label.setPixmap(scaled_pixmap)
                    preview_label.setText("") # Clear placeholder text
                    preview_label.setStyleSheet("") # Remove placeholder style
                    pixmap_loaded = True
                else:
                     print(f"Warning: Could not load pixmap from {image_path_to_load} (isNull).")
            except Exception as e:
                print(f"Error loading kit preview from {image_path_to_load}: {e}")

        constraints_text = self.tr("(PNG, <{max_kb}KB, <{dim}x{dim}px)").format(
             max_kb=self.KIT_IMAGE_MAX_SIZE_KB, dim=self.KIT_IMAGE_MAX_DIMENSION
        )

        if not pixmap_loaded:
            preview_label.setPixmap(QPixmap()) # Clear pixmap
            preview_label.setText(self.tr("No Image"))
            preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;") # Restore placeholder style
            constraints_label.setText(constraints_text) # Show constraints if no image loaded
        elif is_specific_image_set:
             # Show filename if specific image loaded
             constraints_label.setText(os.path.basename(specific_image_path))
        else:
             # Show constraints if default image loaded
             constraints_label.setText(constraints_text)


        # Enable remove button only if a specific image for this kit/side exists and is loaded
        remove_button.setEnabled(is_specific_image_set)

    def _handle_kit_upload(self, kit_id, side):
        """Handles the kit image upload button click."""
        target_path = self._get_kit_image_path(kit_id, side)
        data_key = f"kit_{kit_id}_{side}_exists"
        constraints_label = self._get_kit_widget(kit_id, side, "constraints_label")

        if not constraints_label: return # Cannot provide feedback

        initial_constraints_text = self.tr("(PNG, <{max_kb}KB, <{dim}x{dim}px)").format(
            max_kb=self.KIT_IMAGE_MAX_SIZE_KB, dim=self.KIT_IMAGE_MAX_DIMENSION
        )

        # Open file dialog
        file_filter = self.tr("PNG Images (*.png)")
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.tr("Select Kit Image ({side})").format(side=side.title()),
            "", # Start directory
            file_filter
        )

        if not file_path:
            return # User cancelled

        # --- Validation ---
        file_info = QFileInfo(file_path)
        file_size_kb = file_info.size() / 1024

        # 1. Check Size
        if file_size_kb > self.KIT_IMAGE_MAX_SIZE_KB:
             error_msg = self.tr("Error: File size exceeds {max_kb}KB.").format(max_kb=self.KIT_IMAGE_MAX_SIZE_KB)
             print(error_msg)
             constraints_label.setText(f"<font color='red'>{error_msg}</font>")
             # Reset text after a delay? Or leave it until next successful action.
             return

        # 2. Check Dimensions
        reader = QImageReader(file_path)
        if not reader.canRead():
            error_msg = self.tr("Error: Cannot read image file.")
            print(error_msg)
            constraints_label.setText(f"<font color='red'>{error_msg}</font>")
            return

        img_size = reader.size()
        if img_size.width() > self.KIT_IMAGE_MAX_DIMENSION or img_size.height() > self.KIT_IMAGE_MAX_DIMENSION:
             error_msg = self.tr("Error: Dimensions exceed {dim}x{dim}px.").format(dim=self.KIT_IMAGE_MAX_DIMENSION)
             print(error_msg)
             constraints_label.setText(f"<font color='red'>{error_msg}</font>")
             return

        # --- Copy File ---
        try:
            os.makedirs(self.KIT_IMAGE_FOLDER, exist_ok=True) # Ensure directory exists again just in case
            shutil.copy2(file_path, target_path)
            print(f"Kit image copied to: {target_path}")
            self.club_data_manager.set_data(data_key, True)
            self._update_kit_preview(kit_id, side) # Update preview
        except Exception as e:
            error_msg = self.tr("Error copying image!")
            print(f"Error copying kit image from {file_path} to {target_path}: {e}")
            constraints_label.setText(f"<font color='red'>{error_msg}</font>")
            # Reset data state if copy failed after validation
            if self.club_data_manager.get_data(data_key, False):
                 self.club_data_manager.set_data(data_key, False)
                 self._update_kit_preview(kit_id, side) # Update preview to reflect failure


    def _handle_kit_remove(self, kit_id, side):
        """Handles the remove kit image button click."""
        target_path = self._get_kit_image_path(kit_id, side)
        data_key = f"kit_{kit_id}_{side}_exists"
        constraints_label = self._get_kit_widget(kit_id, side, "constraints_label")

        if os.path.exists(target_path):
            try:
                os.remove(target_path)
                print(f"Removed kit image file: {target_path}")
                self.club_data_manager.set_data(data_key, False)
                self._update_kit_preview(kit_id, side) # Refresh preview (should show default)
            except (FileNotFoundError, PermissionError, OSError) as e:
                error_msg = self.tr("Error removing image!")
                print(f"Error removing kit image file {target_path}: {e}")
                if constraints_label:
                    constraints_label.setText(f"<font color='red'>{error_msg}</font>")
                # Don't change data state on removal error, file might still be there.
        else:
             print(f"Kit image file not found, cannot remove: {target_path}")
             # Ensure data manager state is consistent
             if self.club_data_manager.get_data(data_key, False):
                  self.club_data_manager.set_data(data_key, False)
                  self._update_kit_preview(kit_id, side) # Refresh preview if data was wrong

    # --- Color Picker Handlers ---
    def _handle_color_pick(self, button_attribute_name, data_key):
        """Generic handler to open color picker and update button/data."""
        current_button = getattr(self, button_attribute_name)
        initial_color_hex = self.club_data_manager.get_data(data_key, "#ffffff") # Default white
        initial_color = QColor(initial_color_hex)

        # Use static method getColor for simplicity and platform consistency
        color = QColorDialog.getColor(initial_color, self, f"Select {data_key.replace('_', ' ').title()}")

        if color.isValid():
            color_hex = color.name() # Get hex string like #RRGGBB
            print(f"Color selected for {data_key}: {color_hex}")
            current_button.setStyleSheet(f"background-color: {color_hex}; border: 1px solid black;")
            self.club_data_manager.set_data(data_key, color_hex)
            self._update_kit_color_preview() # Update combined preview

    def _handle_color_pick_1st(self):
        self._handle_color_pick("color_button_1st", "color_1st")

    def _handle_color_pick_2nd(self):
        self._handle_color_pick("color_button_2nd", "color_2nd")

    def _handle_color_pick_3rd(self):
        # Pass was here before, now implement using generic handler
        self._handle_color_pick("color_button_3rd", "color_3rd") # Was empty

    def _update_color_preview(self, button_attribute_name, data_key):
        """Updates a specific color button based on saved data."""
        color_hex = self.club_data_manager.get_data(data_key, "#ffffff") # Default white
        try:
            current_button = getattr(self, button_attribute_name)
            current_button.setStyleSheet(f"background-color: {color_hex}; border: 1px solid black;")
        except AttributeError:
             print(f"Warning: Button attribute '{button_attribute_name}' not found during color preview update.")

    def _update_kit_color_preview(self):
        """Updates the combined color preview frame and label."""
        color1_hex = self.club_data_manager.get_data("color_1st", "#FFFFFF") # Default White
        color2_hex = self.club_data_manager.get_data("color_2nd", "#000000") # Default Black
        color3_hex = self.club_data_manager.get_data("color_3rd", "#000000") # Default Black
        border_thickness = 4 # Adjust thickness as needed

        try:
            # Update Frame Style: Background color1, Border color2
            frame_style = (
                f"QFrame {{"
                f" background-color: {color1_hex};"
                f" border: {border_thickness}px solid {color2_hex};"
                f"}}"
            )
            self.color_preview_frame.setStyleSheet(frame_style)

            # Update Label Style: Text color3
            label_style = (
                f"QLabel {{"
                f" color: {color3_hex};"
                f" background-color: transparent;"
                f" border: none;"
                f"}}"
            )
            self.color_preview_label.setStyleSheet(label_style)
        except AttributeError:
            print("Warning: Color preview frame or label not found during update.")

    def _apply_detailed_tooltips(self):
        """Apply detailed tooltips to all UI elements in the Club window."""
        from app.utils.tooltip_helper import set_tooltip, apply_tab_tooltips, apply_window_tooltip
        import logging

        logger = logging.getLogger(__name__)
        logger.info("Applying detailed tooltips to Club window")

        # Apply tooltip to the window itself
        apply_window_tooltip(self)

        # Apply tooltips to all tabs
        if hasattr(self, 'tab_widget'):
            apply_tab_tooltips(self.tab_widget)

        # Apply tooltips to sub-tabs
        if hasattr(self, 'staff_sub_tab_widget'):
            apply_tab_tooltips(self.staff_sub_tab_widget)

        if hasattr(self, 'kit_sub_tab_widget'):
            apply_tab_tooltips(self.kit_sub_tab_widget)

        # --- Information Tab Tooltips ---
        # General Information section
        if hasattr(self, 'name_input'):
            set_tooltip(self.name_input, "Enter the full official name of the club")
        if hasattr(self, 'short_name_input'):
            set_tooltip(self.short_name_input, "Enter a shortened version of the club name (used in tables and lists)")
        if hasattr(self, 'nickname_input'):
            set_tooltip(self.nickname_input, "Enter the club's common nickname or alias")
        if hasattr(self, 'year_founded_input'):
            set_tooltip(self.year_founded_input, "Enter the year when the club was established")
        if hasattr(self, 'city_input'):
            set_tooltip(self.city_input, "Enter the city where the club is based")
        if hasattr(self, 'country_combo'):
            set_tooltip(self.country_combo, "Select the country where the club is located")
        if hasattr(self, 'region_input'):
            set_tooltip(self.region_input, "Enter the region or continent where the club is located")
        if hasattr(self, 'logo_upload_button'):
            set_tooltip(self.logo_upload_button, "Upload a logo image for the club")
        if hasattr(self, 'remove_logo_button'):
            set_tooltip(self.remove_logo_button, "Remove the current club logo")

        # Stadium Information section
        if hasattr(self, 'stadium_name_input'):
            set_tooltip(self.stadium_name_input, "Enter the name of the club's home stadium")
        if hasattr(self, 'capacity_input'):
            set_tooltip(self.capacity_input, "Enter the total capacity of the stadium")
        if hasattr(self, 'seating_capacity_input'):
            set_tooltip(self.seating_capacity_input, "Enter the number of seats in the stadium")
        if hasattr(self, 'surface_combo'):
            set_tooltip(self.surface_combo, "Select the type of playing surface in the stadium")
        if hasattr(self, 'year_built_input'):
            set_tooltip(self.year_built_input, "Enter the year when the stadium was built")
        if hasattr(self, 'stadium_owner_combo'):
            set_tooltip(self.stadium_owner_combo, "Select who owns the stadium")
        if hasattr(self, 'stadium_upload_button'):
            set_tooltip(self.stadium_upload_button, "Upload an image of the stadium")
        if hasattr(self, 'remove_stadium_button'):
            set_tooltip(self.remove_stadium_button, "Remove the current stadium image")

        # --- Contact Tab Tooltips ---
        if hasattr(self, 'contact_address1_input'):
            set_tooltip(self.contact_address1_input, "Enter the first line of the club's address")
        if hasattr(self, 'contact_address2_input'):
            set_tooltip(self.contact_address2_input, "Enter the second line of the club's address (optional)")
        if hasattr(self, 'contact_city_input'):
            set_tooltip(self.contact_city_input, "Enter the city for the club's contact address")
        if hasattr(self, 'contact_state_input'):
            set_tooltip(self.contact_state_input, "Enter the state or province for the club's contact address")
        if hasattr(self, 'contact_postal_input'):
            set_tooltip(self.contact_postal_input, "Enter the postal or zip code for the club's contact address")
        if hasattr(self, 'contact_phone_input'):
            set_tooltip(self.contact_phone_input, "Enter the club's main phone number")
        if hasattr(self, 'contact_email_input'):
            set_tooltip(self.contact_email_input, "Enter the club's main email address")
        if hasattr(self, 'contact_website_input'):
            set_tooltip(self.contact_website_input, "Enter the club's official website URL")

        # --- Staff Tab Tooltips ---
        # Management sub-tab
        if hasattr(self, 'add_management_button'):
            set_tooltip(self.add_management_button, "Add a new management staff member")
        if hasattr(self, 'remove_management_button'):
            set_tooltip(self.remove_management_button, "Remove the selected management staff member")
        if hasattr(self, 'search_management_input'):
            set_tooltip(self.search_management_input, "Search for management staff members")
        if hasattr(self, 'clear_search_management_button'):
            set_tooltip(self.clear_search_management_button, "Clear the management staff search field")
        if hasattr(self, 'filter_management_button'):
            set_tooltip(self.filter_management_button, "Filter management staff by criteria")
        if hasattr(self, 'clear_filter_management_button'):
            set_tooltip(self.clear_filter_management_button, "Clear all management staff filters")

        # Coaching sub-tab
        if hasattr(self, 'add_coaching_staff_button'):
            set_tooltip(self.add_coaching_staff_button, "Add a new coaching staff member")
        if hasattr(self, 'remove_coaching_staff_button'):
            set_tooltip(self.remove_coaching_staff_button, "Remove the selected coaching staff member")
        if hasattr(self, 'search_coaching_staff_input'):
            set_tooltip(self.search_coaching_staff_input, "Search for coaching staff members")
        if hasattr(self, 'clear_search_coaching_button'):
            set_tooltip(self.clear_search_coaching_button, "Clear the coaching staff search field")
        if hasattr(self, 'filter_coaching_staff_button'):
            set_tooltip(self.filter_coaching_staff_button, "Filter coaching staff by criteria")
        if hasattr(self, 'clear_filter_coaching_button'):
            set_tooltip(self.clear_filter_coaching_button, "Clear all coaching staff filters")

        # Medical sub-tab
        if hasattr(self, 'add_staff_button'):
            set_tooltip(self.add_staff_button, "Add a new medical staff member")
        if hasattr(self, 'remove_staff_button'):
            set_tooltip(self.remove_staff_button, "Remove the selected medical staff member")
        if hasattr(self, 'search_staff_input'):
            set_tooltip(self.search_staff_input, "Search for medical staff members")
        if hasattr(self, 'clear_search_button'):
            set_tooltip(self.clear_search_button, "Clear the medical staff search field")
        if hasattr(self, 'filter_staff_button'):
            set_tooltip(self.filter_staff_button, "Filter medical staff by criteria")
        if hasattr(self, 'clear_filter_button'):
            set_tooltip(self.clear_filter_button, "Clear all medical staff filters")

        # --- Kit Tab Tooltips ---
        # Colors sub-tab
        if hasattr(self, 'color_button_1st'):
            set_tooltip(self.color_button_1st, "Select the primary club color")
        if hasattr(self, 'color_button_2nd'):
            set_tooltip(self.color_button_2nd, "Select the secondary club color")
        if hasattr(self, 'color_button_3rd'):
            set_tooltip(self.color_button_3rd, "Select the tertiary club color")
        if hasattr(self, 'color_preview_frame'):
            set_tooltip(self.color_preview_frame, "Preview of how the club colors will look together")

        # Kit sub-tabs (1st, 2nd, 3rd, GK kits)
        for key in ["1st", "2nd", "3rd", "1st_gk", "2nd_gk"]:
            # Front image
            front_preview = getattr(self, f"kit_{key}_front_preview_label", None)
            if front_preview:
                set_tooltip(front_preview, f"Preview of the {key} kit front design")
            front_upload = getattr(self, f"kit_{key}_front_upload_button", None)
            if front_upload:
                set_tooltip(front_upload, f"Upload an image for the {key} kit front")
            front_remove = getattr(self, f"kit_{key}_front_remove_button", None)
            if front_remove:
                set_tooltip(front_remove, f"Remove the {key} kit front image")

            # Back image
            back_preview = getattr(self, f"kit_{key}_back_preview_label", None)
            if back_preview:
                set_tooltip(back_preview, f"Preview of the {key} kit back design")
            back_upload = getattr(self, f"kit_{key}_back_upload_button", None)
            if back_upload:
                set_tooltip(back_upload, f"Upload an image for the {key} kit back")
            back_remove = getattr(self, f"kit_{key}_back_remove_button", None)
            if back_remove:
                set_tooltip(back_remove, f"Remove the {key} kit back image")

        # --- Sponsors Tab Tooltips ---
        # Will be handled by the sponsors panel's own retranslateUi method

        # --- Team Groups Tab Tooltips ---
        # Will be handled by the groups widget's own retranslateUi method

        print("DEBUG: Finished applying detailed tooltips to Club window UI elements")

    # --- Close Event Method ---
    def changeEvent(self, event):
        """Handle language change events."""
        if event.type() == QEvent.Type.LanguageChange:
            print("ClubWindow: Language change event detected")
            self.retranslateUi()

    def eventFilter(self, obj, event):
        """Filter events to catch language change events."""
        if event.type() == QEvent.Type.LanguageChange:
            print("ClubWindow: Language change event detected through event filter")
            self.retranslateUi()

            # Force update of the current staff tab to ensure dropdown values are preserved
            if hasattr(self, 'staff_sub_tab_widget'):
                current_tab = self.staff_sub_tab_widget.currentIndex()
                print(f"Current staff sub-tab: {current_tab}")

                # Update the current tab's content
                if current_tab == 0:  # Management
                    # Get all rows with Type values and update them
                    if hasattr(self, 'management_staff_model') and hasattr(self, '_management_staff_types_source'):
                        model = self.management_staff_model
                        translated_types = [self.tr(t) for t in self._management_staff_types_source]
                        self._update_staff_dropdown_values(model, self._management_staff_types_source, translated_types)

                elif current_tab == 1:  # Coaching
                    # Get all rows with Type values and update them
                    if hasattr(self, 'coaching_staff_model') and hasattr(self, '_coaching_staff_types_source'):
                        model = self.coaching_staff_model
                        translated_types = [self.tr(t) for t in self._coaching_staff_types_source]
                        self._update_staff_dropdown_values(model, self._coaching_staff_types_source, translated_types)

                elif current_tab == 2:  # Medical
                    # Get all rows with Type values and update them
                    if hasattr(self, 'medical_staff_model') and hasattr(self, '_medical_staff_types_source'):
                        model = self.medical_staff_model
                        translated_types = [self.tr(t) for t in self._medical_staff_types_source]
                        self._update_staff_dropdown_values(model, self._medical_staff_types_source, translated_types)

            return True
        return super().eventFilter(obj, event)

    def _update_staff_dropdown_values(self, model, source_types, translated_types):
        """Update dropdown values in the staff table when language changes."""
        if not model:
            return

        # Get current language
        from PySide6.QtCore import QSettings
        settings = QSettings()
        current_language = settings.value("general/language", "en")
        print(f"Current language: {current_language}")

        # Debug print all source and translated types
        print("Source types (English):")
        for i, t in enumerate(source_types):
            print(f"  {i}: '{t}'")
        print("Translated types:")
        for i, t in enumerate(translated_types):
            print(f"  {i}: '{t}'")

        for row in range(model.rowCount()):
            type_index = model.index(row, 1)  # Column 1 is Type
            if type_index.isValid():
                # First try to get the source value from UserRole
                source_value = model.data(type_index, Qt.ItemDataRole.UserRole)
                current_value = model.data(type_index, Qt.ItemDataRole.EditRole)

                if source_value:
                    print(f"Row {row} has source value: '{source_value}'")

                    # Find the source value in the source types list
                    source_index = -1
                    for i, source_type in enumerate(source_types):
                        if source_type == source_value:
                            source_index = i
                            print(f"Found source value in source list at index {i}")
                            break

                    # If found, update with the appropriate value based on language
                    if source_index >= 0 and source_index < len(translated_types):
                        # If changing to English, use source value
                        if current_language == "en":
                            new_value = source_types[source_index]
                        # If changing to another language, use translated value
                        else:
                            new_value = translated_types[source_index]

                        print(f"Updating dropdown value from '{current_value}' to '{new_value}'")
                        model.setData(type_index, new_value, Qt.ItemDataRole.EditRole)
                        continue  # Skip the rest of the loop for this row

                # If no source value or not found, fall back to the old method
                if current_value:
                    print(f"Row {row} current value: '{current_value}' (no source value)")

                    # Try to find the value in both source and translated lists
                    source_index = -1

                    # First check if current value is in source list (English)
                    for i, source_type in enumerate(source_types):
                        # Use case-insensitive comparison
                        if source_type.lower() == current_value.lower():
                            source_index = i
                            print(f"Found in source list at index {i}")
                            break

                    # If not found in source list, check if it's in translated list
                    if source_index == -1:
                        for i, translated_type in enumerate(translated_types):
                            # Use case-insensitive comparison
                            if translated_type.lower() == current_value.lower():
                                source_index = i
                                print(f"Found in translated list at index {i}")
                                break

                    # If still not found, try partial matching for translated values
                    if source_index == -1:
                        print("Trying partial matching...")
                        best_match_index = -1
                        best_match_score = 0

                        for i, translated_type in enumerate(translated_types):
                            # Skip empty values
                            if not translated_type:
                                continue

                            # Check if current value contains translated type or vice versa
                            if translated_type.lower() in current_value.lower() or current_value.lower() in translated_type.lower():
                                # Calculate match score (length of common substring)
                                match_score = len(set(translated_type.lower()) & set(current_value.lower()))
                                if match_score > best_match_score:
                                    best_match_score = match_score
                                    best_match_index = i
                                    print(f"Potential match: '{translated_type}' with score {match_score}")

                        if best_match_index >= 0:
                            source_index = best_match_index
                            print(f"Found best partial match at index {best_match_index}")

                    # If found in any list, update with the appropriate value
                    if source_index >= 0 and source_index < len(translated_types):
                        # If changing to English, use source value
                        if current_language == "en":
                            new_value = source_types[source_index]
                        # If changing to another language, use translated value
                        else:
                            new_value = translated_types[source_index]

                        print(f"Updating dropdown value from '{current_value}' to '{new_value}'")
                        model.setData(type_index, new_value, Qt.ItemDataRole.EditRole)

                        # Also store the source value for future language changes
                        model.setData(type_index, source_types[source_index], Qt.ItemDataRole.UserRole)
                        print(f"Stored source value '{source_types[source_index]}' for future reference")

    def closeEvent(self, event):
        """Save data on window close and emit closed signal."""
        print(f"ClubWindow closing, saving data...")
        try:
            # Make sure to save sponsors data explicitly
            if hasattr(self, 'sponsors_panel') and self.sponsors_panel:
                print("Calling save_data on sponsors panel...")
                self.sponsors_panel.save_data()

            # Then save all other data
            self.save_data()

            # Check sponsor data was saved properly (debugging)
            if hasattr(self, 'sponsors_manager') and self.sponsors_manager:
                try:
                    test_data = self.sponsors_manager.get_sponsor_data("main", 0)
                    print(f"Verification check - main[0] sponsor name: '{test_data.get('sponsor_name', '')}', logo exists: {test_data.get('logo_exists', False)}")
                except Exception as e:
                    print(f"Error verifying sponsor data: {e}")
        except Exception as e:
            print(f"Error during closeEvent: {e}")

        print("ClubWindow closeEvent complete")
        event.accept()

    # --- Kit Settings Handlers & Preview Update ---
    def _get_kit_setting_attribute(self, kit_key, element, setting):
        """Helper to get a specific kit setting widget/label attribute."""
        # kit_key = 1st, 2nd, 3rd, 1st_gk, 2nd_gk
        # element = name, number
        # setting = color_button, font_combo, font_size_spinbox, vpos_spinbox, preview_label

        # Special case for preview labels which are only on the 'back'
        if setting == "preview_label":
            attr_name = f"kit_{kit_key}_back_{element}_preview_label"
        else:
            attr_name = f"kit_{kit_key}_{element}_{setting}"

        try:
            return getattr(self, attr_name)
        except AttributeError:
            print(f"Error: Could not find widget attribute '{attr_name}'.")
            return None

    def _kit_key_to_id(self, kit_key):
        """Converts a kit key string (e.g., '1st_gk') to its ID string (e.g., '4')."""
        key_map = {
            "1st": "1",
            "2nd": "2",
            "3rd": "3",
            "1st_gk": "4",
            "2nd_gk": "5"
        }
        kit_id = key_map.get(kit_key)
        if kit_id is None:
            print(f"Error: Invalid kit_key '{kit_key}' provided to _kit_key_to_id.")
            return None # Return None to indicate failure
        return kit_id

    def _update_kit_text_previews(self, kit_key):
        """Updates the name and number preview labels based on current settings."""
        kit_ids = {"1st": "1", "2nd": "2", "3rd": "3", "1st_gk": "4", "2nd_gk": "5"}
        kit_id = kit_ids.get(kit_key)
        if not kit_id: return # Invalid kit_key

        name_label = self._get_kit_setting_attribute(kit_key, "name", "preview_label")
        number_label = self._get_kit_setting_attribute(kit_key, "number", "preview_label")
        back_preview = getattr(self, f"kit_{kit_key}_back_preview_label", None) # Parent label

        if not name_label or not number_label or not back_preview:
             print(f"Error: Missing preview labels for kit '{kit_key}'")
             return

        parent_width = back_preview.width() # Use actual parent width
        parent_height = back_preview.height()

        # --- Update Name Preview ---
        name_color = self.club_data_manager.get_data(f"kit_{kit_id}_name_color", "#000000")
        name_family = self.club_data_manager.get_data(f"kit_{kit_id}_name_font_family", "Arial")
        name_size = self.club_data_manager.get_data(f"kit_{kit_id}_name_font_size", 10)
        name_vpos = self.club_data_manager.get_data(f"kit_{kit_id}_name_vpos", 0)

        name_font = QFont(name_family, name_size, QFont.Weight.Bold)
        name_label.setFont(name_font)
        # REMOVED name_label.setStyleSheet(f"color: {name_color}; background-color: transparent;")
        # Set properties on OutlinedLabel for name
        name_label.setTextColor(QColor(name_color))
        name_outline_enabled = self.club_data_manager.get_data(f"kit_{kit_id}_name_outline_enabled", False)
        name_label.setOutlineEnabled(name_outline_enabled)
        if name_outline_enabled:
            name_outline_color = self.club_data_manager.get_data(f"kit_{kit_id}_name_outline_color", "#FFFFFF")
            name_outline_thickness = self.club_data_manager.get_data(f"kit_{kit_id}_name_outline_thickness", 1)
            name_label.setOutlineColor(QColor(name_outline_color))
            name_label.setOutlineThickness(name_outline_thickness)
        # REMOVED outline effect logic

        # --- Update Number Preview ---
        number_color = self.club_data_manager.get_data(f"kit_{kit_id}_number_color", "#000000")
        number_family = self.club_data_manager.get_data(f"kit_{kit_id}_number_font_family", "Arial")
        number_size = self.club_data_manager.get_data(f"kit_{kit_id}_number_font_size", 24)
        number_vpos = self.club_data_manager.get_data(f"kit_{kit_id}_number_vpos", 0)

        number_font = QFont(number_family, number_size, QFont.Weight.Bold)
        number_label.setFont(number_font)
        # REMOVED number_label.setStyleSheet(f"color: {number_color}; background-color: transparent;")
        # Set properties on OutlinedLabel for number
        number_label.setTextColor(QColor(number_color))
        number_outline_enabled = self.club_data_manager.get_data(f"kit_{kit_id}_number_outline_enabled", False)
        number_label.setOutlineEnabled(number_outline_enabled)
        if number_outline_enabled:
            number_outline_color = self.club_data_manager.get_data(f"kit_{kit_id}_number_outline_color", "#FFFFFF")
            number_outline_thickness = self.club_data_manager.get_data(f"kit_{kit_id}_number_outline_thickness", 1)
            number_label.setOutlineColor(QColor(number_outline_color))
            number_label.setOutlineThickness(number_outline_thickness)
        # REMOVED outline effect logic

        # --- Reposition Labels ---
        def position_label(label, base_y_factor, v_offset):
            label.adjustSize()
            size = label.sizeHint()
            x = (parent_width - size.width()) / 2
            # Calculate base Y position centered around the factor
            base_y = (parent_height * base_y_factor) - (size.height() / 2)
            # Apply vertical offset relative to the base position
            y = base_y + v_offset
            # Ensure label stays within parent bounds (optional, but good practice)
            y = max(0, min(y, parent_height - size.height()))
            x = max(0, min(x, parent_width - size.width()))
            label.move(int(x), int(y))

        # Use the same factors as in init_ui for base positioning
        num_base_y_factor = 0.55 # Base position factor for number
        name_base_y_factor = 0.30 # Base position factor for name

        # Position number using its base factor and its specific vpos
        position_label(number_label, num_base_y_factor, number_vpos)

        # Position name using its base factor and its specific vpos
        position_label(name_label, name_base_y_factor, name_vpos)

    def _handle_kit_color_pick(self, kit_key, element):
        """Handles picking a color for kit name or number."""
        data_key = f"kit_{self._kit_key_to_id(kit_key)}_{element}_color"
        current_color_hex = self.club_data_manager.get_data(data_key, "#000000") # Default to black
        current_color = QColor(current_color_hex)

        color_dialog = QColorDialog(current_color, self)
        if color_dialog.exec():
            new_color = color_dialog.selectedColor()
            if new_color.isValid():
                new_hex = new_color.name()
                # Update data manager
                self.club_data_manager.set_data(data_key, new_hex)
                # Update the button style
                color_button = self._get_kit_setting_attribute(kit_key, element, "color_button")
                if color_button:
                    color_button.setStyleSheet(f"background-color: {new_hex}; border: 1px solid black;")
                # Update preview
                self._update_kit_text_previews(kit_key)

    def _handle_kit_font_change(self, font, kit_key, element):
        """Handles font family change for kit name or number."""
        data_key = f"kit_{self._kit_key_to_id(kit_key)}_{element}_font_family"
        font_family = font.family()
        self.club_data_manager.set_data(data_key, font_family)
        self._update_kit_text_previews(kit_key)

    def _handle_kit_spinbox_change(self, value, kit_key, element, setting_type):
        """Handles font size or vpos change for kit name or number."""
        data_key = f"kit_{self._kit_key_to_id(kit_key)}_{element}_{setting_type}"
        self.club_data_manager.set_data(data_key, value)
        self._update_kit_text_previews(kit_key)

    # --- Kit Outline Setting Handlers --- NEW METHODS MOVED HERE ---
    def _handle_kit_outline_toggled(self, state, kit_key, element):
        """Handles the outline checkbox being toggled for name or number."""
        kit_id = self._kit_key_to_id(kit_key)
        data_key = f"kit_{kit_id}_{element}_outline_enabled"
        is_enabled = bool(state == Qt.CheckState.Checked.value) # Convert state to boolean
        self.club_data_manager.set_data(data_key, is_enabled)

        # Show/hide the color button and thickness spinbox widgets
        color_button = self._get_kit_setting_attribute(kit_key, element, "outline_color_button")
        thickness_spinbox = self._get_kit_setting_attribute(kit_key, element, "outline_thickness_spinbox")
        # if color_button: color_button.setVisible(is_enabled) # We hide the whole row instead
        # if thickness_spinbox: thickness_spinbox.setVisible(is_enabled) # We hide the whole row instead

        # Show/hide the entire form layout rows for Outline Color and Thickness
        settings_gb = self._get_kit_setting_attribute(kit_key, element, "settings_group_box")
        if settings_gb and isinstance(settings_gb.layout(), QFormLayout):
            form_layout = settings_gb.layout()
            # Row indices based on order in init_ui:
            # 0=Color, 1=Family, 2=Size, 3=VPos, 4=Enable, 5=OutlineColor, 6=OutlineThickness
            outline_color_row_index = 5
            outline_thickness_row_index = 6
            try:
                form_layout.setRowVisible(outline_color_row_index, is_enabled)
                form_layout.setRowVisible(outline_thickness_row_index, is_enabled)
            except Exception as e:
                 print(f"Error setting row visibility for {kit_key}/{element} using indices: {e}")

        # Update preview
        self._update_kit_text_previews(kit_key)

    def _handle_kit_outline_color_pick(self, kit_key, element):
        """Handles picking a color for the kit name or number outline."""
        kit_id = self._kit_key_to_id(kit_key)
        data_key = f"kit_{kit_id}_{element}_outline_color"
        current_color_hex = self.club_data_manager.get_data(data_key, "#FFFFFF") # Default outline to white
        current_color = QColor(current_color_hex)

        color_dialog = QColorDialog(current_color, self)
        if color_dialog.exec():
            new_color = color_dialog.selectedColor()
            if new_color.isValid():
                new_hex = new_color.name()
                self.club_data_manager.set_data(data_key, new_hex)
                # Update the button style
                color_button = self._get_kit_setting_attribute(kit_key, element, "outline_color_button")
                if color_button:
                    color_button.setStyleSheet(f"background-color: {new_hex}; border: 1px solid black;")
                # Update preview
                self._update_kit_text_previews(kit_key)

    def _handle_kit_outline_thickness_change(self, value, kit_key, element):
        """Handles the outline thickness spinbox value changing."""
        kit_id = self._kit_key_to_id(kit_key)
        data_key = f"kit_{kit_id}_{element}_outline_thickness"
        self.club_data_manager.set_data(data_key, value)
        # Update preview
        self._update_kit_text_previews(kit_key)
    # --- End Kit Outline Setting Handlers ---

    # --- Preview Update Methods ---
    def _update_logo_preview(self):
        """Updates the QLabel with the current logo or the default logo."""
        pixmap_loaded = False
        image_path_to_load = None
        # Ensure this is always a boolean
        is_specific_logo_set = bool(self.current_logo_path and os.path.exists(self.current_logo_path))

        if is_specific_logo_set:
            image_path_to_load = self.current_logo_path
        else:
            # Try loading the default logo if no specific one is set/found
            # Use path relative to this script file for robustness
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.abspath(os.path.join(script_dir, '..', '..')) # Go up two levels
            default_logo_path = os.path.join(project_root, "media", "club_logo", "default.png")
            if os.path.exists(default_logo_path):
                image_path_to_load = default_logo_path

        if image_path_to_load:
            try:
                pixmap = QPixmap(image_path_to_load)
                print(f"DEBUG: Logo Pixmap loaded from {image_path_to_load}. Is null? {pixmap.isNull()}") # Debug print
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(QSize(150, 150), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    self.logo_preview_label.setPixmap(scaled_pixmap)
                    # self.logo_preview_label.setText("") # Handled by retranslateUi
                    self.logo_preview_label.setStyleSheet("") # Remove placeholder style
                    pixmap_loaded = True
                else:
                    print(f"Warning: Could not load pixmap from {image_path_to_load}")
            except Exception as e:
                print(f"Error loading logo preview from {image_path_to_load}: {e}")

        if not pixmap_loaded:
            # self.logo_preview_label.setText(self.tr("No Logo")) # Handled by retranslateUi
            self.logo_preview_label.setPixmap(QPixmap()) # Clear pixmap
            self.logo_preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;") # Restore placeholder style

        # Enable remove button only if a specific logo (not default) is currently set
        self.remove_logo_button.setEnabled(is_specific_logo_set)

        # Update logo preview text and constraints label based on current state
        logo_exists = self.current_logo_path and os.path.exists(self.current_logo_path)
        default_logo_exists = os.path.exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', "media", "club_logo", "default.png"))

        if logo_exists:
            self.logo_constraints_label.setText(self.tr("Logo: {}").format(os.path.basename(self.current_logo_path)))
            self.logo_preview_label.setText("") # Clear text if specific image is shown
        elif default_logo_exists:
             # If only default exists, show original constraints and clear preview text
            self.logo_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))
            self.logo_preview_label.setText("") # Clear text if default image is shown
        else:
            # If neither exists, show constraints and placeholder text
            self.logo_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))
            self.logo_preview_label.setText(self.tr("No Logo")) # Set placeholder text
        # Update remove button tooltip
        self.remove_logo_button.setToolTip(self.tr("Remove Logo"))

    def _update_stadium_preview(self):
        """Updates the QLabel with the current stadium image or the default."""
        pixmap_loaded = False
        image_path_to_load = None
        # Ensure this is always a boolean
        is_specific_image_set = bool(self.current_stadium_image_path and os.path.exists(self.current_stadium_image_path))

        if is_specific_image_set:
            image_path_to_load = self.current_stadium_image_path
        else:
            # Try loading the default stadium image
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.abspath(os.path.join(script_dir, '..', '..')) # Go up two levels
            default_stadium_path = os.path.join(project_root, "media", "stadium", "default.png")
            if os.path.exists(default_stadium_path):
                image_path_to_load = default_stadium_path

        if image_path_to_load:
            try:
                pixmap = QPixmap(image_path_to_load)
                print(f"DEBUG: Stadium Pixmap loaded from {image_path_to_load}. Is null? {pixmap.isNull()}") # Debug print
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(QSize(150, 150), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    self.stadium_preview_label.setPixmap(scaled_pixmap)
                    # self.stadium_preview_label.setText("") # Handled by retranslateUi
                    self.stadium_preview_label.setStyleSheet("")
                    pixmap_loaded = True
                else:
                    print(f"Warning: Could not load pixmap from {image_path_to_load}")
            except Exception as e:
                print(f"Error loading stadium preview from {image_path_to_load}: {e}")

        if not pixmap_loaded:
            # self.stadium_preview_label.setText(self.tr("No Stadium Image")) # Handled by retranslateUi
            self.stadium_preview_label.setPixmap(QPixmap())
            self.stadium_preview_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")

        # Enable remove button only if a specific stadium image is set
        self.remove_stadium_button.setEnabled(is_specific_image_set)

        # Update stadium preview text and constraints label based on current state
        stadium_image_exists = self.current_stadium_image_path and os.path.exists(self.current_stadium_image_path)
        default_stadium_exists = os.path.exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', "media", "stadium", "default.png"))

        if stadium_image_exists:
             self.stadium_constraints_label.setText(self.tr("Stadium Image: {}").format(os.path.basename(self.current_stadium_image_path)))
             self.stadium_preview_label.setText("") # Clear text if specific image is shown
        elif default_stadium_exists:
             # If only default exists, show original constraints and clear preview text
             self.stadium_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))
             self.stadium_preview_label.setText("") # Clear text if default image is shown
        else:
             # If neither exists, show constraints and placeholder text
             self.stadium_constraints_label.setText(self.tr("(PNG, <500KB, <300x300px)"))
             self.stadium_preview_label.setText(self.tr("No Stadium Image")) # Set placeholder text
        # Update stadium remove button tooltip
        self.remove_stadium_button.setToolTip(self.tr("Remove Stadium Image"))

    def _search_medical_staff(self, text):
        """Filters the medical staff table based on the search text."""
        search_term = text.lower()
        for row in range(self.medical_staff_model.rowCount()):
            match = False
            if not search_term: # Show all if search is empty
                match = True
            else:
                for col in range(self.medical_staff_model.columnCount()):
                    item = self.medical_staff_model.item(row, col)
                    if item and search_term in item.text().lower():
                        match = True
                        break # Found match in this row
            self.medical_staff_table.setRowHidden(row, not match)

    def _clear_medical_search(self):
        """Clears the search input and shows all rows (respecting filters)."""
        self.search_staff_input.clear()
        # Re-apply filters to ensure visibility is correct after clearing search
        # The search function itself should ideally be combined/aware of the filter
        self._apply_medical_staff_filter()

    # --- Placeholder Methods for Coaching Staff ---
    def _add_coaching_staff(self):
        model = self.coaching_staff_model
        if model is None:
            print("Error: coaching_staff_model not initialized.")
            return

        # --- ID Generation Logic (Copied & Adapted from Medical) --- #
        current_max_id = 0
        for row in range(model.rowCount()):
            id_item = model.item(row, 0) # ID is column 0
            if id_item:
                try:
                    # Use 'C' prefix for Coaching
                    num_part = int(id_item.text().lstrip('C'))
                    current_max_id = max(current_max_id, num_part)
                except ValueError:
                    pass # Ignore non-standard IDs
        new_id = f"C{current_max_id + 1:03d}" # Format as C001, C002 etc.
        # --- End ID Generation ---

        # Create row items, starting with the new ID
        row_items = [QStandardItem(new_id)] + [QStandardItem("") for _ in range(model.columnCount() - 1)]
        new_row_index = model.rowCount()
        model.insertRow(new_row_index, row_items) # Use insertRow with items

        # Open persistent editors for Type and Country in the new row
        type_index = model.index(new_row_index, 1) # Col 1 = Type
        country_index = model.index(new_row_index, 7) # Col 7 = Country
        if type_index.isValid(): self.coaching_staff_table.openPersistentEditor(type_index)
        if country_index.isValid(): self.coaching_staff_table.openPersistentEditor(country_index)

        # Scroll to the newly added row
        self.coaching_staff_table.scrollToBottom()
        # --- ADD Immediate Save --- #
        self._save_staff_list_to_manager('coaching')
        # -------------------------- #

    def _remove_coaching_staff(self):
        # print("DEBUG: Remove Coaching Staff button clicked") # Keep debug for now
        selection_model = self.coaching_staff_table.selectionModel()
        if not selection_model or not selection_model.hasSelection():
            return # No cell selected or invalid selection model

        # Get selected indexes (could be individual cells)
        selected_indexes = selection_model.selectedIndexes()
        if not selected_indexes:
            return

        # Get unique rows from selected cells
        selected_rows = list(set(index.row() for index in selected_indexes))
        if not selected_rows:
            return

        # Check if any of the selected staff members are used in team groups
        for row in selected_rows:
            staff_id_item = self.coaching_staff_model.item(row, 0)  # ID is in column 0
            staff_name_item = self.coaching_staff_model.item(row, 2)  # Name is in column 2

            if staff_id_item and staff_name_item:
                staff_id = staff_id_item.data(Qt.ItemDataRole.DisplayRole)
                staff_name = staff_name_item.data(Qt.ItemDataRole.DisplayRole)

                # Convert staff_id to int if it's a string
                try:
                    staff_id = int(staff_id) if staff_id else None
                except (ValueError, TypeError):
                    staff_id = None

                if staff_id:
                    # Check if this staff member is used in any team group
                    team_groups_widget = self.findChild(QWidget, "TeamGroupsWidget")
                    if team_groups_widget and hasattr(team_groups_widget, "is_staff_id_used_in_groups"):
                        is_used, group_names = team_groups_widget.is_staff_id_used_in_groups(staff_id, 'coaching')
                        if is_used:
                            # Format the group names for display
                            groups_text = ", ".join(group_names)
                            QMessageBox.warning(
                                self,
                                self.tr("Cannot Remove Staff Member"),
                                self.tr("Cannot remove staff member '{name}' because they are assigned to the following team group(s): {groups}. "
                                        "Please remove the staff member from these groups first.")
                                .format(name=staff_name, groups=groups_text)
                            )
                            return  # Don't proceed with deletion

        # Remove rows in reverse order to avoid index issues if multiple rows are selected
        for row in sorted(selected_rows, reverse=True):
             self.coaching_staff_model.removeRow(row)
        # --- ADD Immediate Save --- #
        self._save_staff_list_to_manager('coaching')
        # -------------------------- #

    def _search_coaching_staff(self, text):
        """Filters the coaching staff table based on the search text AND existing filters.""" # UPDATED Docstring
        print(f"DEBUG: Search Coaching Staff: {text}")
        # Apply simple filtering based on search text
        # No longer directly hides/shows, just trigger combined filter application
        self._apply_coaching_staff_filter() # Call apply filter which uses the search text

    def _clear_coaching_search(self):
        """Clears the search input and reapplies filters.""" # UPDATED Docstring
        print("DEBUG: Clear Coaching Search clicked")
        if hasattr(self, 'search_coaching_staff_input'):
             self.search_coaching_staff_input.clear()
        # Re-apply filters after clearing search
        self._apply_coaching_staff_filter()
    # --- End Placeholder Methods ---

    def _open_persistent_coaching_editors(self, parent, first, last):
        """Opens persistent editors for the 'Type' column in newly inserted rows."""
        # print(f"DEBUG: rowsInserted signal received for rows {first} to {last}") # Debug
        for row in range(first, last + 1):
            type_index = self.coaching_staff_model.index(row, 1) # Column 1 is 'Type'
            if type_index.isValid():
                # print(f"DEBUG: Opening persistent editor for row {row}, col 1") # Debug
                self.coaching_staff_table.openPersistentEditor(type_index)

    def _filter_medical_staff(self):
        """Opens a dialog to set filters and applies them to the medical staff table."""
        print("DEBUG: Filter Medical Staff button clicked")

        # Prepare data for the dialog
        # Get translated types and placeholder for the combo box
        translated_types = [self.tr(t) for t in self._medical_staff_types_source]
        # REMOVED translated_placeholder = self.tr("-- Select Type --") # Placeholder handled by dialog itself

        # --- NEW: Build filterable columns metadata ---
        filterable_columns = {}
        for header in self.medical_staff_headers:
            if header == "ID": continue # Skip ID for filtering

            field_key = header # Use header as the key
            label_text = self.tr(header)
            field_type = 'text' # Default to text
            options = None

            if header == "Type":
                field_type = 'combo'
                options = translated_types

            filterable_columns[field_key] = {
                'type': field_type,
                'label': label_text
            }
            if options:
                filterable_columns[field_key]['options'] = options
        # --- END NEW ---

        dialog_data = self.medical_staff_filters.copy() # Start with current filters
        # REMOVED dialog_data['_medical_staff_types'] = translated_types
        # REMOVED dialog_data['_translated_placeholder'] = translated_placeholder
        # --- ADD Metadata ---
        dialog_data['_filterable_columns'] = filterable_columns
        # Optional: Define display order if needed
        # dialog_data['_column_order'] = [h for h in self.medical_staff_headers if h != 'ID']

        dialog = StaffFilterDialog(
            # headers=self.medical_staff_headers, # Legacy, no longer needed by dialog
            current_filters=dialog_data,
            parent=self
        )

        if dialog.exec(): # Use exec() which returns DialogCode
            new_filters = dialog.get_filters()
            print(f"DEBUG: Dialog accepted. New filters: {new_filters}")
            self.medical_staff_filters = new_filters
            self._apply_medical_staff_filter()
        else:
            print("DEBUG: Dialog cancelled or rejected.")
            # If rejected, filters remain unchanged.
            # If cleared via 'Clear Filters', get_filters would be empty if accept() was called there.
            # Since accept() isn't called on clear, we don't need special handling here.

    def _apply_medical_staff_filter(self):
        """Applies the current filters stored in self.medical_staff_filters to the table."""
        print(f"DEBUG: Applying filters: {self.medical_staff_filters}")
        model = self.medical_staff_model
        if not model: return

        # Create a mapping from header name to column index for easier lookup
        header_to_index = {header: i for i, header in enumerate(self.medical_staff_headers)}

        active_filter_indices = {header_to_index[header]: value.lower()
                                 for header, value in self.medical_staff_filters.items()
                                 if header in header_to_index}

        for row in range(model.rowCount()):
            show_row = True # Assume row is visible unless a filter fails
            if not active_filter_indices: # No filters active, show all
                show_row = True
            else:
                for col_index, filter_value in active_filter_indices.items():
                    item = model.item(row, col_index)
                    cell_value = item.text().lower() if item else ""

                    # Basic substring matching for now
                    if filter_value not in cell_value:
                        show_row = False
                        break # No need to check other columns for this row

            self.medical_staff_table.setRowHidden(row, not show_row)

    def _clear_medical_staff_filter(self):
        """Clears the stored medical staff filters and updates the table view."""
        print("DEBUG: Clearing medical staff filters.")
        self.medical_staff_filters = {}
        self._apply_medical_staff_filter()

    def _clear_medical_search(self):
        """Clears the search input and shows all rows (respecting filters)."""
        self.search_staff_input.clear()
        # Re-apply filters to ensure visibility is correct after clearing search
        # The search function itself should ideally be combined/aware of the filter
        self._apply_medical_staff_filter()

    def _filter_coaching_staff(self):
        """Opens a dialog to set filters and applies them to the coaching staff table."""
        print("DEBUG: Filter Coaching Staff button clicked")
        # --- TEMPORARILY COMMENT OUT FOR DEBUGGING ---
        # # Prepare data for the dialog
        # translated_types = [self.tr(t) for t in self._coaching_staff_types_source]
        #
        # # Build filterable columns metadata
        # filterable_columns = {}
        # for header in self.coaching_staff_headers:
        #     if header == "ID": continue # Skip ID
        #
        #     field_key = header
        #     label_text = self.tr(header)
        #     field_type = 'text'
        #     options = None
        #
        #     if header == "Type":
        #         field_type = 'combo'
        #         options = translated_types
        #     elif header == "Country":
        #         field_type = 'combo'
        #         options = countries # Use imported list
        #
        #     filterable_columns[field_key] = {
        #         'type': field_type,
        #         'label': label_text
        #     }
        #     if options:
        #         filterable_columns[field_key]['options'] = options
        #
        # dialog_data = self.coaching_staff_filters.copy()
        # dialog_data['_filterable_columns'] = filterable_columns
        #
        # dialog = StaffFilterDialog(
        #     current_filters=dialog_data,
        #     parent=self
        # )
        #
        # if dialog.exec():
        #     new_filters = dialog.get_filters()
        #     print(f"DEBUG: Coaching Dialog accepted. New filters: {new_filters}")
        #     self.coaching_staff_filters = new_filters
        #     self._apply_coaching_staff_filter()
        # else:
        #     print("DEBUG: Coaching Dialog cancelled or rejected.")
        # --- END TEMPORARY COMMENT OUT ---

    def _apply_coaching_staff_filter(self):
        """Applies the current coaching filters AND search term to the table."""
        print(f"DEBUG: Applying coaching filters: {self.coaching_staff_filters}")
        model = self.coaching_staff_model
        if not model: return

        header_to_index = {header: i for i, header in enumerate(self.coaching_staff_headers)}
        active_filter_indices = {header_to_index[header]: value.lower()
                                 for header, value in self.coaching_staff_filters.items()
                                 if header in header_to_index}

        current_search_term = self.search_coaching_staff_input.text().lower() if hasattr(self, 'search_coaching_staff_input') else ""

        for row in range(model.rowCount()):
            filter_match = True # Assume matches filters
            search_match = True # Assume matches search

            # Check filters
            if active_filter_indices:
                for col_index, filter_value in active_filter_indices.items():
                    item = model.item(row, col_index)
                    cell_value = item.text().lower() if item else ""
                    # Exact match for combo boxes (Type, Country)
                    if col_index == 1 or col_index == 7:
                        if filter_value != cell_value:
                            filter_match = False
                            break
                    elif filter_value not in cell_value: # Substring match for others
                        filter_match = False
                        break

            # Check search term if filters match (or no filters)
            if filter_match and current_search_term:
                search_match = False # Assume no match until found
                for col in range(model.columnCount()):
                    item = model.item(row, col)
                    if item and current_search_term in item.text().lower():
                        search_match = True
                        break

            # Hide row if it doesn't match filters OR doesn't match search
            self.coaching_staff_table.setRowHidden(row, not (filter_match and search_match))

    def _clear_coaching_staff_filter(self):
        """Clears the stored coaching staff filters and updates the table view."""
        print("DEBUG: Clearing coaching staff filters.")
        self.coaching_staff_filters = {}
        self._apply_coaching_staff_filter() # Re-apply (now empty) filters and current search

    def _add_management_staff(self):
        model = self.management_staff_model
        if model is None:
            print("Error: management_staff_model not initialized.")
            return

        # --- ID Generation Logic (Copied & Adapted from Medical) --- # ADDED
        current_max_id = 0
        for row in range(model.rowCount()):
            id_item = model.item(row, 0) # ID is column 0
            if id_item:
                try:
                    # Use 'MG' prefix for Management
                    num_part = int(id_item.text().lstrip('MG'))
                    current_max_id = max(current_max_id, num_part)
                except ValueError:
                    # Try 'M' prefix for backward compatibility? Or just ignore.
                    try:
                        num_part = int(id_item.text().lstrip('M'))
                        current_max_id = max(current_max_id, num_part)
                    except ValueError:
                        pass # Ignore other non-standard IDs
        new_id = f"MG{current_max_id + 1:03d}" # Format as MG001, MG002 etc.
        # --- End ID Generation --- # ADDED

        # Create row items, starting with the new ID # MODIFIED
        row_items = [QStandardItem(new_id)] + [QStandardItem("") for _ in range(model.columnCount() - 1)]
        new_row_index = model.rowCount()
        model.insertRow(new_row_index, row_items) # Use insertRow with items

        # Open persistent editors for Type and Country in the new row # Already Existing
        type_index = model.index(new_row_index, 1) # Col 1 = Type
        country_index = model.index(new_row_index, 7) # Col 7 = Country
        if type_index.isValid(): self.management_staff_table.openPersistentEditor(type_index)
        if country_index.isValid(): self.management_staff_table.openPersistentEditor(country_index)

        # Scroll to the newly added row
        self.management_staff_table.scrollToBottom()
        # --- ADD Immediate Save --- #
        self._save_staff_list_to_manager('management')
        # -------------------------- #

    def _remove_management_staff(self):
        # print("DEBUG: Remove Management Staff button clicked") # Keep debug for now
        selection_model = self.management_staff_table.selectionModel()
        if not selection_model or not selection_model.hasSelection():
            return # No cell selected or invalid selection model

        # Get selected indexes (could be individual cells)
        selected_indexes = selection_model.selectedIndexes()
        if not selected_indexes:
            return

        # Get unique rows from selected cells
        selected_rows = list(set(index.row() for index in selected_indexes))
        if not selected_rows:
            return

        # Check if any of the selected staff members are used in team groups
        for row in selected_rows:
            staff_id_item = self.management_staff_model.item(row, 0)  # ID is in column 0
            staff_name_item = self.management_staff_model.item(row, 2)  # Name is in column 2

            if staff_id_item and staff_name_item:
                staff_id = staff_id_item.data(Qt.ItemDataRole.DisplayRole)
                staff_name = staff_name_item.data(Qt.ItemDataRole.DisplayRole)

                # Convert staff_id to int if it's a string
                try:
                    staff_id = int(staff_id) if staff_id else None
                except (ValueError, TypeError):
                    staff_id = None

                if staff_id:
                    # Check if this staff member is used in any team group
                    team_groups_widget = self.findChild(QWidget, "TeamGroupsWidget")
                    if team_groups_widget and hasattr(team_groups_widget, "is_staff_id_used_in_groups"):
                        is_used, group_names = team_groups_widget.is_staff_id_used_in_groups(staff_id, 'management')
                        if is_used:
                            # Format the group names for display
                            groups_text = ", ".join(group_names)
                            QMessageBox.warning(
                                self,
                                self.tr("Cannot Remove Staff Member"),
                                self.tr("Cannot remove staff member '{name}' because they are assigned to the following team group(s): {groups}. "
                                        "Please remove the staff member from these groups first.")
                                .format(name=staff_name, groups=groups_text)
                            )
                            return  # Don't proceed with deletion

        # Remove rows in reverse order to avoid index issues if multiple rows are selected
        for row in sorted(selected_rows, reverse=True):
             self.management_staff_model.removeRow(row)
        # --- ADD Immediate Save --- #
        self._save_staff_list_to_manager('management')
        # -------------------------- #

    def _search_management_staff(self, text):
        """Filters the management staff table based on the search text."""
        print(f"DEBUG: Search Management Staff: {text}")
        # This simple search ignores the persistent filters.
        # A better implementation would combine search and filter logic.
        search_term = text.lower()
        for row in range(self.management_staff_model.rowCount()):
            match = False
            if not search_term:
                match = True
            else:
                for col in range(self.management_staff_model.columnCount()):
                    item = self.management_staff_model.item(row, col)
                    if item and search_term in item.text().lower():
                        match = True
                        break
            # Apply search visibility *without* overriding filter visibility initially
            # We only HIDE based on search, we don't RE-SHOW based on search clearing.
            if not match:
                 self.management_staff_table.setRowHidden(row, True)
            elif not self.management_staff_filters: # If no filters are active, clearing search shows row
                 self.management_staff_table.setRowHidden(row, False)
            else: # If filters are active, clearing search doesn't guarantee showing the row
                 # Re-apply filters to determine final visibility
                 self._apply_management_staff_filter() # This might be inefficient if called often
                 pass # Let filter logic decide

    def _clear_management_search(self):
        """Clears the management search input and reapplies filters."""
        print("DEBUG: Clear Management Search clicked")
        if hasattr(self, 'search_management_input'):
             self.search_management_input.clear()
        # Re-apply filters after clearing search
        self._apply_management_staff_filter()

    def _filter_management_staff(self):
        """Opens a dialog to set filters and applies them to the management staff table."""
        print("DEBUG: Filter Management Staff button clicked")
        # Get translated types for the dialog's Type combo box
        translated_types = [self.tr(t) for t in self._management_staff_types_source]
        # REMOVED translated_placeholder = self.tr("-- Select Type --") # Use translated placeholder # Handled by dialog

        # --- NEW: Build filterable columns metadata ---
        filterable_columns = {}
        for header in self.management_staff_headers:
            if header == "ID": continue # Skip ID

            field_key = header
            label_text = self.tr(header)
            field_type = 'text'
            options = None

            if header == "Type":
                field_type = 'combo'
                options = translated_types
            elif header == "Country":
                field_type = 'combo'
                options = countries # Use imported list

            filterable_columns[field_key] = {
                'type': field_type,
                'label': label_text
            }
            if options:
                filterable_columns[field_key]['options'] = options
        # --- END NEW ---

        dialog_data = self.management_staff_filters.copy()
        # REMOVED dialog_data['_medical_staff_types'] = translated_types
        # REMOVED dialog_data['_translated_placeholder'] = translated_placeholder
        # --- ADD Metadata ---
        dialog_data['_filterable_columns'] = filterable_columns
        # Optional: Define display order
        # dialog_data['_column_order'] = [h for h in self.management_staff_headers if h != 'ID']

        dialog = StaffFilterDialog(
            # headers=self.management_staff_headers, # Legacy
            current_filters=dialog_data,
            parent=self
        )

        if dialog.exec():
            new_filters = dialog.get_filters()
            print(f"DEBUG: Management Dialog accepted. New filters: {new_filters}")
            self.management_staff_filters = new_filters
            self._apply_management_staff_filter()
        else:
            print("DEBUG: Management Dialog cancelled or rejected.")

    def _apply_management_staff_filter(self):
        """Applies the current management filters AND search term to the table."""
        print(f"DEBUG: Applying management filters: {self.management_staff_filters}")
        model = self.management_staff_model
        if not model: return

        header_to_index = {header: i for i, header in enumerate(self.management_staff_headers)}
        active_filter_indices = {header_to_index[header]: value.lower()
                                 for header, value in self.management_staff_filters.items()
                                 if header in header_to_index}

        current_search_term = self.search_management_input.text().lower() if hasattr(self, 'search_management_input') else ""

        for row in range(model.rowCount()):
            filter_match = True # Assume matches filters unless proven otherwise
            search_match = True # Assume matches search unless proven otherwise

            # Check filters
            if active_filter_indices:
                for col_index, filter_value in active_filter_indices.items():
                    item = model.item(row, col_index)
                    cell_value = item.text().lower() if item else ""
                    # Special check for ComboBox columns (Type, Country)
                    if col_index == 1 or col_index == 7: # Indices for Type and Country
                        if filter_value != cell_value: # Exact match for combo boxes
                            filter_match = False
                            break
                    elif filter_value not in cell_value: # Substring match for others
                        filter_match = False
                        break

            # Check search term if filters match (or no filters are active)
            if filter_match and current_search_term:
                search_match = False # Assume no match until found
                for col in range(model.columnCount()):
                    item = model.item(row, col)
                    if item and current_search_term in item.text().lower():
                        search_match = True
                        break

            # Hide row if it doesn't match filters OR doesn't match search term
            self.management_staff_table.setRowHidden(row, not (filter_match and search_match))

    def _clear_management_staff_filter(self):
        """Clears the stored management staff filters and updates the table view."""
        print("DEBUG: Clearing management staff filters.")
        self.management_staff_filters = {}
        self._apply_management_staff_filter() # Re-apply (now empty) filters and current search term

    # --- Coaching Staff Methods ---
    def _filter_coaching_staff(self):
        pass  # TODO: Implement coaching staff filtering

    def get_all_players(self):
        """Retrieves all players from the database, including calculated age.

        Returns:
            list: A list of dictionaries, where each dictionary represents a player.
                  Returns an empty list if no players are found or an error occurs.
        """
        if not self.conn:
            print("Cannot get players: No database connection.")
            return []

        sql = f"SELECT {', '.join(self.PLAYER_COLUMNS)} FROM players ORDER BY last_name, first_name"
        players = []
        try:
            self.cursor.execute(sql)
            rows = self.cursor.fetchall() # <--- This fetches ALL rows
            for row in rows:
                player_dict = dict(row)
                player_dict['age'] = self.calculate_age(player_dict.get('dob'))
                players.append(player_dict)
            return players # <--- Returns the full list
        except sqlite3.Error as e:
            print(f"Error getting all players: {e}")
            return [] # Return empty list on error

    # --- ADD Helper Method to Save Staff List --- #
    def _save_staff_list_to_manager(self, staff_type):
        """Extracts staff data from the model and saves it to the data manager.

        Args:
            staff_type (str): 'medical', 'coaching', or 'management'
        """
        model = None
        headers = []
        data_key = f"{staff_type}_staff"
        source_types = []

        if staff_type == 'medical':
            model = self.medical_staff_model
            headers = self.medical_staff_headers
            source_types = self._medical_staff_types_source
        elif staff_type == 'coaching':
            model = self.coaching_staff_model
            headers = self.coaching_staff_headers
            source_types = self._coaching_staff_types_source
        elif staff_type == 'management':
            model = self.management_staff_model
            headers = self.management_staff_headers
            source_types = self._management_staff_types_source
        else:
            print(f"Error: Unknown staff type '{staff_type}' in _save_staff_list_to_manager")
            return

        if not model or not headers:
            print(f"Error: Model or headers not found for staff type '{staff_type}'")
            return

        updated_staff_list = []
        for row in range(model.rowCount()):
            staff_member = {}
            # Use EditRole to get underlying data, especially for combo boxes
            # But header names match the intended keys in the JSON
            for col_idx, header_key in enumerate(headers):
                item = model.item(row, col_idx)

                # Special handling for Type column
                if header_key == "Type":
                    # First try to get the source value from UserRole
                    type_index = model.index(row, col_idx)
                    source_value = model.data(type_index, Qt.ItemDataRole.UserRole)

                    if source_value:
                        # If we have a source value, use it
                        value = source_value
                        print(f"Using source value '{source_value}' for Type in row {row}")
                    elif isinstance(self.sender(), QComboBox):
                        # If called from delegate, use text
                        value = item.text() if item else ""
                    elif item:
                        # Otherwise get the current display value
                        value = model.data(type_index, Qt.ItemDataRole.EditRole)

                        # If we have a value but no source value, try to find the source value
                        if value and not source_value:
                            # Find the source value for this displayed value
                            for i, translated_type in enumerate([self.tr(t) for t in source_types]):
                                if translated_type == value and i < len(source_types):
                                    # Found a match, store the source value
                                    source_value = source_types[i]
                                    print(f"Found source value '{source_value}' for '{value}'")
                                    value = source_value
                                    # Also store it for future use
                                    model.setData(type_index, source_value, Qt.ItemDataRole.UserRole)
                                    break
                    else:
                        value = None
                elif item:
                    value = model.data(item.index(), Qt.ItemDataRole.EditRole) # Get underlying data if possible
                    if value is None: # If EditRole is None (e.g. unassigned combo), get display text
                        value = item.text()
                        # Handle unassigned combo display text
                        if value == self.tr("(Unassigned)") or value == self.tr("-- Select Type --") or value == self.tr("--- Select Country ---"):
                            value = None
                else:
                    value = None # No item

                # Keep Staff ID as string with prefix (don't convert to int)
                if header_key == 'ID' and value is not None:
                    # Keep the prefixed format (M001, C001, MG001, etc.)
                    value = str(value)  # Ensure it's a string

                staff_member[header_key] = value

            # Basic validation: Only include if there's an ID or Name
            if staff_member.get("ID") or staff_member.get("Name"):
                updated_staff_list.append(staff_member)

        print(f"Saving {len(updated_staff_list)} {staff_type} staff members to data manager.")
        self.club_data_manager.set_data(data_key, updated_staff_list)
    # ------------------------------------------ #

    # --- ADD itemChanged Handlers for Staff Tables --- #
    def _handle_medical_staff_item_changed(self, item):
        """Handles item changes in the medical staff table and saves."""
        # We save the entire list on any change for simplicity now
        print(f"Medical staff item changed (row {item.row()}, col {item.column()}), saving list...")
        self._save_staff_list_to_manager('medical')

    def _handle_coaching_staff_item_changed(self, item):
        """Handles item changes in the coaching staff table and saves."""
        print(f"Coaching staff item changed (row {item.row()}, col {item.column()}), saving list...")
        self._save_staff_list_to_manager('coaching')

    def _handle_management_staff_item_changed(self, item):
        """Handles item changes in the management staff table and saves."""
        print(f"Management staff item changed (row {item.row()}, col {item.column()}), saving list...")
        self._save_staff_list_to_manager('management')
    # --------------------------------------------- #

    def _on_staff_sub_tab_changed(self, index):
        print(f"DEBUG: _on_staff_sub_tab_changed called with index: {index}")
        print(f"Staff sub-tab changed to index: {index}")

        if index == 0:  # Management tab
            # --- CORRECTED INDENTATION ---
            if hasattr(self, 'management_staff_model') and hasattr(self, 'management_staff_headers'):
                translated_headers = [self.tr(h) for h in self.management_staff_headers]
                self.management_staff_model.setHorizontalHeaderLabels(translated_headers)
                if hasattr(self, 'management_type_delegate') and hasattr(self, '_management_staff_types_source'):
                    self.management_type_delegate.types = [self.tr(t) for t in self._management_staff_types_source]
                    self.management_type_delegate.placeholder_text = self.tr("-- Select Type --")
                if hasattr(self, 'management_search_label'):
                    self.management_search_label.setText(self.tr("Search:"))
                if hasattr(self, 'filter_management_button'):
                    self.filter_management_button.setText(self.tr("Filter"))
                if hasattr(self, 'clear_filter_management_button'):
                    self.clear_filter_management_button.setText(self.tr("Clear Filter"))
                if hasattr(self, 'clear_search_management_button'):
                    self.clear_search_management_button.setText(self.tr("Clear"))
                if hasattr(self, 'search_management_input'):
                    self.search_management_input.setPlaceholderText(self.tr("Search..."))
            # --- END INDENTED BLOCK ---

        elif index == 1:  # Coaching tab
            try:
                # --- CORRECTED BLOCK ---
                if hasattr(self, 'coaching_staff_model') and hasattr(self, 'coaching_staff_headers'): # Ensure model/headers exist
                    translated_headers = [self.tr(h) for h in self.coaching_staff_headers]
                    self.coaching_staff_model.setHorizontalHeaderLabels(translated_headers)
                    # Update delegates and buttons
                    if hasattr(self, 'coaching_type_delegate') and hasattr(self, '_coaching_staff_types_source'):
                        self.coaching_type_delegate.types = [self.tr(t) for t in self._coaching_staff_types_source]
                        self.coaching_type_delegate.placeholder_text = self.tr("-- Select Type --")
                    if hasattr(self, 'coaching_search_label'):
                        self.coaching_search_label.setText(self.tr("Search:"))
                    if hasattr(self, 'filter_coaching_staff_button'):
                        self.filter_coaching_staff_button.setText(self.tr("Filter"))
                    if hasattr(self, 'clear_filter_coaching_button'):
                        self.clear_filter_coaching_button.setText(self.tr("Clear Filter"))
                    if hasattr(self, 'clear_search_coaching_button'):
                        self.clear_search_coaching_button.setText(self.tr("Clear"))
                    if hasattr(self, 'search_coaching_staff_input'):
                        self.search_coaching_staff_input.setPlaceholderText(self.tr("Search..."))
                    # Check if button is enabled
                    if hasattr(self, 'filter_coaching_staff_button'):
                        is_enabled = self.filter_coaching_staff_button.isEnabled()
                        print(f"DEBUG: Coaching tab selected. filter_coaching_staff_button.isEnabled() = {is_enabled}")
                    else:
                        print("DEBUG: Coaching tab selected, but filter_coaching_staff_button NOT found in _on_staff_sub_tab_changed.")
                else: # Handle case where model/headers might not exist yet
                    print("DEBUG: Coaching tab selected, but model/headers not found.")
            except Exception as e:
                 print(f"ERROR in _on_staff_sub_tab_changed for index 1 (Coaching): {e}")

        elif index == 2:  # Medical tab
            # --- CORRECTED INDENTATION ---
            if hasattr(self, 'medical_staff_model') and hasattr(self, 'medical_staff_headers'):
                translated_headers = [self.tr(h) for h in self.medical_staff_headers]
                self.medical_staff_model.setHorizontalHeaderLabels(translated_headers)
                if hasattr(self, '_medical_staff_types_source') and hasattr(self, 'medical_staff_table'):
                    translated_types = [self.tr(t) for t in self._medical_staff_types_source]
                    translated_placeholder = self.tr("-- Select Type --")
                    type_delegate = StaffTypeDelegate(
                        types_list=translated_types,
                        placeholder_text=translated_placeholder,
                        parent=self.medical_staff_table
                    )
                    self.medical_staff_table.setItemDelegateForColumn(1, type_delegate)
                if hasattr(self, 'medical_search_label'):
                    self.medical_search_label.setText(self.tr("Search:"))
                if hasattr(self, 'filter_staff_button'):
                    self.filter_staff_button.setText(self.tr("Filter"))
                if hasattr(self, 'clear_filter_button'):
                    self.clear_filter_button.setText(self.tr("Clear Filter"))
                if hasattr(self, 'clear_search_button'):
                    self.clear_search_button.setText(self.tr("Clear"))
                if hasattr(self, 'search_staff_input'):
                    self.search_staff_input.setPlaceholderText(self.tr("Search..."))
            # --- END INDENTED BLOCK ---

    def _update_staff_filter_buttons(self):
        # --- CORRECTED INDENTATION ---
        pass # Method body is empty as intended

    def _update_contact_tab_content(self):
        """Updates all translatable content in the Contact tab."""
        # Update Group Box Title
        if hasattr(self, 'contact_group_box'):
            self.contact_group_box.setTitle(self.tr("Contact Information"))
        # Update Form Labels
        if hasattr(self, 'contact_form_layout'):
            contact_labels = {
                self.contact_address1_input: self.tr("Address Line 1:"),
                self.contact_address2_input: self.tr("Address Line 2:"),
                self.contact_city_input: self.tr("City/Town:"),
                self.contact_state_input: self.tr("State/Province:"),
                self.contact_postal_input: self.tr("Postal Code:"),
                self.contact_phone_input: self.tr("Phone:"),
                self.contact_email_input: self.tr("Email:"),
                self.contact_website_input: self.tr("Website:")
            }
            for widget, label_text in contact_labels.items():
                try:
                    label_item = self.contact_form_layout.labelForField(widget)
                    if label_item:
                        label_item.setText(label_text)
                except Exception as e:
                    print(f"Error updating contact label for {widget}: {e}")

    def _update_staff_tab_content(self):
        # --- Management Sub-Tab ---
        if hasattr(self, 'add_management_button'):
            self.add_management_button.setText(self.tr("Add"))
        if hasattr(self, 'remove_management_button'):
            self.remove_management_button.setText(self.tr("Remove"))
        if hasattr(self, 'management_search_label'):
            self.management_search_label.setText(self.tr("Search:"))
        if hasattr(self, 'search_management_input'):
            self.search_management_input.setPlaceholderText(self.tr("Search..."))
        if hasattr(self, 'clear_search_management_button'):
            self.clear_search_management_button.setText(self.tr("Clear"))
        if hasattr(self, 'filter_management_button'):
            self.filter_management_button.setText(self.tr("Filter"))
        if hasattr(self, 'clear_filter_management_button'):
            self.clear_filter_management_button.setText(self.tr("Clear Filter"))
        if hasattr(self, 'management_staff_model') and hasattr(self, 'management_staff_headers'):
            self.management_staff_model.setHorizontalHeaderLabels([self.tr(h) for h in self.management_staff_headers])
            # Force header repaint - might help if headers don't update visually
            if hasattr(self, 'management_staff_table') and self.management_staff_table.horizontalHeader():
                self.management_staff_table.horizontalHeader().viewport().update()
        # --- FIX: Update Management Delegate ---
        if hasattr(self, 'management_type_delegate') and hasattr(self, '_management_staff_types_source'):
            translated_management_types = [self.tr(t) for t in self._management_staff_types_source]
            # print(f"DEBUG: Translated Management Types: {translated_management_types}") # Keep for debug if needed
            self.management_type_delegate.types = translated_management_types
            self.management_type_delegate.placeholder_text = self.tr("-- Select Type --")
        if hasattr(self, 'management_country_delegate'): # Country delegate placeholder update
            self.management_country_delegate.placeholder_text = self.tr("--- Select Country ---")

        management_type_delegate_updated = False
        management_country_delegate_updated = False

        if hasattr(self, 'management_type_delegate') and hasattr(self, '_management_staff_types_source'):
            translated_management_types = [self.tr(t) for t in self._management_staff_types_source]
            # print(f"DEBUG: Translated Management Types: {translated_management_types}") # Keep for debug if needed
            self.management_type_delegate.types = translated_management_types
            self.management_type_delegate.placeholder_text = self.tr("-- Select Type --")
            management_type_delegate_updated = True
        if hasattr(self, 'management_country_delegate'):
            self.management_country_delegate.placeholder_text = self.tr("--- Select Country ---")
            # Note: country delegate types (countries list) don't need translation here
            management_country_delegate_updated = True

        # --- NEW: Close and reopen persistent editors for Management while preserving selections ---
        if hasattr(self, 'management_staff_table') and hasattr(self, 'management_staff_model'):
            model = self.management_staff_model
            table = self.management_staff_table
            for row in range(model.rowCount()):
                if management_type_delegate_updated:
                    type_index = model.index(row, 1) # Column 1 is Type
                    if type_index.isValid():
                        # Store current value before closing editor
                        current_value = model.data(type_index, Qt.ItemDataRole.EditRole)
                        print(f"DEBUG: Management row {row} current type value: '{current_value}'")

                        # If we have a value, find its translated equivalent
                        if current_value:
                            # Find the English source value index
                            source_index = -1
                            for i, source_type in enumerate(self._management_staff_types_source):
                                if source_type == current_value:
                                    source_index = i
                                    break

                            # If found, get the translated value at the same index
                            if source_index >= 0 and source_index < len(translated_management_types):
                                translated_value = translated_management_types[source_index]
                                print(f"DEBUG: Translating '{current_value}' to '{translated_value}'")

                                # Close editor, update model with translated value, then reopen
                                table.closePersistentEditor(type_index)
                                model.setData(type_index, translated_value, Qt.ItemDataRole.EditRole)
                                table.openPersistentEditor(type_index)
                            else:
                                # Just reopen if we can't find a translation
                                table.closePersistentEditor(type_index)
                                table.openPersistentEditor(type_index)
                        else:
                            # No value to preserve, just reopen
                            table.closePersistentEditor(type_index)
                            table.openPersistentEditor(type_index)

                if management_country_delegate_updated:
                    country_index = model.index(row, 7) # Column 7 is Country
                    if country_index.isValid():
                        # Countries don't need translation as they're the same in all languages
                        table.closePersistentEditor(country_index)
                        table.openPersistentEditor(country_index)
        # --- End NEW ---

        # --- Coaching Sub-Tab ---
        # ... (Update buttons, labels, headers as before) ...
        if hasattr(self, 'add_coaching_staff_button'):
            self.add_coaching_staff_button.setText(self.tr("Add"))
        if hasattr(self, 'remove_coaching_staff_button'):
            self.remove_coaching_staff_button.setText(self.tr("Remove"))
        if hasattr(self, 'coaching_search_label'):
            self.coaching_search_label.setText(self.tr("Search:"))
        if hasattr(self, 'search_coaching_staff_input'):
            self.search_coaching_staff_input.setPlaceholderText(self.tr("Search..."))
        if hasattr(self, 'clear_search_coaching_button'):
            self.clear_search_coaching_button.setText(self.tr("Clear"))
        if hasattr(self, 'filter_coaching_staff_button'):
            self.filter_coaching_staff_button.setText(self.tr("Filter"))
        if hasattr(self, 'clear_filter_coaching_button'):
            self.clear_filter_coaching_button.setText(self.tr("Clear Filter"))
        if hasattr(self, 'coaching_staff_model') and hasattr(self, 'coaching_staff_headers'):
            self.coaching_staff_model.setHorizontalHeaderLabels([self.tr(h) for h in self.coaching_staff_headers])
            if hasattr(self, 'coaching_staff_table') and self.coaching_staff_table.horizontalHeader():
                 self.coaching_staff_table.horizontalHeader().viewport().update() # Force repaint

        coaching_type_delegate_updated = False
        coaching_country_delegate_updated = False # Track country update for coaching too

        if hasattr(self, 'coaching_type_delegate') and hasattr(self, '_coaching_staff_types_source'):
            translated_coaching_types = [self.tr(t) for t in self._coaching_staff_types_source]
            # print(f"DEBUG: Translated Coaching Types: {translated_coaching_types}") # Keep for debug if needed
            self.coaching_type_delegate.types = translated_coaching_types
            self.coaching_type_delegate.placeholder_text = self.tr("-- Select Type --")
            coaching_type_delegate_updated = True
        if hasattr(self, 'coaching_country_delegate'):
            self.coaching_country_delegate.placeholder_text = self.tr("--- Select Country ---")
            # Note: country delegate types (countries list) don't need translation here
            coaching_country_delegate_updated = True

        # --- NEW: Close and reopen persistent editors for Coaching while preserving selections ---
        if hasattr(self, 'coaching_staff_table') and hasattr(self, 'coaching_staff_model'):
            model = self.coaching_staff_model
            table = self.coaching_staff_table
            for row in range(model.rowCount()):
                if coaching_type_delegate_updated:
                    type_index = model.index(row, 1) # Column 1 is Type
                    if type_index.isValid():
                        # Store current value before closing editor
                        current_value = model.data(type_index, Qt.ItemDataRole.EditRole)
                        print(f"DEBUG: Coaching row {row} current type value: '{current_value}'")

                        # If we have a value, find its translated equivalent
                        if current_value:
                            # Find the English source value index
                            source_index = -1
                            for i, source_type in enumerate(self._coaching_staff_types_source):
                                if source_type == current_value:
                                    source_index = i
                                    break

                            # If found, get the translated value at the same index
                            if source_index >= 0 and source_index < len(translated_coaching_types):
                                translated_value = translated_coaching_types[source_index]
                                print(f"DEBUG: Translating '{current_value}' to '{translated_value}'")

                                # Close editor, update model with translated value, then reopen
                                table.closePersistentEditor(type_index)
                                model.setData(type_index, translated_value, Qt.ItemDataRole.EditRole)
                                table.openPersistentEditor(type_index)
                            else:
                                # Just reopen if we can't find a translation
                                table.closePersistentEditor(type_index)
                                table.openPersistentEditor(type_index)
                        else:
                            # No value to preserve, just reopen
                            table.closePersistentEditor(type_index)
                            table.openPersistentEditor(type_index)

                if coaching_country_delegate_updated:
                    country_index = model.index(row, 7) # Column 7 is Country
                    if country_index.isValid():
                        # Countries don't need translation as they're the same in all languages
                        table.closePersistentEditor(country_index)
                        table.openPersistentEditor(country_index)
        # --- End NEW ---

        # --- Medical Sub-Tab ---
        # ... (Update buttons, labels, headers as before) ...
        if hasattr(self, 'add_staff_button'):
            self.add_staff_button.setText(self.tr("Add"))
        if hasattr(self, 'remove_staff_button'):
            self.remove_staff_button.setText(self.tr("Remove"))
        if hasattr(self, 'medical_search_label'):
            self.medical_search_label.setText(self.tr("Search:"))
        if hasattr(self, 'search_staff_input'):
            self.search_staff_input.setPlaceholderText(self.tr("Search..."))
        if hasattr(self, 'clear_search_button'):
            self.clear_search_button.setText(self.tr("Clear"))
        if hasattr(self, 'filter_staff_button'):
            self.filter_staff_button.setText(self.tr("Filter"))
        if hasattr(self, 'clear_filter_button'):
            self.clear_filter_button.setText(self.tr("Clear Filter"))
        if hasattr(self, 'medical_staff_model') and hasattr(self, 'medical_staff_headers'):
            self.medical_staff_model.setHorizontalHeaderLabels([self.tr(h) for h in self.medical_staff_headers])
            if hasattr(self, 'medical_staff_table') and self.medical_staff_table.horizontalHeader():
                 self.medical_staff_table.horizontalHeader().viewport().update() # Force repaint

        medical_delegate = None
        if hasattr(self, 'medical_staff_table'):
             delegate = self.medical_staff_table.itemDelegateForColumn(1)
             if isinstance(delegate, StaffTypeDelegate):
                 medical_delegate = delegate

        medical_delegate_updated = False # Track if delegate was updated
        if medical_delegate and hasattr(self, '_medical_staff_types_source'):
             translated_medical_types = [self.tr(t) for t in self._medical_staff_types_source]
             # print(f"DEBUG: Translated Medical Types: {translated_medical_types}") # Keep for debug if needed
             medical_delegate.types = translated_medical_types
             medical_delegate.placeholder_text = self.tr("-- Select Type --")
             medical_delegate_updated = True

        # --- NEW: Close and reopen persistent editors for Medical while preserving selections ---
        if medical_delegate_updated and hasattr(self, 'medical_staff_table') and hasattr(self, 'medical_staff_model'):
            model = self.medical_staff_model
            table = self.medical_staff_table
            for row in range(model.rowCount()):
                type_index = model.index(row, 1) # Column 1 is Type
                if type_index.isValid():
                    # Store current value before closing editor
                    current_value = model.data(type_index, Qt.ItemDataRole.EditRole)
                    print(f"DEBUG: Medical row {row} current type value: '{current_value}'")

                    # If we have a value, find its translated equivalent
                    if current_value:
                        # Find the English source value index
                        source_index = -1
                        for i, source_type in enumerate(self._medical_staff_types_source):
                            if source_type == current_value:
                                source_index = i
                                break

                        # If found, get the translated value at the same index
                        if source_index >= 0 and source_index < len(translated_medical_types):
                            translated_value = translated_medical_types[source_index]
                            print(f"DEBUG: Translating '{current_value}' to '{translated_value}'")

                            # Close editor, update model with translated value, then reopen
                            table.closePersistentEditor(type_index)
                            model.setData(type_index, translated_value, Qt.ItemDataRole.EditRole)
                            table.openPersistentEditor(type_index)
                        else:
                            # Just reopen if we can't find a translation
                            table.closePersistentEditor(type_index)
                            table.openPersistentEditor(type_index)
                    else:
                        # No value to preserve, just reopen
                        table.closePersistentEditor(type_index)
                        table.openPersistentEditor(type_index)
        # --- End NEW ---

        # --- REMOVE Repaint calls here, done implicitly by editor reopen? ---
        # if hasattr(self, 'management_staff_table'):
        #     self.management_staff_table.viewport().update()
        # if hasattr(self, 'coaching_staff_table'):
        #     self.coaching_staff_table.viewport().update()
        # if hasattr(self, 'medical_staff_table'):
        #     self.medical_staff_table.viewport().update()

    def _update_kit_tab_content(self):
        print("DEBUG: Entered _update_kit_tab_content")
        # Update Kit sub-tab titles
        if hasattr(self, 'kit_sub_tab_widget'):
            for i, title in enumerate(["Colors", "1st Kit", "2nd Kit", "3rd Kit", "1st GK Kit", "2nd GK Kit"]):
                print(f"DEBUG: Setting kit_sub_tab_widget tab {i} title to {self.tr(title)}")
                self.kit_sub_tab_widget.setTabText(i, self.tr(title))

        # Colors sub-tab labels
        if hasattr(self, 'label_color_1st'):
            print(f"DEBUG: Setting label_color_1st to {self.tr('1st Color:')}")
            self.label_color_1st.setText(self.tr("1st Color:"))
        if hasattr(self, 'label_color_2nd'):
            print(f"DEBUG: Setting label_color_2nd to {self.tr('2nd Color:')}")
            self.label_color_2nd.setText(self.tr("2nd Color:"))
        if hasattr(self, 'label_color_3rd'):
            print(f"DEBUG: Setting label_color_3rd to {self.tr('3rd Color:')}")
            self.label_color_3rd.setText(self.tr("3rd Color:"))
        if hasattr(self, 'color_preview_label'):
            print(f"DEBUG: Setting color_preview_label to {self.tr('FOOT|DATA')}")
            self.color_preview_label.setText(self.tr("FOOT|DATA"))

        kit_keys = ["1st", "2nd", "3rd", "1st_gk", "2nd_gk"]
        for key in kit_keys:
            # Group boxes
            gb = getattr(self, f"kit_{key}_images_group_box", None)
            if gb:
                print(f"DEBUG: Setting {key} images_group_box title to {self.tr('Images')}")
                gb.setTitle(self.tr("Images"))
            gb = getattr(self, f"kit_{key}_settings_group_box", None)
            if gb:
                print(f"DEBUG: Setting {key} settings_group_box title to {self.tr('Settings')}")
                gb.setTitle(self.tr("Settings"))
            gb = getattr(self, f"kit_{key}_name_settings_group_box", None)
            if gb:
                print(f"DEBUG: Setting {key} name_settings_group_box title to {self.tr('Name')}" )
                gb.setTitle(self.tr("Name"))
            gb = getattr(self, f"kit_{key}_number_settings_group_box", None)
            if gb:
                print(f"DEBUG: Setting {key} number_settings_group_box title to {self.tr('Number')}")
                gb.setTitle(self.tr("Number"))

            # Section labels
            lbl = getattr(self, f"kit_{key}_front_title_label", None)
            if lbl:
                print(f"DEBUG: Setting {key} front_title_label to {self.tr('Front')}")
                lbl.setText(self.tr("Front"))
            lbl = getattr(self, f"kit_{key}_back_title_label", None)
            if lbl:
                print(f"DEBUG: Setting {key} back_title_label to {self.tr('Back')}")
                lbl.setText(self.tr("Back"))

            # Button texts (Upload/Remove)
            btn = getattr(self, f"kit_{key}_front_upload_button", None)
            if btn:
                print(f"DEBUG: Setting {key} front_upload_button to {self.tr('Upload')}")
                btn.setText(self.tr("Upload"))
            btn = getattr(self, f"kit_{key}_front_remove_button", None)
            if btn:
                print(f"DEBUG: Setting {key} front_remove_button to {self.tr('Remove')}")
                btn.setText(self.tr("Remove"))
            btn = getattr(self, f"kit_{key}_back_upload_button", None)
            if btn:
                print(f"DEBUG: Setting {key} back_upload_button to {self.tr('Upload')}")
                btn.setText(self.tr("Upload"))
            btn = getattr(self, f"kit_{key}_back_remove_button", None)
            if btn:
                print(f"DEBUG: Setting {key} back_remove_button to {self.tr('Remove')}")
                btn.setText(self.tr("Remove"))

            # Constraints labels (if any)
            lbl = getattr(self, f"kit_{key}_front_constraints_label", None)
            if lbl:
                print(f"DEBUG: Setting {key} front_constraints_label to {self.tr('(PNG, <300KB, <300x300px)')}")
                lbl.setText(self.tr("(PNG, <300KB, <300x300px)"))
            lbl = getattr(self, f"kit_{key}_back_constraints_label", None)
            if lbl:
                print(f"DEBUG: Setting {key} back_constraints_label to {self.tr('(PNG, <300KB, <300x300px)')}")
                lbl.setText(self.tr("(PNG, <300KB, <300x300px)"))

            # Name settings form labels (update existing label widgets)
            gb = getattr(self, f"kit_{key}_name_settings_group_box", None)
            if gb and isinstance(gb.layout(), QFormLayout):
                form = gb.layout()
                name_labels = [
                    self.tr("Font Color:"),
                    self.tr("Font Family:"),
                    self.tr("Font Size:"),
                    self.tr("Vertical Offset:"),
                    self.tr("Enable Outline:"),
                    self.tr("Outline Color:"),
                    self.tr("Outline Thickness:")
                ]
                for i, label_text in enumerate(name_labels):
                    item = form.itemAt(i, QFormLayout.LabelRole)
                    if item and item.widget():
                        print(f"DEBUG: Updating {key} name_settings_group_box form row {i} label to {label_text}")
                        item.widget().setText(label_text)

            # Number settings form labels (update existing label widgets)
            gb = getattr(self, f"kit_{key}_number_settings_group_box", None)
            if gb and isinstance(gb.layout(), QFormLayout):
                form = gb.layout()
                number_labels = [
                    self.tr("Font Color:"),
                    self.tr("Font Family:"),
                    self.tr("Font Size:"),
                    self.tr("Vertical Offset:"),
                    self.tr("Enable Outline:"),
                    self.tr("Outline Color:"),
                    self.tr("Outline Thickness:")
                ]
                for i, label_text in enumerate(number_labels):
                    item = form.itemAt(i, QFormLayout.LabelRole)
                    if item and item.widget():
                        print(f"DEBUG: Updating {key} number_settings_group_box form row {i} label to {label_text}")
                        item.widget().setText(label_text)

        # Force repaint
        if hasattr(self, 'kit_sub_tab_widget'):
            print("DEBUG: Forcing kit_sub_tab_widget repaint()")
            self.kit_sub_tab_widget.repaint()

    def eventFilter(self, watched, event):
        # Check if the event is for the specific button we are watching
        coaching_filter_button = getattr(self, 'filter_coaching_staff_button', None) # Get button safely

        if coaching_filter_button and watched == coaching_filter_button:
            # Optional: Keep for verbose debug
            # print(f"DEBUG EventFilter: Button received event type: {event.type()}")

            # Check for MouseButtonRelease or Space KeyPress
            should_trigger_action = False
            if event.type() == QEvent.Type.MouseButtonRelease:
                # Check if the release happened inside the button's bounds
                if coaching_filter_button.rect().contains(event.pos()):
                    print("DEBUG EventFilter: MouseButtonRelease on filter_coaching_staff_button - Calling action directly.")
                    should_trigger_action = True
            elif event.type() == QEvent.Type.KeyPress and event.key() == Qt.Key.Key_Space:
                 print(f"DEBUG EventFilter: Space KeyPress on filter_coaching_staff_button - Calling action directly.")
                 should_trigger_action = True

            # --- Directly call the slot if triggered ---
            if should_trigger_action:
                try:
                    # --- Call RENAMED method ---
                    print(f"DEBUG EventFilter: Attempting to call RENAMED method: _open_coaching_staff_filter_dialog")
                    self._open_coaching_staff_filter_dialog() # CORRECTED Call
                    # --------------------------
                except Exception as e:
                    print(f"ERROR directly calling _open_coaching_staff_filter_dialog from eventFilter: {e}")
                # --- End Direct Call ---

        # Important: Pass the event along for normal processing regardless of direct call
        return super().eventFilter(watched, event)

    def _open_coaching_staff_filter_dialog(self): # RENAMED
        """Opens a dialog to set filters and applies them to the coaching staff table."""
        print("DEBUG: _open_coaching_staff_filter_dialog called")
        # Get translated types for the dialog's Type combo box
        translated_types = [self.tr(t) for t in self._coaching_staff_types_source]

        # Build filterable columns metadata
        filterable_columns = {}
        for header in self.coaching_staff_headers:
            if header == "ID": continue # Skip ID

            field_key = header
            label_text = self.tr(header)
            field_type = 'text'
            options = None

            if header == "Type":
                field_type = 'combo'
                options = translated_types
            elif header == "Country":
                field_type = 'combo'
                options = countries # Use imported list

            filterable_columns[field_key] = {
                'type': field_type,
                'label': label_text
            }
            if options:
                filterable_columns[field_key]['options'] = options

        dialog_data = self.coaching_staff_filters.copy()
        # Add metadata
        dialog_data['_filterable_columns'] = filterable_columns

        dialog = StaffFilterDialog(
            current_filters=dialog_data,
            parent=self
        )

        if dialog.exec():
            new_filters = dialog.get_filters()
            print(f"DEBUG: Coaching Dialog accepted. New filters: {new_filters}")
            self.coaching_staff_filters = new_filters
            self._apply_coaching_staff_filter()
        else:
            print("DEBUG: Coaching Dialog cancelled or rejected.")

    def _load_table_states(self):
        """Load saved column states for all tables (width, order, sorting)."""
        settings = QSettings()

        # Load states for staff tables
        staff_tables = {
            'management': self.management_staff_table,
            'coaching': self.coaching_staff_table,
            'medical': self.medical_staff_table
        }

        for table_name, table in staff_tables.items():
            if table and hasattr(table, 'horizontalHeader'):
                header_state = settings.value(f"clubWindow/{table_name}StaffTable/headerState")
                header = table.horizontalHeader()

                if isinstance(header_state, QByteArray) and not header_state.isEmpty():
                    try:
                        if header.restoreState(header_state):
                            # Get restored sorting info for debugging
                            sort_column = header.sortIndicatorSection()
                            sort_order = header.sortIndicatorOrder()
                            print(f"Restored {table_name} staff table state (sort: column {sort_column}, order {sort_order})")
                        else:
                            print(f"Failed to restore {table_name} staff table header state")
                    except Exception as e:
                        print(f"Error restoring {table_name} staff table header state: {e}")
                else:
                    print(f"No saved header state found for {table_name} staff table - using defaults")

        # Load state for Team Groups table
        if hasattr(self, 'groups_widget') and self.groups_widget:
            team_groups_table = getattr(self.groups_widget, 'table_view', None)
            if team_groups_table and hasattr(team_groups_table, 'horizontalHeader'):
                header_state = settings.value(f"clubWindow/teamGroupsTable/headerState")
                header = team_groups_table.horizontalHeader()

                if isinstance(header_state, QByteArray) and not header_state.isEmpty():
                    try:
                        if header.restoreState(header_state):
                            sort_column = header.sortIndicatorSection()
                            sort_order = header.sortIndicatorOrder()
                            print(f"Restored team groups table state (sort: column {sort_column}, order {sort_order})")
                        else:
                            print(f"Failed to restore team groups table header state")
                    except Exception as e:
                        print(f"Error restoring team groups table header state: {e}")
                else:
                    print(f"No saved header state found for team groups table - using defaults")

    def _save_table_states(self):
        """Save column states for all tables (width, order, sorting)."""
        settings = QSettings()

        # Save states for staff tables
        staff_tables = {
            'management': self.management_staff_table,
            'coaching': self.coaching_staff_table,
            'medical': self.medical_staff_table
        }

        for table_name, table in staff_tables.items():
            if table and hasattr(table, 'horizontalHeader'):
                try:
                    header = table.horizontalHeader()
                    # Save complete header state (includes column width, order, and sorting)
                    header_state = header.saveState()
                    settings.setValue(f"clubWindow/{table_name}StaffTable/headerState", header_state)

                    # Also save sorting information separately for debugging
                    sort_column = header.sortIndicatorSection()
                    sort_order = header.sortIndicatorOrder()
                    settings.setValue(f"clubWindow/{table_name}StaffTable/sortColumn", sort_column)
                    settings.setValue(f"clubWindow/{table_name}StaffTable/sortOrder", int(sort_order))

                    print(f"Saved {table_name} staff table state (sort: column {sort_column}, order {sort_order})")
                except Exception as e:
                    print(f"Error saving {table_name} staff table state: {e}")

        # Save state for Team Groups table
        if hasattr(self, 'groups_widget') and self.groups_widget:
            team_groups_table = getattr(self.groups_widget, 'table_view', None)
            if team_groups_table and hasattr(team_groups_table, 'horizontalHeader'):
                try:
                    header = team_groups_table.horizontalHeader()
                    header_state = header.saveState()
                    settings.setValue(f"clubWindow/teamGroupsTable/headerState", header_state)

                    sort_column = header.sortIndicatorSection()
                    sort_order = header.sortIndicatorOrder()
                    settings.setValue(f"clubWindow/teamGroupsTable/sortColumn", sort_column)
                    settings.setValue(f"clubWindow/teamGroupsTable/sortOrder", int(sort_order))

                    print(f"Saved team groups table state (sort: column {sort_column}, order {sort_order})")
                except Exception as e:
                    print(f"Error saving team groups table state: {e}")

    # Keep the old method name for backward compatibility
    def _save_staff_table_states(self):
        """Legacy method - calls the new comprehensive save method."""
        self._save_table_states()

    def _connect_header_state_signals(self):
        """Connect signals to save column states when headers change."""
        # Connect staff table signals
        staff_tables = {
            'management': self.management_staff_table,
            'coaching': self.coaching_staff_table,
            'medical': self.medical_staff_table
        }

        for table_name, table in staff_tables.items():
            if table and hasattr(table, 'horizontalHeader'):
                header = table.horizontalHeader()
                # Connect signals for column resize, move, and sorting
                header.sectionResized.connect(self._save_table_states)
                header.sectionMoved.connect(self._save_table_states)
                header.sortIndicatorChanged.connect(self._save_table_states)
                print(f"Connected header state signals for {table_name} staff table (resize, move, sort)")

        # Connect Team Groups table signals
        if hasattr(self, 'groups_widget') and self.groups_widget:
            team_groups_table = getattr(self.groups_widget, 'table_view', None)
            if team_groups_table and hasattr(team_groups_table, 'horizontalHeader'):
                header = team_groups_table.horizontalHeader()
                header.sectionResized.connect(self._save_table_states)
                header.sectionMoved.connect(self._save_table_states)
                header.sortIndicatorChanged.connect(self._save_table_states)
                print(f"Connected header state signals for team groups table (resize, move, sort)")

    def closeEvent(self, event):
        """Handle window close event to save all table states."""
        # Save all table states before closing
        self._save_table_states()

        # Call parent close event
        super().closeEvent(event)

    def save_data(self):
        """Save all club data to files."""
        print("ClubWindow: Saving all club data...")

        # First save any section-specific data
        try:
            # Save sponsors data if the panel is initialized
            if hasattr(self, 'sponsors_panel') and self.sponsors_panel:
                print("  - Saving sponsors panel data...")
                try:
                    self.sponsors_panel.save_data()
                    print("  - Sponsors panel data saved successfully")
                except Exception as e:
                    print(f"  - ERROR saving sponsors panel data: {e}")
        except Exception as e:
            print(f"ERROR during save_data sections: {e}")

        # Finally, save the main club data manager
        try:
            if hasattr(self, 'club_data_manager') and self.club_data_manager:
                print("  - Saving club_data_manager...")
                self.club_data_manager.save_data()
                print("  - Club data saved successfully")
        except Exception as e:
            print(f"ERROR saving club data: {e}")

        print("ClubWindow: save_data complete")

# --- Example Run Block ---
# (Useful for testing this window in isolation)
if __name__ == '__main__':
    # Need dummy translator and basic app setup for tr() to work
    QCoreApplication.setOrganizationName("TestOrg")
    QCoreApplication.setApplicationName("ClubWindowTest")
    app = QApplication(sys.argv)
    translator = QTranslator() # Dummy translator
    translator.load("translations/app_el.qm")
    QCoreApplication.installTranslator(translator)

    window = ClubWindow()
    window.show()
    sys.exit(app.exec())