{"club_name": "acma", "short_name": "acm", "nickname": "", "year_founded": 2024, "city": "milanoo", "country": "Germany", "region": "", "logo_exists": true, "stadium_name": "ss", "capacity": 0, "seating_capacity": 0, "surface_type": "Artificial Turf", "year_built": 2024, "stadium_owner": "Club Owned", "stadium_image_exists": true, "color_1st": "#aa0000", "color_2nd": "#000000", "color_3rd": "#ffffff", "contact_address1": "", "contact_address2": "", "contact_city": "", "contact_state": "", "contact_postal": "", "contact_phone": "", "contact_email": "", "contact_website": "", "medical_staff": [{"ID": 1, "Type": "Physiotherapists", "Name": "Head physio", "Phone": "", "Email": "", "Office": "", "Working hours": ""}, {"ID": 2, "Type": "", "Name": "", "Phone": "", "Email": "", "Office": "", "Working hours": ""}], "coaching_staff": [{"ID": 1, "Type": "Head Coach", "Name": "HeadCoach name", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}, {"ID": 2, "Type": "", "Name": "", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}], "management_staff": [{"ID": 3, "Type": "Chairman", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": 4, "Type": "Chief Executive Officer (CEO)", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": 5, "Type": "Operations Manager", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": 6, "Type": "Club Technical Director", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": 7, "Type": "Chief Financial Officer (CFO)", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": 8, "Type": "Head of Marketing", "Name": "<PERSON>", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "Italy", "Address": "", "Office": "", "Working hours": ""}, {"ID": 9, "Type": "", "Name": "", "Phone 1": "", "Phone 2": "", "Email": "", "City": "", "Country": "", "Address": "", "Office": "", "Working hours": ""}], "kit_1_front_exists": true, "kit_1_back_exists": true, "kit_1_name_color": "#ffffff", "kit_1_name_font_family": "<PERSON><PERSON><PERSON>", "kit_1_name_font_size": 9, "kit_1_name_vpos": -22, "kit_1_name_outline_enabled": false, "kit_1_name_outline_color": "#ffff00", "kit_1_name_outline_thickness": 1, "kit_1_number_color": "#ffffff", "kit_1_number_font_family": "<PERSON><PERSON><PERSON>", "kit_1_number_font_size": 36, "kit_1_number_vpos": -19, "kit_1_number_outline_enabled": false, "kit_1_number_outline_color": "#ffffff", "kit_1_number_outline_thickness": 1, "kit_2_front_exists": true, "kit_2_back_exists": true, "kit_2_name_color": "#000000", "kit_2_name_font_family": "<PERSON><PERSON>", "kit_2_name_font_size": 12, "kit_2_name_vpos": -19, "kit_2_name_outline_enabled": false, "kit_2_name_outline_color": "#FFFFFF", "kit_2_name_outline_thickness": 1, "kit_2_number_color": "#000000", "kit_2_number_font_family": "<PERSON><PERSON>", "kit_2_number_font_size": 36, "kit_2_number_vpos": 0, "kit_2_number_outline_enabled": false, "kit_2_number_outline_color": "#FFFFFF", "kit_2_number_outline_thickness": 1, "kit_3_front_exists": true, "kit_3_back_exists": true, "kit_3_name_color": "#000000", "kit_3_name_font_family": "<PERSON><PERSON>", "kit_3_name_font_size": 10, "kit_3_name_vpos": 0, "kit_3_name_outline_enabled": false, "kit_3_name_outline_color": "#FFFFFF", "kit_3_name_outline_thickness": 1, "kit_3_number_color": "#000000", "kit_3_number_font_family": "<PERSON><PERSON>", "kit_3_number_font_size": 24, "kit_3_number_vpos": 0, "kit_3_number_outline_enabled": false, "kit_3_number_outline_color": "#FFFFFF", "kit_3_number_outline_thickness": 1, "kit_4_front_exists": true, "kit_4_back_exists": true, "kit_4_name_color": "#000000", "kit_4_name_font_family": "<PERSON><PERSON><PERSON>", "kit_4_name_font_size": 9, "kit_4_name_vpos": -22, "kit_4_name_outline_enabled": false, "kit_4_name_outline_color": "#FFFFFF", "kit_4_name_outline_thickness": 1, "kit_4_number_color": "#000000", "kit_4_number_font_family": "<PERSON><PERSON><PERSON>", "kit_4_number_font_size": 36, "kit_4_number_vpos": -14, "kit_4_number_outline_enabled": false, "kit_4_number_outline_color": "#FFFFFF", "kit_4_number_outline_thickness": 1, "kit_5_front_exists": true, "kit_5_back_exists": true, "kit_5_name_color": "#000000", "kit_5_name_font_family": "<PERSON><PERSON>", "kit_5_name_font_size": 10, "kit_5_name_vpos": 0, "kit_5_name_outline_enabled": true, "kit_5_name_outline_color": "#FFFFFF", "kit_5_name_outline_thickness": 1, "kit_5_number_color": "#000000", "kit_5_number_font_family": "<PERSON><PERSON>", "kit_5_number_font_size": 24, "kit_5_number_vpos": 0, "kit_5_number_outline_enabled": false, "kit_5_number_outline_color": "#FFFFFF", "kit_5_number_outline_thickness": 1}